{"nm": "Main Scene", "ddd": 0, "h": 142, "w": 240, "meta": {"g": "@lottiefiles/creator 1.44.0"}, "layers": [{"ty": 4, "nm": "50", "sr": 1, "st": 0, "op": 180, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [10950, 7345.005859375002]}, "s": {"a": 0, "k": [0.17589000000000002, -0.17589000000000002]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [80.8246, 9.0646]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "bm": 0, "hd": false, "nm": "Group 1", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-7, -2], [14, 0], [-7, 3]], "o": [[6, -2], [6, 3], [-14, 0], [0, 0]], "v": [[5148, 4153], [5173, 4153], [5160, 4158], [5148, 4153]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 2", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-102, -7], [-48, 0], [-3, -5], [106, 10]], "o": [[-102, -9], [50, 3], [47, -1], [6, 10], [0, 0]], "v": [[4848, 4149], [4855, 4135], [5032, 4140], [5124, 4148], [4848, 4149]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 3", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, -6], [-4, 12], [4, 4], [-5, 9], [-6, -23], [0, 0], [0, 0], [4, 11], [-8, 7], [9, -39], [1, -39], [5, 0], [-3, 11]], "o": [[4, -16], [-13, 5], [4, -9], [-5, -5], [7, -11], [0, 0], [0, 0], [4, -16], [-4, -12], [19, -18], [-10, 42], [-1, 17], [-5, 0], [0, 0]], "v": [[2317, 4200], [2304, 4187], [2292, 4178], [2290, 4154], [2290, 4130], [2306, 4146], [2314, 4175], [2320, 4145], [2319, 4095], [2326, 4063], [2344, 4103], [2332, 4188], [2321, 4220], [2317, 4200]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 4", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 0], [0, -5], [6, 0], [-3, 6]], "o": [[3, -5], [2, 0], [0, 6], [-5, 0], [0, 0]], "v": [[2375, 4230], [2386, 4220], [2390, 4230], [2379, 4240], [2375, 4230]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 5", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, -5], [2, 0], [3, 6]], "o": [[-3, -5], [6, 0], [0, 6], [-3, 0], [0, 0]], "v": [[2365, 4340], [2369, 4330], [2380, 4340], [2376, 4350], [2365, 4340]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 6", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 3], [-20, 0], [3, 6], [-8, 0], [4, -30], [0, 27]], "o": [[0, -9], [-20, -8], [8, 0], [-3, -5], [11, 0], [-6, 40], [0, 0]], "v": [[2320, 4336], [2305, 4314], [2306, 4290], [2315, 4280], [2324, 4270], [2333, 4308], [2320, 4336]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 7", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 7", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-10, 7], [11, -21], [0, 15]], "o": [[0, -5], [15, -13], [-12, 23], [0, 0]], "v": [[2280, 4351], [2297, 4329], [2301, 4338], [2280, 4351]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 8", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 8", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -3], [11, 1], [-8, 3]], "o": [[7, -3], [4, 3], [-11, 0], [0, 0]], "v": [[4958, 4383], [4977, 4384], [4964, 4389], [4958, 4383]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 9", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 9", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 3], [5, -14], [6, 11]], "o": [[-4, -7], [9, -9], [-4, 11], [0, 0]], "v": [[2356, 4381], [2354, 4363], [2368, 4381], [2356, 4381]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 10", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 10", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 0], [0, -5], [6, 0], [-3, 6]], "o": [[3, -5], [2, 0], [0, 6], [-5, 0], [0, 0]], "v": [[2285, 4380], [2296, 4370], [2300, 4380], [2289, 4390], [2285, 4380]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 11", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 11", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, -3], [0, 4], [-34, -11], [66, -3], [-3, 5]], "o": [[3, -5], [-14, 3], [0, -11], [30, 10], [-29, 1], [0, 0]], "v": [[4837, 4386], [4816, 4382], [4790, 4379], [4920, 4380], [4883, 4393], [4837, 4386]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 12", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 12", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 3], [0, -7], [-18, 3], [17, -8], [7, 13]], "o": [[-4, -6], [3, -4], [0, 7], [22, -4], [-27, 13], [0, 0]], "v": [[5125, 4399], [5124, 4383], [5130, 4389], [5158, 4395], [5165, 4399], [5125, 4399]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 13", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 13", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 0], [3, -5], [3, 0], [0, 6]], "o": [[0, -5], [5, 0], [-3, 6], [-2, 0], [0, 0]], "v": [[5080, 4400], [5091, 4390], [5095, 4400], [5084, 4410], [5080, 4400]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 14", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 14", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-16, -13], [8, 0], [7, 9]], "o": [[-13, -16], [16, 13], [-2, 0], [0, 0]], "v": [[5049, 4413], [5053, 4409], [5066, 4430], [5049, 4413]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 15", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 15", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-16, 0], [0, -4], [-10, 0], [4, -3], [19, 8]], "o": [[-25, -10], [6, 0], [0, 5], [57, -1], [-10, 9], [0, 0]], "v": [[5584, 4440], [5559, 4410], [5570, 4418], [5588, 4427], [5719, 4434], [5584, 4440]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 16", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 16", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-36, 2], [8, -3], [10, 10]], "o": [[-7, -8], [28, 0], [-58, 19], [0, 0]], "v": [[5444, 4436], [5484, 4423], [5520, 4427], [5444, 4436]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 17", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 17", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-11, -3], [20, 0], [3, 5]], "o": [[-4, -5], [23, 6], [-8, 0], [0, 0]], "v": [[5286, 4441], [5300, 4437], [5306, 4450], [5286, 4441]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 18", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 18", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 8], [-8, -14], [9, 9]], "o": [[-4, -3], [0, -15], [7, 11], [0, 0]], "v": [[2287, 4453], [2280, 4433], [2314, 4429], [2287, 4453]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 19", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 19", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 3], [129, 6], [6, 4], [3, -6], [10, 3], [35, -1], [3, 8], [9, -3], [-11, -7], [20, 0], [-12, 7], [18, 0], [-5, -7], [21, 5], [29, 2], [5, 4], [162, 10], [68, 9], [56, -1], [69, 9], [29, 0], [-8, 10], [38, -1], [-17, -2], [4, -5], [6, 3], [51, 0], [5, 10], [36, 6], [-112, -5], [-5, -7], [-3, 9], [-53, -4], [-99, -4], [-557, -37], [6, -4], [39, -4], [28, -7], [84, 4]], "o": [[-33, -1], [-14, -5], [-34, -1], [-6, -4], [-4, 5], [-21, -9], [-15, 1], [-3, -7], [-12, 5], [9, 6], [-23, 0], [12, -8], [-15, 0], [5, 9], [-16, -4], [-29, -2], [-10, -6], [-190, -11], [-117, -14], [-11, 0], [-69, -8], [-40, -1], [8, -10], [-30, 1], [15, 2], [-3, 5], [-6, -4], [-67, 0], [-5, -7], [-56, -9], [78, 3], [6, 8], [4, -7], [223, 13], [120, 6], [186, 12], [-6, 4], [-39, 4], [-58, 16], [0, 0]], "v": [[9025, 4462], [8950, 4454], [8705, 4436], [8633, 4427], [8615, 4431], [8591, 4435], [8467, 4418], [8434, 4405], [8413, 4397], [8412, 4411], [8395, 4420], [8380, 4410], [8372, 4400], [8356, 4411], [8334, 4416], [8253, 4405], [8191, 4395], [7970, 4375], [7630, 4349], [7255, 4318], [7110, 4302], [6933, 4286], [6892, 4273], [6854, 4261], [6833, 4267], [6854, 4281], [6838, 4284], [6734, 4278], [6633, 4265], [6560, 4242], [6625, 4237], [6763, 4254], [6776, 4253], [6858, 4247], [7568, 4285], [9112, 4382], [9438, 4411], [9356, 4425], [9235, 4445], [9025, 4462]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 20", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[8710, 4400], [8699, 4390], [8695, 4400], [8706, 4410], [8710, 4400]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 21", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[8887, 4384], [8868, 4383], [8874, 4389], [8887, 4384]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 22", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[7280, 4290], [7270, 4280], [7260, 4290], [7270, 4300], [7280, 4290]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 23", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[6677, 4254], [6658, 4253], [6664, 4259], [6677, 4254]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 20", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 24", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 1], [-13, 1], [2, -6], [8, 13]], "o": [[-3, -6], [4, 0], [13, -2], [-5, 15], [0, 0]], "v": [[5925, 4461], [5927, 4449], [5958, 4446], [5977, 4454], [5925, 4461]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 21", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 25", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-17, -17], [33, 1], [-15, 10]], "o": [[22, -16], [11, 11], [-34, -1], [0, 0]], "v": [[6197, 4463], [6245, 4464], [6219, 4476], [6197, 4463]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 22", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 26", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 0], [-4, -8], [2, 0], [7, 8]], "o": [[-7, -9], [5, 0], [3, 8], [-2, 0], [0, 0]], "v": [[5880, 4465], [5878, 4450], [5894, 4465], [5896, 4480], [5880, 4465]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 23", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 27", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-15, -2], [33, 0], [-18, 2]], "o": [[18, -2], [15, 2], [-33, 0], [0, 0]], "v": [[7368, 4483], [7428, 4483], [7395, 4487], [7368, 4483]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 24", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 28", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 0], [0, -5], [6, 0], [-3, 6]], "o": [[3, -5], [2, 0], [0, 6], [-5, 0], [0, 0]], "v": [[6345, 4480], [6356, 4470], [6360, 4480], [6349, 4490], [6345, 4480]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 25", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 29", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-11, 0], [4, 10], [-3, 0], [-4, -8], [10, 16]], "o": [[-3, -5], [14, 0], [-4, -8], [3, 0], [6, 18], [0, 0]], "v": [[5975, 4490], [5989, 4480], [6004, 4465], [6003, 4450], [6014, 4465], [5975, 4490]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 26", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 30", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [38, -2], [12, 13], [-18, -6], [-27, -1], [-7, -5], [-34, -1], [-10, -4], [5, 15], [-12, -10], [-5, 8], [-17, -11], [0, 6], [-12, 0], [0, 0], [0, 0], [57, 1], [2, 3]], "o": [[-6, -5], [-19, 1], [-19, -21], [10, 3], [28, 1], [7, 6], [34, 1], [16, 7], [-6, -15], [10, 9], [5, -8], [13, 8], [0, -5], [0, 0], [0, 0], [-16, 11], [-44, -1], [0, 0]], "v": [[6587, 4503], [6416, 4488], [6367, 4469], [6366, 4454], [6435, 4461], [6498, 4473], [6573, 4485], [6654, 4495], [6667, 4485], [6674, 4479], [6695, 4480], [6726, 4485], [6750, 4490], [6772, 4480], [6794, 4480], [6773, 4495], [6671, 4509], [6587, 4503]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 27", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 31", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -3], [11, 1], [-8, 3]], "o": [[7, -3], [4, 3], [-11, 0], [0, 0]], "v": [[6848, 4513], [6867, 4514], [6854, 4519], [6848, 4513]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 28", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 32", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [10, -3], [0, 5], [-29, -12], [58, 1], [3, 4]], "o": [[-3, -5], [-11, 3], [0, -10], [12, 5], [-55, -1], [0, 0]], "v": [[8233, 4527], [8209, 4523], [8190, 4519], [8415, 4531], [8337, 4537], [8233, 4527]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 29", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 33", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-16, -5], [-4, 6], [-15, 0], [10, -6], [8, 3], [0, -9], [-10, -2], [10, 0], [17, 11]], "o": [[-22, -15], [12, 3], [3, -5], [20, 0], [-8, 5], [-7, -3], [0, 9], [11, 3], [-8, 0], [0, 0]], "v": [[7045, 4519], [7037, 4505], [7065, 4500], [7098, 4490], [7111, 4499], [7083, 4503], [7070, 4513], [7088, 4533], [7090, 4538], [7045, 4519]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 30", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 34", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [37, 0], [0, 4], [-59, -10], [6, -10], [24, 14]], "o": [[-10, -6], [-38, 0], [0, -12], [94, 15], [-8, 13], [0, 0]], "v": [[7416, 4538], [7329, 4527], [7260, 4519], [7378, 4513], [7475, 4540], [7416, 4538]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 31", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 35", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 3], [0, 9], [26, -4], [0, 16], [-10, -4], [-36, -5], [44, 0], [7, -9], [11, 2], [6, -4], [0, 6]], "o": [[0, -5], [6, -3], [0, -12], [-27, 4], [0, -13], [7, 3], [63, 9], [-11, 0], [-7, 8], [-12, -1], [-7, 5], [0, 0]], "v": [[6870, 4541], [6880, 4525], [6890, 4503], [6857, 4493], [6824, 4479], [6839, 4465], [6918, 4480], [6983, 4510], [6950, 4526], [6916, 4538], [6883, 4543], [6870, 4541]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 36", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, 0], [0, -8], [-13, 3], [0, 5]], "o": [[0, -5], [-11, 0], [0, 9], [11, -3], [0, 0]], "v": [[6950, 4509], [6930, 4500], [6910, 4514], [6930, 4523], [6950, 4509]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 32", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 37", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-8, -18], [12, 0], [-1, 15]], "o": [[0, -25], [12, 27], [-5, 0], [0, 0]], "v": [[2361, 4523], [2370, 4515], [2370, 4550], [2361, 4523]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 33", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 38", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, -4], [0, -14], [6, 0], [0, 17]], "o": [[0, -17], [6, 3], [0, 13], [-5, 0], [0, 0]], "v": [[2300, 4519], [2310, 4495], [2320, 4526], [2310, 4550], [2300, 4519]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 34", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 39", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-15, 0], [4, 11], [-27, -6], [-10, 0], [20, -18], [-8, 21], [18, -3], [0, -7], [13, 16]], "o": [[-10, -12], [12, 0], [-5, -14], [18, 4], [17, -1], [-27, 23], [4, -12], [-13, 1], [0, 20], [0, 0]], "v": [[7120, 4545], [7125, 4530], [7136, 4514], [7162, 4505], [7213, 4512], [7209, 4531], [7187, 4534], [7169, 4522], [7145, 4537], [7120, 4545]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 35", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 40", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-26, -14], [37, 0], [0, 5]], "o": [[0, -16], [25, 13], [-21, 0], [0, 0]], "v": [[7300, 4571], [7350, 4567], [7338, 4580], [7300, 4571]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 36", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 41", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -2], [10, 6], [-122, -20], [0, 0], [0, 0], [47, 6]], "o": [[-47, -6], [-6, 3], [-22, -14], [0, 0], [0, 0], [-33, -1], [0, 0]], "v": [[9830, 4634], [9733, 4627], [9703, 4621], [9955, 4636], [10035, 4648], [9975, 4647], [9830, 4634]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 37", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 42", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [-8, -5], [13, 1], [11, 5]], "o": [[0, 0], [0, 0], [11, 0], [13, 9], [-8, 0], [0, 0]], "v": [[10765, 4670], [10745, 4661], [10765, 4661], [10800, 4670], [10800, 4679], [10765, 4670]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 38", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 43", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 3], [4, -19], [7, 18]], "o": [[-4, -10], [9, -9], [-3, 17], [0, 0]], "v": [[2387, 4666], [2385, 4642], [2398, 4667], [2387, 4666]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 39", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 44", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, 3], [3, -6], [0, 8]], "o": [[0, -2], [8, -3], [-6, 10], [0, 0]], "v": [[13990, 4726], [14006, 4716], [14015, 4720], [13990, 4726]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 40", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 45", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-4, 4], [-9, -9], [25, 16]], "o": [[-8, -5], [4, -4], [18, 18], [0, 0]], "v": [[12680, 4790], [12673, 4774], [12697, 4783], [12680, 4790]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 41", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 46", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-8, 14], [0, 0], [0, 0], [9, 0], [0, 4]], "o": [[1, -5], [0, 0], [0, 0], [-1, 20], [-7, 0], [0, 0]], "v": [[2420, 4793], [2435, 4760], [2449, 4735], [2448, 4768], [2433, 4800], [2420, 4793]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 42", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 47", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, -3], [17, 0], [-3, 5]], "o": [[3, -5], [13, 9], [-8, 0], [0, 0]], "v": [[13296, 4808], [13311, 4805], [13305, 4817], [13296, 4808]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 43", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 48", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, -5], [2, 0], [3, 6]], "o": [[-3, -5], [6, 0], [0, 6], [-3, 0], [0, 0]], "v": [[14575, 4930], [14579, 4920], [14590, 4930], [14586, 4940], [14575, 4930]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 44", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 49", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 3], [0, -6], [6, -3], [0, 6]], "o": [[0, -5], [6, -3], [0, 5], [-5, 3], [0, 0]], "v": [[19730, 5041], [19740, 5025], [19750, 5029], [19740, 5045], [19730, 5041]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 45", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 50", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -8], [3, -3], [-1, 11]], "o": [[0, -11], [3, 7], [-3, 4], [0, 0]], "v": [[19641, 5054], [19647, 5048], [19646, 5067], [19641, 5054]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 46", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 51", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, 17], [0, 0], [0, 0], [1, 0], [6, 4]], "o": [[-7, -5], [0, 0], [0, 0], [-1, 17], [0, 0], [0, 0]], "v": [[2294, 5402], [2297, 5370], [2310, 5345], [2308, 5378], [2305, 5410], [2294, 5402]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 47", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 52", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 0], [0, -5], [6, 0], [-3, 6]], "o": [[3, -5], [2, 0], [0, 6], [-5, 0], [0, 0]], "v": [[2285, 5440], [2296, 5430], [2300, 5440], [2289, 5450], [2285, 5440]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 48", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 53", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 0], [0, -5], [6, 0], [-3, 6]], "o": [[3, -5], [2, 0], [0, 6], [-5, 0], [0, 0]], "v": [[2275, 5470], [2286, 5460], [2290, 5470], [2279, 5480], [2275, 5470]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 49", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 54", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 0], [3, -8], [2, 0], [0, 8]], "o": [[0, -8], [5, 0], [-4, 8], [-2, 0], [0, 0]], "v": [[19880, 5545], [19890, 5530], [19894, 5545], [19884, 5560], [19880, 5545]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 50", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 55", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, -5], [2, 0], [3, 6]], "o": [[-3, -5], [6, 0], [0, 6], [-3, 0], [0, 0]], "v": [[20005, 5570], [20009, 5560], [20020, 5570], [20016, 5580], [20005, 5570]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 51", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 56", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 8], [0, -5], [5, -8], [0, 6]], "o": [[0, -5], [5, -8], [0, 6], [-5, 8], [0, 0]], "v": [[19970, 5575], [19980, 5550], [19990, 5545], [19980, 5570], [19970, 5575]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 52", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 57", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 8], [0, -17], [5, -8], [0, 17]], "o": [[0, -11], [8, -12], [0, 11], [-8, 12], [0, 0]], "v": [[2260, 5565], [2270, 5530], [2280, 5535], [2270, 5570], [2260, 5565]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 53", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 58", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -3], [-19, 24], [3, -11], [-16, 21], [-1, -28], [-3, 7], [-5, -4], [0, 5], [-5, 0], [0, -8], [10, 0], [2, -6], [11, 12]], "o": [[-10, -9], [-23, 8], [10, -15], [-9, 25], [12, -17], [0, 16], [2, -6], [5, 3], [0, -6], [6, 0], [0, 8], [-11, 0], [-3, 8], [0, 0]], "v": [[19947, 5625], [19915, 5614], [19906, 5578], [19917, 5572], [19947, 5585], [19961, 5596], [19967, 5612], [19981, 5606], [19990, 5601], [20000, 5590], [20010, 5605], [19991, 5620], [19968, 5631], [19947, 5625]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 54", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 59", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-8, 0], [0, -2], [8, -4], [0, 5]], "o": [[0, -6], [8, 0], [0, 2], [-8, 3], [0, 0]], "v": [[2230, 5630], [2245, 5620], [2260, 5624], [2245, 5634], [2230, 5630]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 55", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 60", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 0], [-3, -5], [6, 0], [3, 6]], "o": [[-3, -5], [5, 0], [3, 6], [-5, 0], [0, 0]], "v": [[19785, 5660], [19789, 5650], [19805, 5660], [19801, 5670], [19785, 5660]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 56", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 61", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, 11], [9, -20], [3, -8], [0, 14]], "o": [[-1, -8], [15, -19], [-5, 11], [-5, 13], [0, 0]], "v": [[2231, 5689], [2246, 5654], [2252, 5655], [2237, 5690], [2231, 5689]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 57", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 62", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -3], [11, 1], [-8, 3]], "o": [[7, -3], [4, 3], [-11, 0], [0, 0]], "v": [[2228, 5713], [2247, 5714], [2234, 5719], [2228, 5713]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 58", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 63", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -8], [3, -3], [-1, 11]], "o": [[0, -11], [3, 7], [-3, 4], [0, 0]], "v": [[19721, 5834], [19727, 5828], [19726, 5847], [19721, 5834]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 59", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 64", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-4, 14], [0, -16], [4, -16], [0, 22]], "o": [[0, -14], [6, -20], [0, 11], [-7, 27], [0, 0]], "v": [[2192, 5870], [2200, 5820], [2208, 5815], [2200, 5865], [2192, 5870]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 60", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 65", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-10, 10], [7, -28], [8, 21]], "o": [[-4, -11], [16, -16], [-9, 38], [0, 0]], "v": [[2164, 5956], [2174, 5922], [2184, 5935], [2164, 5956]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 61", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 66", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -13], [0, 6], [-8, 11], [-1, -31], [3, 0], [0, 9]], "o": [[0, -15], [-5, 8], [0, -5], [14, -18], [0, 17], [-3, 0], [0, 0]], "v": [[20000, 6063], [19990, 6060], [19980, 6064], [19995, 6034], [20010, 6048], [20005, 6080], [20000, 6063]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 62", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 67", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-8, 7], [0, -6], [8, -4], [0, 2]], "o": [[0, -2], [9, -7], [0, 5], [-8, 3], [0, 0]], "v": [[19970, 6256], [19985, 6240], [20000, 6238], [19985, 6254], [19970, 6256]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 63", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 68", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 0], [0, -5], [6, 0], [-3, 6]], "o": [[3, -5], [2, 0], [0, 6], [-5, 0], [0, 0]], "v": [[19985, 6340], [19996, 6330], [20000, 6340], [19989, 6350], [19985, 6340]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 64", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 69", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 12], [1, -41], [9, 37]], "o": [[-4, -14], [9, -19], [-1, 53], [0, 0]], "v": [[19974, 6564], [19978, 6517], [19988, 6543], [19974, 6564]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 65", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 70", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, 16], [-11, 0], [2, -16], [2, -13], [11, 16]], "o": [[-3, -6], [6, 0], [0, -34], [5, 0], [-2, 15], [-4, 27], [0, 0]], "v": [[19945, 6721], [19949, 6710], [19960, 6681], [19980, 6620], [19984, 6648], [19977, 6699], [19945, 6721]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 66", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 71", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 0], [0, -5], [6, 0], [-3, 6]], "o": [[3, -5], [2, 0], [0, 6], [-5, 0], [0, 0]], "v": [[19955, 6880], [19966, 6870], [19970, 6880], [19959, 6890], [19955, 6880]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 67", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 72", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 8], [0, -18], [9, 14]], "o": [[-2, -4], [7, -12], [0, 23], [0, 0]], "v": [[2256, 7022], [2261, 7000], [2270, 7008], [2256, 7022]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 68", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 73", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 8], [12, -27], [0, 10]], "o": [[0, -8], [12, -18], [-6, 13], [0, 0]], "v": [[2231, 7020], [2240, 6990], [2240, 7015], [2231, 7020]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 69", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 74", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, -5], [6, -3], [0, 8]], "o": [[0, -9], [6, 0], [0, 6], [-5, 3], [0, 0]], "v": [[2300, 7046], [2310, 7030], [2320, 7039], [2310, 7055], [2300, 7046]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 70", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 75", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, -8], [2, 0], [4, 8]], "o": [[-3, -8], [6, 0], [0, 8], [-2, 0], [0, 0]], "v": [[2206, 7055], [2210, 7040], [2220, 7055], [2216, 7070], [2206, 7055]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 71", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 76", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, 0], [0, -5], [6, 0], [-9, -15], [16, -4], [0, 32]], "o": [[-1, -26], [8, 0], [0, 6], [-7, 0], [15, 22], [-14, 4], [0, 0]], "v": [[2254, 7088], [2266, 7050], [2280, 7060], [2269, 7070], [2273, 7094], [2272, 7122], [2254, 7088]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 72", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 77", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -8], [3, -3], [-1, 11]], "o": [[0, -11], [3, 7], [-3, 4], [0, 0]], "v": [[2191, 7274], [2197, 7268], [2196, 7287], [2191, 7274]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 73", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 78", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 0], [3, -8], [2, 0], [0, 8]], "o": [[0, -8], [5, 0], [-4, 8], [-2, 0], [0, 0]], "v": [[2210, 7305], [2220, 7290], [2224, 7305], [2214, 7320], [2210, 7305]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 74", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 79", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 9], [-6, 6], [0, -5], [-11, 10], [0, -6], [6, -3], [-17, -14], [16, 0], [-3, 6]], "o": [[4, -5], [-23, -29], [3, -3], [0, 4], [11, -10], [0, 6], [-15, 9], [17, 14], [-5, 0], [0, 0]], "v": [[2315, 7319], [2308, 7292], [2274, 7222], [2280, 7225], [2300, 7215], [2320, 7208], [2310, 7225], [2315, 7280], [2319, 7330], [2315, 7319]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 75", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 80", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [4, 15], [-5, 3], [2, -69], [-6, 4], [4, 6], [-10, -8], [10, -6], [-4, 38]], "o": [[3, -21], [-4, -16], [10, -7], [-1, 31], [6, -4], [-4, -7], [13, 11], [-29, 19], [0, 0]], "v": [[2242, 7303], [2239, 7238], [2241, 7205], [2251, 7283], [2260, 7325], [2263, 7307], [2274, 7309], [2278, 7331], [2242, 7303]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 76", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 81", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-10, 0], [0, -27], [9, 24]], "o": [[-9, -25], [6, 0], [-2, 54], [0, 0]], "v": [[2356, 7374], [2359, 7290], [2368, 7340], [2356, 7374]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 77", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 82", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -8], [3, -3], [-1, 11]], "o": [[0, -11], [3, 7], [-3, 4], [0, 0]], "v": [[2181, 7384], [2187, 7378], [2186, 7397], [2181, 7384]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 78", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 83", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2, 0], [0, -8], [6, 0], [-3, 8]], "o": [[4, -8], [2, 0], [0, 8], [-5, 0], [0, 0]], "v": [[2196, 7415], [2206, 7400], [2210, 7415], [2200, 7430], [2196, 7415]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 79", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 84", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, -6], [7, -7], [0, 13]], "o": [[0, -13], [7, 7], [-6, 6], [0, 0]], "v": [[2254, 7440], [2264, 7430], [2264, 7450], [2254, 7440]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 80", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 85", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 0], [0, -5], [6, 0], [-3, 6]], "o": [[3, -5], [2, 0], [0, 6], [-5, 0], [0, 0]], "v": [[2195, 7460], [2206, 7450], [2210, 7460], [2199, 7470], [2195, 7460]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 81", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 86", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, -3], [0, -3], [6, 0], [0, 6]], "o": [[0, -5], [6, 3], [0, 2], [-5, 0], [0, 0]], "v": [[2200, 7539], [2210, 7535], [2220, 7546], [2210, 7550], [2200, 7539]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 82", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 87", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -4], [-10, 23], [-5, 0], [0, 6], [8, 0], [0, -6], [6, 4], [-6, 14], [4, 5], [-7, -4], [4, 48], [-5, 0], [1, -50], [0, -18], [5, 0], [0, 6]], "o": [[0, -5], [-13, 5], [7, -14], [4, 0], [0, -5], [-8, 0], [0, 5], [-6, -3], [5, -13], [-4, -4], [12, 7], [-1, -10], [11, 0], [-1, 19], [-1, 17], [-5, 0], [0, 0]], "v": [[2310, 7550], [2295, 7547], [2291, 7526], [2312, 7500], [2320, 7490], [2305, 7480], [2290, 7491], [2280, 7495], [2280, 7466], [2282, 7434], [2288, 7434], [2301, 7358], [2308, 7340], [2331, 7460], [2329, 7528], [2319, 7560], [2310, 7550]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 88", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[2320, 7460], [2310, 7450], [2300, 7460], [2310, 7470], [2320, 7460]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 83", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 89", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -7], [2, -7], [0, 14]], "o": [[0, -14], [2, 6], [-3, 6], [0, 0]], "v": [[2242, 7730], [2247, 7718], [2247, 7743], [2242, 7730]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 84", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 90", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-4, 16], [-14, -14], [8, -35], [0, 0], [0, 0]], "o": [[-6, -16], [5, -26], [12, 13], [0, 0], [0, 0], [0, 0]], "v": [[2171, 7776], [2166, 7717], [2188, 7703], [2193, 7762], [2183, 7805], [2171, 7776]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 85", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 91", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-8, -8], [2, -7], [4, 13]], "o": [[-4, -17], [5, 5], [-3, 8], [0, 0]], "v": [[2187, 7951], [2192, 7938], [2198, 7961], [2187, 7951]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 86", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 92", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 4], [-4, -8], [19, 12]], "o": [[-8, -5], [5, -3], [6, 17], [0, 0]], "v": [[2161, 7961], [2158, 7946], [2174, 7955], [2161, 7961]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 87", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 93", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -8], [3, -3], [-1, 11]], "o": [[0, -11], [3, 7], [-3, 4], [0, 0]], "v": [[2151, 7984], [2157, 7978], [2156, 7997], [2151, 7984]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 88", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 94", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 0], [3, -5], [3, 0], [0, 6]], "o": [[0, -5], [5, 0], [-3, 6], [-2, 0], [0, 0]], "v": [[2240, 8050], [2251, 8040], [2255, 8050], [2244, 8060], [2240, 8050]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 89", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 95", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -8], [3, -3], [-1, 11]], "o": [[0, -11], [3, 7], [-3, 4], [0, 0]], "v": [[19881, 8104], [19887, 8098], [19886, 8117], [19881, 8104]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 90", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 96", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, -6], [7, -7], [0, 13]], "o": [[0, -13], [7, 7], [-6, 6], [0, 0]], "v": [[2244, 8100], [2254, 8090], [2254, 8110], [2244, 8100]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 91", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 97", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 9], [-1, -26], [9, 25]], "o": [[-3, -9], [7, -14], [0, 35], [0, 0]], "v": [[2056, 8114], [2060, 8082], [2070, 8098], [2056, 8114]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 92", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 98", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 13], [-13, 0], [3, -5], [-4, -7], [13, -5], [-5, 19]], "o": [[5, -17], [-11, -18], [8, 0], [-3, 4], [12, 19], [-8, 3], [0, 0]], "v": [[2149, 8142], [2143, 8094], [2145, 8073], [2154, 8082], [2156, 8102], [2154, 8164], [2149, 8142]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 93", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 99", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -8], [3, -3], [-1, 11]], "o": [[0, -11], [3, 7], [-3, 4], [0, 0]], "v": [[2221, 8164], [2227, 8158], [2226, 8177], [2221, 8164]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 94", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 100", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -8], [3, -3], [-1, 11]], "o": [[0, -11], [3, 7], [-3, 4], [0, 0]], "v": [[2031, 8174], [2037, 8168], [2036, 8187], [2031, 8174]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 95", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 101", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [4, 8], [-12, 1], [2, -38], [5, -5], [-2, 23]], "o": [[2, -21], [-6, -11], [14, -1], [-2, 26], [-5, 5], [0, 0]], "v": [[2264, 8214], [2260, 8160], [2268, 8143], [2282, 8188], [2269, 8244], [2264, 8214]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 96", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 102", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 4], [-4, -8], [19, 12]], "o": [[-8, -5], [5, -3], [6, 17], [0, 0]], "v": [[2241, 8261], [2238, 8246], [2254, 8255], [2241, 8261]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 97", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 103", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 3], [10, -15], [4, 15]], "o": [[-3, -12], [11, -7], [-5, 9], [0, 0]], "v": [[2297, 8261], [2300, 8235], [2310, 8269], [2297, 8261]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 98", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 104", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-13, 8], [4, -12], [-5, -11], [14, 15]], "o": [[-11, -11], [8, -5], [-4, 10], [10, 18], [0, 0]], "v": [[2157, 8283], [2162, 8224], [2167, 8234], [2170, 8271], [2157, 8283]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 99", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 105", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 4], [0, -8], [9, 14]], "o": [[-3, -5], [5, -3], [0, 18], [0, 0]], "v": [[2255, 8371], [2260, 8355], [2270, 8364], [2255, 8371]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 100", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 106", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -7], [2, -7], [0, 14]], "o": [[0, -14], [2, 6], [-3, 6], [0, 0]], "v": [[2292, 8380], [2297, 8368], [2297, 8393], [2292, 8380]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 101", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 107", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 19], [3, 9], [-6, 0], [10, -58], [3, 6]], "o": [[-4, -6], [6, -18], [-3, -10], [12, 0], [-4, 22], [0, 0]], "v": [[2248, 8514], [2250, 8469], [2254, 8418], [2259, 8400], [2262, 8485], [2248, 8514]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 102", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 108", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [-1, 9], [-2, -7], [9, 15]], "o": [[-4, -6], [6, 0], [0, -10], [8, 18], [0, 0]], "v": [[2126, 8511], [2129, 8500], [2141, 8483], [2146, 8477], [2126, 8511]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 103", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 109", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, -5], [2, 0], [3, 6]], "o": [[-3, -5], [6, 0], [0, 6], [-3, 0], [0, 0]], "v": [[2255, 8540], [2259, 8530], [2270, 8540], [2266, 8550], [2255, 8540]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 104", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 110", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [3, -11], [2, 0], [0, 11]], "o": [[0, -11], [5, 0], [-3, 11], [-2, 0], [0, 0]], "v": [[19880, 9280], [19889, 9260], [19893, 9280], [19884, 9300], [19880, 9280]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 105", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 111", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 3], [0, -11], [6, -3], [0, 11]], "o": [[0, -11], [6, -3], [0, 11], [-5, 3], [0, 0]], "v": [[19860, 9391], [19870, 9365], [19880, 9379], [19870, 9405], [19860, 9391]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 106", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 112", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-8, -8], [8, -14], [1, 25]], "o": [[0, -22], [8, 8], [-9, 16], [0, 0]], "v": [[1890, 9516], [1901, 9497], [1901, 9527], [1890, 9516]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 107", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 113", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, -3], [10, -16], [0, 21]], "o": [[1, -19], [9, 6], [-5, 8], [0, 0]], "v": [[19834, 9596], [19843, 9567], [19842, 9618], [19834, 9596]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 108", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 114", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-8, 7], [0, -14], [8, 0], [0, 2]], "o": [[0, -2], [12, -10], [0, 9], [-8, 0], [0, 0]], "v": [[2450, 9686], [2465, 9670], [2480, 9674], [2465, 9690], [2450, 9686]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 109", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 115", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [50, 1], [6, 15], [-6, 6], [15, 0], [-3, -9], [9, -14], [33, 0], [5, 5], [20, -7], [0, 9], [-5, 11], [-6, 11], [14, -9], [0, -11], [1, 20], [5, -13], [6, 0], [-20, 25], [-2, -8], [-21, 15], [-6, 0], [-7, 8], [11, 10], [-6, -12], [14, 12], [10, -3], [-15, 23], [-46, -3], [-51, -26], [-5, -18], [-7, 14], [-22, -19], [-17, 6], [-9, -9], [-16, 20], [-44, -2], [0, -5], [-8, 3], [2, -47], [29, 18], [8, -8], [0, 10]], "o": [[0, -5], [-214, -2], [-3, -9], [12, -12], [-6, 0], [3, 8], [-13, 19], [-23, 0], [-8, -8], [-24, 9], [0, -2], [6, -11], [11, -21], [-5, 3], [0, 21], [0, -7], [-5, 12], [-21, 0], [13, -16], [3, 8], [17, -12], [5, 0], [11, -13], [-10, -10], [8, 13], [-9, -7], [-19, 5], [14, -22], [95, 6], [43, 21], [5, 21], [9, -21], [22, 18], [7, -3], [15, 15], [14, -19], [28, 1], [0, 5], [11, -4], [-3, 69], [-14, -9], [-13, 13], [0, 0]], "v": [[3230, 9703], [3138, 9694], [2873, 9674], [2878, 9648], [2868, 9610], [2864, 9626], [2854, 9666], [2796, 9690], [2744, 9680], [2705, 9680], [2550, 9683], [2560, 9660], [2580, 9620], [2569, 9576], [2560, 9601], [2539, 9608], [2530, 9618], [2510, 9640], [2508, 9585], [2532, 9572], [2567, 9562], [2608, 9540], [2630, 9525], [2630, 9497], [2625, 9500], [2617, 9501], [2581, 9493], [2577, 9474], [2660, 9448], [2835, 9486], [2901, 9540], [2915, 9548], [2979, 9544], [3116, 9586], [3146, 9597], [3179, 9592], [3248, 9572], [3300, 9583], [3314, 9587], [3325, 9641], [3288, 9701], [3257, 9699], [3230, 9703]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 116", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[3000, 9676], [2990, 9665], [2980, 9669], [2990, 9680], [3000, 9676]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 117", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [19, 13], [18, -9], [-11, -7], [-9, 12], [6, -22], [-33, -2], [0, 5]], "o": [[0, -4], [-29, -18], [-16, 9], [9, 6], [12, -15], [-5, 23], [19, 2], [0, 0]], "v": [[3300, 9662], [3265, 9632], [3208, 9621], [3201, 9641], [3228, 9632], [3235, 9640], [3265, 9667], [3300, 9662]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 118", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [3, -5], [-6, 0], [-3, 6]], "o": [[3, -5], [-5, 0], [-3, 6], [5, 0], [0, 0]], "v": [[3175, 9650], [3171, 9640], [3155, 9650], [3159, 9660], [3175, 9650]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 119", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[2935, 9640], [2931, 9630], [2920, 9640], [2924, 9650], [2935, 9640]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 120", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, -10], [-10, 8], [2, 3]], "o": [[-9, -9], [5, 8], [8, -6], [0, 0]], "v": [[2686, 9612], [2655, 9630], [2676, 9629], [2686, 9612]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 121", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[2620, 9610], [2610, 9600], [2600, 9610], [2610, 9620], [2620, 9610]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 122", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[2790, 9610], [2780, 9600], [2770, 9610], [2780, 9620], [2790, 9610]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 123", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-7, 4], [3, 4], [4, -23], [-11, 0], [4, 12]], "o": [[-4, -9], [7, -4], [-9, -8], [-2, 10], [11, 0], [0, 0]], "v": [[2866, 9563], [2872, 9539], [2878, 9524], [2842, 9563], [2856, 9580], [2866, 9563]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 124", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [3, 8], [0, -15], [-10, -5], [6, 0], [-17, 0], [-3, 6]], "o": [[2, -7], [-7, -17], [0, 6], [9, 6], [-18, 0], [8, 0], [0, 0]], "v": [[2683, 9528], [2681, 9500], [2640, 9489], [2658, 9509], [2663, 9520], [2664, 9540], [2683, 9528]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 110", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 125", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, -5], [2, 0], [3, 6]], "o": [[-3, -5], [6, 0], [0, 6], [-3, 0], [0, 0]], "v": [[19855, 9710], [19859, 9700], [19870, 9710], [19866, 9720], [19855, 9710]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 111", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 126", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -13], [12, 0], [-12, 8]], "o": [[19, -13], [0, 6], [-19, 0], [0, 0]], "v": [[4830, 9760], [4860, 9760], [4838, 9770], [4830, 9760]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 112", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 127", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [242, 6], [10, 16], [0, -11], [10, 9], [7, -7], [-9, 14], [10, 8], [-17, -5], [9, 15], [6, 2], [-16, 0], [0, 8], [-9, -3], [-8, 7], [0, -3], [-11, 7], [-6, -8], [-29, -4], [-8, -8], [-6, 3], [0, -6], [-8, 3], [-3, -4], [-11, 3], [3, 5], [-40, -15], [-8, 9], [7, -4], [-9, 27], [0, -19], [-8, 5], [-17, -11], [-7, 5], [-11, -5], [7, 0], [-3, -6], [-11, 2], [-8, -5], [-6, 11], [-5, -4], [-11, 14], [-5, 0], [13, -13], [-45, -20], [-9, 8], [-11, -11], [-18, 0], [-3, -5], [-8, 7], [-8, -8], [-7, 18], [0, -13], [-8, 2], [-7, -9], [-50, 11], [2, 7], [-7, -13], [-25, 2], [-13, -15], [-8, 0], [0, -14], [-16, 0], [3, -28], [21, -2], [8, 2]], "o": [[-8, -2], [-556, -14], [-6, -9], [0, 10], [-10, -7], [-13, 13], [3, -6], [-17, -14], [14, 5], [-5, -11], [-14, -5], [8, 0], [0, -9], [8, 4], [8, -7], [0, 4], [16, -8], [3, 6], [29, 3], [7, 8], [5, -3], [0, 7], [8, -4], [3, 5], [11, -3], [-6, -9], [6, 2], [8, -10], [-15, 9], [7, -21], [0, 7], [15, -11], [7, 4], [7, -5], [11, 4], [-7, 1], [4, 5], [12, -1], [9, 6], [5, -9], [5, 3], [11, -14], [13, 0], [-19, 19], [30, 13], [9, -7], [9, 9], [19, 0], [5, 8], [9, -7], [9, 9], [7, -18], [1, 10], [8, -2], [16, 20], [23, -4], [-6, -15], [3, 5], [33, -2], [10, 11], [7, 0], [0, 19], [17, 0], [-2, 26], [-14, 0], [0, 0]], "v": [[4730, 9765], [4275, 9750], [3468, 9707], [3460, 9709], [3447, 9711], [3423, 9711], [3376, 9699], [3364, 9674], [3363, 9664], [3370, 9651], [3350, 9628], [3366, 9580], [3380, 9565], [3395, 9556], [3425, 9550], [3440, 9544], [3461, 9539], [3490, 9539], [3549, 9557], [3616, 9577], [3640, 9585], [3650, 9590], [3665, 9597], [3685, 9599], [3710, 9603], [3724, 9588], [3776, 9597], [3801, 9584], [3803, 9573], [3785, 9520], [3820, 9511], [3833, 9514], [3888, 9514], [3913, 9512], [3945, 9511], [3952, 9519], [3945, 9531], [3973, 9538], [4009, 9544], [4032, 9536], [4051, 9526], [4081, 9506], [4111, 9480], [4109, 9527], [4152, 9590], [4207, 9598], [4235, 9604], [4285, 9620], [4325, 9630], [4344, 9631], [4368, 9632], [4389, 9619], [4399, 9613], [4414, 9628], [4442, 9642], [4533, 9655], [4567, 9636], [4604, 9609], [4654, 9614], [4715, 9630], [4747, 9650], [4760, 9675], [4781, 9700], [4798, 9733], [4770, 9767], [4730, 9765]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 128", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 4], [0, -8], [-9, 14]], "o": [[3, -5], [-5, -3], [0, 18], [0, 0]], "v": [[4245, 9711], [4240, 9695], [4230, 9704], [4245, 9711]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 129", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-10, -4], [0, 10], [14, -2], [2, -20], [-6, 0], [0, 12]], "o": [[0, -15], [9, 3], [0, -10], [-17, 2], [-2, 15], [6, 0], [0, 0]], "v": [[3640, 9689], [3655, 9674], [3670, 9664], [3648, 9652], [3622, 9683], [3629, 9710], [3640, 9689]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 130", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 7], [6, -3], [0, 5], [12, 13], [-9, 11], [7, 0], [-20, -44], [-14, 12], [-7, -2]], "o": [[6, 3], [0, -6], [-5, 3], [0, -4], [-17, -18], [8, -10], [-27, 0], [6, 16], [9, -8], [0, 0]], "v": [[3458, 9677], [3470, 9670], [3460, 9665], [3450, 9662], [3429, 9631], [3420, 9595], [3422, 9580], [3405, 9683], [3429, 9687], [3458, 9677]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 131", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[4280, 9680], [4269, 9670], [4265, 9680], [4276, 9690], [4280, 9680]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 132", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -8], [-5, 0], [0, 8]], "o": [[0, -8], [-5, 0], [0, 8], [6, 0], [0, 0]], "v": [[3800, 9635], [3790, 9620], [3780, 9635], [3790, 9650], [3800, 9635]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 133", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -7], [-12, 0], [0, 5]], "o": [[0, -12], [-7, 7], [5, 0], [0, 0]], "v": [[3560, 9611], [3533, 9593], [3551, 9620], [3560, 9611]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 113", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 134", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, -4], [4, -8], [0, 25]], "o": [[0, -11], [5, 3], [-7, 21], [0, 0]], "v": [[19830, 9769], [19840, 9755], [19843, 9775], [19830, 9769]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 114", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 135", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 11], [0, -21], [6, -3], [-1, 5]], "o": [[0, -4], [8, -19], [1, 12], [-5, 3], [0, 0]], "v": [[19801, 9843], [19810, 9815], [19819, 9817], [19810, 9845], [19801, 9843]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 115", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 136", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 0], [3, -5], [3, 0], [0, 6]], "o": [[0, -5], [5, 0], [-3, 6], [-2, 0], [0, 0]], "v": [[19830, 9850], [19841, 9840], [19845, 9850], [19834, 9860], [19830, 9850]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 116", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 137", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, 0], [3, -5], [2, 0], [3, 6]], "o": [[-3, -5], [9, 0], [-3, 6], [-2, 0], [0, 0]], "v": [[19825, 9940], [19835, 9930], [19845, 9940], [19835, 9950], [19825, 9940]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 117", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 138", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-28, -37], [13, -5], [-1, 35]], "o": [[1, -43], [18, 25], [-32, 12], [0, 0]], "v": [[7824, 9945], [7858, 9939], [7864, 9974], [7824, 9945]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 118", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 139", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [27, 3], [0, 0], [0, 0], [-28, -10], [-13, 9], [-10, -6], [0, 6], [-7, 22], [0, 0], [0, 0], [0, 0], [0, 0], [-14, -6], [-15, 0], [-10, -8], [-17, 0], [23, -30], [-3, -16], [303, 19]], "o": [[-115, -7], [0, 0], [0, 0], [3, -58], [7, 3], [16, -10], [13, 8], [0, -2], [0, 0], [0, 0], [0, 0], [0, 0], [37, 1], [15, 5], [15, 0], [11, 8], [41, 0], [-8, 11], [4, 32], [0, 0]], "v": [[7475, 9964], [7215, 9945], [7164, 9939], [7167, 9882], [7215, 9806], [7252, 9796], [7290, 9790], [7360, 9798], [7374, 9755], [7388, 9715], [7395, 9751], [7402, 9787], [7470, 9788], [7564, 9800], [7618, 9810], [7664, 9825], [7715, 9840], [7753, 9905], [7743, 9953], [7475, 9964]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 119", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 140", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, -3], [0, -6], [6, 0], [0, 9]], "o": [[0, -8], [6, 3], [0, 5], [-5, 0], [0, 0]], "v": [[7900, 9974], [7910, 9965], [7920, 9981], [7910, 9990], [7900, 9974]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 120", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 141", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 0], [0, -5], [6, 0], [-3, 6]], "o": [[3, -5], [2, 0], [0, 6], [-5, 0], [0, 0]], "v": [[19815, 10020], [19826, 10010], [19830, 10020], [19819, 10030], [19815, 10020]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 121", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 142", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 18], [-21, -32], [-22, 8], [0, -17], [43, 8]], "o": [[-16, -3], [0, -31], [14, 21], [26, -10], [0, 9], [0, 0]], "v": [[8633, 10023], [8610, 9994], [8654, 9995], [8696, 10010], [8740, 10021], [8633, 10023]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 122", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 143", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, -3], [3, -5], [0, 18]], "o": [[0, -8], [6, 4], [-9, 14], [0, 0]], "v": [[19780, 10084], [19790, 10075], [19795, 10091], [19780, 10084]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 123", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 144", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1, -12], [6, -3], [-3, 6]], "o": [[4, -13], [0, 4], [-7, 2], [0, 0]], "v": [[7972, 4611], [7991, 4607], [7980, 4619], [7972, 4611]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 124", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 145", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 0], [-11, -8], [6, 0], [11, 8]], "o": [[-10, -8], [5, 0], [10, 8], [-5, 0], [0, 0]], "v": [[5164, 4655], [5156, 4640], [5186, 4655], [5194, 4670], [5164, 4655]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 125", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 146", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, 3], [3, -6], [0, 8]], "o": [[0, -2], [8, -3], [-6, 10], [0, 0]], "v": [[5160, 4746], [5176, 4736], [5185, 4740], [5160, 4746]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 126", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 147", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -3], [11, 1], [-8, 3]], "o": [[7, -3], [4, 3], [-11, 0], [0, 0]], "v": [[5668, 4683], [5687, 4684], [5674, 4689], [5668, 4683]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 127", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 148", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, -5], [2, 0], [3, 6]], "o": [[-3, -5], [6, 0], [0, 6], [-3, 0], [0, 0]], "v": [[5865, 4730], [5869, 4720], [5880, 4730], [5876, 4740], [5865, 4730]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 128", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 149", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-8, -3], [14, 0], [3, 6]], "o": [[-3, -6], [18, 7], [-6, 0], [0, 0]], "v": [[5805, 4730], [5814, 4726], [5821, 4740], [5805, 4730]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 129", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 150", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, -3], [16, -6], [-4, 9]], "o": [[3, -8], [12, 7], [-10, 4], [0, 0]], "v": [[6247, 4765], [6261, 4756], [6256, 4773], [6247, 4765]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 130", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 151", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, -5], [6, 0], [0, 6]], "o": [[0, -5], [6, 0], [0, 6], [-5, 0], [0, 0]], "v": [[6020, 4780], [6030, 4770], [6040, 4780], [6030, 4790], [6020, 4780]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 131", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 152", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, -3], [0, -3], [6, 0], [0, 6]], "o": [[0, -5], [6, 3], [0, 2], [-5, 0], [0, 0]], "v": [[6390, 4829], [6400, 4825], [6410, 4836], [6400, 4840], [6390, 4829]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 132", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 153", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, 0], [3, -5], [6, 0], [0, 6]], "o": [[0, -5], [8, 0], [-3, 6], [-5, 0], [0, 0]], "v": [[6800, 4840], [6816, 4830], [6825, 4840], [6809, 4850], [6800, 4840]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 133", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 154", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -3], [10, -3], [-9, 10]], "o": [[7, -7], [3, 3], [-14, 6], [0, 0]], "v": [[7637, 4869], [7655, 4862], [7643, 4874], [7637, 4869]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 134", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 155", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-11, 0], [21, -7], [3, 5]], "o": [[-4, -6], [25, 0], [-8, 4], [0, 0]], "v": [[7295, 4880], [7309, 4870], [7315, 4883], [7295, 4880]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 135", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 156", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, -5], [6, 0], [0, 6]], "o": [[0, -5], [6, 0], [0, 6], [-5, 0], [0, 0]], "v": [[8130, 4940], [8140, 4930], [8150, 4940], [8140, 4950], [8130, 4940]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 136", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 157", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, -5], [2, 0], [3, 6]], "o": [[-3, -5], [6, 0], [0, 6], [-3, 0], [0, 0]], "v": [[19315, 5140], [19319, 5130], [19330, 5140], [19326, 5150], [19315, 5140]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 137", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 158", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 0], [3, 6], [-5, 0], [17, -23], [0, 15]], "o": [[0, -9], [5, 0], [-3, -5], [15, 0], [-13, 17], [0, 0]], "v": [[19150, 5277], [19161, 5260], [19165, 5250], [19169, 5240], [19165, 5274], [19150, 5277]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 138", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 159", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, 0], [3, -5], [6, 0], [0, 6]], "o": [[0, -5], [8, 0], [-3, 6], [-5, 0], [0, 0]], "v": [[7930, 6120], [7946, 6110], [7955, 6120], [7939, 6130], [7930, 6120]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 139", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 160", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 100], [-38, 1], [27, 21], [-7, 14], [-37, 19], [0, -16], [-11, 3], [-40, -40], [58, 3], [-36, -16], [-7, 7], [0, -14], [-12, 21], [-5, -15], [20, -53], [55, -23], [45, 16]], "o": [[-85, -30], [-7, -89], [31, -1], [-12, -8], [15, -26], [34, -17], [0, 5], [15, -4], [68, 68], [-47, -3], [18, 8], [8, -8], [1, 17], [11, -20], [14, 34], [-23, 60], [-42, 17], [0, 0]], "v": [[10175, 6568], [10024, 6359], [10066, 6234], [10074, 6193], [10068, 6163], [10167, 6076], [10320, 6069], [10340, 6073], [10415, 6122], [10428, 6208], [10404, 6236], [10439, 6237], [10450, 6246], [10464, 6241], [10483, 6235], [10470, 6423], [10338, 6565], [10175, 6568]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 161", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[10250, 6426], [10240, 6415], [10230, 6419], [10240, 6430], [10250, 6426]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 162", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[10440, 6410], [10430, 6400], [10420, 6410], [10430, 6420], [10440, 6410]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 163", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[10180, 6406], [10170, 6395], [10160, 6399], [10170, 6410], [10180, 6406]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 164", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-9, 6], [8, 5]], "o": [[-19, -12], [0, 10], [2, -2], [0, 0]], "v": [[10450, 6320], [10420, 6319], [10460, 6333], [10450, 6320]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 165", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[10230, 6310], [10226, 6300], [10215, 6310], [10219, 6320], [10230, 6310]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 166", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[10410, 6290], [10400, 6280], [10390, 6290], [10400, 6300], [10410, 6290]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 167", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [14, 0], [-2, -17], [-2, 52]], "o": [[0, -23], [-13, 0], [5, 53], [0, 0]], "v": [[10328, 6230], [10310, 6200], [10294, 6223], [10328, 6230]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 168", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-26, -20], [-7, 16], [0, 6], [10, -16], [-14, 0], [3, 8]], "o": [[-8, -19], [18, 13], [3, -10], [1, -15], [-9, 15], [4, 0], [0, 0]], "v": [[10204, 6245], [10245, 6246], [10272, 6243], [10279, 6214], [10190, 6220], [10201, 6260], [10204, 6245]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 169", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, -29], [-9, 14], [5, 10]], "o": [[-13, -23], [6, 19], [5, -10], [0, 0]], "v": [[10362, 6214], [10346, 6242], [10362, 6247], [10362, 6214]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 170", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[10170, 6240], [10166, 6230], [10155, 6240], [10159, 6250], [10170, 6240]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 171", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [-12, -12], [0, 17]], "o": [[0, -8], [-15, 0], [10, 9], [0, 0]], "v": [[10140, 6204], [10125, 6190], [10116, 6223], [10140, 6204]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 172", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [4, -13], [-7, 2], [0, 4]], "o": [[-1, -12], [-3, 6], [6, -3], [0, 0]], "v": [[10171, 6207], [10152, 6211], [10160, 6219], [10171, 6207]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 173", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-8, -13], [-12, 0], [12, 8]], "o": [[-20, -13], [3, 6], [18, 0], [0, 0]], "v": [[10340, 6100], [10305, 6100], [10333, 6110], [10340, 6100]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 140", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 174", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 3], [0, -6], [6, -3], [0, 6]], "o": [[0, -5], [6, -3], [0, 5], [-5, 3], [0, 0]], "v": [[11260, 8211], [11270, 8195], [11280, 8199], [11270, 8215], [11260, 8211]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 141", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 175", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [34, 73], [6, 192], [-176, 178], [-9, -23], [0, 17], [-11, -4], [-13, 0], [3, -5], [-25, -6], [-24, -33], [-22, -109], [4, -185], [78, -201], [81, -31], [48, 17]], "o": [[-50, -17], [-48, -100], [-14, -450], [60, -61], [7, 16], [0, -11], [9, 3], [14, 0], [-3, 5], [35, 9], [52, 70], [18, 92], [-7, 285], [-56, 146], [-39, 15], [0, 0]], "v": [[6195, 7665], [6046, 7505], [5973, 7108], [6244, 6057], [6378, 5982], [6430, 5974], [6446, 5964], [6486, 5970], [6506, 5979], [6545, 5999], [6621, 6053], [6731, 6321], [6747, 6635], [6621, 7355], [6377, 7669], [6195, 7665]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 142", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 176", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-55, 134], [-49, 23], [-56, -112], [0, -63], [88, -45], [50, 26]], "o": [[-112, -59], [20, -47], [106, -49], [20, 39], [-1, 118], [-51, 26], [0, 0]], "v": [[8209, 7940], [8106, 7590], [8235, 7459], [8525, 7571], [8550, 7697], [8418, 7941], [8209, 7940]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 143", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 177", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, -3], [0, -3], [6, 0], [0, 6]], "o": [[0, -5], [6, 3], [0, 2], [-5, 0], [0, 0]], "v": [[13090, 7289], [13100, 7285], [13110, 7296], [13100, 7300], [13090, 7289]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 144", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 178", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [90, 91], [21, 36], [-16, 5], [16, 0], [-15, 128], [-230, 110], [-108, 0], [-110, -240], [124, -264], [251, -16], [64, 14]], "o": [[-127, -27], [-41, -42], [-43, -74], [15, -5], [-26, 0], [39, -324], [91, -44], [262, -1], [113, 247], [-117, 248], [-67, 4], [0, 0]], "v": [[13370, 8461], [13060, 8291], [12947, 8150], [12880, 7952], [12876, 7930], [12850, 7620], [13275, 6936], [13537, 6878], [14125, 7255], [14107, 8071], [13545, 8474], [13370, 8461]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 179", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2], [17, -2], [0, -18], [-48, 28]], "o": [[23, -14], [0, -3], [-32, 3], [0, 15], [0, 0]], "v": [[13527, 8015], [13570, 7985], [13539, 7984], [13470, 8030], [13527, 8015]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 180", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [3, 3], [-7, -10], [-6, 16]], "o": [[3, -8], [-9, -9], [8, 13], [0, 0]], "v": [[13503, 7966], [13503, 7946], [13476, 7971], [13503, 7966]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 181", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 32], [11, -2], [-1, -11], [-9, 25], [2, 4]], "o": [[-4, -7], [1, -12], [-10, 2], [13, 126], [4, -10], [0, 0]], "v": [[13814, 7768], [13798, 7587], [13782, 7572], [13767, 7595], [13810, 7794], [13814, 7768]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 182", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [3, 5], [10, -27], [-7, 0], [-11, 23]], "o": [[10, -23], [-3, -6], [-21, 55], [4, 0], [0, 0]], "v": [[13980, 7738], [13993, 7687], [13969, 7726], [13955, 7780], [13980, 7738]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 183", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[13690, 7606], [13680, 7595], [13670, 7599], [13680, 7610], [13690, 7606]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 184", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [7, -25], [-35, 67]], "o": [[16, -31], [-8, 0], [-10, 35], [0, 0]], "v": [[13719, 7517], [13745, 7460], [13685, 7560], [13719, 7517]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 185", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[13880, 7500], [13876, 7490], [13865, 7500], [13869, 7510], [13880, 7500]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 186", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -2], [-14, 0], [6, 3]], "o": [[-7, -2], [-7, 3], [14, 0], [0, 0]], "v": [[13923, 7503], [13898, 7503], [13910, 7508], [13923, 7503]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 187", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [19, 19], [0, 6], [9, 5], [-4, 14], [13, -20], [6, 9], [16, -10], [-4, 10], [12, 0], [-2, -4], [8, 0], [-5, -26], [-23, -7], [0, -8], [-7, 18], [-23, -9], [3, -4], [-16, 16]], "o": [[20, -19], [-8, -8], [0, -5], [-11, -6], [6, -22], [-4, 8], [-7, -11], [-16, 10], [3, -8], [-11, 0], [3, 4], [-12, 0], [8, 45], [11, 3], [0, 18], [4, -11], [16, 6], [-9, 14], [0, 0]], "v": [[13254, 7404], [13255, 7356], [13240, 7331], [13224, 7312], [13213, 7281], [13184, 7271], [13169, 7269], [13141, 7267], [13126, 7267], [13111, 7254], [13096, 7262], [13086, 7270], [13077, 7303], [13120, 7375], [13140, 7396], [13166, 7396], [13200, 7393], [13224, 7412], [13254, 7404]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 145", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 188", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-15, 0], [-1, -11], [9, 23]], "o": [[-8, -22], [7, 0], [2, 24], [0, 0]], "v": [[12196, 8474], [12203, 8450], [12217, 8470], [12196, 8474]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 146", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 189", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [85, 13], [202, 0], [-5, 16], [99, 6], [10, 9], [9, 0], [11, 2], [92, 12], [290, 13], [15, 5], [28, -6], [7, 11], [15, -2], [10, 6], [60, 3], [25, 3], [52, 5], [45, 6], [36, -9], [2, 2], [49, 3], [41, 5], [41, 2], [19, 6], [58, 4], [107, 15], [13, -1], [52, 6], [41, 4], [112, 18], [142, 6], [61, 5], [8, -1], [39, 4], [121, 8], [240, 19], [52, 2], [0, 1], [25, 2], [52, 6], [39, 3], [44, 8], [47, 3], [61, 8], [28, 2], [50, 5], [310, 6], [30, 6], [155, -4], [49, 7], [129, 11], [14, 3], [91, 6], [8, 3], [158, 7], [22, 2], [105, 8], [30, 6], [50, 5], [12, 2], [43, 4], [14, 3], [96, 8], [29, 6], [7, -1], [41, 4], [28, 2], [57, 4], [44, 5], [3, -2], [124, 13], [138, 4], [111, 8], [34, 2], [6, 4], [53, 2], [88, 6], [14, 0], [51, 7], [3, -2], [47, 7], [77, 4], [77, 10], [41, 3], [58, 5], [36, 3], [14, 1], [9, 1], [0, 9], [12, -17], [14, 11], [-29, 14], [0, -22], [-6, 0], [3, 17], [-14, 5], [-22, 10], [-24, 0], [48, 50], [-11, 7], [12, 13], [-3, 3], [0, -17], [-9, 15], [6, 7], [-4, 3], [32, -2], [-9, 4], [17, 0], [4, 8], [-6, 0], [8, 10], [-10, 4], [15, 12], [-2, 20], [25, 6], [-6, 84], [11, 7], [-4, 5], [-4, -2], [-1, 29], [-5, 5], [0, 20], [12, -16], [10, 18], [-1, -40], [4, -33], [-11, -3], [-1, 32], [-2, -94], [5, -13], [-3, 29], [7, -49], [8, 22], [-3, 15], [16, 5], [0, 7], [-10, -4], [11, 19], [-3, 4], [0, -7], [-5, 4], [15, 14], [-5, -18], [8, 11], [-16, -9], [10, 12], [-22, 17], [0, 6], [13, -11], [-1, 35], [-10, -15], [-11, 32], [0, 8], [5, -14], [-5, 27], [-1, 7], [0, -25], [-6, 0], [4, 7], [-10, -4], [-3, 13], [9, 4], [0, 11], [-5, -3], [3, -6], [-8, 7], [-3, 27], [-8, 4], [0, 11], [5, -3], [-13, 13], [3, 7], [-6, 0], [-2, -7], [-4, 103], [10, 0], [0, 68], [3, -18], [12, 8], [-9, 5], [8, 10], [-3, 22], [-2, 16], [-3, 4], [6, 8], [-8, 10], [12, 25], [1, -15], [8, 20], [8, 0], [9, 14], [-51, 45], [15, 16], [-3, 4], [-2, 24], [-4, 25], [0, 22], [-2, -13], [4, 38], [-4, 3], [12, 0], [0, -8], [7, -4], [-4, 7], [5, 0], [3, -13], [0, 0], [0, 0], [-23, 7], [17, 0], [0, -5], [5, 0], [-3, 14], [-6, -9], [-8, 38], [15, -19], [-5, 38], [-5, 19], [6, 6], [4, -27], [4, -3], [-3, -5], [13, -57], [-6, -6], [18, 0], [-3, -18], [4, -1], [-7, -25], [9, 12], [-4, -32], [7, 4], [3, -13], [3, -26], [8, -12], [7, -24], [2, 10], [0, -32], [17, 19], [-16, 0], [6, 8], [-20, 55], [-1, 8], [-4, 14], [-6, 30], [-5, 3], [3, 6], [-7, 0], [3, 12], [-9, 0], [15, 15], [-11, 22], [3, 10], [-9, -12], [-5, 39], [11, -27], [8, 0], [9, -17], [5, 0], [-13, 8], [18, 0], [-31, 80], [2, 7], [-9, 4], [3, 10], [-11, 17], [0, 0], [0, 0], [7, -30], [-18, 33], [-3, 27], [-3, 5], [-13, 60], [-9, 14], [16, 1], [-13, 3], [0, 5], [-4, -3], [-7, 40], [-8, 25], [-7, 51], [-6, 6], [-3, 15], [-10, 19], [-1, 24], [-8, 3], [5, 34], [-5, -7], [-5, 86], [10, -6], [-3, -10], [8, 7], [1, 19], [3, 6], [-9, 0], [7, 11], [-1, -59], [5, 3], [0, 35], [3, 5], [-9, -1], [13, 20], [0, -20], [5, 3], [-3, 11], [6, 0], [-6, 21], [25, -5], [-29, 13], [-5, 49], [17, 12], [-1, 26], [0, 0], [0, 0], [12, 51], [-14, 2], [-14, 2], [-282, -15], [-11, -4], [-30, 1], [-11, -5], [-4, 5], [-51, -10], [8, -5], [-25, 0], [4, 6], [-19, -12], [-31, -1], [0, -18], [-5, 0], [0, 5], [-11, -7], [-60, -4], [63, 0], [0, 0], [0, 0], [-16, 0], [-3, -5], [27, 8], [46, -2], [48, 19], [11, 0], [8, 1], [10, -18], [10, 0], [-20, 16], [3, 1], [-4, -30], [19, -7], [-12, -12], [-36, 0], [-41, -6], [-35, -3], [-7, -5], [-14, 11], [-99, -12], [-13, 0], [5, -7], [60, 2], [0, -12], [-13, -2], [0, 7], [-11, -9], [-25, 4], [-15, -5], [-7, 10], [0, -6], [-7, -1], [-29, -3], [0, 6], [-8, -6], [-13, 3], [5, -14], [-7, 5], [-4, -6], [0, 16], [-8, -7], [-30, -4], [-15, -12], [-16, 10], [21, 5], [-7, 11], [-9, -16], [-20, 18], [12, -22], [-32, 0], [-4, 6], [-6, -10], [-50, 1], [-6, -5], [-9, 4], [-25, -15], [0, 6], [-30, 2], [8, -9], [-26, 3], [-8, -6], [5, 21], [0, 0], [0, 0], [-56, 4], [0, -8], [-22, 4], [-5, 0], [-9, -6], [4, 11], [-26, -17], [-7, 17], [-7, -11], [-4, 15], [-1, -19], [-7, 3], [-20, 1], [-11, 4], [12, -9], [-14, 0], [-6, 5], [10, -24], [-18, 0], [-3, 0], [20, 5], [-19, 5], [7, -11], [-8, 9], [8, 5], [-25, -13], [-4, 7], [-13, -5], [-27, 1], [0, -3], [-24, 0], [20, 6], [-31, 1], [0, -5], [-24, -3], [12, 5], [-11, 11], [0, -24], [-36, -1], [-19, -3], [11, 13], [-60, -14], [-7, 8], [13, -4], [-4, 6], [0, -14], [-6, 3], [25, 0], [0, 6], [-21, -3], [3, 10], [-16, -6], [-61, -4], [-5, -5], [8, 13], [-28, 0], [4, -12], [-11, 17], [-59, -4], [0, 6], [11, -3], [3, 6], [-16, 0], [-4, -5], [-10, 0], [-33, -6], [-14, 11], [-7, -4], [-22, -2], [0, -13], [-13, 3], [-2, -7], [-7, 4], [2, 7], [-27, -1], [-3, -1], [-86, -2], [-14, -5], [0, 4], [-22, 1], [0, -4], [-19, -1], [0, 5], [-5, -3], [3, -5], [-12, 0], [3, 6], [-72, -19], [-107, 6], [-7, -4], [-140, -1], [2, -3], [-54, 0], [10, 11], [-18, -18], [0, 14], [-32, -6], [-12, -1], [-4, -3], [-6, 0], [8, 5], [-142, -19], [-55, -4], [-6, -3], [-15, 2], [-11, -6], [-6, 6], [-37, -4], [4, -10], [-23, 21], [-24, -11], [0, 12], [-48, -15], [-35, -1], [-3, -3], [-11, 0], [23, 13], [-58, -18], [-38, -1], [0, 0], [0, 0], [4, 4], [-14, -4], [-39, -5], [-21, -4], [-9, 5], [-17, -7], [-13, 13], [-28, -10], [-3, 3], [-10, -6], [-6, 6], [21, 7], [-79, -20], [-8, 3], [0, -6], [-22, 12], [-6, -4], [-19, 1], [-11, -6], [-10, 14], [-6, -3], [-15, 6], [0, -7], [-5, 8], [-8, -13], [-31, 1], [-3, -3], [-28, 1], [0, -14], [-7, 11], [-15, 0], [-23, -7], [0, 6], [-5, -3], [6, 10], [-22, 0], [11, -7], [-7, 0], [-4, 5], [-16, -4], [-7, 2], [-7, -5], [-16, -1], [17, 11], [-9, 0], [-3, -8], [0, 14], [-16, -3], [0, -8], [-8, 0], [0, 6], [-35, -1], [-15, -34], [-8, 10], [0, -11], [-4, 22], [-13, -14], [-16, 9], [-28, -2], [9, 2], [-50, -1], [-2, -2], [0, 9], [9, 0], [-3, 6], [57, -11], [-30, 17], [3, 5], [-18, 10], [0, 0], [0, 0], [0, 7], [-16, -4], [-3, 0], [6, 14], [-5, 0], [3, 8], [12, 0], [-3, 3], [-722, -49], [-168, -12], [-195, -13], [-164, -7], [-88, -3], [-9, 1], [-9, -6], [7, 0], [-1, -29], [-7, 4], [4, -7], [5, 4], [-3, -29], [5, -7], [-4, -3], [-4, 8], [-5, -12], [6, -6], [-21, 0], [0, -5], [-27, 10], [-4, -17], [-5, 0], [0, -14], [-6, 4], [4, 5], [-5, 23], [-3, 16], [-15, -4], [0, 9], [-6, 0], [-12, 13], [0, 0], [0, 0], [3, -18], [-18, -37], [0, -14], [-11, 17], [5, 0], [0, 11], [-7, -26], [-5, 0], [9, -11], [15, 8], [7, -2], [-13, -24], [1, -10], [-1, -3], [-2, -24], [14, -5], [20, -16], [-12, -11], [-6, 0], [-3, -12], [-9, 7], [0, -21], [7, 4], [-4, -8], [5, 28], [-6, 8], [0, -19], [-14, 18], [0, -6], [-9, -3], [10, 12], [-8, 5], [3, 8], [-10, -4], [3, 27], [-9, -1], [0, 16], [-15, -5], [-3, 20], [25, 0], [0, -6], [8, 4], [0, -5], [12, 0], [0, 8], [-9, 0], [5, 15], [-16, 4], [21, -5], [-1, 6], [-1, 12], [-5, -3], [3, -5], [-9, -6], [5, 0], [-10, -11], [-16, 3], [0, -12], [-8, 3], [-5, -36], [2, -5], [1, -50], [4, -7], [-5, -4], [3, -16], [0, 0], [0, 0], [19, 0], [-4, 11], [5, 3], [0, -17], [14, 36], [0, 0], [0, 0], [-25, 0], [0, 6], [-11, 6], [4, -12], [-17, -9], [6, -4], [-6, -20], [3, -8], [-20, 23], [-1, -40], [4, -12], [5, 25], [15, -11], [-21, -17], [-5, 14], [-3, -10], [5, -22], [1, 8], [12, 0], [-1, -50], [1, -3], [-7, 5], [3, -12], [0, 0], [0, 0], [0, 0], [0, 0], [8, 3], [-4, -11], [13, 11], [6, -11], [-10, -16], [3, -6], [-8, -12], [0, 0], [0, 0], [-5, 3], [1, -37], [-3, -38], [-1, -40], [-10, 4], [0, -7], [4, -18], [11, -3], [-8, 15], [12, 4], [-14, -17], [17, 3], [3, 11], [10, -2], [0, 23], [5, 3], [-2, 9], [25, -6], [-9, -17], [8, 5], [0, -5], [-8, 0], [-4, -7], [14, 3], [-32, -25], [24, 9], [-6, -23], [7, -10], [-6, -30], [0, -13], [-3, 52], [0, -44], [-6, 20], [-5, 46], [-5, -3], [4, -11], [-7, 4], [0, 10], [-5, 3], [12, -185], [11, -190], [6, -39], [7, -12], [-8, -5], [9, -5], [0, -19], [8, -24], [-13, 0], [-2, 6], [0, -10], [11, -3], [0, -16], [-2, 25], [-8, 8], [3, -19], [14, 0], [5, -40], [13, -20], [-12, 0], [15, -56], [7, -72], [3, -29], [5, 0], [-4, -10], [5, -3], [-3, -18], [8, -35], [3, -22], [0, 0], [0, 0], [17, 2]], "o": [[-16, -3], [-194, -30], [-28, 0], [8, -27], [-56, -3], [-8, -8], [-9, -1], [-30, -4], [-108, -13], [-55, -2], [-15, -6], [-58, 11], [-3, -4], [-35, 5], [-4, -2], [-59, -2], [-25, -3], [-52, -6], [-155, -18], [-20, 4], [-2, -2], [-49, -3], [-41, -5], [-41, -3], [-19, -5], [-122, -8], [-45, -6], [-14, 0], [-52, -5], [-79, -6], [-107, -17], [-74, -3], [-60, -5], [-8, 1], [-38, -4], [-198, -14], [-41, -3], [-52, -1], [0, -1], [-25, -2], [-52, -5], [-38, -4], [-44, -8], [-47, -3], [-60, -8], [-27, -2], [-106, -12], [-124, -2], [-67, -13], [-61, 1], [-48, -6], [-129, -11], [-14, -2], [-91, -6], [-16, -5], [-52, -2], [-22, -3], [-104, -9], [-30, -5], [-49, -5], [-12, -2], [-43, -3], [-14, -2], [-96, -8], [-28, -5], [-7, 2], [-41, -4], [-108, -8], [-33, -2], [-44, -5], [-16, 10], [-77, -8], [-259, -6], [-36, -3], [-34, -2], [-7, -4], [-54, -2], [-88, -6], [-14, 0], [-52, -8], [-4, 2], [-47, -7], [-134, -6], [-19, -2], [-41, -4], [-58, -6], [-36, -3], [-14, -2], [-10, 0], [0, -15], [-12, 16], [-24, -20], [28, -12], [0, 8], [5, 0], [-5, -23], [11, -4], [78, -35], [63, 0], [-26, -27], [12, -7], [-7, -9], [9, -9], [0, 16], [4, -6], [-6, -7], [6, -7], [-10, 0], [16, -8], [-8, 0], [-3, -8], [7, 0], [-9, -11], [10, -4], [-13, -11], [5, -42], [-19, -5], [16, -239], [-6, -4], [3, -5], [4, 3], [5, -162], [3, -3], [-1, -33], [-14, 18], [-9, -14], [1, 28], [-11, 86], [6, 1], [2, -87], [0, 47], [-9, 21], [5, -49], [-7, 39], [-5, -13], [5, -22], [-12, -3], [0, -9], [14, 5], [-6, -13], [3, -3], [0, 7], [12, -7], [-8, -8], [5, 19], [-15, -22], [9, 6], [-11, -13], [14, -11], [0, -9], [-25, 22], [0, -23], [10, 14], [7, -20], [-1, -8], [-8, 23], [2, -16], [2, -22], [0, 13], [5, 0], [-5, -7], [11, 4], [3, -12], [-8, -3], [0, -10], [6, 4], [-5, 9], [7, -6], [4, -28], [7, -4], [0, -11], [-15, 9], [6, -6], [-3, -8], [6, 0], [6, 14], [2, -56], [-14, 0], [0, -25], [-8, 42], [-8, -4], [9, -6], [-6, -7], [2, -22], [2, -15], [4, -3], [-9, -9], [8, -10], [-10, -25], [-1, 18], [-5, -13], [-9, 0], [-16, -25], [47, -43], [-8, -9], [3, -3], [1, -24], [3, -25], [0, -22], [7, 36], [-3, -20], [12, -7], [-5, 0], [0, 8], [-7, 4], [4, -7], [-5, 0], [0, 0], [0, 0], [-12, -29], [20, -7], [-5, 0], [0, 6], [-4, 0], [3, -18], [10, 17], [6, -29], [-15, 19], [3, -22], [7, -22], [-6, -6], [-4, 23], [-5, 3], [3, 5], [-17, 67], [15, 15], [-12, 0], [3, 13], [-12, 5], [5, 18], [-8, -11], [4, 29], [-6, -4], [-14, 59], [-3, 16], [-8, 12], [-8, 27], [-7, -29], [0, 36], [-13, -17], [5, 0], [-16, -20], [3, -8], [2, -8], [4, -14], [6, -30], [5, -4], [-4, -6], [6, 0], [-3, -13], [17, 0], [-8, -8], [9, -17], [-5, -15], [13, 19], [4, -32], [-3, 8], [-9, 0], [-9, 16], [-15, 0], [19, -12], [-19, 0], [13, -32], [-3, -7], [10, -3], [-3, -10], [0, 0], [0, 0], [-4, 22], [-18, 69], [7, -15], [3, -26], [3, -5], [31, -134], [6, -9], [-17, -1], [9, -2], [0, -6], [5, 3], [6, -40], [15, -52], [2, -19], [5, -7], [8, -60], [5, -10], [1, -26], [10, -3], [-4, -28], [9, 12], [2, -56], [-5, 3], [4, 12], [-6, -6], [-2, -19], [-9, -16], [4, 0], [-20, -31], [1, 30], [-4, -3], [1, -35], [-3, -6], [19, 1], [-6, -10], [0, 16], [-5, -3], [3, -11], [-7, 0], [11, -39], [-27, 5], [19, -8], [6, -51], [-14, -9], [0, 0], [0, 0], [18, 41], [-6, -31], [9, 0], [151, -14], [176, 9], [12, 3], [30, -1], [11, 6], [4, -7], [3, 1], [-11, 7], [19, 0], [-7, -11], [7, 4], [71, 2], [0, 8], [6, 0], [0, -11], [4, 3], [105, 6], [0, 0], [0, 0], [13, 11], [15, 0], [8, 13], [-23, -8], [-82, 5], [-30, -12], [-12, 0], [-30, -6], [-5, 11], [-13, 0], [14, -11], [-49, -10], [1, 7], [-33, 15], [4, 4], [37, 1], [41, 6], [35, 2], [19, 15], [16, -14], [44, 5], [14, 1], [-4, 7], [-76, -3], [0, 8], [12, 2], [0, -9], [10, 8], [22, -3], [21, 8], [4, -7], [0, 6], [6, 0], [31, 3], [0, -7], [7, 6], [19, -5], [-4, 11], [6, -4], [9, 14], [0, -7], [7, 5], [90, 9], [11, 10], [25, -15], [-10, -2], [9, -13], [10, 18], [21, -18], [-19, 37], [17, 0], [5, -8], [5, 10], [37, -1], [7, 6], [28, -12], [13, 8], [0, -6], [43, -2], [-8, 10], [19, -2], [12, 10], [0, 0], [0, 0], [15, 19], [43, -3], [0, 7], [17, -3], [23, -1], [7, 4], [-7, -17], [31, 20], [4, -10], [8, 11], [4, -16], [1, 14], [6, -4], [20, -2], [16, -6], [-13, 9], [8, 0], [23, -23], [-4, 10], [14, 0], [16, 0], [-22, -5], [24, -7], [-6, 10], [4, -4], [-21, -14], [12, 6], [3, -5], [12, 5], [26, 0], [0, 3], [34, 2], [-24, -8], [17, -1], [0, 6], [74, 9], [-10, -4], [20, -19], [0, 17], [23, 0], [29, 4], [-15, -18], [27, 6], [7, -8], [-11, 4], [8, -13], [0, 5], [18, -11], [-14, 0], [0, -7], [26, 3], [-4, -9], [12, 5], [60, 4], [13, 13], [-4, -6], [36, 0], [-7, 17], [4, -7], [52, 4], [0, -5], [-10, 3], [-5, -7], [14, 0], [3, 6], [9, 0], [103, 18], [7, -6], [6, 4], [50, 2], [0, 6], [15, -4], [2, 6], [7, -5], [-3, -7], [22, 0], [3, 2], [86, 3], [15, 6], [0, -4], [22, -1], [0, 4], [19, 1], [0, -6], [6, 4], [-4, 5], [12, 0], [-8, -14], [60, 15], [30, -1], [13, 8], [39, 1], [-2, 3], [84, 1], [-15, -18], [15, 15], [0, -7], [26, 4], [13, 1], [3, 4], [6, 0], [-16, -11], [39, 6], [55, 4], [5, 4], [15, -1], [12, 6], [6, -6], [50, 4], [-7, 19], [21, -18], [28, 14], [0, -11], [14, 4], [35, 1], [4, 3], [19, -1], [-32, -18], [25, 7], [0, 0], [0, 0], [-26, -7], [-4, -4], [56, 16], [23, 2], [22, 5], [9, -6], [36, 13], [7, -7], [22, 8], [2, -2], [13, 7], [6, -6], [-59, -20], [43, 11], [8, -3], [0, 17], [11, -6], [6, 4], [19, -1], [15, 8], [7, -9], [17, 11], [8, -3], [0, 8], [10, -16], [4, 7], [29, -2], [3, 2], [42, -3], [0, 14], [5, -8], [38, -1], [9, 3], [0, -6], [18, 11], [-4, -6], [28, 0], [-9, 6], [6, 0], [3, -5], [15, 4], [7, -3], [7, 6], [44, 3], [-10, -7], [7, 0], [5, 14], [0, -4], [17, 2], [0, 8], [8, 0], [0, -5], [57, 3], [3, 6], [12, -15], [0, 21], [3, -14], [26, 26], [7, -5], [27, 2], [-35, -10], [28, 1], [8, 7], [0, -5], [-8, 0], [7, -12], [-45, 9], [9, -5], [-3, -5], [0, 0], [0, 0], [-21, -5], [0, -10], [12, 4], [57, 1], [-3, -8], [5, 0], [-4, -8], [-11, 0], [11, -12], [154, 11], [168, 11], [424, 30], [66, 3], [88, 4], [10, -1], [10, 6], [-8, 0], [0, 28], [6, -3], [-4, 6], [-5, -3], [2, 26], [-6, 7], [4, 4], [7, -10], [3, 8], [-19, 19], [10, 0], [0, 15], [22, -9], [3, 11], [5, 0], [0, 14], [6, -4], [-3, -6], [6, -23], [5, -22], [13, 3], [0, -8], [7, 0], [0, 0], [0, 0], [4, 19], [-4, 23], [14, 28], [0, 27], [4, -6], [-6, 0], [0, -26], [3, 11], [7, 0], [-1, 1], [-15, -8], [-17, 7], [5, 11], [-1, 9], [1, 3], [3, 34], [-9, 4], [-31, 25], [7, 8], [5, 0], [5, 15], [14, -12], [0, 8], [-7, -4], [12, 20], [-3, -15], [7, -12], [1, 24], [8, -11], [0, 6], [14, 5], [-9, -10], [6, -4], [-3, -8], [13, 5], [-2, -23], [7, 2], [0, -20], [14, 4], [5, -22], [-16, 0], [0, 6], [-8, -3], [0, 11], [-5, 0], [0, -8], [11, 0], [-5, -15], [22, -6], [-7, 1], [1, -7], [0, -11], [6, 4], [-3, 5], [10, 6], [-5, 0], [11, 12], [18, -3], [0, 9], [11, -4], [4, 26], [-9, 26], [-1, 31], [-5, 7], [5, 3], [0, 0], [0, 0], [-8, -21], [-19, 0], [4, -8], [-6, -4], [0, 38], [0, 0], [0, 0], [3, 33], [6, 0], [0, -5], [16, -8], [-3, 9], [16, 8], [-7, 4], [5, 16], [-8, 20], [16, -19], [1, 23], [-6, 18], [-5, -24], [-22, 17], [10, 9], [14, -44], [3, 6], [-5, 21], [-1, -9], [-18, 0], [1, 30], [-7, 19], [5, -3], [0, 0], [0, 0], [0, 0], [0, 0], [-1, 20], [-8, -3], [6, 14], [-13, -10], [-10, 16], [4, 6], [-3, 5], [0, 0], [0, 0], [0, -12], [6, -4], [0, 32], [4, 39], [2, 53], [6, -2], [1, 34], [-2, 12], [-16, 4], [7, -13], [-25, -9], [11, 14], [-10, -1], [-3, -12], [-13, 2], [0, -16], [-6, -4], [8, -25], [-18, 5], [8, 15], [-6, -4], [0, 6], [8, 0], [5, 8], [-31, -8], [28, 21], [-14, -6], [6, 24], [-4, 6], [6, 30], [0, 43], [3, -47], [0, 27], [7, -21], [0, -7], [6, 4], [-4, 14], [6, -4], [0, -10], [12, -8], [-5, 80], [-18, 298], [-1, 6], [-9, 16], [8, 5], [-7, 5], [1, 18], [-16, 49], [5, 0], [4, -8], [1, 9], [-13, 3], [0, 28], [3, -29], [4, -4], [-2, 25], [-15, 0], [-17, 145], [-17, 26], [12, 0], [-13, 53], [-4, 30], [-3, 28], [-6, 0], [3, 11], [-5, 4], [3, 18], [-9, 36], [0, 0], [0, 0], [-64, 1], [0, 0]], "v": [[19385, 10684], [19200, 10655], [18232, 10580], [18205, 10561], [18104, 10524], [18003, 10505], [17972, 10489], [17935, 10484], [17585, 10439], [17085, 10406], [16959, 10392], [16889, 10391], [16756, 10391], [16723, 10387], [16589, 10384], [16473, 10375], [16320, 10365], [16180, 10350], [16004, 10329], [15457, 10300], [15417, 10304], [15324, 10295], [15160, 10280], [15010, 10266], [14900, 10251], [14760, 10235], [14515, 10211], [14410, 10201], [14290, 10191], [14120, 10175], [13748, 10129], [13490, 10105], [13245, 10090], [13120, 10083], [13035, 10077], [12745, 10055], [12200, 10014], [12030, 10005], [11935, 10001], [11890, 9995], [11750, 9981], [11585, 9965], [11435, 9945], [11270, 9925], [11075, 9905], [10915, 9886], [10775, 9872], [10160, 9845], [9880, 9830], [9382, 9809], [9182, 9800], [8860, 9769], [8600, 9745], [8410, 9731], [8230, 9715], [7770, 9683], [7635, 9675], [7405, 9655], [7160, 9629], [7015, 9610], [6904, 9596], [6804, 9585], [6700, 9574], [6500, 9555], [6273, 9529], [6208, 9521], [6120, 9516], [5995, 9505], [5735, 9485], [5595, 9471], [5510, 9465], [5135, 9456], [4745, 9435], [4270, 9416], [4143, 9406], [4070, 9395], [3962, 9384], [3705, 9369], [3520, 9358], [3402, 9344], [3302, 9334], [3210, 9325], [2985, 9305], [2740, 9286], [2630, 9276], [2450, 9260], [2280, 9244], [2190, 9236], [2148, 9231], [2130, 9213], [2116, 9215], [2086, 9221], [2096, 9161], [2140, 9176], [2150, 9190], [2154, 9160], [2166, 9124], [2225, 9099], [2395, 9040], [2418, 8963], [2399, 8920], [2400, 8896], [2393, 8874], [2420, 8896], [2455, 8900], [2451, 8876], [2447, 8857], [2400, 8849], [2398, 8844], [2396, 8820], [2376, 8805], [2382, 8790], [2381, 8776], [2382, 8757], [2375, 8735], [2358, 8690], [2328, 8617], [2314, 8523], [2321, 8166], [2316, 8149], [2329, 8144], [2339, 8097], [2355, 7842], [2360, 7801], [2346, 7783], [2321, 7783], [2311, 7815], [2305, 7925], [2305, 8028], [2317, 7974], [2329, 7994], [2320, 8102], [2313, 8092], [2294, 8091], [2278, 8110], [2275, 8059], [2261, 8025], [2240, 8005], [2256, 7997], [2260, 7981], [2254, 7952], [2260, 7959], [2269, 7965], [2259, 7906], [2255, 7920], [2251, 7930], [2253, 7886], [2252, 7878], [2265, 7842], [2290, 7810], [2271, 7814], [2231, 7792], [2243, 7782], [2267, 7762], [2279, 7710], [2269, 7720], [2266, 7715], [2272, 7673], [2290, 7696], [2301, 7720], [2304, 7708], [2312, 7703], [2333, 7689], [2324, 7663], [2310, 7638], [2320, 7625], [2324, 7643], [2328, 7645], [2346, 7585], [2367, 7527], [2380, 7499], [2371, 7484], [2348, 7448], [2353, 7424], [2358, 7410], [2373, 7423], [2385, 7313], [2374, 7240], [2358, 7160], [2354, 7150], [2329, 7194], [2331, 7180], [2333, 7158], [2327, 7105], [2334, 7037], [2343, 7003], [2339, 6983], [2339, 6956], [2335, 6909], [2320, 6895], [2310, 6893], [2286, 6870], [2254, 6845], [2289, 6774], [2325, 6710], [2316, 6687], [2326, 6638], [2336, 6550], [2343, 6465], [2347, 6448], [2367, 6435], [2371, 6394], [2370, 6360], [2360, 6374], [2348, 6396], [2344, 6392], [2342, 6380], [2326, 6403], [2319, 6425], [2310, 6403], [2331, 6335], [2340, 6310], [2330, 6320], [2321, 6330], [2318, 6304], [2331, 6291], [2354, 6265], [2345, 6255], [2334, 6235], [2349, 6160], [2350, 6116], [2334, 6149], [2319, 6196], [2315, 6210], [2296, 6322], [2280, 6434], [2274, 6470], [2262, 6495], [2259, 6522], [2225, 6703], [2220, 6710], [2215, 6738], [2211, 6776], [2195, 6792], [2145, 7034], [2126, 7085], [2099, 7151], [2083, 7178], [2060, 7189], [2021, 7227], [2033, 7120], [2031, 7106], [2047, 6810], [2055, 6780], [2065, 6740], [2083, 6660], [2103, 6599], [2106, 6581], [2111, 6570], [2117, 6550], [2126, 6530], [2129, 6503], [2134, 6462], [2144, 6413], [2149, 6410], [2175, 6381], [2156, 6365], [2136, 6380], [2105, 6410], [2080, 6440], [2069, 6381], [2074, 6310], [2095, 6166], [2114, 6095], [2125, 6076], [2136, 6053], [2150, 6005], [2170, 5975], [2164, 6015], [2144, 6110], [2145, 6170], [2164, 6096], [2175, 6040], [2205, 5923], [2243, 5783], [2228, 5769], [2223, 5763], [2240, 5749], [2248, 5744], [2270, 5677], [2296, 5560], [2368, 5230], [2382, 5184], [2395, 5144], [2421, 5028], [2433, 4965], [2448, 4915], [2455, 4863], [2457, 4827], [2472, 4745], [2460, 4665], [2457, 4688], [2452, 4695], [2439, 4650], [2431, 4605], [2432, 4560], [2428, 4540], [2405, 4574], [2398, 4624], [2391, 4555], [2386, 4482], [2397, 4474], [2419, 4409], [2410, 4423], [2401, 4446], [2397, 4420], [2392, 4400], [2390, 4369], [2369, 4315], [2374, 4292], [2403, 4223], [2390, 4151], [2371, 4101], [2372, 4065], [2384, 4093], [2410, 4057], [2419, 4018], [2460, 4014], [3044, 4015], [3384, 4039], [3460, 4044], [3534, 4052], [3559, 4053], [4060, 4079], [4050, 4090], [4068, 4100], [4095, 4090], [4222, 4096], [4290, 4106], [4360, 4125], [4370, 4140], [4380, 4131], [4447, 4103], [4565, 4115], [4654, 4130], [4625, 4131], [4649, 4150], [4701, 4170], [4734, 4179], [4639, 4205], [4450, 4191], [4281, 4172], [4206, 4151], [4170, 4149], [4069, 4181], [4042, 4200], [4050, 4180], [4070, 4159], [4009, 4187], [3977, 4213], [3915, 4288], [3988, 4296], [4130, 4308], [4268, 4324], [4343, 4338], [4474, 4351], [4655, 4349], [4758, 4359], [4774, 4371], [4676, 4379], [4585, 4390], [4608, 4408], [4630, 4399], [4645, 4400], [4699, 4406], [4766, 4410], [4802, 4408], [4810, 4406], [4823, 4418], [4888, 4423], [4940, 4418], [4953, 4418], [4990, 4423], [5007, 4434], [5011, 4444], [5029, 4449], [5100, 4428], [5112, 4428], [5180, 4444], [5304, 4469], [5339, 4468], [5345, 4438], [5340, 4420], [5361, 4423], [5392, 4423], [5401, 4427], [5419, 4480], [5456, 4469], [5471, 4472], [5547, 4484], [5627, 4492], [5657, 4495], [5728, 4499], [5750, 4502], [5804, 4488], [5848, 4497], [5871, 4506], [5919, 4513], [5927, 4500], [5921, 4475], [5938, 4497], [6018, 4514], [6080, 4521], [6113, 4526], [6155, 4519], [6269, 4535], [6273, 4523], [6293, 4523], [6353, 4528], [6368, 4529], [6383, 4524], [6389, 4528], [6401, 4545], [6448, 4536], [6505, 4526], [6510, 4529], [6511, 4540], [6536, 4531], [6653, 4546], [6674, 4560], [6705, 4560], [6688, 4539], [6685, 4529], [6725, 4540], [6757, 4566], [6750, 4550], [6780, 4550], [6805, 4550], [6833, 4549], [6903, 4556], [6950, 4560], [6993, 4566], [7010, 4560], [7018, 4551], [7050, 4559], [7093, 4574], [7263, 4584], [7264, 4564], [7290, 4570], [7333, 4591], [7409, 4596], [7430, 4585], [7485, 4580], [7535, 4578], [7527, 4573], [7515, 4570], [7560, 4571], [7570, 4575], [7555, 4550], [7530, 4539], [7565, 4532], [7596, 4523], [7613, 4519], [7745, 4534], [7864, 4550], [7895, 4550], [7934, 4540], [7974, 4555], [7996, 4559], [8091, 4554], [8180, 4551], [8161, 4547], [8136, 4541], [8154, 4530], [8185, 4539], [8208, 4549], [8285, 4560], [8446, 4569], [8472, 4566], [8524, 4576], [8610, 4602], [8634, 4606], [8662, 4612], [8679, 4616], [8687, 4596], [8724, 4586], [8770, 4589], [8931, 4595], [9113, 4609], [9140, 4612], [9180, 4603], [9220, 4608], [9255, 4617], [9290, 4609], [9300, 4605], [9305, 4621], [9320, 4630], [9335, 4620], [9410, 4626], [9631, 4639], [9699, 4644], [10027, 4664], [10095, 4670], [10191, 4676], [10279, 4664], [10308, 4662], [10370, 4668], [10418, 4666], [10487, 4675], [10517, 4683], [10534, 4690], [10530, 4680], [10665, 4689], [10835, 4706], [10945, 4718], [10982, 4721], [11028, 4729], [11056, 4730], [11132, 4727], [11193, 4746], [11219, 4743], [11310, 4730], [11420, 4738], [11530, 4746], [11618, 4756], [11687, 4764], [11714, 4770], [11710, 4755], [11750, 4754], [11865, 4768], [11935, 4768], [11887, 4756], [11832, 4735], [11850, 4734], [11988, 4765], [12068, 4778], [12123, 4779], [12167, 4781], [12352, 4782], [12403, 4786], [12448, 4795], [12472, 4801], [12502, 4802], [12479, 4781], [12534, 4779], [12626, 4793], [12640, 4798], [12750, 4820], [12782, 4816], [12828, 4822], [12882, 4831], [12914, 4824], [12937, 4813], [13216, 4856], [13230, 4863], [13238, 4864], [13355, 4850], [13416, 4859], [13475, 4862], [13530, 4864], [13580, 4878], [13589, 4881], [13626, 4867], [13823, 4886], [13840, 4880], [13850, 4875], [13935, 4880], [13967, 4870], [13990, 4880], [13987, 4890], [14005, 4881], [14038, 4879], [14078, 4883], [14103, 4888], [14145, 4900], [14180, 4890], [14178, 4880], [14196, 4894], [14280, 4901], [14310, 4898], [14340, 4916], [14355, 4930], [14370, 4920], [14431, 4913], [14514, 4955], [14534, 4946], [14550, 4941], [14612, 4932], [14630, 4932], [14701, 4960], [14765, 4955], [14798, 4954], [14831, 4936], [14885, 4942], [14970, 4929], [14954, 4920], [14945, 4910], [14892, 4909], [14854, 4888], [14865, 4870], [14892, 4843], [14925, 4826], [14888, 4816], [14850, 4793], [14873, 4785], [14900, 4792], [14966, 4775], [14970, 4760], [14974, 4745], [14947, 4730], [14932, 4725], [16765, 4820], [17350, 4860], [18010, 4905], [18955, 4964], [19235, 4976], [19412, 4981], [19447, 4989], [19453, 5000], [19442, 5045], [19453, 5083], [19456, 5089], [19439, 5094], [19434, 5137], [19428, 5198], [19424, 5217], [19439, 5211], [19453, 5213], [19447, 5239], [19451, 5280], [19470, 5289], [19526, 5300], [19557, 5310], [19571, 5330], [19580, 5356], [19590, 5375], [19594, 5359], [19598, 5307], [19615, 5236], [19640, 5213], [19660, 5204], [19671, 5190], [19705, 5166], [19728, 5141], [19734, 5176], [19735, 5243], [19754, 5325], [19780, 5401], [19815, 5430], [19811, 5420], [19800, 5400], [19827, 5400], [19842, 5420], [19835, 5498], [19805, 5486], [19765, 5476], [19751, 5591], [19759, 5628], [19760, 5650], [19766, 5698], [19752, 5747], [19698, 5783], [19675, 5826], [19699, 5840], [19715, 5861], [19734, 5871], [19760, 5887], [19748, 5894], [19744, 5899], [19767, 5871], [19771, 5830], [19780, 5840], [19795, 5846], [19810, 5837], [19826, 5854], [19831, 5846], [19830, 5825], [19836, 5804], [19846, 5797], [19858, 5769], [19868, 5737], [19880, 5714], [19899, 5695], [19922, 5675], [19899, 5650], [19870, 5661], [19855, 5666], [19840, 5670], [19799, 5710], [19790, 5695], [19806, 5680], [19815, 5659], [19829, 5633], [19838, 5613], [19826, 5603], [19829, 5569], [19840, 5555], [19845, 5570], [19857, 5589], [19866, 5600], [19874, 5619], [19915, 5632], [19940, 5644], [19954, 5654], [19976, 5697], [19981, 5755], [19966, 5870], [19956, 5939], [19957, 5959], [19960, 5995], [19953, 6025], [19942, 5998], [19904, 5970], [19883, 5955], [19880, 5935], [19870, 5959], [19843, 5963], [19832, 5935], [19835, 5969], [19878, 6020], [19890, 6010], [19910, 5990], [19924, 5994], [19946, 6023], [19962, 6044], [19960, 6081], [19963, 6126], [19990, 6120], [20008, 6142], [20003, 6205], [19991, 6197], [19968, 6182], [19957, 6451], [19976, 6444], [19996, 6406], [19991, 6456], [19981, 6480], [19959, 6464], [19940, 6520], [19940, 6580], [19939, 6745], [19943, 6762], [19938, 6785], [19958, 6761], [19977, 6737], [19976, 6769], [19963, 6796], [19957, 6808], [19948, 6812], [19924, 6812], [19924, 6878], [19925, 6900], [19934, 6932], [19950, 6954], [19950, 6933], [19960, 6905], [19968, 6957], [19973, 7085], [19982, 7228], [19998, 7295], [20010, 7303], [19974, 7916], [19950, 7943], [19940, 7930], [19934, 7906], [19921, 7916], [19914, 7928], [19889, 7905], [19867, 7888], [19850, 7861], [19840, 7825], [19834, 7802], [19802, 7766], [19791, 7792], [19791, 7806], [19780, 7809], [19794, 7820], [19816, 7832], [19802, 7838], [19805, 7882], [19815, 7914], [19800, 7945], [19798, 8016], [19801, 8081], [19812, 8159], [19832, 8113], [19850, 8091], [19894, 8139], [19919, 7992], [19930, 7985], [19934, 8012], [19939, 8026], [19950, 8000], [19960, 7975], [19960, 8180], [19930, 8670], [19896, 9155], [19882, 9187], [19881, 9216], [19879, 9230], [19867, 9272], [19854, 9348], [19849, 9450], [19863, 9438], [19869, 9441], [19850, 9463], [19830, 9493], [19851, 9516], [19863, 9474], [19866, 9501], [19844, 9535], [19819, 9585], [19773, 9837], [19757, 9920], [19749, 10060], [19726, 10200], [19714, 10308], [19699, 10360], [19695, 10379], [19691, 10404], [19687, 10443], [19677, 10540], [19655, 10645], [19649, 10685], [19532, 10686], [19385, 10684]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 190", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-57, 488], [506, 60], [45, 5], [45, 6], [51, 6], [0, 0], [0, 0], [-6, 52], [-5, 37], [0, 19], [-5, 3], [-291, -35], [-11, 5], [-3, 9], [-20, 170], [18, 18], [715, 83], [7, 2], [-2, 20], [-5, 47], [-21, 172], [-9, 67], [0, 5], [-7, -1], [6, 10], [-55, -3], [12, 8], [23, 0], [0, 6], [6, 0], [9, 15], [218, 26], [16, -16], [41, -344], [44, -369], [-3, -12], [-134, -14], [-236, -29], [-182, -21], [-47, -6], [-49, -6], [-102, -12], [-17, 20]], "o": [[12, -15], [15, -128], [-93, -11], [-46, -6], [-46, -5], [0, 0], [0, 0], [3, -25], [6, -52], [5, -37], [0, -19], [4, -2], [333, 39], [9, -5], [3, -10], [36, -299], [-19, -19], [-190, -23], [-7, -2], [3, -18], [17, -139], [5, -46], [8, -68], [0, -6], [25, 3], [-6, -10], [20, 2], [-8, -6], [-24, 0], [0, -5], [-7, 0], [-16, -28], [-512, -61], [-12, 11], [-122, 1032], [-25, 214], [7, 28], [112, 13], [169, 20], [47, 5], [47, 6], [50, 5], [231, 29], [0, 0]], "v": [[18941, 9834], [19020, 9260], [18575, 9090], [18323, 9060], [18158, 9040], [17982, 9019], [17889, 9007], [17894, 8961], [17910, 8820], [17931, 8658], [17940, 8556], [17948, 8516], [18485, 8575], [19032, 8629], [19054, 8603], [19095, 8276], [19113, 7949], [18400, 7850], [18043, 7805], [18035, 7768], [18049, 7650], [18110, 7138], [18135, 6932], [18150, 6799], [18163, 6791], [18195, 6780], [18280, 6769], [18290, 6761], [18233, 6750], [18190, 6740], [18178, 6730], [18149, 6702], [17914, 6647], [17375, 6601], [17304, 7081], [17055, 9184], [17014, 9596], [17190, 9649], [17620, 9700], [18040, 9750], [18210, 9770], [18385, 9791], [18660, 9824], [18941, 9834]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 191", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 49], [10, 18], [99, 12], [255, 30], [8, 0], [-22, 172], [-6, 55], [-55, -8], [-24, 0], [-40, 328], [3, 11], [228, 26], [41, 5], [140, 16], [-5, 39], [-26, 219], [-11, 82], [120, 14], [160, 19], [14, -18], [98, -824], [12, -105], [23, -190], [6, -44], [17, -139], [-225, -28], [-85, -9], [-46, -6], [-47, -6], [-249, -29], [-30, -4], [-21, 15]], "o": [[13, -9], [57, -466], [-10, -19], [-27, -3], [-255, -30], [-15, 0], [5, -38], [18, -158], [125, 17], [51, 0], [20, -169], [-8, -26], [-104, -12], [-129, -16], [-189, -22], [5, -39], [52, -439], [14, -115], [-176, -21], [-194, -23], [-12, 14], [-71, 600], [-6, 44], [-23, 190], [-19, 164], [-22, 187], [298, 36], [45, 6], [45, 5], [101, 12], [99, 12], [84, 13], [0, 0]], "v": [[16875, 9596], [16902, 9514], [16960, 8920], [16835, 8885], [16321, 8825], [15843, 8770], [15850, 8585], [15870, 8415], [15938, 8274], [16940, 8390], [17035, 8047], [17065, 7720], [16795, 7661], [16530, 7630], [16150, 7585], [15975, 7527], [16019, 7160], [16092, 6557], [15980, 6420], [15560, 6370], [15312, 6363], [15199, 7225], [15111, 7975], [15060, 8400], [15009, 8825], [14964, 9208], [15155, 9410], [15578, 9460], [15743, 9480], [15910, 9500], [16505, 9570], [16740, 9599], [16875, 9596]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 192", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-156, 110], [-86, 344], [178, 353], [596, -20], [200, -106], [30, 18], [-30, -23], [20, 0], [0, 4], [11, 0], [0, -2], [3, -9], [-23, 2], [-14, -1], [33, -20], [62, -86], [53, -294], [0, -165], [-32, -133], [8, 5], [0, -9], [-7, 2], [-18, -50], [-4, -10], [12, -3], [-18, -1], [18, -6], [7, -8], [8, 3], [0, -11], [-13, 0], [4, -6], [-12, 12], [-22, -43], [12, 5], [-8, -13], [-19, 1], [-16, -26], [-89, -67], [-415, 80]], "o": [[189, -36], [254, -179], [107, -430], [-248, -492], [-246, 8], [-97, 52], [-38, -22], [22, 17], [-11, 0], [0, -4], [-11, 0], [0, 2], [-6, 13], [16, -1], [24, 1], [-55, 34], [-153, 208], [-19, 103], [0, 188], [4, 17], [-6, -4], [0, 8], [8, -4], [15, 43], [4, 11], [-16, 4], [24, 1], [-6, 2], [-8, 8], [-11, -4], [0, 9], [11, 0], [-8, 13], [12, -12], [13, 26], [-18, -6], [3, 5], [28, -2], [28, 47], [318, 238], [0, 0]], "v": [[13836, 9179], [14380, 8949], [14929, 8105], [14820, 6901], [13465, 6143], [12838, 6303], [12711, 6337], [12678, 6338], [12680, 6356], [12660, 6348], [12640, 6340], [12620, 6344], [12614, 6365], [12636, 6379], [12690, 6379], [12681, 6401], [12384, 6701], [12089, 7420], [12066, 7745], [12107, 8149], [12101, 8166], [12090, 8174], [12103, 8185], [12142, 8257], [12175, 8352], [12163, 8373], [12165, 8379], [12201, 8428], [12176, 8446], [12146, 8454], [12130, 8464], [12151, 8480], [12165, 8490], [12227, 8519], [12296, 8582], [12298, 8607], [12263, 8627], [12303, 8634], [12355, 8662], [12615, 8916], [13836, 9179]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 193", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-8, 0], [0, 6], [8, 0], [0, 0]], "v": [[12060, 8790], [12045, 8780], [12030, 8790], [12045, 8800], [12060, 8790]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 194", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[12230, 8780], [12219, 8770], [12215, 8780], [12226, 8790], [12230, 8780]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 195", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[2587, 8748], [2581, 8754], [2586, 8767], [2587, 8748]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 196", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -18], [-7, 2], [-2, 6]], "o": [[6, -18], [-4, 10], [6, -2], [0, 0]], "v": [[2408, 8712], [2386, 8716], [2392, 8728], [2408, 8712]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 197", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, -3], [-29, 0], [3, 8]], "o": [[-3, -7], [-27, 7], [16, 0], [0, 0]], "v": [[12274, 8715], [12249, 8707], [12254, 8728], [12274, 8715]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 198", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[12310, 8720], [12299, 8710], [12295, 8720], [12306, 8730], [12310, 8720]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 199", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 7], [-13, -16], [0, 8]], "o": [[0, -2], [-16, -13], [13, 16], [0, 0]], "v": [[12180, 8716], [12163, 8699], [12159, 8703], [12180, 8716]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 200", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [4, -12], [-10, 4], [0, 1]], "o": [[0, -12], [-4, 9], [7, -3], [0, 0]], "v": [[12140, 8685], [12117, 8685], [12126, 8693], [12140, 8685]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 201", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -7], [7, 3], [1, -14], [-7, 11], [-15, -15], [18, 35]], "o": [[-8, -16], [0, 7], [-8, -3], [-2, 18], [8, -13], [23, 24], [0, 0]], "v": [[12335, 8664], [12320, 8647], [12307, 8654], [12293, 8672], [12300, 8680], [12327, 8682], [12335, 8664]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 202", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [25, -6], [-27, 5], [-4, -6], [0, 12]], "o": [[0, -21], [-28, 7], [8, -1], [7, 12], [0, 0]], "v": [[12270, 8682], [12205, 8644], [12203, 8672], [12225, 8680], [12270, 8682]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 203", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-10, 10], [0, 14], [44, 108], [54, 132], [26, 63], [130, 314], [29, 71], [-17, 12], [5, 4], [0, -8], [26, 57], [62, 151], [38, 0], [96, -30], [17, -20], [0, -8], [-156, -372], [-28, -71], [0, 0], [0, 0], [0, 0], [0, 0], [-6, -17], [3, -6], [-6, 0], [4, -5], [-7, -5], [-24, -58], [-161, -387], [-63, -153], [-35, -1], [-96, 31]], "o": [[97, -32], [11, -11], [0, -14], [-45, -109], [-55, -132], [-53, -126], [-45, -110], [-50, -120], [9, -7], [-4, -4], [0, 25], [-14, -30], [-111, -267], [-9, 0], [-134, 42], [-13, 14], [0, 13], [35, 83], [0, 0], [0, 0], [0, 0], [0, 0], [26, -6], [3, 12], [-3, 5], [6, 0], [-3, 6], [7, 4], [50, 121], [110, 265], [32, 77], [11, 0], [0, 0]], "v": [[9947, 8624], [10141, 8549], [10160, 8504], [10079, 8282], [9898, 7845], [9750, 7490], [9420, 6695], [9284, 6366], [9248, 6224], [9257, 6204], [9250, 6210], [9192, 6140], [9054, 5810], [8881, 5500], [8689, 5555], [8493, 5635], [8470, 5676], [8725, 6305], [8840, 6583], [8893, 6710], [8869, 6731], [8845, 6752], [8877, 6746], [8915, 6759], [8915, 6790], [8920, 6800], [8925, 6809], [8933, 6827], [8989, 6940], [9335, 7775], [9670, 8583], [9753, 8680], [9947, 8624]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 204", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[11807, 8574], [11788, 8573], [11794, 8579], [11807, 8574]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 205", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-8, 0], [0, 6], [8, 0], [0, 0]], "v": [[12040, 8540], [12025, 8530], [12010, 8540], [12025, 8550], [12040, 8540]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 206", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 11], [10, -8], [0, 9], [16, -9], [-30, -7], [-1, -1], [-8, 3]], "o": [[13, -5], [-9, -11], [-11, 9], [0, -9], [-23, 12], [17, 4], [1, 0], [0, 0]], "v": [[12144, 8534], [12149, 8514], [12124, 8511], [12110, 8511], [12088, 8512], [12095, 8531], [12127, 8539], [12144, 8534]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 207", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-4, -5], [12, -2], [-24, -1], [4, 12]], "o": [[-4, -8], [-5, 0], [3, 5], [-17, 3], [25, 1], [0, 0]], "v": [[12024, 8505], [12008, 8490], [12005, 8499], [11988, 8513], [11997, 8519], [12024, 8505]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 208", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-66, 33], [-68, 69], [-23, 201], [41, 129], [159, 75], [118, 0], [-9, 11], [8, 8], [-3, -24], [56, -20], [80, -75], [0, -260], [-146, -122], [-195, 38]], "o": [[49, -10], [90, -44], [135, -138], [13, -116], [-57, -178], [-108, -52], [-79, 0], [8, -9], [-16, -16], [2, 18], [-108, 38], [-175, 164], [0, 201], [172, 143], [0, 0]], "v": [[8418, 8475], [8600, 8408], [8785, 8276], [9021, 7771], [8980, 7416], [8618, 6992], [8328, 6925], [8242, 6912], [8242, 6888], [8203, 6910], [8138, 6955], [7873, 7114], [7610, 7749], [7868, 8318], [8418, 8475]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 209", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, -2], [-16, 0], [6, 3]], "o": [[-7, -2], [-10, 3], [17, 0], [0, 0]], "v": [[11893, 8463], [11863, 8463], [11875, 8468], [11893, 8463]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 210", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [26, -11], [-24, 1], [0, 4]], "o": [[0, -10], [-17, 8], [15, -1], [0, 0]], "v": [[12030, 8458], [11995, 8460], [12003, 8467], [12030, 8458]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 211", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, -13], [-4, 11], [3, 3]], "o": [[-10, -10], [6, 11], [2, -7], [0, 0]], "v": [[12176, 8403], [12166, 8421], [12178, 8421], [12176, 8403]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 212", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, -3], [-13, 0], [3, 8]], "o": [[-4, -8], [-11, 6], [5, 0], [0, 0]], "v": [[6044, 8395], [6029, 8386], [6040, 8410], [6044, 8395]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 213", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[5970, 8390], [5966, 8380], [5955, 8390], [5959, 8400], [5970, 8390]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 214", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [10, -16], [-11, 4], [-70, 0], [-25, -13], [-48, 22], [-3, 9], [-9, 0], [-50, 25], [-95, 636], [15, 124], [210, 201], [199, 43], [114, -31], [154, -262], [0, 0], [0, 0], [14, 2], [39, 3], [8, 2], [14, 53], [470, 70], [232, -346], [5, -17], [-35, -34], [-230, -25], [-33, -5], [-70, 46], [-92, 0], [-65, -48], [-6, -31], [-8, -16], [28, -72], [204, 0], [212, 109], [53, -33], [9, -96], [36, -410], [9, -93], [-43, -32], [-270, -32], [-282, -33], [-23, 0], [-17, 44], [-10, 137], [18, 28], [754, 89], [0, 0], [0, 0], [-1, 1], [-41, -8], [-94, 21], [-67, 450], [4, 73], [0, 0], [0, 0], [-38, 0], [0, -1], [14, -32], [28, -272], [-22, -119], [-99, -130], [-135, -60], [16, -20], [-15, 6], [0, -12], [-11, 15], [-12, -14], [-10, 15], [0, -16], [18, 0], [0, -8], [-27, 15], [-26, 8]], "o": [[57, -17], [-5, 8], [9, -3], [101, -1], [31, 15], [28, -13], [3, -10], [9, 0], [388, -195], [21, -145], [-45, -369], [-128, -122], [-130, -29], [-274, 75], [0, 0], [0, 0], [-53, -3], [-14, -2], [-38, -3], [-9, -3], [-113, -425], [-441, -65], [-26, 38], [-12, 44], [32, 29], [61, 7], [121, 18], [80, -52], [91, 0], [46, 34], [2, 6], [23, 49], [-61, 162], [-181, -1], [-143, -74], [-50, 30], [-9, 99], [-26, 300], [-6, 63], [26, 18], [44, 5], [282, 34], [49, 0], [12, -29], [6, -69], [-42, -67], [0, 0], [0, 0], [4, -77], [0, 0], [108, 22], [433, -101], [11, -80], [0, 0], [0, 0], [73, 5], [37, 0], [0, 1], [-86, 191], [-16, 158], [41, 217], [100, 131], [77, 34], [-11, 13], [12, -4], [1, 16], [11, -15], [13, 13], [13, -17], [0, 9], [-6, 0], [0, 14], [17, -8], [0, 0]], "v": [[6089, 8354], [6174, 8352], [6183, 8357], [6327, 8350], [6487, 8365], [6570, 8358], [6626, 8318], [6647, 8300], [6754, 8254], [7476, 7010], [7491, 6384], [7121, 5557], [6640, 5314], [6150, 5319], [5504, 5827], [5448, 5921], [5352, 5914], [5230, 5904], [5135, 5895], [5050, 5885], [5012, 5793], [4083, 5005], [3015, 5451], [2959, 5552], [2998, 5684], [3315, 5750], [3485, 5771], [3710, 5738], [3936, 5670], [4179, 5744], [4274, 5863], [4291, 5905], [4280, 6203], [3900, 6434], [3342, 6278], [3113, 6230], [3039, 6388], [2975, 7120], [2915, 7800], [2971, 7943], [3305, 8000], [3897, 8069], [4451, 8130], [4568, 8053], [4625, 7625], [4611, 7513], [3872, 7368], [3678, 7345], [3685, 7205], [3694, 7063], [3770, 7077], [4275, 7078], [5036, 6239], [5047, 6021], [5041, 5918], [5173, 5926], [5373, 5935], [5440, 5937], [5414, 5997], [5230, 6745], [5244, 7382], [5469, 7941], [5858, 8257], [5928, 8318], [5933, 8327], [5950, 8338], [5963, 8339], [5989, 8338], [6015, 8336], [6070, 8329], [5991, 8370], [5980, 8385], [6011, 8384], [6089, 8354]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 215", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -11], [-11, 0], [0, 6]], "o": [[0, -11], [-2, 4], [10, 1], [0, 0]], "v": [[11740, 8389], [11706, 8388], [11721, 8397], [11740, 8389]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 216", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, -14], [-3, 3], [7, 7]], "o": [[-10, -9], [3, 10], [3, -3], [0, 0]], "v": [[11641, 8367], [11636, 8373], [11648, 8385], [11641, 8367]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 217", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [-7, -11], [9, 24]], "o": [[-3, -9], [-5, 0], [14, 22], [0, 0]], "v": [[11804, 8356], [11789, 8340], [11792, 8360], [11804, 8356]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 218", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-9, 0], [3, 6]], "o": [[-3, -5], [-5, 0], [0, 6], [8, 0], [0, 0]], "v": [[12135, 8370], [12119, 8360], [12110, 8370], [12126, 8380], [12135, 8370]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 219", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[12110, 8310], [12099, 8300], [12095, 8310], [12106, 8320], [12110, 8310]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 220", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 3], [0, -12], [-8, 12]], "o": [[3, -5], [-15, -9], [0, 13], [0, 0]], "v": [[12015, 8281], [12010, 8265], [11950, 8280], [12015, 8281]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 221", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -14], [-21, -1], [2, 6]], "o": [[-5, -16], [0, 11], [8, 1], [0, 0]], "v": [[12138, 8278], [12090, 8268], [12128, 8289], [12138, 8278]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 222", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 9], [0, -9], [9, 0], [-3, -5], [-16, 10]], "o": [[8, -5], [-7, -10], [0, 6], [-8, 0], [7, 12], [0, 0]], "v": [[12067, 8262], [12070, 8240], [12060, 8238], [12044, 8250], [12035, 8260], [12067, 8262]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 223", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-7, -11], [-23, 0], [28, 7]], "o": [[-44, -11], [3, 6], [40, -1], [0, 0]], "v": [[11625, 8250], [11565, 8250], [11613, 8259], [11625, 8250]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 224", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-11, -3], [0, 6], [-3, 8], [18, 7], [4, -12], [7, 3], [6, -14], [-11, 5], [-3, -5], [0, 12]], "o": [[0, -5], [11, 3], [0, -6], [3, -8], [-24, -9], [-4, 8], [-7, -2], [-10, 20], [8, -3], [8, 13], [0, 0]], "v": [[11140, 8231], [11160, 8227], [11180, 8222], [11185, 8198], [11161, 8173], [11126, 8177], [11107, 8187], [11083, 8207], [11085, 8226], [11105, 8230], [11140, 8231]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 225", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[11010, 8220], [10999, 8210], [10995, 8220], [11006, 8230], [11010, 8220]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 226", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[11057, 8224], [11038, 8223], [11044, 8229], [11057, 8224]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 227", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, 17], [26, -9], [0, -18], [9, -8], [-7, 3], [-2, -4], [-22, -1], [-20, 31]], "o": [[20, -32], [-14, -16], [-45, 16], [0, 9], [-10, 8], [6, -4], [3, 5], [34, 3], [0, 0]], "v": [[11318, 8195], [11324, 8142], [11277, 8134], [11220, 8177], [11203, 8207], [11197, 8216], [11213, 8217], [11257, 8227], [11318, 8195]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 228", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[11580, 8200], [11576, 8190], [11565, 8200], [11569, 8210], [11580, 8200]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 229", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [3, -5], [-6, 0], [-3, 6]], "o": [[3, -5], [-5, 0], [-3, 6], [5, 0], [0, 0]], "v": [[11035, 8190], [11031, 8180], [11015, 8190], [11019, 8200], [11035, 8190]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 230", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[11210, 8140], [11206, 8130], [11195, 8140], [11199, 8150], [11210, 8140]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 231", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[11930, 8130], [11919, 8120], [11915, 8130], [11926, 8140], [11930, 8130]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 232", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [13, 3], [-2, -8], [6, 0], [0, 6], [17, 0], [0, -10], [-8, 0], [0, -5], [-16, 12]], "o": [[13, -9], [-6, -1], [2, 8], [-5, 0], [0, -5], [-20, 0], [0, 8], [8, 0], [0, 12], [0, 0]], "v": [[11286, 8118], [11285, 8073], [11278, 8086], [11270, 8100], [11260, 8090], [11230, 8080], [11200, 8095], [11215, 8110], [11230, 8119], [11286, 8118]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 233", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, 4], [0, -12], [-11, 4], [-4, -6], [0, 13]], "o": [[0, -5], [-36, -9], [0, 6], [11, -3], [8, 12], [0, 0]], "v": [[11410, 8121], [11388, 8106], [11310, 8111], [11329, 8115], [11355, 8120], [11410, 8121]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 234", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [17, -18], [-20, 3], [-2, 6]], "o": [[5, -17], [-14, 13], [13, -2], [0, 0]], "v": [[19748, 8062], [19714, 8065], [19721, 8077], [19748, 8062]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 235", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [3, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-6, 0], [-3, 6], [9, 0], [0, 0]], "v": [[11340, 8060], [11331, 8050], [11315, 8060], [11324, 8070], [11340, 8060]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 236", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -8], [-2, 0], [-4, 8]], "o": [[3, -8], [-6, 0], [0, 8], [2, 0], [0, 0]], "v": [[19944, 8055], [19940, 8040], [19930, 8055], [19934, 8070], [19944, 8055]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 237", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[11980, 8040], [11970, 8030], [11960, 8040], [11970, 8050], [11980, 8040]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 238", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -8], [-2, 0], [-4, 8]], "o": [[3, -8], [-6, 0], [0, 8], [2, 0], [0, 0]], "v": [[2294, 7875], [2290, 7860], [2280, 7875], [2284, 7890], [2294, 7875]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 239", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-13, -17], [-1, 14], [6, -9], [-16, 0], [-3, 11]], "o": [[5, -19], [12, 16], [0, -15], [-31, 54], [6, 0], [0, 0]], "v": [[19877, 7810], [19896, 7807], [19910, 7809], [19877, 7771], [19861, 7830], [19877, 7810]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 240", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -8], [-11, -7], [-1, 0], [3, 12]], "o": [[-4, -15], [-8, 8], [8, 5], [0, 0], [0, 0]], "v": [[19763, 7779], [19746, 7770], [19750, 7790], [19767, 7800], [19763, 7779]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 241", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[2300, 7786], [2290, 7775], [2280, 7779], [2290, 7790], [2300, 7786]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 242", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [16, -13], [20, 17], [1, 7], [4, -14], [-21, 8], [-3, -9], [-16, 16]], "o": [[16, -16], [-10, 8], [-15, -13], [-1, -8], [-9, 29], [7, -3], [7, 19], [0, 0]], "v": [[19818, 7748], [19817, 7728], [19777, 7716], [19749, 7679], [19741, 7690], [19766, 7733], [19786, 7744], [19818, 7748]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 243", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, -14], [0, -6], [-3, 11], [7, 7]], "o": [[-9, -8], [3, 10], [1, 7], [3, -10], [0, 0]], "v": [[19799, 7686], [19794, 7693], [19801, 7723], [19807, 7716], [19799, 7686]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 244", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -81], [-7, 11], [0, 30]], "o": [[0, -71], [-4, 46], [6, -8], [0, 0]], "v": [[2370, 7601], [2355, 7620], [2359, 7670], [2370, 7601]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 245", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 14], [9, 11], [-48, -5], [6, 30], [-27, -25], [0, 9], [16, 0], [3, 7], [18, 9], [0, 0], [0, 0], [-3, -10], [21, 4], [6, -8], [7, 6], [8, -5], [-18, -34], [12, 0], [0, 8], [26, -13], [-22, 18], [29, 5], [0, -4], [8, 3], [-23, -15], [0, -11], [-17, 9], [4, -12], [-14, -11], [3, -3], [-4, -11], [0, 27]], "o": [[0, -11], [9, -19], [-17, -20], [37, 4], [-7, -34], [13, 11], [0, -15], [-7, 0], [-3, -8], [0, 0], [0, 0], [17, 16], [6, 14], [-14, -3], [-8, 9], [-7, -6], [-12, 7], [17, 33], [-7, 0], [0, -18], [-23, 13], [21, -18], [-14, -3], [0, 5], [-22, -9], [11, 7], [0, 18], [15, -8], [-3, 7], [14, 11], [-3, 3], [8, 25], [0, 0]], "v": [[19000, 7630], [19011, 7586], [19011, 7546], [19072, 7516], [19104, 7490], [19132, 7477], [19150, 7480], [19084, 7420], [19067, 7406], [19028, 7375], [18995, 7359], [19026, 7388], [19063, 7435], [19045, 7448], [19009, 7457], [18986, 7461], [18960, 7460], [18967, 7509], [18974, 7550], [18960, 7535], [18929, 7530], [18925, 7510], [18915, 7478], [18890, 7481], [18875, 7484], [18880, 7508], [18900, 7540], [18920, 7550], [18934, 7554], [18954, 7587], [18974, 7613], [18975, 7639], [19000, 7630]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 246", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[19760, 7640], [19749, 7630], [19745, 7640], [19756, 7650], [19760, 7640]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 247", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[19790, 7620], [19779, 7610], [19775, 7620], [19786, 7630], [19790, 7620]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 248", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [-3, -5], [-5, 0], [3, 6]], "o": [[-3, -5], [-6, 0], [3, 6], [6, 0], [0, 0]], "v": [[11705, 7570], [11689, 7560], [11685, 7570], [11701, 7580], [11705, 7570]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 249", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[19720, 7476], [19710, 7465], [19700, 7469], [19710, 7480], [19720, 7476]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 250", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -6], [-10, -11], [0, 14]], "o": [[0, -11], [-3, 3], [18, 20], [0, 0]], "v": [[18890, 7462], [18842, 7424], [18855, 7450], [18890, 7462]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 251", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [25, -5], [-29, -27], [2, -7], [-12, 11], [3, 9]], "o": [[-11, -30], [-31, 5], [13, 12], [-3, 8], [12, -10], [0, 0]], "v": [[18953, 7421], [18905, 7388], [18903, 7424], [18922, 7459], [18938, 7454], [18953, 7421]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 252", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, -15], [-10, 3], [0, 6]], "o": [[-1, -15], [-3, 9], [10, -1], [0, 0]], "v": [[11941, 7445], [11912, 7448], [11924, 7458], [11941, 7445]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 253", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [21, -4], [0, -10], [-20, 8]], "o": [[22, -8], [-18, 3], [0, 8], [0, 0]], "v": [[19324, 7414], [19323, 7382], [19280, 7414], [19324, 7414]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 254", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[18910, 7370], [18900, 7360], [18890, 7370], [18900, 7380], [18910, 7370]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 255", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[11965, 7350], [11961, 7340], [11950, 7350], [11954, 7360], [11965, 7350]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 256", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 2], [0, -13], [-8, 2], [0, 7]], "o": [[0, -7], [-8, -3], [0, 13], [6, -3], [0, 0]], "v": [[11855, 7330], [11843, 7313], [11830, 7330], [11843, 7348], [11855, 7330]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 257", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 8], [20, -7], [-33, 6], [0, -9], [-10, 15]], "o": [[3, -6], [-11, -11], [-34, 13], [17, -4], [0, 17], [0, 0]], "v": [[19386, 7329], [19378, 7304], [19337, 7299], [19335, 7318], [19360, 7326], [19386, 7329]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 258", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [20, -8], [-9, -11], [0, 18]], "o": [[0, -19], [-13, 5], [15, 18], [0, 0]], "v": [[12020, 7306], [11986, 7286], [11981, 7306], [12020, 7306]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 259", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [14, 0], [-21, -12], [0, 13]], "o": [[0, -19], [-20, 0], [22, 13], [0, 0]], "v": [[18800, 7310], [18754, 7250], [18758, 7309], [18800, 7310]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 260", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -8], [-2, 0], [0, 8]], "o": [[0, -8], [-5, 0], [4, 8], [2, 0], [0, 0]], "v": [[18930, 7295], [18920, 7280], [18916, 7295], [18926, 7310], [18930, 7295]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 261", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [4, 4], [15, -4], [-17, -23], [-2, 20]], "o": [[2, -16], [-1, -2], [-25, 6], [18, 25], [0, 0]], "v": [[11861, 7278], [11855, 7232], [11826, 7236], [11818, 7266], [11861, 7278]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 262", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[12000, 7266], [11990, 7255], [11980, 7259], [11990, 7270], [12000, 7266]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 263", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -11], [-5, 0], [0, 8]], "o": [[0, -8], [-5, -3], [0, 12], [6, 0], [0, 0]], "v": [[11950, 7246], [11940, 7225], [11930, 7239], [11940, 7260], [11950, 7246]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 264", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-4, 4], [6, 8], [-38, -29], [-11, 1], [4, -12], [-5, 0], [9, 23], [8, -3], [-5, 44], [3, 23], [16, 0], [-16, -24], [7, 0], [-9, 8], [8, 12], [-3, 8], [6, 0], [0, 5], [-9, -2], [-2, 6], [7, -18], [-7, 21], [13, -16], [-27, -48], [9, -139], [-15, 0], [1, 18]], "o": [[-2, -19], [4, -4], [-20, -24], [20, 16], [15, -3], [-4, 9], [11, 0], [-3, -8], [-14, 5], [1, -11], [-5, -34], [-18, 0], [10, 15], [-14, 0], [3, -4], [-8, -13], [3, -9], [-6, 0], [0, -6], [10, 2], [7, -19], [-8, 21], [5, -15], [-21, 26], [23, 40], [-4, 60], [5, 0], [0, 0]], "v": [[18949, 7226], [18953, 7184], [18949, 7163], [18997, 7177], [19053, 7202], [19067, 7214], [19070, 7230], [19074, 7196], [19054, 7187], [19039, 7125], [19035, 7063], [19009, 7020], [19006, 7045], [19011, 7070], [18974, 7013], [18965, 6984], [18956, 6946], [18951, 6930], [18940, 6920], [18956, 6912], [18977, 6903], [18936, 6905], [18916, 6903], [18906, 6904], [18913, 6995], [18927, 7180], [18942, 7260], [18949, 7226]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 265", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [10, 4], [9, 8], [0, -16], [-4, 3], [-8, -11], [-10, 15]], "o": [[3, -6], [-10, -3], [-16, -14], [0, 10], [3, -4], [16, 22], [0, 0]], "v": [[12015, 7230], [12003, 7213], [11968, 7192], [11950, 7195], [11957, 7207], [11977, 7220], [12015, 7230]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 266", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [28, 29], [0, -15], [-26, -22], [0, 7]], "o": [[0, -2], [-37, -40], [0, 11], [44, 35], [0, 0]], "v": [[11860, 7217], [11810, 7160], [11760, 7126], [11806, 7183], [11860, 7217]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 267", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[19220, 7210], [19216, 7200], [19205, 7210], [19209, 7220], [19220, 7210]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 268", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -8], [-5, 0], [0, 8]], "o": [[0, -8], [-5, 0], [0, 8], [6, 0], [0, 0]], "v": [[11930, 7195], [11920, 7180], [11910, 7195], [11920, 7210], [11930, 7195]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 269", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[11750, 7130], [11739, 7120], [11735, 7130], [11746, 7140], [11750, 7130]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 270", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[2077, 7108], [2071, 7114], [2076, 7127], [2077, 7108]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 271", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [3, 3], [-5, -14], [-7, 23]], "o": [[4, -11], [-8, -9], [6, 19], [0, 0]], "v": [[2365, 7099], [2366, 7073], [2338, 7108], [2365, 7099]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 272", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[10647, 7114], [10628, 7113], [10634, 7119], [10647, 7114]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 273", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-100, 139], [-1, 113], [-16, -11], [0, 8], [-34, -13], [-5, 14], [-12, -17], [-7, 11], [7, -32], [-15, 0], [12, 8], [-2, 2], [-12, -7], [14, -6], [-24, 6], [0, 5], [-16, -2], [1, 13], [-47, -19], [18, -7], [-14, 0], [-11, 12], [-9, -16], [-10, 4], [0, -6], [-6, 4], [3, 10], [-16, -13], [-7, 20], [-9, -9], [-13, 0], [5, -13], [-16, 7], [-30, 4], [-16, -19], [-15, 19], [4, -22], [-9, 6], [-12, -11], [0, 6], [6, 2], [-12, 0], [-2, -7], [0, 11], [-6, 2], [0, -14], [-9, -7], [4, 7], [-42, 4], [-5, 3], [-16, 4], [0, 5], [92, 0], [3, 1], [28, 2], [122, 12], [127, 3], [3, 4], [20, -11], [-4, 11], [15, 2], [14, 80], [216, 85], [120, -6], [92, -62], [-7, -7], [15, -13], [0, -5], [20, -39], [9, -35], [42, 16], [-3, -10], [-17, 0], [0, 0], [0, 0], [-233, -159], [-66, -13], [-69, 14]], "o": [[153, -33], [85, -120], [1, -50], [5, 3], [0, -19], [21, 8], [6, -14], [11, 15], [15, -23], [-5, 24], [15, 0], [-8, -5], [31, -20], [11, 7], [-24, 9], [11, -3], [0, -5], [22, 4], [-1, -18], [27, 10], [-16, 7], [10, 1], [18, -19], [6, 10], [9, -4], [0, 6], [5, -3], [-6, -15], [24, 19], [4, -11], [7, 7], [17, 0], [-5, 13], [10, -4], [49, -7], [17, 21], [14, -18], [-1, 5], [13, -7], [10, 9], [0, -6], [-7, -3], [12, -1], [4, 7], [1, -9], [19, -7], [0, 7], [10, 8], [-9, -15], [19, -2], [5, -3], [15, -3], [0, -9], [-33, 0], [-3, -1], [-49, -2], [-85, -8], [-59, -1], [-10, -10], [-16, 8], [4, -10], [-21, -3], [-46, -253], [-96, -38], [-119, 5], [-58, 39], [3, 2], [-15, 13], [-1, 6], [-20, 40], [-18, 67], [-12, -4], [3, 8], [0, 0], [0, 0], [-13, 300], [51, 35], [64, 13], [0, 0]], "v": [[10379, 7086], [10806, 6795], [10961, 6375], [10991, 6306], [11000, 6296], [11035, 6290], [11067, 6283], [11087, 6286], [11109, 6291], [11135, 6321], [11147, 6350], [11150, 6340], [11140, 6328], [11201, 6310], [11197, 6326], [11200, 6343], [11220, 6328], [11249, 6323], [11276, 6311], [11346, 6311], [11355, 6330], [11352, 6339], [11389, 6319], [11418, 6316], [11444, 6327], [11460, 6331], [11470, 6335], [11473, 6312], [11484, 6310], [11585, 6311], [11602, 6308], [11637, 6320], [11653, 6337], [11666, 6345], [11740, 6330], [11813, 6344], [11845, 6346], [11861, 6353], [11877, 6351], [11912, 6357], [11930, 6362], [11918, 6347], [11927, 6341], [11953, 6353], [11959, 6348], [11972, 6328], [12060, 6365], [12077, 6391], [12087, 6393], [12133, 6366], [12176, 6358], [12213, 6346], [12240, 6331], [12035, 6311], [11970, 6309], [11915, 6304], [11705, 6286], [11317, 6265], [11203, 6256], [11020, 6260], [11006, 6256], [10989, 6238], [10950, 6145], [10517, 5586], [10230, 5543], [9914, 5644], [9726, 5813], [9703, 5841], [9675, 5875], [9638, 5957], [9585, 6094], [9504, 6163], [9492, 6171], [9527, 6185], [9556, 6185], [9552, 6287], [9892, 6998], [10115, 7090], [10379, 7086]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 274", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, -4], [4, 6], [6, 0], [-10, -8], [0, 15]], "o": [[0, -5], [-5, 3], [-3, -5], [-7, 0], [23, 18], [0, 0]], "v": [[11890, 7079], [11881, 7075], [11865, 7070], [11847, 7060], [11854, 7075], [11890, 7079]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 275", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -16], [-5, 0], [0, 17]], "o": [[0, -16], [-5, 0], [1, 17], [4, 0], [0, 0]], "v": [[2370, 7030], [2361, 7000], [2352, 7030], [2362, 7060], [2370, 7030]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 276", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 4], [0, -8], [-9, 14]], "o": [[3, -5], [-5, -3], [0, 18], [0, 0]], "v": [[2365, 6981], [2360, 6965], [2350, 6974], [2365, 6981]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 277", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -10], [5, 14], [2, -3], [-4, -9], [0, 22]], "o": [[0, -14], [-6, 11], [-3, -9], [-3, 3], [8, 21], [0, 0]], "v": [[19950, 6973], [19941, 6968], [19926, 6964], [19915, 6952], [19916, 6974], [19950, 6973]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 278", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [-8, -11], [-2, 33]], "o": [[2, -19], [-10, 0], [10, 17], [0, 0]], "v": [[18997, 6945], [18991, 6910], [18977, 6972], [18997, 6945]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 279", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [13, -8], [-12, 0], [-3, 7]], "o": [[7, -18], [-11, 7], [4, 0], [0, 0]], "v": [[2383, 6937], [2362, 6904], [2370, 6950], [2383, 6937]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 280", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 4], [5, 0], [-1, -15], [-8, 0], [4, 14]], "o": [[-3, -12], [0, -5], [-6, 0], [1, 17], [9, 0], [0, 0]], "v": [[18956, 6846], [18950, 6818], [18940, 6810], [18932, 6838], [18948, 6867], [18956, 6846]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 281", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, -17], [-10, 4], [0, -6], [-5, 3], [0, 8]], "o": [[0, -15], [-4, 6], [10, -3], [0, 6], [6, -3], [0, 0]], "v": [[2410, 6825], [2363, 6833], [2373, 6837], [2390, 6841], [2400, 6845], [2410, 6825]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 282", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3], [0, -28], [-13, 11], [3, 20]], "o": [[-2, -17], [-4, -17], [0, 29], [10, -9], [0, 0]], "v": [[19896, 6776], [19891, 6740], [19870, 6799], [19885, 6820], [19896, 6776]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 283", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[18350, 6810], [18340, 6800], [18330, 6810], [18340, 6820], [18350, 6810]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 284", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-9, 6], [8, 5]], "o": [[-19, -12], [0, 10], [2, -2], [0, 0]], "v": [[18750, 6800], [18720, 6799], [18760, 6813], [18750, 6800]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 285", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -3], [-11, 0], [-1, 11]], "o": [[1, -11], [-11, 7], [4, 0], [0, 0]], "v": [[2387, 6790], [2379, 6775], [2378, 6810], [2387, 6790]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 286", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -6], [-24, -11], [0, 9]], "o": [[0, -9], [-3, 2], [46, 20], [0, 0]], "v": [[18700, 6785], [18602, 6745], [18641, 6770], [18700, 6785]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 287", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-9, 0], [3, 6]], "o": [[-3, -5], [-5, 0], [0, 6], [8, 0], [0, 0]], "v": [[18585, 6730], [18569, 6720], [18560, 6730], [18576, 6740], [18585, 6730]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 288", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-4, 10], [15, -11], [2, -12], [11, 0], [0, -3], [-16, -1], [0, 26]], "o": [[0, -19], [6, -16], [-8, 8], [-2, 14], [-9, -1], [0, 11], [13, 0], [0, 0]], "v": [[2390, 6696], [2397, 6643], [2387, 6638], [2368, 6673], [2347, 6694], [2330, 6697], [2373, 6730], [2390, 6696]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 289", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-13, -8], [0, 12]], "o": [[0, -5], [-15, 0], [19, 12], [0, 0]], "v": [[10900, 6710], [10883, 6700], [10880, 6710], [10900, 6710]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 290", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[19697, 6714], [19678, 6713], [19684, 6719], [19697, 6714]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 291", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[18450, 6670], [18439, 6660], [18435, 6670], [18446, 6680], [18450, 6670]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 292", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [27, 0], [-17, -17], [-20, 8]], "o": [[21, -8], [-22, 0], [14, 14], [0, 0]], "v": [[17944, 6634], [17919, 6600], [17912, 6628], [17944, 6634]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 293", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -3], [0, -6], [-5, 0], [0, 9]], "o": [[0, -8], [-5, 3], [0, 5], [6, 0], [0, 0]], "v": [[17890, 6594], [17880, 6585], [17870, 6601], [17880, 6610], [17890, 6594]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 294", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [16, 0], [-27, -7], [0, 15]], "o": [[0, -9], [-29, 0], [30, 8], [0, 0]], "v": [[8100, 6585], [8075, 6570], [8068, 6593], [8100, 6585]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 295", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [-6, -9], [0, 10]], "o": [[0, -3], [-18, -5], [6, 9], [0, 0]], "v": [[8180, 6568], [8168, 6557], [8125, 6570], [8180, 6568]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 296", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[7970, 6560], [7959, 6550], [7955, 6560], [7966, 6570], [7970, 6560]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 297", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [16, 0], [-20, -10], [0, 12]], "o": [[0, -9], [-13, 0], [29, 14], [0, 0]], "v": [[17520, 6550], [17464, 6520], [17483, 6548], [17520, 6550]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 298", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-4, 9], [3, 16], [0, 13], [10, -9], [4, 4], [-4, -10], [-4, 2], [-1, -10], [8, -3], [0, -11], [6, -2], [-10, 0], [-3, -11], [3, 24]], "o": [[-1, -10], [4, -9], [-2, -16], [-1, -18], [-8, 6], [-4, -4], [3, 9], [4, -3], [1, 10], [-8, 3], [0, 11], [-8, 4], [9, 1], [6, 25], [0, 0]], "v": [[2429, 6523], [2434, 6489], [2436, 6444], [2431, 6391], [2417, 6379], [2396, 6383], [2396, 6393], [2409, 6406], [2417, 6420], [2404, 6444], [2390, 6469], [2378, 6493], [2381, 6499], [2403, 6520], [2429, 6523]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 299", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-10, 7], [-1, 19], [-8, -2], [26, 10], [-14, 0], [29, 30], [-15, 12], [13, 17], [-14, 10], [30, 29], [5, -1], [6, 6], [3, -4], [4, 10], [8, -14], [11, 9], [5, -8], [-3, 18], [7, 2], [-9, -13], [8, 3], [-3, 12], [6, 0], [0, -7], [6, 10], [0, -7], [9, 2], [4, 0], [0, 13], [7, 7], [-9, 0], [0, -7], [-9, 9], [16, 13], [-8, -19], [13, -3], [-25, -11], [35, 0], [-26, -9], [15, 0], [3, -4], [-20, 5], [-9, -11], [5, 15], [-14, -26], [8, -10], [-3, -6], [19, 0], [-11, -11], [-8, 8], [-6, -24], [-8, 0], [3, 7], [-14, 8], [18, 20], [-19, -12], [10, -7], [-15, -1], [7, -3], [-8, -9], [13, -16], [-35, -33], [-19, 4], [2, -15], [4, -7], [-26, 6], [-1, 44], [5, 0], [-3, 5], [8, 6], [21, -17], [0, 0], [0, 0], [6, 0], [3, 6], [-1, -10], [12, 34], [-7, 2], [0, 8], [-6, 0], [3, -5], [-11, -3], [-4, -19], [0, 38], [-28, 10], [-38, -20], [13, -3], [-3, -10], [0, -5], [-10, 0], [14, -4], [-8, -45], [-28, -2], [3, -5], [10, 3], [7, -68], [-14, 0], [-6, 15]], "o": [[5, -14], [9, -7], [0, -22], [24, 4], [-11, -4], [17, -1], [-31, -32], [16, -11], [-12, -17], [13, -10], [-19, -19], [-4, 1], [-7, -5], [-4, 3], [-9, -25], [-6, 9], [-10, -8], [-10, 16], [2, -10], [-13, -5], [3, 5], [-9, -3], [3, -10], [-6, 0], [0, 8], [-5, -8], [0, 6], [-10, -2], [-5, -1], [0, -13], [-9, -9], [7, 0], [0, 9], [9, -9], [-23, -22], [3, 9], [-22, 3], [24, 10], [-37, 1], [6, 2], [-15, 0], [-11, 11], [12, -4], [14, 17], [-8, -26], [8, 15], [-7, 8], [8, 12], [-14, 0], [4, 3], [20, -21], [3, 11], [7, 0], [-2, -7], [32, -17], [-17, -19], [9, 6], [-11, 7], [11, 0], [-9, 3], [7, 9], [-19, 23], [27, 24], [21, -4], [0, 10], [-10, 16], [19, -5], [0, -27], [-5, 0], [3, -5], [-10, -8], [0, 0], [0, 0], [4, -13], [-5, 0], [-2, -7], [5, 29], [-8, -22], [5, -2], [0, -8], [5, 0], [-3, 5], [13, 3], [8, 39], [0, -23], [31, -12], [32, 16], [-12, 2], [3, 8], [0, 5], [15, 1], [-19, 5], [4, 25], [18, 2], [-3, 4], [-28, -7], [-6, 53], [8, 0], [0, 0]], "v": [[8271, 6513], [8298, 6474], [8315, 6429], [8328, 6398], [8310, 6357], [8314, 6351], [8300, 6315], [8283, 6267], [8286, 6235], [8288, 6204], [8269, 6159], [8226, 6128], [8206, 6120], [8187, 6117], [8173, 6104], [8139, 6082], [8117, 6081], [8095, 6080], [8072, 6074], [8063, 6053], [8026, 6121], [8017, 6124], [8007, 6099], [8001, 6080], [7990, 6093], [7980, 6090], [7970, 6087], [7953, 6095], [7928, 6091], [7920, 6067], [7908, 6032], [7908, 6020], [7920, 6032], [7932, 6032], [7924, 6003], [7874, 5995], [7857, 6013], [7860, 6028], [7848, 6039], [7823, 6063], [7807, 6067], [7773, 6074], [7811, 6103], [7846, 6116], [7856, 6118], [7900, 6121], [7900, 6155], [7894, 6179], [7854, 6220], [7846, 6243], [7867, 6235], [7907, 6240], [7926, 6260], [7934, 6247], [7954, 6220], [7976, 6161], [7990, 6125], [7989, 6141], [7995, 6151], [8002, 6157], [8000, 6175], [7992, 6210], [8009, 6266], [8071, 6293], [8095, 6307], [8087, 6337], [8133, 6367], [8156, 6310], [8148, 6260], [8144, 6251], [8136, 6231], [8095, 6242], [8068, 6263], [8074, 6242], [8071, 6220], [8057, 6208], [8055, 6213], [8031, 6198], [8030, 6162], [8040, 6144], [8051, 6130], [8056, 6139], [8070, 6153], [8096, 6189], [8120, 6192], [8154, 6152], [8232, 6162], [8257, 6188], [8244, 6207], [8250, 6231], [8268, 6241], [8269, 6247], [8250, 6335], [8288, 6368], [8315, 6380], [8292, 6383], [8236, 6478], [8245, 6540], [8271, 6513]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 300", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [18, -12], [-18, 2], [0, 4]], "o": [[0, -11], [-12, 7], [12, 0], [0, 0]], "v": [[8110, 6498], [8080, 6500], [8088, 6507], [8110, 6498]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 301", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 8], [0, -22], [-5, 0], [0, 6]], "o": [[0, -7], [-8, -12], [0, 15], [6, 0], [0, 0]], "v": [[8200, 6488], [8190, 6460], [8180, 6473], [8190, 6500], [8200, 6488]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 302", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[8050, 6470], [8039, 6460], [8035, 6470], [8046, 6480], [8050, 6470]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 303", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 11], [12, 0], [-1, -24], [-3, 11], [-8, -3]], "o": [[9, 3], [0, -11], [-16, 0], [1, 18], [3, -11], [0, 0]], "v": [[8106, 6444], [8120, 6432], [8100, 6414], [8081, 6445], [8087, 6457], [8106, 6444]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 304", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -3], [2, -7], [-8, 3], [-2, 7]], "o": [[3, -8], [-7, 2], [-3, 8], [7, -2], [0, 0]], "v": [[8158, 6451], [8149, 6442], [8132, 6459], [8141, 6468], [8158, 6451]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 305", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-9, 0], [3, 6]], "o": [[-3, -5], [-5, 0], [0, 6], [8, 0], [0, 0]], "v": [[9535, 6440], [9519, 6430], [9510, 6440], [9526, 6450], [9535, 6440]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 306", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[11020, 6446], [11010, 6435], [11000, 6439], [11010, 6450], [11020, 6446]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 307", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[7920, 6420], [7909, 6410], [7905, 6420], [7916, 6430], [7920, 6420]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 308", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[8010, 6410], [8000, 6400], [7990, 6410], [8000, 6420], [8010, 6410]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 309", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[11020, 6410], [11010, 6400], [11000, 6410], [11010, 6420], [11020, 6410]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 310", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -14], [-24, -10], [5, 9]], "o": [[-10, -16], [0, 5], [2, 0], [0, 0]], "v": [[7942, 6393], [7900, 6379], [7948, 6409], [7942, 6393]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 311", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -8], [-3, -6], [0, 24]], "o": [[0, -16], [-6, 6], [11, 17], [0, 0]], "v": [[8060, 6388], [8049, 6377], [8044, 6399], [8060, 6388]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 312", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, 3], [-4, -10], [0, 14]], "o": [[0, -5], [-10, -4], [6, 15], [0, 0]], "v": [[12390, 6401], [12376, 6387], [12367, 6396], [12390, 6401]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 313", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -5], [-13, 20], [15, 0], [15, 4], [-3, -7], [-28, -5], [-5, -20], [0, 0], [0, 0], [-35, -3]], "o": [[22, 2], [0, 17], [10, -17], [-55, -1], [-11, -2], [2, 7], [43, 8], [0, 0], [0, 0], [13, -25], [0, 0]], "v": [[12500, 6358], [12540, 6371], [12578, 6360], [12573, 6340], [12327, 6325], [12313, 6333], [12368, 6354], [12427, 6388], [12433, 6412], [12446, 6383], [12500, 6358]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 314", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -3], [0, -6], [-5, 0], [0, 9]], "o": [[0, -8], [-5, 3], [0, 5], [6, 0], [0, 0]], "v": [[8170, 6384], [8160, 6375], [8150, 6391], [8160, 6400], [8170, 6384]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 315", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, -10], [-6, 13], [2, 3]], "o": [[-8, -8], [4, 6], [7, -12], [0, 0]], "v": [[11463, 6350], [11436, 6392], [11454, 6378], [11463, 6350]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 316", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -6], [-8, 3], [0, 2]], "o": [[0, -2], [-8, 0], [0, 5], [8, -4], [0, 0]], "v": [[11500, 6384], [11485, 6380], [11470, 6390], [11485, 6394], [11500, 6384]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 317", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[7855, 6380], [7851, 6370], [7840, 6380], [7844, 6390], [7855, 6380]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 318", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, -28], [-5, 3], [7, 12]], "o": [[-13, -26], [3, 12], [5, -4], [0, 0]], "v": [[11809, 6359], [11795, 6369], [11811, 6385], [11809, 6359]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 319", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [3, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-6, 0], [-3, 6], [9, 0], [0, 0]], "v": [[12240, 6370], [12231, 6360], [12215, 6370], [12224, 6380], [12240, 6370]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 320", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [22, 0], [0, 0], [0, 0], [4, -6], [-6, 0], [-2, 4]], "o": [[11, -18], [0, 0], [0, 0], [13, 11], [-3, 5], [5, 0], [0, 0]], "v": [[12303, 6373], [12268, 6320], [12245, 6321], [12268, 6340], [12285, 6370], [12289, 6380], [12303, 6373]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 321", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[12350, 6370], [12339, 6360], [12335, 6370], [12346, 6380], [12350, 6370]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 322", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [-3, -5], [-5, 0], [3, 6]], "o": [[-3, -5], [-6, 0], [3, 6], [6, 0], [0, 0]], "v": [[7995, 6360], [7979, 6350], [7975, 6360], [7991, 6370], [7995, 6360]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 323", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -25], [-7, 3], [-2, 9]], "o": [[5, -24], [-3, 13], [7, -2], [0, 0]], "v": [[2418, 6337], [2397, 6341], [2403, 6357], [2418, 6337]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 324", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[11347, 6354], [11328, 6353], [11334, 6359], [11347, 6354]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 325", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, -6], [-23, 0], [0, 4]], "o": [[0, -14], [-21, 8], [11, 0], [0, 0]], "v": [[11550, 6352], [11524, 6337], [11530, 6360], [11550, 6352]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 326", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[11640, 6350], [11629, 6340], [11625, 6350], [11636, 6360], [11640, 6350]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 327", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -2], [-14, 0], [6, 3]], "o": [[-7, -2], [-7, 3], [14, 0], [0, 0]], "v": [[8223, 6343], [8198, 6343], [8210, 6348], [8223, 6343]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 328", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[7880, 6330], [7869, 6320], [7865, 6330], [7876, 6340], [7880, 6330]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 329", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-8, -5], [-5, 0], [8, 5]], "o": [[-8, -5], [-5, 0], [8, 5], [6, 0], [0, 0]], "v": [[7840, 6320], [7815, 6310], [7820, 6320], [7845, 6330], [7840, 6320]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 330", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [3, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-6, 0], [-3, 6], [9, 0], [0, 0]], "v": [[8000, 6320], [7991, 6310], [7975, 6320], [7984, 6330], [8000, 6320]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 331", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-12, 8], [18, 0], [-4, -8], [8, -7], [-22, 0], [7, 7]], "o": [[-9, -9], [16, -12], [-6, 0], [3, 8], [-14, 15], [6, 0], [0, 0]], "v": [[8312, 6298], [8316, 6276], [8308, 6230], [8304, 6245], [8295, 6273], [8314, 6310], [8312, 6298]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 332", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 16], [-5, 7], [5, 6], [3, 36], [8, -60], [-8, -10], [8, 0], [0, -5], [-18, 11]], "o": [[7, -5], [-1, -16], [4, -7], [-8, -13], [-3, -33], [-5, 36], [9, 11], [-6, 0], [0, 12], [0, 0]], "v": [[2418, 6272], [2428, 6234], [2434, 6192], [2433, 6168], [2421, 6115], [2395, 6178], [2400, 6245], [2401, 6260], [2390, 6270], [2418, 6272]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 333", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[9480, 6260], [9469, 6250], [9465, 6260], [9476, 6270], [9480, 6260]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 334", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[7827, 6254], [7808, 6253], [7814, 6259], [7827, 6254]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 335", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [13, -11], [-13, 0], [-3, 9]], "o": [[5, -14], [-17, 14], [5, 0], [0, 0]], "v": [[2194, 6234], [2185, 6230], [2179, 6250], [2194, 6234]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 336", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[2240, 6230], [2230, 6220], [2220, 6230], [2230, 6240], [2240, 6230]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 337", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -17], [-6, 0], [3, 11]], "o": [[-5, -19], [0, 7], [1, 0], [0, 0]], "v": [[8623, 6200], [8600, 6188], [8626, 6220], [8623, 6200]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 338", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [26, 5], [-18, -22], [-2, 7], [-8, 0]], "o": [[24, 0], [-31, -6], [8, 9], [2, -6], [0, 0]], "v": [[9456, 6200], [9445, 6178], [9421, 6207], [9438, 6211], [9456, 6200]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 339", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -17], [-24, 0], [17, 25]], "o": [[-15, -24], [-9, 25], [19, 0], [0, 0]], "v": [[8193, 6184], [8169, 6176], [8191, 6210], [8193, 6184]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 340", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 6], [2, -14], [8, -13], [-9, 0], [-4, 18]], "o": [[3, -17], [-7, -7], [-2, 34], [-5, 7], [8, 0], [0, 0]], "v": [[2384, 6158], [2380, 6116], [2368, 6126], [2357, 6178], [2363, 6190], [2384, 6158]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 341", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[8225, 6180], [8221, 6170], [8210, 6180], [8214, 6190], [8225, 6180]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 342", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-4, -17], [-3, 8], [5, 5]], "o": [[-8, -8], [4, 13], [2, -7], [0, 0]], "v": [[8312, 6158], [8307, 6171], [8318, 6181], [8312, 6158]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 343", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1, -6], [-6, -8], [13, 28]], "o": [[-7, -13], [-2, 5], [18, 21], [0, 0]], "v": [[19917, 6165], [19902, 6151], [19910, 6175], [19917, 6165]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 344", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-7, -12], [-7, 7], [4, 4]], "o": [[-9, -9], [4, 6], [6, -6], [0, 0]], "v": [[9367, 6154], [9346, 6172], [9363, 6171], [9367, 6154]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 345", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -9], [-5, 3], [0, 6]], "o": [[0, -5], [-5, 0], [0, 8], [6, -3], [0, 0]], "v": [[8540, 6149], [8530, 6140], [8520, 6156], [8530, 6165], [8540, 6149]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 346", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [-15, -6], [0, 15]], "o": [[0, -8], [-20, 0], [22, 9], [0, 0]], "v": [[9330, 6155], [9316, 6140], [9302, 6163], [9330, 6155]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 347", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[11960, 6166], [11950, 6155], [11940, 6159], [11950, 6170], [11960, 6166]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 348", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[19850, 6150], [19846, 6140], [19835, 6150], [19839, 6160], [19850, 6150]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 349", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -8], [-5, 0], [-1, -10], [-4, 9], [9, 9]], "o": [[-10, -11], [0, 7], [6, 0], [0, 11], [3, -8], [0, 0]], "v": [[8256, 6102], [8240, 6098], [8250, 6110], [8261, 6128], [8267, 6132], [8256, 6102]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 350", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[11890, 6130], [11880, 6120], [11870, 6130], [11880, 6140], [11890, 6130]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 351", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-9, -6], [8, 12]], "o": [[-3, -5], [-7, 0], [19, 12], [0, 0]], "v": [[11725, 6120], [11707, 6110], [11710, 6120], [11725, 6120]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 352", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [17, 9], [0, -2], [3, -9], [-29, 0], [0, 2]], "o": [[0, -3], [-16, -8], [0, 2], [-5, 13], [20, 0], [0, 0]], "v": [[8410, 6116], [8380, 6096], [8350, 6085], [8344, 6104], [8374, 6120], [8410, 6116]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 353", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -6], [-11, 0], [0, 4]], "o": [[0, -14], [-8, 8], [5, 0], [0, 0]], "v": [[12010, 6113], [11985, 6081], [12001, 6120], [12010, 6113]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 354", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -9], [-8, 0], [0, 2]], "o": [[0, -11], [-3, 5], [9, 0], [0, 0]], "v": [[19920, 6116], [19895, 6110], [19904, 6120], [19920, 6116]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 355", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [7, 8], [-23, -31], [0, 28]], "o": [[0, -11], [-7, 0], [-23, -28], [26, 36], [0, 0]], "v": [[11840, 6080], [11826, 6060], [11800, 6045], [11801, 6069], [11840, 6080]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 356", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[11170, 6060], [11160, 6050], [11150, 6060], [11160, 6070], [11170, 6060]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 357", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[7660, 6050], [7650, 6040], [7640, 6050], [7650, 6060], [7660, 6050]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 358", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, -9], [-5, 8], [7, 0], [-9, 11], [14, 0], [0, 6], [6, -4], [-9, -5], [20, 0], [0, -5], [-9, 14]], "o": [[5, -8], [5, 9], [4, -6], [-8, 0], [10, -12], [-9, 0], [0, -5], [-8, 5], [15, 9], [-8, 0], [0, 14], [0, 0]], "v": [[8126, 6049], [8140, 6050], [8154, 6051], [8149, 6040], [8150, 6025], [8146, 6010], [8130, 5999], [8119, 5996], [8120, 6010], [8104, 6040], [8090, 6050], [8126, 6049]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 359", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[11070, 6050], [11059, 6040], [11055, 6050], [11066, 6060], [11070, 6050]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 360", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[19875, 6050], [19871, 6040], [19860, 6050], [19864, 6060], [19875, 6050]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 361", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, 10], [0, -23], [-10, 16]], "o": [[2, -4], [-11, -16], [0, 24], [0, 0]], "v": [[19924, 6052], [19915, 6027], [19903, 6035], [19924, 6052]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 362", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, 6], [5, 0], [-8, -21], [0, 16]], "o": [[0, -5], [-5, 0], [3, -5], [-13, 0], [7, 18], [0, 0]], "v": [[11200, 6040], [11189, 6030], [11185, 6020], [11181, 6010], [11176, 6034], [11200, 6040]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 363", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [3, -5], [-6, 0], [-3, 6]], "o": [[3, -5], [-5, 0], [-3, 6], [5, 0], [0, 0]], "v": [[12055, 6040], [12051, 6030], [12035, 6040], [12039, 6050], [12055, 6040]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 364", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-3, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-9, 0], [3, 6], [2, 0], [0, 0]], "v": [[19845, 6040], [19835, 6030], [19825, 6040], [19835, 6050], [19845, 6040]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 365", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[8085, 6030], [8081, 6020], [8070, 6030], [8074, 6040], [8085, 6030]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 366", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, -13], [-4, 11], [3, 3]], "o": [[-10, -10], [6, 11], [2, -7], [0, 0]], "v": [[11086, 6013], [11076, 6031], [11088, 6031], [11086, 6013]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 367", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[19795, 6020], [19791, 6010], [19780, 6020], [19784, 6030], [19795, 6020]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 368", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 14], [10, -4], [-22, 16], [14, 1], [10, -11], [-8, 0], [4, -15], [-10, 9], [-7, -3]], "o": [[9, 3], [0, -14], [-20, 8], [15, -12], [-9, 0], [-11, 13], [8, 0], [-6, 17], [7, -6], [0, 0]], "v": [[19796, 5997], [19810, 5981], [19795, 5966], [19798, 5944], [19799, 5930], [19765, 5950], [19759, 5970], [19765, 5993], [19770, 6003], [19796, 5997]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 369", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[8090, 5996], [8080, 5985], [8070, 5989], [8080, 6000], [8090, 5996]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 370", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [22, 0], [3, 6], [8, 0], [-7, -15], [3, -3], [-10, 0], [1, -7], [-15, 20]], "o": [[19, -25], [-11, 0], [-3, -5], [-11, 0], [6, 10], [-3, 3], [10, -1], [-4, 17], [0, 0]], "v": [[11096, 5973], [11091, 5940], [11065, 5930], [11044, 5920], [11039, 5939], [11044, 5963], [11057, 5967], [11073, 5978], [11096, 5973]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 371", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[11005, 5920], [11001, 5910], [10990, 5920], [10994, 5930], [11005, 5920]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 372", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -7], [-4, -9], [0, 31]], "o": [[0, -19], [-6, 6], [9, 24], [0, 0]], "v": [[19810, 5893], [19800, 5876], [19796, 5903], [19810, 5893]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 373", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -11], [-2, 0], [-3, 11]], "o": [[3, -11], [-5, 0], [0, 11], [2, 0], [0, 0]], "v": [[19863, 5900], [19859, 5880], [19850, 5900], [19854, 5920], [19863, 5900]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 374", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, -3], [-3, -5], [0, 18]], "o": [[0, -8], [-6, 4], [9, 14], [0, 0]], "v": [[11340, 5894], [11330, 5885], [11325, 5901], [11340, 5894]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 375", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, -13], [-4, 11], [3, 3]], "o": [[-10, -10], [6, 11], [2, -7], [0, 0]], "v": [[11656, 5873], [11646, 5891], [11658, 5891], [11656, 5873]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 376", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-51, -6], [-19, -3], [4, 11], [16, 1], [30, 6], [55, 0], [3, -14], [-85, 0], [-4, -12], [-3, 0], [3, 9]], "o": [[-6, -15], [32, 3], [26, 5], [-2, -8], [-16, -2], [-30, -6], [-86, 0], [-3, 15], [76, 0], [4, 8], [3, 0], [0, 0]], "v": [[4046, 5874], [4097, 5864], [4189, 5876], [4218, 5869], [4184, 5853], [4100, 5839], [3945, 5827], [3842, 5843], [3935, 5860], [4036, 5875], [4047, 5890], [4046, 5874]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 377", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [25, -7], [-3, -5], [-8, 13]], "o": [[10, -15], [-12, 3], [7, 12], [0, 0]], "v": [[9425, 5879], [9402, 5867], [9386, 5881], [9425, 5879]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 378", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -18], [-8, 0], [0, 8]], "o": [[0, -18], [-2, 7], [7, 0], [0, 0]], "v": [[11007, 5875], [10982, 5878], [10993, 5890], [11007, 5875]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 379", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -8], [-5, 0], [0, 5]], "o": [[0, -6], [-5, -3], [0, 9], [6, 0], [0, 0]], "v": [[11300, 5881], [11290, 5865], [11280, 5874], [11290, 5890], [11300, 5881]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 380", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -7], [-12, 0], [0, 5]], "o": [[0, -12], [-7, 7], [5, 0], [0, 0]], "v": [[4260, 5871], [4233, 5853], [4251, 5880], [4260, 5871]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 381", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[9645, 5870], [9641, 5860], [9630, 5870], [9634, 5880], [9645, 5870]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 382", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[11240, 5866], [11230, 5855], [11220, 5859], [11230, 5870], [11240, 5866]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 383", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -40], [-11, 0], [6, 23]], "o": [[-12, -53], [0, 35], [4, 0], [0, 0]], "v": [[19888, 5828], [19870, 5808], [19890, 5870], [19888, 5828]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 384", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [20, -1], [-22, -7], [0, 11]], "o": [[0, -5], [-34, 1], [36, 11], [0, 0]], "v": [[3820, 5830], [3783, 5821], [3770, 5830], [3820, 5830]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 385", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[3655, 5820], [3651, 5810], [3640, 5820], [3644, 5830], [3655, 5820]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 386", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-12, 0], [3, 6]], "o": [[-3, -5], [-8, 0], [0, 6], [11, 0], [0, 0]], "v": [[3735, 5820], [3714, 5810], [3700, 5820], [3721, 5830], [3735, 5820]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 387", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-29, -6], [-115, -4], [0, 14], [12, 0], [48, 6], [24, 10], [8, -7], [12, 6], [25, -4], [19, 4], [4, -2], [-80, -8], [-1, -9], [-10, 6], [3, 9]], "o": [[-5, -14], [19, 4], [310, 8], [0, -5], [-109, -1], [-67, -8], [-10, -5], [-10, 8], [-10, -5], [-25, 4], [-19, -4], [-22, 14], [57, 6], [2, 21], [5, -3], [0, 0]], "v": [[2843, 5793], [2871, 5784], [3115, 5797], [3440, 5791], [3418, 5782], [2995, 5763], [2907, 5746], [2876, 5750], [2843, 5752], [2780, 5749], [2700, 5749], [2658, 5746], [2738, 5776], [2826, 5798], [2840, 5815], [2843, 5793]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 388", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[3625, 5810], [3621, 5800], [3610, 5810], [3614, 5820], [3625, 5810]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 389", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -13], [-16, 0], [12, 8]], "o": [[-20, -13], [0, 6], [21, 0], [0, 0]], "v": [[3530, 5800], [3490, 5800], [3518, 5810], [3530, 5800]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 390", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[11185, 5800], [11181, 5790], [11170, 5800], [11174, 5810], [11185, 5800]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 391", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -8], [-5, 0], [0, 8]], "o": [[0, -8], [-5, 0], [0, 8], [6, 0], [0, 0]], "v": [[2630, 5755], [2620, 5740], [2610, 5755], [2620, 5770], [2630, 5755]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 392", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [10, 1], [-18, -13], [-9, 15]], "o": [[3, -5], [-124, -6], [20, 16], [0, 0]], "v": [[2415, 5730], [2403, 5719], [2288, 5726], [2415, 5730]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 393", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -8], [-2, 0], [-4, 8]], "o": [[3, -8], [-6, 0], [0, 8], [2, 0], [0, 0]], "v": [[19964, 5725], [19960, 5710], [19950, 5725], [19954, 5740], [19964, 5725]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 394", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [47, -2], [-40, 0], [-3, -8], [-16, 19]], "o": [[19, -23], [-42, 3], [11, 0], [8, 19], [0, 0]], "v": [[19620, 5625], [19575, 5591], [19561, 5610], [19586, 5625], [19620, 5625]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 395", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -17], [-5, 3], [0, 14]], "o": [[0, -14], [-5, 0], [0, 16], [5, -3], [0, 0]], "v": [[18848, 5525], [18839, 5500], [18830, 5531], [18839, 5556], [18848, 5525]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 396", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, -13], [-4, 11], [3, 3]], "o": [[-10, -10], [6, 11], [2, -7], [0, 0]], "v": [[19766, 5423], [19756, 5441], [19768, 5441], [19766, 5423]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 397", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[18855, 5380], [18851, 5370], [18840, 5380], [18844, 5390], [18855, 5380]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 398", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[19010, 5380], [19006, 5370], [18995, 5380], [18999, 5390], [19010, 5380]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 399", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, 14], [7, 0], [-9, 9], [0, 41], [-20, -60], [-6, 20], [6, -47], [-15, 25], [6, 0], [-9, 14], [4, 10], [-9, -5], [4, -15], [-10, 6], [5, -4], [-15, 0], [0, 10], [-6, 4], [9, 6], [-6, 4], [22, 15], [-6, 5], [0, -5], [-4, 0], [-1, 19], [5, 0], [0, 9], [10, -17], [5, 3], [0, -19], [10, 3], [0, -4], [27, -16], [-33, 12], [25, -35], [0, 0], [0, 0], [0, -7], [-5, 0], [0, 8]], "o": [[0, -8], [-5, 0], [0, -14], [-9, 0], [7, -7], [0, -73], [10, 34], [13, -39], [-5, 42], [3, -5], [-6, 0], [9, -13], [-5, -13], [8, 5], [-4, 18], [7, -4], [-7, 9], [4, 0], [0, -11], [8, -4], [-9, -6], [20, -14], [-13, -10], [6, -3], [0, 6], [5, 0], [1, -19], [-6, 0], [-1, -17], [-6, 9], [-14, -9], [0, 11], [-8, -4], [0, 5], [-46, 28], [21, -8], [0, 0], [0, 0], [18, 22], [0, 6], [6, 0], [0, 0]], "v": [[19210, 5365], [19200, 5350], [19190, 5325], [19178, 5300], [19178, 5288], [19190, 5206], [19217, 5188], [19235, 5203], [19254, 5225], [19275, 5260], [19270, 5250], [19275, 5226], [19284, 5185], [19289, 5174], [19295, 5207], [19302, 5224], [19307, 5225], [19342, 5270], [19350, 5251], [19361, 5224], [19359, 5209], [19355, 5193], [19349, 5117], [19339, 5095], [19350, 5099], [19358, 5110], [19368, 5075], [19360, 5040], [19350, 5023], [19339, 5023], [19320, 5035], [19270, 5072], [19255, 5084], [19240, 5086], [19190, 5125], [19154, 5167], [19142, 5247], [19122, 5275], [19156, 5315], [19190, 5368], [19200, 5380], [19210, 5365]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 400", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [20, -5], [0, 14], [3, -4], [-10, 6], [-4, -6], [-6, 23]], "o": [[4, -17], [-19, 5], [0, -9], [-9, 8], [5, -3], [9, 14], [0, 0]], "v": [[18983, 5350], [18964, 5336], [18940, 5324], [18934, 5313], [18950, 5355], [18965, 5360], [18983, 5350]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 401", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[19065, 5360], [19061, 5350], [19050, 5360], [19054, 5370], [19065, 5360]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 402", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[18900, 5350], [18896, 5340], [18885, 5350], [18889, 5360], [18900, 5350]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 403", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[19040, 5350], [19030, 5340], [19020, 5350], [19030, 5360], [19040, 5350]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 404", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, -7], [-21, 19], [7, 2]], "o": [[-7, -3], [-16, 25], [13, -12], [0, 0]], "v": [[18880, 5302], [18859, 5311], [18871, 5324], [18880, 5302]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 405", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -9], [-5, 3], [0, 6]], "o": [[0, -5], [-5, 0], [0, 8], [6, -3], [0, 0]], "v": [[18920, 5309], [18910, 5300], [18900, 5316], [18910, 5325], [18920, 5309]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 406", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 7], [8, 5], [0, -7], [19, -24], [-15, -4], [8, 21]], "o": [[-4, -8], [8, -10], [-5, -3], [0, 7], [-30, 36], [23, 5], [0, 0]], "v": [[19104, 5265], [19109, 5236], [19110, 5215], [19100, 5221], [19065, 5276], [19047, 5323], [19104, 5265]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 407", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-4, 15], [22, -19], [-4, 12], [6, 3], [-13, -15], [-14, 0], [7, 5]], "o": [[-8, -5], [8, -24], [-17, 15], [4, -9], [-14, -9], [6, 8], [16, 0], [0, 0]], "v": [[18990, 5300], [18985, 5269], [18971, 5264], [18955, 5268], [18951, 5246], [18939, 5294], [18976, 5308], [18990, 5300]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 408", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-12, 0], [9, 9], [7, -12], [-23, 6], [10, 6]], "o": [[-10, -7], [11, 0], [-10, -10], [-16, 25], [16, -4], [0, 0]], "v": [[18868, 5279], [18870, 5270], [18872, 5258], [18850, 5260], [18861, 5293], [18868, 5279]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 409", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [14, -17], [-6, 0], [-3, 6]], "o": [[11, -17], [-8, 9], [6, 0], [0, 0]], "v": [[19025, 5269], [19010, 5265], [19008, 5280], [19025, 5269]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 410", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, -14], [-6, 17], [2, 2]], "o": [[-8, -9], [6, 15], [4, -10], [0, 0]], "v": [[19234, 5241], [19217, 5267], [19230, 5264], [19234, 5241]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 411", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[19320, 5270], [19309, 5260], [19305, 5270], [19316, 5280], [19320, 5270]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 412", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[19207, 5218], [19201, 5224], [19206, 5237], [19207, 5218]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 413", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -12], [-13, 0], [0, 12]], "o": [[0, -19], [-13, 19], [6, 0], [0, 0]], "v": [[19150, 5198], [19140, 5190], [19140, 5220], [19150, 5198]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 414", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[18870, 5186], [18860, 5175], [18850, 5179], [18860, 5190], [18870, 5186]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 415", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -3], [0, -5], [-5, 3], [0, 5]], "o": [[0, -6], [-5, 3], [0, 6], [6, -3], [0, 0]], "v": [[18980, 5149], [18970, 5145], [18960, 5161], [18970, 5165], [18980, 5149]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 416", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-11, -4], [0, 15], [9, -5], [17, 0], [0, -5], [8, 0], [0, 8], [-7, 0], [10, 6], [9, -12], [-19, -15], [0, 16]], "o": [[0, -6], [21, 6], [0, -5], [-10, 4], [-18, 0], [0, 4], [-8, 0], [0, -8], [8, 0], [-11, -7], [-11, 15], [26, 20], [0, 0]], "v": [[14390, 5139], [14409, 5135], [14480, 5107], [14463, 5106], [14413, 5114], [14380, 5122], [14365, 5130], [14350, 5115], [14363, 5100], [14360, 5090], [14332, 5097], [14342, 5132], [14390, 5139]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 417", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [14, -17], [-6, 0], [-3, 6]], "o": [[11, -17], [-8, 9], [6, 0], [0, 0]], "v": [[18955, 5139], [18940, 5135], [18938, 5150], [18955, 5139]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 418", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 3], [4, 5], [4, -7], [8, 5], [-12, -14], [10, 16]], "o": [[-4, -5], [7, -2], [-4, -5], [-6, 8], [-12, -6], [14, 18], [0, 0]], "v": [[14616, 5131], [14622, 5117], [14627, 5104], [14611, 5109], [14589, 5114], [14589, 5123], [14616, 5131]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 419", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[14210, 5110], [14200, 5100], [14190, 5110], [14200, 5120], [14210, 5110]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 420", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -9], [-8, 0], [0, 2]], "o": [[0, -11], [-3, 5], [9, 0], [0, 0]], "v": [[14520, 5116], [14495, 5110], [14504, 5120], [14520, 5116]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 421", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [10, -9], [-8, -5], [0, 12]], "o": [[0, -13], [-3, 4], [20, 13], [0, 0]], "v": [[14010, 5100], [13972, 5084], [13980, 5100], [14010, 5100]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 422", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [18, 0], [0, -5], [-19, 13]], "o": [[12, -8], [-13, 0], [0, 13], [0, 0]], "v": [[14060, 5100], [14053, 5090], [14030, 5100], [14060, 5100]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 423", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[14430, 5106], [14420, 5095], [14410, 5099], [14420, 5110], [14430, 5106]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 424", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[14550, 5100], [14540, 5090], [14530, 5100], [14540, 5110], [14550, 5100]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 425", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[15050, 5100], [15039, 5090], [15035, 5100], [15046, 5110], [15050, 5100]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 426", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [14, -8], [-8, -5], [-7, 11]], "o": [[7, -11], [-6, 4], [18, 11], [0, 0]], "v": [[14274, 5091], [14238, 5076], [14241, 5091], [14274, 5091]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 427", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[14400, 5090], [14389, 5080], [14385, 5090], [14396, 5100], [14400, 5090]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 428", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[12050, 5086], [12040, 5075], [12030, 5079], [12040, 5090], [12050, 5086]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 429", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [7, 12], [6, -9], [0, -5], [-5, 8], [0, -9]], "o": [[0, 6], [6, 0], [-9, -14], [-4, 7], [0, 6], [6, -10], [0, 0]], "v": [[14930, 5078], [14940, 5090], [14938, 5070], [14918, 5063], [14910, 5085], [14920, 5080], [14930, 5078]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 430", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -19], [-9, 9], [9, 9]], "o": [[-9, -9], [0, 19], [9, -9], [0, 0]], "v": [[19412, 5048], [19400, 5060], [19412, 5072], [19412, 5048]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 431", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, -2], [-19, 0], [9, 3]], "o": [[-10, -2], [-10, 3], [19, 0], [0, 0]], "v": [[14128, 5063], [14093, 5063], [14110, 5068], [14128, 5063]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 432", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -2], [-14, 0], [6, 3]], "o": [[-7, -2], [-7, 3], [14, 0], [0, 0]], "v": [[14543, 5063], [14518, 5063], [14530, 5068], [14543, 5063]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 433", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[15020, 5060], [15010, 5050], [15000, 5060], [15010, 5070], [15020, 5060]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 434", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[14210, 5050], [14199, 5040], [14195, 5050], [14206, 5060], [14210, 5050]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 435", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[14240, 5050], [14236, 5040], [14225, 5050], [14229, 5060], [14240, 5050]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 436", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-7, -10], [0, 12], [9, 0], [-4, 6], [8, -3], [-2, -6], [11, 0], [0, -5], [-34, 12]], "o": [[21, -8], [6, 10], [0, -10], [-9, 0], [4, -7], [-8, 3], [1, 6], [-11, 0], [0, 12], [0, 0]], "v": [[14286, 5050], [14322, 5052], [14330, 5048], [14314, 5030], [14306, 5019], [14299, 5012], [14288, 5029], [14270, 5040], [14250, 5050], [14286, 5050]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 437", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, -1], [-18, -8], [0, 12]], "o": [[0, -5], [-25, 0], [27, 12], [0, 0]], "v": [[14850, 5050], [14823, 5041], [14815, 5050], [14850, 5050]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 438", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[14900, 5056], [14890, 5045], [14880, 5049], [14890, 5060], [14900, 5056]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 439", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1, -30], [-4, 4], [5, 14]], "o": [[-9, -23], [1, 18], [4, -4], [0, 0]], "v": [[14031, 4999], [14023, 5007], [14033, 5031], [14031, 4999]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 440", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, -1], [-19, -8], [-2, 0], [0, 6]], "o": [[0, -5], [-22, 0], [11, 5], [1, 1], [0, 0]], "v": [[14650, 5030], [14628, 5021], [14625, 5030], [14648, 5039], [14650, 5030]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 441", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, 3], [-23, -22], [8, 24]], "o": [[-4, -8], [-20, -5], [27, 26], [0, 0]], "v": [[13985, 5007], [13958, 4986], [13961, 5004], [13985, 5007]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 442", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-10, -15], [-6, 3], [7, 7]], "o": [[-13, -13], [3, 6], [6, -4], [0, 0]], "v": [[14261, 5007], [14245, 5019], [14262, 5024], [14261, 5007]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 443", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[14470, 5020], [14460, 5010], [14450, 5020], [14460, 5030], [14470, 5020]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 444", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[14560, 5026], [14550, 5015], [14540, 5019], [14550, 5030], [14560, 5026]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 445", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -12], [-13, 14], [0, 3]], "o": [[0, -3], [-9, 0], [0, 18], [8, -8], [0, 0]], "v": [[14720, 4986], [14705, 4980], [14690, 5001], [14705, 5006], [14720, 4986]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 446", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-5, 0], [-3, 6]], "o": [[3, -5], [-9, 0], [0, 6], [6, 0], [0, 0]], "v": [[19415, 5010], [19406, 5000], [19390, 5010], [19399, 5020], [19415, 5010]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 447", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[14440, 5000], [14430, 4990], [14420, 5000], [14430, 5010], [14440, 5000]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 448", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-8, 0], [0, 6], [8, 0], [0, 0]], "v": [[14850, 5000], [14835, 4990], [14820, 5000], [14835, 5010], [14850, 5000]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 449", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[13930, 4990], [13920, 4980], [13910, 4990], [13920, 5000], [13930, 4990]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 450", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, -9], [-45, -3], [3, 6]], "o": [[-8, -12], [-11, 10], [24, 1], [0, 0]], "v": [[11736, 4981], [11642, 4965], [11700, 4988], [11736, 4981]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 451", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 5], [9, -4], [15, 1], [0, -6], [-25, 10]], "o": [[9, -3], [0, -6], [-10, 4], [-16, -1], [0, 11], [0, 0]], "v": [[11824, 4984], [11840, 4968], [11823, 4965], [11778, 4971], [11750, 4980], [11824, 4984]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 452", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -3], [0, -2], [-8, 0], [0, 6]], "o": [[0, -5], [-8, 4], [0, 2], [8, 0], [0, 0]], "v": [[12080, 4980], [12065, 4976], [12050, 4986], [12065, 4990], [12080, 4980]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 453", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-9, 0], [3, 6]], "o": [[-3, -5], [-5, 0], [0, 6], [8, 0], [0, 0]], "v": [[10675, 4970], [10659, 4960], [10650, 4970], [10666, 4980], [10675, 4970]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 454", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[11625, 4970], [11621, 4960], [11610, 4970], [11614, 4980], [11625, 4970]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 455", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-9, 0], [3, 6]], "o": [[-3, -5], [-5, 0], [0, 6], [8, 0], [0, 0]], "v": [[11915, 4970], [11899, 4960], [11890, 4970], [11906, 4980], [11915, 4970]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 456", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, -13], [-4, 11], [3, 3]], "o": [[-10, -10], [6, 11], [2, -7], [0, 0]], "v": [[12196, 4953], [12186, 4971], [12198, 4971], [12196, 4953]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 457", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-13, -2], [-4, 7], [32, -3], [-3, 14], [17, 0], [-36, -7], [0, 7]], "o": [[0, -4], [13, 3], [5, -9], [-37, 2], [2, -13], [-40, 0], [34, 6], [0, 0]], "v": [[8200, 4960], [8224, 4957], [8255, 4950], [8219, 4942], [8178, 4928], [8157, 4910], [8127, 4962], [8200, 4960]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 458", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -9], [-23, 0], [2, 2]], "o": [[-10, -9], [0, 5], [23, 0], [0, 0]], "v": [[10629, 4966], [10550, 4961], [10592, 4970], [10629, 4966]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 459", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [25, 3], [-24, -9], [-15, 9]], "o": [[9, -6], [-37, -4], [20, 8], [0, 0]], "v": [[11569, 4961], [11546, 4948], [11512, 4963], [11569, 4961]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 460", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[12120, 4960], [12109, 4950], [12105, 4960], [12116, 4970], [12120, 4960]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 461", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [3, 6], [11, -10], [-29, 18]], "o": [[7, -5], [-5, -8], [-24, 24], [0, 0]], "v": [[8299, 4951], [8306, 4931], [8284, 4934], [8299, 4951]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 462", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[11450, 4950], [11446, 4940], [11435, 4950], [11439, 4960], [11450, 4950]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 463", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-10, 7], [33, 1], [0, -5], [-8, 0], [3, -9], [-6, 0], [1, 5]], "o": [[-2, -5], [17, -13], [-20, -1], [0, 5], [9, 0], [-4, 8], [6, 0], [0, 0]], "v": [[12068, 4951], [12083, 4928], [12066, 4913], [12030, 4921], [12045, 4930], [12054, 4945], [12059, 4960], [12068, 4951]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 464", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, 0], [-12, -8], [0, 13]], "o": [[0, -5], [-19, 0], [19, 13], [0, 0]], "v": [[7920, 4940], [7898, 4930], [7890, 4940], [7920, 4940]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 465", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[7960, 4940], [7949, 4930], [7945, 4940], [7956, 4950], [7960, 4940]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 466", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [30, -8], [-3, -5], [22, 0], [0, -5], [-32, 14]], "o": [[31, -15], [-11, 3], [3, 5], [-23, 0], [0, 14], [0, 0]], "v": [[8083, 4938], [8090, 4907], [8076, 4921], [8041, 4930], [8000, 4940], [8083, 4938]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 467", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[14430, 4940], [14419, 4930], [14415, 4940], [14426, 4950], [14430, 4940]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 468", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 5], [5, 0], [-10, -10], [-11, 0], [21, 4]], "o": [[-12, -2], [4, -5], [-12, 0], [3, 4], [19, -2], [0, 0]], "v": [[14472, 4943], [14455, 4929], [14451, 4920], [14447, 4943], [14474, 4949], [14472, 4943]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 469", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[12170, 4930], [12160, 4920], [12150, 4930], [12160, 4940], [12170, 4930]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 470", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [3, 4], [16, -1], [-21, -9], [-7, 3], [-5, 0]], "o": [[6, 0], [-2, -4], [-46, 4], [11, 5], [8, -2], [0, 0]], "v": [[7681, 4920], [7686, 4912], [7653, 4906], [7625, 4920], [7658, 4924], [7681, 4920]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 471", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[7740, 4920], [7730, 4910], [7720, 4920], [7730, 4930], [7740, 4920]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 472", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -9], [-5, 3], [0, 6]], "o": [[0, -5], [-5, 0], [0, 8], [6, -3], [0, 0]], "v": [[8260, 4909], [8250, 4900], [8240, 4916], [8250, 4925], [8260, 4909]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 473", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, -8], [-11, 0], [0, 2]], "o": [[0, -9], [-4, 6], [12, 0], [0, 0]], "v": [[10730, 4926], [10695, 4920], [10709, 4930], [10730, 4926]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 474", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-14, 0], [3, 6], [17, 0], [0, -14], [-10, -2], [-2, -1], [4, 8]], "o": [[-4, -10], [11, 0], [-3, -5], [-22, 0], [0, 11], [9, 3], [2, 0], [0, 0]], "v": [[11526, 4915], [11541, 4900], [11555, 4890], [11519, 4880], [11490, 4899], [11508, 4923], [11529, 4929], [11526, 4915]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 475", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, 0], [-3, -5], [-5, 0], [-3, 6]], "o": [[3, -5], [-12, 0], [3, 6], [5, 0], [0, 0]], "v": [[11615, 4920], [11600, 4910], [11585, 4920], [11600, 4930], [11615, 4920]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 476", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-5, 0], [-3, 6]], "o": [[3, -5], [-9, 0], [0, 6], [6, 0], [0, 0]], "v": [[12275, 4920], [12266, 4910], [12250, 4920], [12259, 4930], [12275, 4920]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 477", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[12310, 4920], [12306, 4910], [12295, 4920], [12299, 4930], [12310, 4920]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 478", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -12], [-18, 1], [17, 7]], "o": [[-28, -12], [0, 6], [27, 0], [0, 0]], "v": [[12375, 4920], [12330, 4920], [12363, 4929], [12375, 4920]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 479", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-3, -5], [-6, 0], [0, 6]], "o": [[0, -5], [-8, 0], [3, 6], [5, 0], [0, 0]], "v": [[14310, 4920], [14294, 4910], [14285, 4920], [14301, 4930], [14310, 4920]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 480", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[7590, 4910], [7580, 4900], [7570, 4910], [7580, 4920], [7590, 4910]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 481", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [16, -4], [0, -5], [-13, 16]], "o": [[10, -12], [-12, 3], [0, 14], [0, 0]], "v": [[7939, 4906], [7931, 4897], [7910, 4911], [7939, 4906]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 482", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -2], [-14, 0], [6, 3]], "o": [[-7, -2], [-7, 3], [14, 0], [0, 0]], "v": [[8023, 4913], [7998, 4913], [8010, 4918], [8023, 4913]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 483", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-13, -8], [0, 12]], "o": [[0, -5], [-15, 0], [19, 12], [0, 0]], "v": [[8220, 4910], [8203, 4900], [8200, 4910], [8220, 4910]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 484", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [13, 5], [0, -15], [-18, 12]], "o": [[11, -7], [-19, -7], [0, 11], [0, 0]], "v": [[10958, 4911], [10955, 4896], [10920, 4911], [10958, 4911]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 485", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, -4], [8, 21], [0, 0], [0, 0], [-2, -5], [-8, 13]], "o": [[5, -8], [-17, 4], [0, 0], [0, 0], [-7, 14], [7, 11], [0, 0]], "v": [[11896, 4908], [11881, 4903], [11849, 4882], [11839, 4856], [11826, 4880], [11817, 4913], [11896, 4908]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 486", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, 0], [-3, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-11, 0], [3, 6], [8, 0], [0, 0]], "v": [[11960, 4910], [11939, 4900], [11925, 4910], [11946, 4920], [11960, 4910]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 487", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-7, 0], [4, 3], [0, -10], [6, 0], [-3, 8], [14, -1], [-24, -26], [-12, 4], [3, -5], [-24, 2], [4, 11]], "o": [[-3, -8], [7, 0], [-10, -11], [0, 6], [-5, 0], [4, -10], [-26, 2], [13, 13], [10, -4], [-4, 5], [33, -3], [0, 0]], "v": [[7353, 4884], [7361, 4870], [7367, 4864], [7260, 4860], [7250, 4870], [7246, 4856], [7231, 4843], [7228, 4881], [7265, 4894], [7278, 4896], [7315, 4902], [7353, 4884]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 488", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-39, -1], [0, 15], [10, 6], [19, -15], [2, 3], [40, 2], [4, 3], [-33, -10], [-4, 10], [45, 1], [15, 4], [0, -6], [-5, 0], [0, -5], [12, 0], [-38, -2], [9, -3], [-37, -1], [0, 6]], "o": [[0, -7], [96, 3], [0, -7], [-14, -9], [-13, 11], [-2, -2], [-41, -3], [-10, -10], [14, 4], [4, -11], [-31, 0], [-17, -3], [0, 6], [6, 0], [0, 6], [-35, 0], [17, 1], [-30, 8], [19, 1], [0, 0]], "v": [[7500, 4899], [7563, 4890], [7677, 4876], [7659, 4852], [7617, 4860], [7589, 4875], [7512, 4867], [7432, 4856], [7536, 4855], [7563, 4847], [7512, 4831], [7428, 4824], [7400, 4829], [7410, 4840], [7420, 4850], [7398, 4860], [7435, 4881], [7448, 4887], [7465, 4908], [7500, 4899]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 489", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[7557, 4904], [7538, 4903], [7544, 4909], [7557, 4904]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 490", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-8, 0], [0, 6], [8, 0], [0, 0]], "v": [[7860, 4900], [7845, 4890], [7830, 4900], [7845, 4910], [7860, 4900]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 491", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[10335, 4900], [10331, 4890], [10320, 4900], [10324, 4910], [10335, 4900]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 492", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -9], [-5, 3], [0, 6]], "o": [[0, -5], [-5, 0], [0, 8], [6, -3], [0, 0]], "v": [[10370, 4889], [10360, 4880], [10350, 4896], [10360, 4905], [10370, 4889]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 493", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [3, -5], [-6, 0], [-3, 6]], "o": [[3, -5], [-5, 0], [-3, 6], [5, 0], [0, 0]], "v": [[10875, 4900], [10871, 4890], [10855, 4900], [10859, 4910], [10875, 4900]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 494", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, 5], [0, -9], [26, 3], [0, -6], [-10, 20]], "o": [[8, -14], [-9, -3], [0, 14], [-7, -1], [0, 19], [0, 0]], "v": [[11809, 4891], [11805, 4866], [11790, 4875], [11753, 4891], [11740, 4900], [11809, 4891]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 495", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[7760, 4896], [7750, 4885], [7740, 4889], [7750, 4900], [7760, 4896]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 496", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -2], [-14, 0], [6, 3]], "o": [[-7, -2], [-7, 3], [14, 0], [0, 0]], "v": [[7803, 4893], [7778, 4893], [7790, 4898], [7803, 4893]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 497", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [10, 4], [-13, -15], [0, 15]], "o": [[0, -5], [-2, 0], [-16, -6], [17, 19], [0, 0]], "v": [[7900, 4890], [7896, 4880], [7874, 4873], [7870, 4883], [7900, 4890]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 498", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-8, 0], [0, 6], [8, 0], [0, 0]], "v": [[7990, 4890], [7975, 4880], [7960, 4890], [7975, 4900], [7990, 4890]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 499", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 4], [0, -5], [-8, 0], [0, 2]], "o": [[0, -2], [-8, -3], [0, 6], [8, 0], [0, 0]], "v": [[8040, 4896], [8025, 4886], [8010, 4890], [8025, 4900], [8040, 4896]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 500", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-8, 0], [0, 6], [8, 0], [0, 0]], "v": [[8150, 4890], [8135, 4880], [8120, 4890], [8135, 4900], [8150, 4890]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 501", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -9], [-8, 0], [0, 2]], "o": [[0, -11], [-3, 5], [9, 0], [0, 0]], "v": [[10290, 4896], [10265, 4890], [10274, 4900], [10290, 4896]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 502", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [-3, -5], [-8, 0], [3, 6]], "o": [[-3, -5], [-8, 0], [3, 6], [8, 0], [0, 0]], "v": [[10525, 4890], [10504, 4880], [10495, 4890], [10516, 4900], [10525, 4890]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 503", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [18, 0], [-20, -14], [0, 20]], "o": [[0, -15], [-25, 0], [19, 14], [0, 0]], "v": [[10610, 4880], [10585, 4860], [10575, 4892], [10610, 4880]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 504", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, 0], [0, -5], [-14, 0], [4, 6]], "o": [[-3, -5], [-10, 0], [0, 6], [14, 0], [0, 0]], "v": [[10675, 4890], [10649, 4880], [10630, 4890], [10656, 4900], [10675, 4890]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 505", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -3], [-14, 0], [-3, 6]], "o": [[3, -6], [-18, 7], [6, 0], [0, 0]], "v": [[11435, 4890], [11426, 4886], [11419, 4900], [11435, 4890]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 506", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 7], [-5, -14], [0, 11]], "o": [[0, -2], [-13, -11], [5, 14], [0, 0]], "v": [[11690, 4896], [11675, 4880], [11666, 4884], [11690, 4896]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 507", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -9], [-8, 0], [0, 2]], "o": [[0, -11], [-3, 5], [9, 0], [0, 0]], "v": [[6850, 4886], [6825, 4880], [6834, 4890], [6850, 4886]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 508", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 5], [9, -3], [-3, 5], [66, -1], [0, 5], [47, 0], [10, 8], [22, -9], [20, 8], [-8, -13], [8, 3], [7, -8], [-28, 0], [-10, -5], [-4, 12], [-21, -2], [4, -10], [-9, 0], [-2, 5], [-16, 4], [11, -8], [-71, -1], [-4, -5], [-21, 8]], "o": [[9, -3], [0, -6], [-8, 3], [8, -12], [-32, 1], [0, -4], [-54, 1], [-12, -10], [-20, 9], [-34, -13], [3, 5], [-8, -3], [-10, 12], [19, 0], [22, 12], [2, -5], [29, 3], [-3, 8], [8, 0], [2, -5], [23, -5], [-18, 13], [31, 1], [9, 9], [0, 0]], "v": [[7184, 4884], [7200, 4868], [7184, 4864], [7174, 4861], [7088, 4845], [7030, 4839], [6945, 4832], [6845, 4820], [6802, 4819], [6746, 4820], [6695, 4820], [6687, 4824], [6661, 4834], [6683, 4848], [6736, 4858], [6857, 4853], [6899, 4848], [6933, 4866], [6943, 4880], [6962, 4871], [6995, 4855], [7010, 4859], [7062, 4873], [7126, 4883], [7184, 4884]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 509", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-10, -15], [-6, 3], [7, 7]], "o": [[-13, -13], [3, 6], [6, -4], [0, 0]], "v": [[8081, 4867], [8065, 4879], [8082, 4884], [8081, 4867]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 510", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[12140, 4880], [12129, 4870], [12125, 4880], [12136, 4890], [12140, 4880]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 511", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[11265, 4870], [11261, 4860], [11250, 4870], [11254, 4880], [11265, 4870]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 512", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, 9], [13, 0], [1, -12], [-9, 0], [0, 2]], "o": [[0, -2], [4, -11], [-12, 0], [-1, 11], [8, 0], [0, 0]], "v": [[11350, 4876], [11356, 4856], [11343, 4840], [11323, 4860], [11336, 4880], [11350, 4876]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 513", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, -3], [-8, -8], [12, 23]], "o": [[-6, -11], [-6, 4], [18, 21], [0, 0]], "v": [[11400, 4860], [11380, 4845], [11383, 4865], [11400, 4860]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 514", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 5], [8, -2], [3, 8], [10, -1], [-17, -7], [9, -6], [-11, -7], [-26, 10]], "o": [[14, -5], [-6, -4], [-7, 3], [-3, -8], [-16, 0], [15, 6], [-11, 6], [16, 10], [0, 0]], "v": [[11614, 4871], [11621, 4856], [11595, 4853], [11576, 4844], [11553, 4831], [11554, 4839], [11561, 4855], [11561, 4871], [11614, 4871]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 515", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, 0], [-23, -10], [24, 19]], "o": [[-10, -8], [-16, 1], [40, 19], [0, 0]], "v": [[11904, 4865], [11865, 4851], [11875, 4865], [11904, 4865]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 516", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-3, -5], [-6, 0], [0, 6]], "o": [[0, -5], [-8, 0], [3, 6], [5, 0], [0, 0]], "v": [[12220, 4870], [12204, 4860], [12195, 4870], [12211, 4880], [12220, 4870]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 517", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[10120, 4860], [10109, 4850], [10105, 4860], [10116, 4870], [10120, 4860]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 518", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, 3], [-4, -10], [0, 14]], "o": [[0, -5], [-10, -4], [6, 15], [0, 0]], "v": [[10170, 4861], [10156, 4847], [10147, 4856], [10170, 4861]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 519", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[11110, 4860], [11099, 4850], [11095, 4860], [11106, 4870], [11110, 4860]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 520", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[11490, 4860], [11479, 4850], [11475, 4860], [11486, 4870], [11490, 4860]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 521", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[14935, 4860], [14931, 4850], [14920, 4860], [14924, 4870], [14935, 4860]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 522", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-10, -6], [8, 12], [-11, 4], [0, 5], [5, -13], [5, 0], [-11, 17], [6, 4], [3, -16], [20, -4], [6, 8], [0, -10], [15, -2], [7, 8], [3, -6], [17, 14], [0, 0], [0, 0], [-4, 6], [28, 0], [9, 3], [0, -10], [31, 9], [-24, -4], [0, 4], [30, 1], [0, -7], [-18, 4], [8, -5], [-2, -7], [6, -2], [0, 5], [11, 13], [29, 0], [-43, 2], [18, 4], [12, -6], [-10, -12], [2, -2], [17, 21], [-21, -4], [-5, 17], [3, 3], [0, -9], [15, 20], [-8, -21], [6, 0], [4, 11], [11, -8], [-12, -5], [11, -6], [11, 7], [31, -2], [0, 5], [6, -3], [-1, -7], [12, -1], [-9, 12], [17, -17], [-46, 0], [0, -5], [-41, 0], [7, -7], [21, 2], [-8, -8], [-42, -1], [80, 0], [0, -19], [-30, 0], [0, 5], [-18, -2], [23, -9], [-34, -3], [-7, 4], [13, 1], [4, -1], [-19, 10], [4, -13], [-22, 0], [-5, -5], [-18, 7], [24, -15], [-5, 0], [12, 0], [-12, 5], [4, 9], [-35, -23], [-25, 16], [-16, -15], [25, 0], [-4, -10], [-12, 13], [-12, -7], [-17, 1], [-7, -4], [0, 10]], "o": [[0, -5], [24, 13], [-3, -5], [11, -3], [0, -12], [-2, 5], [-9, 0], [4, -6], [-6, -4], [-5, 22], [-15, 4], [-8, -10], [0, 1], [-15, 2], [-8, -9], [-4, 6], [0, 0], [0, 0], [20, 3], [4, -6], [-24, 0], [-10, -4], [0, 19], [-21, -6], [4, 0], [0, -5], [-35, -1], [0, 7], [16, -4], [-7, 5], [2, 7], [-7, 3], [0, -5], [-15, -18], [-45, 0], [22, -1], [-12, -2], [-17, 9], [6, 8], [-5, 5], [-11, -14], [18, 4], [4, -12], [-3, -4], [0, 21], [-15, -21], [3, 8], [-5, 0], [-5, -15], [-12, 9], [11, 4], [-7, 5], [-13, -8], [-29, 1], [0, -5], [-7, 2], [2, 9], [-17, 2], [15, -17], [-21, 21], [25, 0], [0, 5], [48, -1], [-5, 5], [-27, -2], [5, 6], [87, 3], [-47, 0], [0, 3], [30, 0], [0, -5], [44, 4], [-16, 7], [24, 3], [8, -5], [-11, -1], [-16, 2], [16, -9], [-5, 11], [16, 0], [7, 7], [29, -11], [-8, 5], [14, 0], [-5, -1], [14, -6], [-7, -19], [25, 16], [24, -16], [15, 16], [-18, 0], [5, 14], [4, -4], [11, 8], [17, -2], [15, 10], [0, 0]], "v": [[6550, 4848], [6569, 4850], [6615, 4851], [6630, 4835], [6650, 4819], [6602, 4831], [6590, 4840], [6593, 4773], [6590, 4755], [6574, 4778], [6541, 4813], [6505, 4805], [6425, 4800], [6398, 4805], [6359, 4794], [6341, 4789], [6305, 4776], [6275, 4752], [6312, 4756], [6355, 4750], [6316, 4740], [6256, 4734], [6240, 4743], [6190, 4759], [6203, 4749], [6210, 4742], [6155, 4731], [6100, 4741], [6128, 4746], [6142, 4748], [6132, 4770], [6123, 4787], [6110, 4782], [6090, 4750], [6030, 4726], [6018, 4709], [6023, 4703], [5980, 4710], [5971, 4736], [5979, 4754], [5932, 4719], [5944, 4707], [5975, 4690], [5976, 4663], [5970, 4673], [5936, 4675], [5834, 4666], [5829, 4680], [5812, 4660], [5790, 4651], [5789, 4667], [5789, 4680], [5756, 4675], [5683, 4664], [5630, 4657], [5618, 4653], [5607, 4671], [5589, 4687], [5580, 4674], [5562, 4672], [5595, 4700], [5640, 4709], [5715, 4717], [5779, 4725], [5732, 4731], [5706, 4739], [5793, 4753], [5831, 4770], [5750, 4804], [5805, 4810], [5860, 4801], [5893, 4796], [5915, 4809], [5938, 4821], [5993, 4818], [5985, 4809], [5958, 4809], [5970, 4780], [5984, 4785], [6007, 4800], [6046, 4810], [6083, 4811], [6109, 4831], [6105, 4840], [6135, 4820], [6147, 4810], [6164, 4786], [6212, 4794], [6265, 4794], [6308, 4793], [6297, 4810], [6276, 4824], [6345, 4828], [6374, 4834], [6426, 4845], [6469, 4849], [6550, 4848]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 523", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [14, 0], [0, -5], [-14, 0], [0, 6]], "o": [[0, -5], [-14, 0], [0, 6], [14, 0], [0, 0]], "v": [[10030, 4850], [10005, 4840], [9980, 4850], [10005, 4860], [10030, 4850]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 524", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[10797, 4854], [10778, 4853], [10784, 4859], [10797, 4854]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 525", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[10970, 4850], [10959, 4840], [10955, 4850], [10966, 4860], [10970, 4850]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 526", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-8, -9], [11, 17]], "o": [[-3, -6], [-6, 0], [14, 17], [0, 0]], "v": [[11235, 4841], [11218, 4830], [11220, 4845], [11235, 4841]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 527", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [3, -3], [-7, 0], [3, -5], [-13, 0], [0, 0], [0, 0]], "o": [[-13, -9], [-4, 3], [7, 0], [-3, 6], [0, 0], [0, 0], [0, 0]], "v": [[11762, 4844], [11733, 4834], [11739, 4840], [11745, 4850], [11762, 4860], [11784, 4860], [11762, 4844]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 528", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-9, 0], [3, 6]], "o": [[-3, -5], [-5, 0], [0, 6], [8, 0], [0, 0]], "v": [[12255, 4850], [12239, 4840], [12230, 4850], [12246, 4860], [12255, 4850]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 529", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, 0], [-3, -5], [-5, 0], [-3, 6]], "o": [[3, -5], [-12, 0], [3, 6], [5, 0], [0, 0]], "v": [[6205, 4840], [6190, 4830], [6175, 4840], [6190, 4850], [6205, 4840]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 530", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[10240, 4840], [10236, 4830], [10225, 4840], [10229, 4850], [10240, 4840]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 531", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [19, 0], [0, -5], [-19, 0], [0, 6]], "o": [[0, -5], [-19, 0], [0, 6], [19, 0], [0, 0]], "v": [[10510, 4840], [10475, 4830], [10440, 4840], [10475, 4850], [10510, 4840]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 532", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -2], [-14, 0], [6, 3]], "o": [[-7, -2], [-7, 3], [14, 0], [0, 0]], "v": [[10933, 4843], [10908, 4843], [10920, 4848], [10933, 4843]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 533", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, -5], [-5, 9], [7, -5], [-25, -5], [-2, -1], [0, 9]], "o": [[0, -11], [-16, 5], [5, -9], [-19, 11], [12, 2], [1, 0], [0, 0]], "v": [[11840, 4834], [11819, 4825], [11805, 4820], [11802, 4814], [11813, 4843], [11838, 4849], [11840, 4834]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 534", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [46, 0], [0, -7], [-38, -1], [0, 5]], "o": [[0, -10], [-62, 0], [0, 4], [40, 1], [0, 0]], "v": [[12180, 4840], [12125, 4828], [12040, 4838], [12110, 4847], [12180, 4840]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 535", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[7340, 4836], [7330, 4825], [7320, 4829], [7330, 4840], [7340, 4836]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 536", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [14, -12], [2, -1], [-12, 0], [0, 10]], "o": [[0, -14], [-10, 7], [-2, 2], [14, 0], [0, 0]], "v": [[7390, 4824], [7373, 4821], [7351, 4837], [7368, 4840], [7390, 4824]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 537", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [22, -8], [-24, 6], [4, -12], [-7, 2], [-2, 8]], "o": [[3, -18], [-23, 9], [15, -4], [-4, 9], [7, -3], [0, 0]], "v": [[10198, 4818], [10155, 4796], [10161, 4817], [10176, 4827], [10182, 4838], [10198, 4818]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 538", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[10380, 4836], [10370, 4825], [10360, 4829], [10370, 4840], [10380, 4836]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 539", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[11460, 4830], [11450, 4820], [11440, 4830], [11450, 4840], [11460, 4830]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 540", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -12], [-11, 0], [0, 12]], "o": [[2, -18], [-12, 18], [4, 0], [0, 0]], "v": [[11777, 4818], [11770, 4810], [11768, 4840], [11777, 4818]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 541", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-7, -9], [-12, 4], [52, -1], [-15, -14], [-5, 13]], "o": [[4, -10], [5, 7], [38, -10], [-48, 1], [10, 11], [0, 0]], "v": [[11883, 4828], [11897, 4827], [11928, 4833], [11900, 4814], [11857, 4833], [11883, 4828]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 542", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[12940, 4830], [12936, 4820], [12925, 4830], [12929, 4840], [12940, 4830]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 543", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [79, 4], [-66, -7], [0, 10]], "o": [[0, -10], [-71, -4], [86, 9], [0, 0]], "v": [[7130, 4819], [7050, 4805], [7025, 4820], [7130, 4819]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 544", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -11], [-22, 0], [3, 5]], "o": [[-6, -11], [-2, 4], [21, 1], [0, 0]], "v": [[7224, 4819], [7156, 4818], [7191, 4826], [7224, 4819]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 545", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, -3], [-31, 0], [3, 5]], "o": [[-3, -5], [-28, 7], [14, 0], [0, 0]], "v": [[7746, 4821], [7720, 4817], [7726, 4830], [7746, 4821]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 546", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-9, 0], [3, 6]], "o": [[-3, -5], [-5, 0], [0, 6], [8, 0], [0, 0]], "v": [[9085, 4820], [9069, 4810], [9060, 4820], [9076, 4830], [9085, 4820]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 547", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [13, -8], [-12, -5], [0, 10]], "o": [[0, -12], [-7, 4], [26, 10], [0, 0]], "v": [[10010, 4821], [9967, 4807], [9976, 4821], [10010, 4821]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 548", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, 3], [-13, -13], [0, 10]], "o": [[0, -5], [-20, -5], [9, 10], [0, 0]], "v": [[11370, 4821], [11350, 4807], [11337, 4823], [11370, 4821]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 549", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [-3, -5], [-5, 0], [3, 6]], "o": [[-3, -5], [-6, 0], [3, 6], [6, 0], [0, 0]], "v": [[6935, 4810], [6919, 4800], [6915, 4810], [6931, 4820], [6935, 4810]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 550", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 2], [-4, -11], [10, 0], [-3, -6], [3, 14]], "o": [[-1, -5], [-12, -2], [4, 10], [-8, 0], [8, 12], [0, 0]], "v": [[7573, 4784], [7554, 4771], [7543, 4784], [7534, 4800], [7525, 4811], [7573, 4784]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 551", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-9, 0], [3, 6]], "o": [[-3, -5], [-5, 0], [0, 6], [8, 0], [0, 0]], "v": [[8005, 4810], [7989, 4800], [7980, 4810], [7996, 4820], [8005, 4810]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 552", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-8, -9], [11, 17]], "o": [[-3, -6], [-6, 0], [14, 17], [0, 0]], "v": [[9935, 4801], [9918, 4790], [9920, 4805], [9935, 4801]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 553", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[10130, 4816], [10120, 4805], [10110, 4809], [10120, 4820], [10130, 4816]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 554", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [47, 0], [0, -6], [-25, 1], [-4, -3], [0, 16]], "o": [[0, -12], [-34, 0], [0, 6], [25, 0], [11, 12], [0, 0]], "v": [[11710, 4805], [11650, 4790], [11590, 4800], [11635, 4808], [11687, 4813], [11710, 4805]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 555", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [3, -5], [-6, 0], [-3, 6]], "o": [[3, -5], [-5, 0], [-3, 6], [5, 0], [0, 0]], "v": [[11745, 4810], [11741, 4800], [11725, 4810], [11729, 4820], [11745, 4810]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 556", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [29, -1], [-18, -5], [0, 11]], "o": [[0, -6], [-39, 1], [41, 11], [0, 0]], "v": [[6900, 4800], [6848, 4791], [6820, 4799], [6900, 4800]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 557", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[9377, 4804], [9358, 4803], [9364, 4809], [9377, 4804]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 558", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-3, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-9, 0], [3, 6], [2, 0], [0, 0]], "v": [[9865, 4800], [9855, 4790], [9845, 4800], [9855, 4810], [9865, 4800]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 559", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-9, -6], [8, 12]], "o": [[-3, -5], [-7, 0], [19, 12], [0, 0]], "v": [[10035, 4800], [10017, 4790], [10020, 4800], [10035, 4800]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 560", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-8, 0], [0, 6], [8, 0], [0, 0]], "v": [[10550, 4800], [10535, 4790], [10520, 4800], [10535, 4810], [10550, 4800]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 561", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[11167, 4804], [11148, 4803], [11154, 4809], [11167, 4804]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 562", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[12390, 4800], [12379, 4790], [12375, 4800], [12386, 4810], [12390, 4800]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 563", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[5357, 4794], [5338, 4793], [5344, 4799], [5357, 4794]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 564", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3, -7], [1, 11], [64, 5], [12, 6], [0, -26], [-30, 1], [-10, 5], [0, -7], [-22, 5], [-1, -10], [-4, 9], [-7, 0]], "o": [[7, 0], [3, 6], [-1, -19], [-40, -3], [-25, -14], [0, 14], [20, 0], [11, -6], [0, 7], [26, -5], [0, 11], [2, -7], [0, 0]], "v": [[5605, 4780], [5623, 4793], [5627, 4784], [5554, 4757], [5461, 4741], [5400, 4772], [5438, 4788], [5493, 4778], [5510, 4780], [5545, 4784], [5581, 4791], [5587, 4793], [5605, 4780]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 565", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 0], [-2, -6], [-13, 13], [2, 1], [28, 3], [0, -8], [-8, 3], [-3, -5], [9, -2], [-17, -1], [4, 6]], "o": [[-3, -5], [5, 0], [3, 9], [10, -9], [-2, 0], [-37, -3], [0, 7], [8, -4], [3, 5], [-11, 3], [17, 1], [0, 0]], "v": [[5695, 4790], [5699, 4780], [5712, 4792], [5734, 4787], [5748, 4769], [5693, 4763], [5640, 4770], [5655, 4777], [5675, 4780], [5663, 4793], [5673, 4799], [5695, 4790]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 566", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[7407, 4794], [7388, 4793], [7394, 4799], [7407, 4794]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 567", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [19, -13], [-19, 0], [0, 6]], "o": [[0, -13], [-12, 8], [12, 0], [0, 0]], "v": [[7740, 4790], [7710, 4790], [7718, 4800], [7740, 4790]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 568", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, 0], [0, -5], [-14, 0], [3, 5]], "o": [[-3, -5], [-10, 0], [0, 5], [13, 0], [0, 0]], "v": [[10494, 4789], [10469, 4780], [10450, 4789], [10475, 4798], [10494, 4789]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 569", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[7485, 4780], [7481, 4770], [7470, 4780], [7474, 4790], [7485, 4780]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 570", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -3], [0, 8], [3, -4], [-17, 0], [0, 6]], "o": [[0, -5], [-8, 4], [0, -7], [-9, 9], [8, 0], [0, 0]], "v": [[7930, 4780], [7915, 4776], [7900, 4769], [7894, 4763], [7916, 4790], [7930, 4780]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 571", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, -8], [-11, 6], [2, 2]], "o": [[-8, -8], [3, 5], [12, -6], [0, 0]], "v": [[9658, 4765], [9616, 4781], [9641, 4779], [9658, 4765]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 572", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, 0], [-12, -8], [0, 13]], "o": [[0, -5], [-19, 0], [19, 13], [0, 0]], "v": [[9740, 4780], [9718, 4770], [9710, 4780], [9740, 4780]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 573", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 7], [18, -18], [0, 0], [0, 0], [0, 5]], "o": [[0, -5], [-13, -11], [0, 0], [0, 0], [20, 0], [0, 0]], "v": [[5380, 4771], [5364, 4750], [5327, 4758], [5306, 4780], [5343, 4780], [5380, 4771]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 574", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[7970, 4770], [7966, 4760], [7955, 4770], [7959, 4780], [7970, 4770]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 575", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-24, -26], [-8, 3], [15, 11]], "o": [[-31, -22], [7, 7], [11, -4], [0, 0]], "v": [[9114, 4753], [9092, 4764], [9119, 4772], [9114, 4753]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 576", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-3, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-9, 0], [3, 6], [2, 0], [0, 0]], "v": [[9435, 4770], [9425, 4760], [9415, 4770], [9425, 4780], [9435, 4770]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 577", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -3], [0, -6], [-5, 0], [0, 9]], "o": [[0, -8], [-5, 3], [0, 5], [6, 0], [0, 0]], "v": [[9500, 4764], [9490, 4755], [9480, 4771], [9490, 4780], [9500, 4764]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 578", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [23, -1], [-20, -6], [0, 10]], "o": [[0, -4], [-34, 1], [32, 9], [0, 0]], "v": [[9970, 4768], [9928, 4761], [9910, 4769], [9970, 4768]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 579", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [17, 4], [3, -2], [-7, 0], [3, -5], [-18, 12]], "o": [[10, -6], [-13, -4], [-2, 3], [7, 0], [-8, 12], [0, 0]], "v": [[4818, 4761], [4809, 4747], [4781, 4745], [4789, 4750], [4795, 4760], [4818, 4761]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 580", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, 3], [0, -17], [-35, 3], [16, 11]], "o": [[-10, -6], [-17, -4], [0, 19], [35, -4], [0, 0]], "v": [[4913, 4745], [4874, 4728], [4853, 4744], [4892, 4762], [4913, 4745]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 581", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [22, -5], [15, 8], [2, -28], [-52, 13]], "o": [[22, -6], [-6, 1], [-26, -14], [-2, 31], [0, 0]], "v": [[5288, 4764], [5291, 4738], [5252, 4726], [5223, 4741], [5288, 4764]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 582", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, -15], [14, 4], [0, -6], [-16, 11], [0, 9]], "o": [[-1, -14], [-8, 11], [-12, -3], [0, 14], [6, -5], [0, 0]], "v": [[6440, 4731], [6426, 4732], [6392, 4743], [6370, 4748], [6428, 4757], [6440, 4731]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 583", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, -1], [-18, -8], [0, 12]], "o": [[0, -5], [-25, 0], [27, 12], [0, 0]], "v": [[6530, 4760], [6503, 4751], [6495, 4760], [6530, 4760]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 584", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[7230, 4766], [7220, 4755], [7210, 4759], [7220, 4770], [7230, 4766]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 585", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 0], [1, 10], [10, -8], [-15, 0], [3, 6]], "o": [[-3, -5], [5, 0], [-1, -15], [-16, 13], [5, 0], [0, 0]], "v": [[7305, 4760], [7309, 4750], [7317, 4731], [7303, 4722], [7301, 4770], [7305, 4760]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 586", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, 0], [-3, 4], [-11, -10], [-5, 24], [28, 7], [0, -9], [9, 1], [-1, -7], [8, 3], [0, 5], [9, -7], [11, 9], [26, -1], [-21, -18], [0, 16]], "o": [[0, -5], [-6, 0], [4, -3], [25, 23], [2, -11], [-31, -9], [0, 7], [-10, 0], [2, 7], [-8, -4], [0, -6], [-11, 9], [-7, -6], [-46, 3], [23, 19], [0, 0]], "v": [[5000, 4750], [4988, 4740], [4983, 4733], [5009, 4744], [5208, 4740], [5170, 4714], [5130, 4715], [5113, 4726], [5097, 4737], [5085, 4744], [5070, 4728], [5055, 4730], [5026, 4731], [4966, 4722], [4941, 4743], [5000, 4750]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 587", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, -1], [0, 0], [0, 0], [-2, -1], [0, 6]], "o": [[0, -5], [0, 0], [0, 0], [14, 4], [1, 1], [0, 0]], "v": [[9250, 4750], [9223, 4741], [9195, 4742], [9220, 4750], [9248, 4759], [9250, 4750]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 588", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[9470, 4750], [9459, 4740], [9455, 4750], [9466, 4760], [9470, 4750]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 589", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[10280, 4750], [10269, 4740], [10265, 4750], [10276, 4760], [10280, 4750]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 590", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-3, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-9, 0], [3, 6], [2, 0], [0, 0]], "v": [[5665, 4740], [5655, 4730], [5645, 4740], [5655, 4750], [5665, 4740]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 591", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, 1], [-3, -6], [0, 11]], "o": [[0, -4], [-11, -1], [7, 11], [0, 0]], "v": [[7440, 4742], [7420, 4733], [7405, 4741], [7440, 4742]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 592", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-3, -5], [-6, 0], [0, 6]], "o": [[0, -5], [-8, 0], [3, 6], [5, 0], [0, 0]], "v": [[10150, 4740], [10134, 4730], [10125, 4740], [10141, 4750], [10150, 4740]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 593", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-8, 0], [0, 6], [8, 0], [0, 0]], "v": [[10360, 4740], [10345, 4730], [10330, 4740], [10345, 4750], [10360, 4740]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 594", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -9], [-8, 0], [0, 2]], "o": [[0, -11], [-3, 5], [9, 0], [0, 0]], "v": [[10420, 4746], [10395, 4740], [10404, 4750], [10420, 4746]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 595", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[11080, 4740], [11069, 4730], [11065, 4740], [11076, 4750], [11080, 4740]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 596", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[8860, 4730], [8850, 4720], [8840, 4730], [8850, 4740], [8860, 4730]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 597", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [26, 0], [-14, -11], [0, 14]], "o": [[0, -9], [-11, 1], [22, 16], [0, 0]], "v": [[9160, 4731], [9100, 4710], [9104, 4725], [9160, 4731]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 598", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -12], [-24, 1], [15, 7]], "o": [[-28, -12], [0, 6], [32, 0], [0, 0]], "v": [[9325, 4730], [9260, 4730], [9303, 4739], [9325, 4730]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 599", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [20, -13], [-22, 0], [0, 6]], "o": [[0, -13], [-12, 8], [15, 0], [0, 0]], "v": [[10090, 4730], [10050, 4730], [10063, 4740], [10090, 4730]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 600", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, -4], [-4, -5], [-5, 4], [4, 5]], "o": [[-3, -5], [-5, 3], [3, 5], [5, -3], [0, 0]], "v": [[8604, 4709], [8589, 4706], [8586, 4721], [8601, 4724], [8604, 4709]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 601", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -6], [-3, -6], [0, 12]], "o": [[0, -7], [-1, 1], [8, 12], [0, 0]], "v": [[8720, 4719], [8641, 4706], [8644, 4719], [8720, 4719]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 602", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[8750, 4720], [8739, 4710], [8735, 4720], [8746, 4730], [8750, 4720]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 603", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [-16, -12], [0, 22]], "o": [[0, -11], [-16, 0], [14, 11], [0, 0]], "v": [[8900, 4710], [8885, 4690], [8885, 4722], [8900, 4710]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 604", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[9000, 4720], [8989, 4710], [8985, 4720], [8996, 4730], [9000, 4720]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 605", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [13, 0], [-12, -8], [-7, 12]], "o": [[3, -6], [-18, 0], [19, 12], [0, 0]], "v": [[9065, 4720], [9048, 4710], [9040, 4720], [9065, 4720]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 606", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -4], [-25, 0], [4, 6]], "o": [[-3, -5], [-21, 7], [11, 0], [0, 0]], "v": [[9205, 4720], [9185, 4717], [9191, 4730], [9205, 4720]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 607", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[9707, 4724], [9688, 4723], [9694, 4729], [9707, 4724]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 608", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-3, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-9, 0], [3, 6], [2, 0], [0, 0]], "v": [[5095, 4710], [5085, 4700], [5075, 4710], [5085, 4720], [5095, 4710]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 609", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, -13], [-4, 11], [3, 3]], "o": [[-10, -10], [6, 11], [2, -7], [0, 0]], "v": [[6086, 4693], [6076, 4711], [6088, 4711], [6086, 4693]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 610", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-8, 0], [0, 6], [8, 0], [0, 0]], "v": [[9350, 4710], [9335, 4700], [9320, 4710], [9335, 4720], [9350, 4710]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 611", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 0], [0, 8], [6, 0], [0, -9], [-9, 4], [-4, -15], [-5, 0], [4, 8]], "o": [[-3, -8], [6, 0], [0, -8], [-25, 0], [0, 5], [10, -4], [3, 11], [6, 0], [0, 0]], "v": [[8166, 4695], [8170, 4680], [8180, 4665], [8168, 4650], [8110, 4671], [8126, 4673], [8147, 4689], [8162, 4710], [8166, 4695]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 612", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-12, 0], [3, 6]], "o": [[-3, -5], [-8, 0], [0, 6], [11, 0], [0, 0]], "v": [[9785, 4700], [9764, 4690], [9750, 4700], [9771, 4710], [9785, 4700]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 613", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[9830, 4706], [9820, 4695], [9810, 4699], [9820, 4710], [9830, 4706]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 614", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 5], [-7, -3], [7, 34], [11, -14], [-3, 9], [29, 7], [9, -2], [-9, -18], [-53, -2], [6, 3]], "o": [[-7, -2], [0, -4], [12, 4], [-1, -3], [-11, 14], [5, -12], [-21, -5], [-15, 3], [20, 36], [28, 0], [0, 0]], "v": [[5263, 4693], [5250, 4679], [5263, 4676], [5271, 4630], [5250, 4650], [5235, 4659], [5203, 4633], [5148, 4628], [5141, 4653], [5224, 4697], [5263, 4693]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 615", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-25, 6], [23, 0], [10, 13], [22, -32], [13, 17], [0, -7], [13, 4], [0, -6], [-47, 1], [-1, 12], [-4, -12], [-14, 18], [6, -17]], "o": [[-6, 16], [35, -8], [-10, 0], [-22, -28], [-14, 19], [-7, -11], [0, 8], [-11, -3], [0, 13], [27, 0], [0, -14], [10, 25], [12, -16], [0, 0]], "v": [[5454, 4683], [5476, 4694], [5496, 4680], [5459, 4656], [5371, 4663], [5343, 4665], [5330, 4659], [5310, 4665], [5290, 4669], [5363, 4688], [5401, 4672], [5407, 4670], [5447, 4682], [5454, 4683]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 616", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 4], [7, -10], [-7, 0], [11, -7], [-25, 9]], "o": [[9, -3], [0, -12], [-3, 5], [9, 0], [-17, 11], [0, 0]], "v": [[8224, 4694], [8240, 4680], [8194, 4671], [8202, 4680], [8200, 4690], [8224, 4694]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 617", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[8320, 4690], [8310, 4680], [8300, 4690], [8310, 4700], [8320, 4690]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 618", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-12, -10], [-3, 17], [2, -5], [21, 6], [0, -17], [-6, 15]], "o": [[3, -10], [18, 15], [1, -6], [-3, 5], [-38, -10], [0, 14], [0, 0]], "v": [[8383, 4688], [8402, 4688], [8466, 4681], [8464, 4679], [8424, 4678], [8360, 4690], [8383, 4688]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 619", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[8510, 4690], [8500, 4680], [8490, 4690], [8500, 4700], [8510, 4690]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 620", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [14, 0], [0, -5], [-10, 0], [-3, 6]], "o": [[4, -6], [-14, 0], [0, 6], [11, 0], [0, 0]], "v": [[8745, 4690], [8726, 4680], [8700, 4690], [8719, 4700], [8745, 4690]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 621", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [23, -6], [0, -7], [-16, 16]], "o": [[18, -18], [-11, 3], [0, 17], [0, 0]], "v": [[8848, 4688], [8840, 4667], [8820, 4686], [8848, 4688]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 622", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, 0], [0, 0], [0, 0], [14, -1], [-3, -6], [-13, 11], [3, 4]], "o": [[-3, -5], [0, 0], [0, 0], [20, 11], [-10, 0], [5, 7], [10, -9], [0, 0]], "v": [[9164, 4659], [9132, 4650], [9105, 4651], [9130, 4665], [9137, 4680], [9125, 4691], [9151, 4684], [9164, 4659]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 623", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -7], [6, 9], [0, -18], [-13, 4], [-3, -6], [0, 14]], "o": [[0, -5], [-5, 0], [0, 7], [-13, -17], [0, 5], [13, -3], [7, 11], [0, 0]], "v": [[9640, 4679], [9630, 4670], [9620, 4682], [9608, 4677], [9550, 4681], [9574, 4684], [9604, 4689], [9640, 4679]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 624", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [33, 0], [-11, -7], [-8, 13]], "o": [[4, -6], [-38, 0], [20, 13], [0, 0]], "v": [[4985, 4680], [4938, 4670], [4900, 4680], [4985, 4680]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 625", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-3, -5], [-6, 0], [0, 6]], "o": [[0, -5], [-8, 0], [3, 6], [5, 0], [0, 0]], "v": [[5060, 4680], [5044, 4670], [5035, 4680], [5051, 4690], [5060, 4680]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 626", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -2], [-14, 0], [6, 3]], "o": [[-7, -2], [-7, 3], [14, 0], [0, 0]], "v": [[5133, 4683], [5108, 4683], [5120, 4688], [5133, 4683]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 627", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, -14], [8, -3], [-15, 0], [-3, 6]], "o": [[9, -14], [-4, 6], [-10, 4], [12, 1], [0, 0]], "v": [[6965, 4679], [6952, 4666], [6930, 4683], [6937, 4689], [6965, 4679]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 628", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[7340, 4686], [7330, 4675], [7320, 4679], [7330, 4690], [7340, 4686]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 629", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[8270, 4686], [8260, 4675], [8250, 4679], [8260, 4690], [8270, 4686]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 630", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 4], [-6, -9], [-3, 4], [12, 13], [14, -19], [-20, 0], [4, 5]], "o": [[-3, -5], [5, -3], [5, 10], [4, -4], [-21, -23], [-18, 25], [8, 0], [0, 0]], "v": [[8595, 4681], [8599, 4665], [8619, 4677], [8635, 4688], [8620, 4658], [8584, 4654], [8586, 4690], [8595, 4681]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 631", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, 0], [-3, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-11, 0], [3, 6], [8, 0], [0, 0]], "v": [[8930, 4680], [8909, 4670], [8895, 4680], [8916, 4690], [8930, 4680]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 632", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, 0], [-3, -7], [23, 3], [0, -7], [-26, 0], [0, 2]], "o": [[0, -16], [-7, 0], [4, 10], [-18, -2], [0, 6], [25, 0], [0, 0]], "v": [[9460, 4686], [9434, 4650], [9426, 4663], [9400, 4672], [9370, 4679], [9415, 4690], [9460, 4686]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 633", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [-3, -8], [-5, 0], [3, 8]], "o": [[-4, -8], [-4, 0], [4, 8], [4, 0], [0, 0]], "v": [[9504, 4675], [9489, 4660], [9486, 4675], [9501, 4690], [9504, 4675]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 634", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [9, 3], [0, -11], [-21, 0], [0, 6]], "o": [[0, -5], [-8, 0], [-11, -4], [0, 11], [17, 0], [0, 0]], "v": [[9240, 4670], [9226, 4660], [9196, 4654], [9180, 4664], [9210, 4680], [9240, 4670]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 635", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -10], [-7, -7], [-5, 9], [2, 4]], "o": [[-7, -10], [0, 4], [9, 9], [4, -7], [0, 0]], "v": [[9274, 4648], [9240, 4648], [9252, 4668], [9271, 4668], [9274, 4648]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 636", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, 7], [-6, 18], [17, -8], [7, 7], [4, -4], [-11, -8], [10, -1], [-12, -15], [12, 13], [0, -17], [6, 0], [-3, 6], [18, 0], [3, -6], [26, 2], [9, -5], [0, 6], [11, -3], [-14, -9], [-60, -1], [-25, -3], [0, 13]], "o": [[0, -4], [-11, -8], [9, -22], [-12, 7], [-7, -7], [-3, 4], [13, 10], [-12, 0], [8, 10], [-23, -24], [0, 6], [-5, 0], [4, -6], [-16, 0], [-3, 5], [-26, -2], [-11, 5], [0, -5], [-21, 6], [5, 3], [60, 1], [48, 7], [0, 0]], "v": [[5130, 4650], [5116, 4630], [5110, 4594], [5101, 4579], [5072, 4578], [5053, 4573], [5066, 4595], [5071, 4610], [5071, 4628], [5063, 4623], [5020, 4610], [5009, 4620], [5005, 4610], [4981, 4600], [4945, 4610], [4892, 4616], [4828, 4622], [4810, 4621], [4790, 4617], [4778, 4644], [4896, 4651], [5050, 4659], [5130, 4650]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 637", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, 8], [7, -12], [-31, 0], [0, 5]], "o": [[0, -4], [-18, -11], [-15, 22], [17, 0], [0, 0]], "v": [[5550, 4662], [5529, 4641], [5499, 4642], [5520, 4670], [5550, 4662]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 638", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[6030, 4660], [6026, 4650], [6015, 4660], [6019, 4670], [6030, 4660]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 639", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[6110, 4660], [6100, 4650], [6090, 4660], [6100, 4670], [6110, 4660]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 640", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[7660, 4660], [7650, 4650], [7640, 4660], [7650, 4670], [7660, 4660]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 641", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [14, -5], [0, -5], [-14, 17]], "o": [[11, -13], [-9, 3], [0, 13], [0, 0]], "v": [[7790, 4655], [7786, 4646], [7770, 4661], [7790, 4655]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 642", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [4, 11], [24, -1], [0, 5], [8, -13], [16, 0], [-4, 6], [19, -12], [-15, -9], [4, 0], [-9, -11], [0, 17], [6, 0], [8, 8], [-16, 0], [-16, -18], [0, 0], [0, 0], [-45, -5]], "o": [[37, 4], [-3, -9], [-20, 1], [0, -13], [-3, 6], [-18, 0], [8, -12], [-11, 7], [11, 6], [-4, 0], [18, 21], [0, -5], [-6, 0], [-14, -13], [9, 0], [0, 0], [0, 0], [8, -30], [0, 0]], "v": [[8046, 4618], [8088, 4609], [8046, 4597], [8010, 4590], [7965, 4590], [7929, 4600], [7905, 4590], [7861, 4589], [7866, 4609], [7878, 4620], [7887, 4640], [7940, 4650], [7929, 4640], [7904, 4625], [7906, 4610], [7953, 4642], [7984, 4674], [7992, 4643], [8046, 4618]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 643", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [0, -9], [-6, 18]], "o": [[2, -7], [-9, 0], [0, 17], [0, 0]], "v": [[8068, 4652], [8056, 4640], [8040, 4656], [8068, 4652]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 644", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[8325, 4660], [8321, 4650], [8310, 4660], [8314, 4670], [8325, 4660]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 645", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [39, 7], [0, -11], [-9, 3], [0, -6], [-8, 14]], "o": [[9, -13], [-25, -5], [0, 9], [8, -4], [0, 14], [0, 0]], "v": [[8515, 4659], [8483, 4637], [8450, 4645], [8465, 4654], [8480, 4659], [8515, 4659]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 646", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-5, 0], [-3, 6]], "o": [[3, -5], [-9, 0], [0, 6], [6, 0], [0, 0]], "v": [[9375, 4660], [9366, 4650], [9350, 4660], [9359, 4670], [9375, 4660]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 647", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, 0], [12, -9], [-10, -11], [-16, 16]], "o": [[13, -13], [-4, 0], [-18, 13], [14, 17], [0, 0]], "v": [[6838, 4648], [6842, 4600], [6812, 4616], [6801, 4646], [6838, 4648]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 648", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -8], [4, -10], [-9, 3], [0, 15]], "o": [[1, -15], [-4, 0], [0, 8], [-4, 11], [8, -2], [0, 0]], "v": [[7835, 4627], [7828, 4600], [7820, 4614], [7813, 4646], [7820, 4658], [7835, 4627]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 649", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -17], [-9, 0], [2, 7]], "o": [[-6, -18], [0, 9], [9, 0], [0, 0]], "v": [[8368, 4648], [8340, 4644], [8356, 4660], [8368, 4648]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 650", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [-3, -5], [20, 0], [-16, -12], [-2, 25]], "o": [[1, -10], [-6, 0], [4, 6], [-30, 1], [23, 18], [0, 0]], "v": [[8927, 4629], [8919, 4610], [8915, 4620], [8888, 4630], [8873, 4644], [8927, 4629]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 651", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [27, -15], [-21, 0], [-3, 6]], "o": [[11, -16], [-24, 12], [12, 0], [0, 0]], "v": [[9065, 4649], [9040, 4647], [9037, 4660], [9065, 4649]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 652", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 7], [0, -14], [-14, 0], [2, 2]], "o": [[-2, -1], [-15, -12], [0, 10], [12, 0], [0, 0]], "v": [[4759, 4647], [4738, 4631], [4720, 4634], [4742, 4650], [4759, 4647]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 653", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [3, -5], [-6, 0], [-3, 6]], "o": [[3, -5], [-5, 0], [-3, 6], [5, 0], [0, 0]], "v": [[5955, 4640], [5951, 4630], [5935, 4640], [5939, 4650], [5955, 4640]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 654", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, 0], [2, -7], [-13, 0], [2, 7]], "o": [[-3, -7], [-7, 0], [-3, 7], [13, 0], [0, 0]], "v": [[6978, 4638], [6960, 4625], [6943, 4638], [6960, 4650], [6978, 4638]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 655", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 5], [5, -15], [-12, 4], [-16, 0]], "o": [[16, 0], [0, -15], [-3, 10], [10, -4], [0, 0]], "v": [[7601, 4640], [7630, 4631], [7542, 4639], [7554, 4647], [7601, 4640]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 656", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[7750, 4640], [7740, 4630], [7730, 4640], [7740, 4650], [7750, 4640]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 657", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [21, -8], [-27, 2], [3, 7]], "o": [[-6, -15], [-24, 9], [14, 0], [0, 0]], "v": [[8283, 4633], [8256, 4626], [8264, 4646], [8283, 4633]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 658", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-5, 0], [-3, 6]], "o": [[3, -5], [-9, 0], [0, 6], [6, 0], [0, 0]], "v": [[8415, 4640], [8406, 4630], [8390, 4640], [8399, 4650], [8415, 4640]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 659", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[6120, 4630], [6110, 4620], [6100, 4630], [6110, 4640], [6120, 4630]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 660", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [0, -9], [-6, 18]], "o": [[2, -7], [-9, 0], [0, 17], [0, 0]], "v": [[6788, 4622], [6776, 4610], [6760, 4626], [6788, 4622]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 661", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [3, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-6, 0], [-3, 6], [9, 0], [0, 0]], "v": [[6910, 4630], [6901, 4620], [6885, 4630], [6894, 4640], [6910, 4630]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 662", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-11, -18], [7, 0], [-19, -4], [-2, -1], [12, 11]], "o": [[-22, -21], [3, 6], [-7, 1], [19, 4], [3, 0], [0, 0]], "v": [[8612, 4620], [8575, 4610], [8568, 4621], [8590, 4630], [8629, 4639], [8612, 4620]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 663", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -9], [-8, 0], [0, 2]], "o": [[0, -11], [-3, 5], [9, 0], [0, 0]], "v": [[4600, 4626], [4575, 4620], [4584, 4630], [4600, 4626]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 664", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 4], [-12, -12], [0, 11]], "o": [[0, -5], [-15, -5], [9, 10], [0, 0]], "v": [[7710, 4621], [7695, 4606], [7687, 4623], [7710, 4621]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 665", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [-4, -6], [-8, 0], [0, -5], [-24, 22]], "o": [[0, 0], [0, 0], [-29, 0], [3, 6], [8, 0], [0, 15], [0, 0]], "v": [[8192, 4610], [8214, 4590], [8166, 4590], [8125, 4600], [8146, 4610], [8160, 4620], [8192, 4610]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 666", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[8540, 4620], [8530, 4610], [8520, 4620], [8530, 4630], [8540, 4620]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 667", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[2530, 4610], [2520, 4600], [2510, 4610], [2520, 4620], [2530, 4610]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 668", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -8], [0, 7], [8, -8], [7, 2], [4, -10], [-11, 4], [-2, -5], [0, 11]], "o": [[0, -12], [-3, 3], [0, -10], [-6, 6], [-7, -3], [-4, 12], [8, -4], [7, 11], [0, 0]], "v": [[4420, 4610], [4376, 4587], [4370, 4580], [4359, 4577], [4335, 4584], [4316, 4596], [4325, 4607], [4344, 4609], [4420, 4610]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 669", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 6], [8, -3], [3, 4], [-11, -11], [-31, -1], [13, 20], [-8, -22], [20, 17], [9, -4], [4, 6], [-6, -16], [17, 1], [0, 4], [14, 0], [0, -9], [-49, 4], [-4, -6], [10, 2], [2, -6], [-35, 7]], "o": [[4, 0], [0, -6], [-8, 4], [-7, -12], [5, 5], [55, 3], [-15, -23], [7, 17], [-8, -6], [-8, 3], [-9, -16], [4, 10], [-13, 0], [0, -5], [-16, 0], [0, 15], [14, -1], [4, 7], [-10, -2], [-3, 8], [0, 0]], "v": [[4553, 4611], [4560, 4599], [4545, 4593], [4525, 4591], [4567, 4581], [4633, 4593], [4676, 4575], [4625, 4562], [4557, 4561], [4527, 4556], [4505, 4551], [4483, 4554], [4464, 4566], [4440, 4558], [4415, 4550], [4390, 4565], [4452, 4580], [4485, 4590], [4475, 4598], [4453, 4606], [4553, 4611]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 670", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [4, -3], [4, 8], [-9, 0], [0, -6], [-12, 2], [26, -2], [3, 1], [15, 2], [-6, -21], [-1, -1], [-11, -6], [15, 18]], "o": [[-7, -8], [-4, 3], [-3, -9], [8, 0], [0, 6], [27, -4], [-11, 1], [-3, -1], [-26, -4], [4, 13], [0, 1], [28, 16], [0, 0]], "v": [[4750, 4605], [4730, 4595], [4716, 4585], [4725, 4570], [4740, 4581], [4762, 4588], [4774, 4560], [4750, 4560], [4717, 4554], [4695, 4572], [4704, 4596], [4725, 4608], [4750, 4605]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 671", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 2], [-4, -10], [6, 18]], "o": [[-2, -6], [-7, -2], [7, 18], [0, 0]], "v": [[5148, 4608], [5132, 4592], [5126, 4604], [5148, 4608]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 672", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-8, -13], [-15, 0], [12, 8]], "o": [[-20, -13], [3, 6], [21, 0], [0, 0]], "v": [[5210, 4610], [5165, 4610], [5198, 4620], [5210, 4610]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 673", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [30, -12], [0, -8], [-25, 14], [0, 4]], "o": [[0, -10], [-14, 5], [0, 13], [16, -9], [0, 0]], "v": [[6100, 4578], [6066, 4580], [6040, 4604], [6070, 4602], [6100, 4578]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 674", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, 0], [3, -5], [-20, 13]], "o": [[13, -8], [-9, 0], [-8, 13], [0, 0]], "v": [[7320, 4610], [7318, 4600], [7295, 4610], [7320, 4610]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 675", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [24, -3], [0, 7], [13, -4], [19, 7], [3, -9], [-2, -4], [0, 9], [-24, -6], [2, -4], [-19, 0], [4, 12]], "o": [[-6, -15], [-7, 1], [0, -7], [-13, 3], [-27, -9], [-3, 7], [4, 10], [0, -4], [25, 6], [-3, 4], [27, 0], [0, 0]], "v": [[7804, 4605], [7763, 4589], [7750, 4579], [7728, 4574], [7671, 4567], [7633, 4567], [7632, 4587], [7660, 4591], [7704, 4594], [7744, 4612], [7774, 4620], [7804, 4605]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 676", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[8457, 4614], [8438, 4613], [8444, 4619], [8457, 4614]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 677", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [20, -5], [4, 14], [-2, -17], [21, 22], [17, -27], [-18, 0], [-30, -15], [-17, 21]], "o": [[12, -15], [-16, 4], [-6, -18], [5, 27], [-28, -31], [-11, 18], [29, 0], [37, 20], [0, 0]], "v": [[4291, 4593], [4283, 4582], [4255, 4569], [4231, 4558], [4206, 4566], [4142, 4560], [4150, 4580], [4207, 4595], [4291, 4593]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 678", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [4, -13], [10, 8], [7, -12], [-47, -5], [-2, -1], [0, 5]], "o": [[0, -14], [-3, 7], [-12, -10], [-8, 11], [31, 3], [1, 1], [0, 0]], "v": [[4910, 4601], [4842, 4580], [4823, 4579], [4800, 4581], [4848, 4601], [4908, 4608], [4910, 4601]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 679", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, -3], [-3, -5], [0, 18]], "o": [[0, -8], [-6, 4], [9, 14], [0, 0]], "v": [[6730, 4594], [6720, 4585], [6715, 4601], [6730, 4594]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 680", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [14, 0], [7, 9], [7, -18], [-9, -6], [8, 0], [0, -5], [-15, 15]], "o": [[15, -15], [-7, 0], [-12, -17], [-4, 11], [10, 6], [-7, 0], [0, 14], [0, 0]], "v": [[3888, 4588], [3889, 4560], [3864, 4543], [3844, 4544], [3851, 4571], [3853, 4580], [3840, 4590], [3888, 4588]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 681", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-8, -3], [27, 12], [-5, -13], [4, 0], [4, 8], [5, -15], [20, -1], [3, -7], [-53, 1], [-3, 3]], "o": [[3, -3], [25, 9], [-22, -10], [3, 7], [-5, 0], [-6, -17], [-2, 6], [-20, 1], [-3, 8], [41, -1], [0, 0]], "v": [[4056, 4587], [4076, 4587], [4064, 4563], [4044, 4566], [4041, 4580], [4026, 4565], [3988, 4560], [3948, 4572], [3906, 4585], [3976, 4595], [4056, 4587]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 682", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -9], [-8, 0], [0, 2]], "o": [[0, -11], [-3, 5], [9, 0], [0, 0]], "v": [[7840, 4586], [7815, 4580], [7824, 4590], [7840, 4586]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 683", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [31, 2], [-4, -7], [-18, 12]], "o": [[8, -5], [-33, -1], [7, 12], [0, 0]], "v": [[4348, 4561], [4310, 4551], [4265, 4560], [4348, 4561]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 684", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[2470, 4550], [2459, 4540], [2455, 4550], [2466, 4560], [2470, 4550]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 685", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -2], [-14, 0], [6, 3]], "o": [[-7, -2], [-7, 3], [14, 0], [0, 0]], "v": [[7943, 4553], [7918, 4553], [7930, 4558], [7943, 4553]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 686", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -12], [-16, 1], [18, 8]], "o": [[-27, -12], [0, 6], [24, 0], [0, 0]], "v": [[6625, 4540], [6590, 4540], [6618, 4549], [6625, 4540]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 687", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [0, -9], [-6, 18]], "o": [[2, -7], [-9, 0], [0, 17], [0, 0]], "v": [[5798, 4512], [5786, 4500], [5770, 4516], [5798, 4512]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 688", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -5], [-3, 0], [0, 6]], "o": [[0, -5], [-5, 0], [3, 6], [2, 0], [0, 0]], "v": [[4080, 4510], [4069, 4500], [4065, 4510], [4076, 4520], [4080, 4510]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 689", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-4, -10], [-6, 0], [8, 9], [7, 0], [0, -8], [-1, 17]], "o": [[0, -10], [2, 6], [6, 0], [-7, -8], [-8, 0], [0, 18], [0, 0]], "v": [[4111, 4508], [4117, 4508], [4132, 4520], [4130, 4505], [4104, 4490], [4090, 4505], [4111, 4508]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 690", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[3960, 4450], [3950, 4440], [3940, 4450], [3950, 4460], [3960, 4450]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 691", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[4980, 4456], [4970, 4445], [4960, 4449], [4970, 4460], [4980, 4456]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 692", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[4135, 4430], [4131, 4420], [4120, 4430], [4124, 4440], [4135, 4430]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 693", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -6], [-8, 3], [0, 2]], "o": [[0, -2], [-8, 0], [0, 5], [8, -4], [0, 0]], "v": [[4210, 4364], [4195, 4360], [4180, 4370], [4195, 4374], [4210, 4364]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 694", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [11, 0], [0, -5], [-8, 0], [-3, 6]], "o": [[3, -5], [-12, 0], [0, 6], [8, 0], [0, 0]], "v": [[4505, 4370], [4491, 4360], [4470, 4370], [4484, 4380], [4505, 4370]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 695", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[3450, 4350], [3440, 4340], [3430, 4350], [3440, 4360], [3450, 4350]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 696", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [15, -13], [-8, 0], [-7, 9]], "o": [[13, -16], [-17, 13], [2, 0], [0, 0]], "v": [[3951, 4343], [3948, 4339], [3934, 4360], [3951, 4343]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 697", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -3], [0, -6], [-5, 0], [0, 9]], "o": [[0, -8], [-5, 3], [0, 5], [6, 0], [0, 0]], "v": [[2510, 4334], [2500, 4325], [2490, 4341], [2500, 4350], [2510, 4334]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 698", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -18], [-7, 2], [-2, 6]], "o": [[6, -18], [-4, 10], [6, -2], [0, 0]], "v": [[3888, 4332], [3866, 4336], [3872, 4348], [3888, 4332]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 699", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, -3], [-17, -6], [-8, 0], [14, 11]], "o": [[-11, -7], [-13, 6], [10, 4], [12, 0], [0, 0]], "v": [[4057, 4335], [4022, 4327], [4026, 4341], [4060, 4349], [4057, 4335]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 700", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, -3], [2, -7], [-8, 3], [-2, 7]], "o": [[3, -8], [-7, 2], [-3, 8], [7, -2], [0, 0]], "v": [[3808, 4321], [3799, 4312], [3782, 4329], [3791, 4338], [3808, 4321]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 701", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [17, -6], [0, -7], [-8, 3], [-3, -5], [0, 13]], "o": [[0, -14], [-8, 3], [0, 6], [8, -4], [8, 13], [0, 0]], "v": [[3560, 4310], [3504, 4284], [3490, 4301], [3505, 4307], [3525, 4310], [3560, 4310]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 702", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -15], [-12, 3], [6, 6]], "o": [[-11, -11], [0, 4], [13, -4], [0, 0]], "v": [[3612, 4298], [3580, 4310], [3601, 4313], [3612, 4298]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 703", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [21, 1], [-17, -12], [5, 17]], "o": [[-3, -7], [-35, -1], [24, 18], [0, 0]], "v": [[3428, 4288], [3385, 4274], [3365, 4286], [3428, 4288]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 704", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-8, 0], [0, 6], [8, 0], [0, 0]], "v": [[3480, 4290], [3465, 4280], [3450, 4290], [3465, 4300], [3480, 4290]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 705", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, 0], [-3, -5], [-8, 0], [0, 6]], "o": [[0, -5], [-11, 0], [3, 6], [8, 0], [0, 0]], "v": [[3320, 4280], [3299, 4270], [3285, 4280], [3306, 4290], [3320, 4280]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 706", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [3, -5], [-6, 0], [-3, 6]], "o": [[3, -5], [-5, 0], [-3, 6], [5, 0], [0, 0]], "v": [[3465, 4200], [3461, 4190], [3445, 4200], [3449, 4210], [3465, 4200]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 707", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -9], [-8, 0], [0, 2]], "o": [[0, -11], [-3, 5], [9, 0], [0, 0]], "v": [[3430, 4196], [3405, 4190], [3414, 4200], [3430, 4196]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 708", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [4, 0], [0, -14], [-2, 0], [-2, 14]], "o": [[3, -14], [-5, 0], [0, 14], [2, 0], [0, 0]], "v": [[2512, 4135], [2509, 4110], [2500, 4135], [2504, 4160], [2512, 4135]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 709", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [0, -8], [-6, 0], [3, 8]], "o": [[-4, -8], [-2, 0], [0, 8], [5, 0], [0, 0]], "v": [[4214, 4115], [4204, 4100], [4200, 4115], [4210, 4130], [4214, 4115]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9882, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 1, "parent": 11}, {"ty": 4, "nm": "10", "sr": 1, "st": 0, "op": 180, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [1195, 1235.0000000000002]}, "s": {"a": 0, "k": [1.0347300000000001, -1.0347300000000004]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-25.8304, 23.3471]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "bm": 0, "hd": false, "nm": "Group 1", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-30, 8], [4, -32], [22, 34]], "o": [[-19, -29], [29, -7], [-4, 37], [0, 0]], "v": [[1245, 907], [1263, 846], [1318, 899], [1245, 907]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9686, 0.0275, 0.0275]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [1234.488037109375, 929.625]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [1234.488037109375, 929.625]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 2", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-25, 0], [0, -25], [25, 25]], "o": [[-25, -25], [25, 0], [0, 25], [0, 0]], "v": [[1550, 910], [1549, 860], [1600, 911], [1550, 910]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9569, 0.0157, 0.0157]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [1530.75, 929.25]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [1530.75, 929.25]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 3", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[22, 22], [-22, 0], [4, -19]], "o": [[-24, -24], [21, 0], [-5, 25]], "v": [[1521, 1161], [1515, 1100], [1568, 1157]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9686, 0, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [1500.696044921875, 1176.625]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [1500.696044921875, 1176.625]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 4", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-16, 52], [-96, -87], [37, -43], [59, 30]], "o": [[-82, -41], [24, -81], [72, 65], [-29, 34], [0, 0]], "v": [[1192, 1364], [1046, 1157], [1251, 1167], [1313, 1358], [1192, 1364]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9686, 0.0078, 0.0078]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [1186.7594604492188, 1242.0187377929683]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [1186.7594604492188, 1242.0187377929683]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 5", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [10, 25], [-28, 28], [0, 0], [0, 0], [0, 10], [-25, 94], [0, 0], [0, 0], [-20, 0], [0, -37], [409, -410], [14, -8], [27, 0], [34, 34]], "o": [[0, 0], [0, 0], [-46, 45], [-4, -10], [0, 0], [0, 0], [-34, -35], [0, -10], [0, 0], [0, 0], [417, -418], [37, 0], [0, 20], [-282, 282], [-28, 14], [-10, 0], [0, 0]], "v": [[563, 2028], [500, 1966], [457, 2008], [383, 2034], [420, 1975], [464, 1931], [402, 1866], [340, 1783], [385, 1595], [430, 1425], [953, 903], [1501, 380], [2050, 929], [1538, 1468], [1000, 1995], [642, 2090], [563, 2028]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 11], [26, 0], [0, -22], [-26, 16], [18, -28], [-18, 0], [-11, 11]], "o": [[11, -11], [0, -26], [-23, 0], [0, 18], [28, -18], [-16, 26], [8, 0], [0, 0]], "v": [[610, 1900], [630, 1860], [570, 1800], [510, 1855], [540, 1858], [572, 1890], [575, 1920], [610, 1900]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 7", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 29], [28, 0], [0, -21], [-72, -73], [25, 0], [-9, -24], [-90, 23]], "o": [[76, -19], [0, -28], [-24, 0], [0, 9], [119, 119], [-34, 0], [17, 48], [0, 0]], "v": [[1044, 1694], [1150, 1627], [806, 1280], [750, 1329], [882, 1477], [987, 1610], [927, 1667], [1044, 1694]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 8", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-5, 57], [81, 75], [65, 1], [41, -39], [-115, -118], [-84, 54]], "o": [[46, -31], [6, -73], [-72, -67], [-54, 0], [-92, 84], [102, 104], [0, 0]], "v": [[1346, 1456], [1428, 1314], [1316, 1093], [1127, 1000], [1007, 1049], [1044, 1375], [1346, 1456]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 9", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 28], [40, 8], [-32, -59], [-17, 7], [-5, 0]], "o": [[20, 0], [0, -38], [-62, -12], [13, 26], [8, -3], [0, 0]], "v": [[1571, 1220], [1620, 1150], [1535, 1054], [1461, 1171], [1548, 1225], [1571, 1220]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 10", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [12, 12], [22, -9], [-18, -7], [-34, -6], [-7, 9]], "o": [[11, -12], [-17, -16], [-18, 7], [9, 3], [4, 1], [0, 0]], "v": [[1730, 1064], [1729, 1035], [1375, 964], [1373, 1006], [1711, 1079], [1730, 1064]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 11", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [78, 0], [-59, -59], [-38, 30]], "o": [[58, -45], [-79, 0], [39, 39], [0, 0]], "v": [[1624, 959], [1558, 810], [1514, 946], [1624, 959]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 12", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [21, 32], [-44, -78], [-36, 40]], "o": [[21, -23], [-49, -74], [28, 50], [0, 0]], "v": [[1336, 939], [1336, 845], [1211, 918], [1336, 939]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 13", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [36, -33], [9, 9], [-13, 14], [0, 3], [12, -15], [20, 18], [0, -19], [-10, 0], [-15, 16]], "o": [[35, -36], [-18, 17], [-9, -9], [9, -10], [0, -13], [-12, 14], [-20, -18], [0, 11], [5, 0], [0, 0]], "v": [[1434, 841], [1424, 811], [1388, 822], [1393, 792], [1410, 767], [1370, 775], [1335, 770], [1300, 771], [1398, 870], [1434, 841]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 14", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [36, -33], [9, 9], [-13, 14], [0, 3], [12, -15], [19, 18], [5, -8], [-19, -2], [-15, 16]], "o": [[35, -36], [-18, 17], [-9, -9], [9, -10], [0, -13], [-12, 14], [-16, -15], [-7, 12], [5, 1], [0, 0]], "v": [[1514, 761], [1504, 731], [1468, 742], [1473, 712], [1490, 687], [1450, 695], [1415, 690], [1385, 680], [1476, 789], [1514, 761]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9765, 0.0157, 0.0157]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [1195, 1235.0000000000002]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [1195, 1235.0000000000002]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 2, "parent": 11}, {"ty": 4, "nm": "20", "sr": 1, "st": 0, "op": 180, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [1126.4399375915527, 743.8405303955077]}, "s": {"a": 0, "k": [1.3380900000000004, -1.3380900000000002]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [71.4188, 114.2141]}, "r": {"a": 0, "k": 23}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "bm": 0, "hd": false, "nm": "Group 1", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-17, 17], [-12, 0], [30, -42], [21, 22]], "o": [[-19, -18], [7, -7], [44, 0], [-18, 26], [0, 0]], "v": [[1616, 614], [1612, 532], [1645, 520], [1674, 608], [1616, 614]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9686, 0.0157, 0.0157]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 2", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-24, -4], [0, -18], [17, -2], [3, 9]], "o": [[-10, -26], [17, 2], [0, 18], [-13, 2], [0, 0]], "v": [[1864, 766], [1892, 722], [1915, 750], [1892, 778], [1864, 766]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9686, 0.0157, 0.0157]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 3", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-6, 85], [-35, 0], [0, -109], [39, -4], [15, 19]], "o": [[-27, -35], [6, -74], [55, 0], [0, 88], [-19, 2], [0, 0]], "v": [[1311, 865], [1282, 699], [1342, 590], [1420, 744], [1359, 888], [1311, 865]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9686, 0.0157, 0.0157]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 4", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-23, -5], [0, -16], [20, -2], [3, 8]], "o": [[-8, -22], [13, 2], [0, 20], [-16, 2], [0, 0]], "v": [[1677, 947], [1716, 902], [1735, 930], [1709, 958], [1677, 947]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9686, 0.0157, 0.0157]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Group 5", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [4, 117], [8, 0], [44, -16], [36, 69], [-38, 42], [-51, 11], [-36, -14], [3, 112], [-17, 9], [-93, 4], [-38, 2], [-258, 11], [-56, 3], [0, -79], [-8, -164], [6, -11], [181, -7], [78, -5], [12, 9]], "o": [[-16, -11], [-3, -95], [-7, 0], [-147, 54], [-29, -54], [33, -38], [27, -6], [13, 5], [-4, -118], [10, -5], [94, -4], [39, -3], [259, -11], [122, -7], [0, 29], [9, 193], [-12, 23], [-261, 9], [-54, 4], [0, 0]], "v": [[485, 1156], [462, 1006], [446, 870], [354, 899], [119, 880], [132, 735], [285, 646], [430, 664], [442, 541], [457, 402], [645, 386], [885, 375], [1425, 350], [1996, 324], [2130, 403], [2145, 754], [2149, 1071], [1930, 1105], [579, 1164], [485, 1156]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [3, -2], [-17, 0], [14, 7]], "o": [[-14, -8], [-7, 6], [8, 0], [0, 0]], "v": [[560, 1097], [530, 1087], [570, 1110], [560, 1097]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 7", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[617, 1104], [598, 1103], [604, 1109], [617, 1104]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 8", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[687, 1104], [668, 1103], [674, 1109], [687, 1104]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 9", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[720, 1100], [716, 1090], [705, 1100], [709, 1110], [720, 1100]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 10", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[760, 1100], [750, 1090], [740, 1100], [750, 1110], [760, 1100]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 11", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [26, -2], [-49, 0], [23, 2]], "o": [[-23, -2], [-26, 2], [50, 0], [0, 0]], "v": [[903, 1093], [813, 1093], [855, 1096], [903, 1093]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 12", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [8, 0], [0, -5], [-5, 0], [-3, 6]], "o": [[3, -5], [-9, 0], [0, 6], [6, 0], [0, 0]], "v": [[975, 1090], [966, 1080], [950, 1090], [959, 1100], [975, 1090]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 13", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1047, 1084], [1028, 1083], [1034, 1089], [1047, 1084]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 14", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1117, 1084], [1098, 1083], [1104, 1089], [1117, 1084]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 15", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1157, 1084], [1138, 1083], [1144, 1089], [1157, 1084]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 16", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[1190, 1080], [1180, 1070], [1170, 1080], [1180, 1090], [1190, 1080]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 17", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1227, 1074], [1208, 1073], [1214, 1079], [1227, 1074]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 18", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, -2], [-19, 0], [9, 3]], "o": [[-10, -2], [-10, 3], [19, 0], [0, 0]], "v": [[1288, 1073], [1253, 1073], [1270, 1078], [1288, 1073]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 19", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, -2], [-19, 0], [9, 3]], "o": [[-10, -2], [-10, 3], [19, 0], [0, 0]], "v": [[1358, 1073], [1323, 1073], [1340, 1078], [1358, 1073]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 20", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, -9], [-8, 0], [0, 2]], "o": [[0, -11], [-3, 5], [9, 0], [0, 0]], "v": [[1410, 1076], [1385, 1070], [1394, 1080], [1410, 1076]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 21", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[1440, 1070], [1430, 1060], [1420, 1070], [1430, 1080], [1440, 1070]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 22", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[527, 1048], [521, 1054], [526, 1067], [527, 1048]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 23", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1477, 1064], [1458, 1063], [1464, 1069], [1477, 1064]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 24", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, -2], [-19, 0], [9, 3]], "o": [[-10, -2], [-10, 3], [19, 0], [0, 0]], "v": [[1538, 1063], [1503, 1063], [1520, 1068], [1538, 1063]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 25", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1587, 1064], [1568, 1063], [1574, 1069], [1587, 1064]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 26", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[1620, 1060], [1616, 1050], [1605, 1060], [1609, 1070], [1620, 1060]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 27", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-3, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-9, 0], [3, 6], [2, 0], [0, 0]], "v": [[1655, 1060], [1645, 1050], [1635, 1060], [1645, 1070], [1655, 1060]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 28", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [18, -2], [-38, 0], [21, 2]], "o": [[-20, -2], [-17, 2], [39, 0], [0, 0]], "v": [[1767, 1053], [1697, 1053], [1735, 1057], [1767, 1053]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 29", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1837, 1054], [1818, 1053], [1824, 1059], [1837, 1054]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 30", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[1870, 1050], [1860, 1040], [1850, 1050], [1860, 1060], [1870, 1050]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 31", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1907, 1044], [1888, 1043], [1894, 1049], [1907, 1044]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 32", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1947, 1044], [1928, 1043], [1934, 1049], [1947, 1044]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 33", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[2017, 1044], [1998, 1043], [2004, 1049], [2017, 1044]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 34", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [3, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-3, 0], [-3, 6], [6, 0], [0, 0]], "v": [[2080, 1030], [2076, 1020], [2065, 1030], [2069, 1040], [2080, 1030]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 35", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[527, 1008], [521, 1014], [526, 1027], [527, 1008]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 36", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [48, 82], [25, 23], [0, 2], [-42, 0], [6, 21], [0, 15], [0, 0], [0, 0], [0, -13], [-42, -41], [39, -55], [41, 69], [35, -21], [0, -4], [-20, -16], [-57, 32]], "o": [[71, -40], [-15, -25], [-25, -23], [0, -3], [74, 0], [-3, -13], [0, 0], [0, 0], [-166, 9], [0, 7], [129, 128], [-29, 41], [-10, -16], [-20, 12], [0, 11], [40, 34], [0, 0]], "v": [[1065, 981], [1108, 749], [1036, 662], [990, 615], [1066, 610], [1136, 588], [1130, 537], [1130, 509], [1023, 515], [810, 543], [886, 630], [1014, 888], [905, 845], [857, 850], [820, 879], [893, 978], [1065, 981]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 37", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-7, 15], [11, 25], [17, -73], [-48, 0], [-9, 3]], "o": [[8, -4], [12, -22], [-29, -69], [-10, 41], [21, 0], [0, 0]], "v": [[1755, 994], [1784, 961], [1785, 904], [1625, 920], [1700, 1000], [1755, 994]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 38", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[527, 968], [521, 974], [526, 987], [527, 968]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 39", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 112], [119, -35], [-9, -137], [-109, 47]], "o": [[75, -32], [0, -177], [-89, 27], [11, 166], [0, 0]], "v": [[1403, 976], [1520, 751], [1308, 499], [1182, 756], [1403, 976]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 40", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [35, 53], [19, 0], [-71, -109], [-11, -18], [-16, 0], [0, 0], [0, 0]], "o": [[-19, -29], [-50, -79], [-22, 0], [42, 64], [13, 22], [0, 0], [0, 0], [0, 0]], "v": [[1887, 938], [1790, 788], [1704, 690], [1757, 808], [1853, 958], [1896, 990], [1921, 990], [1887, 938]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 41", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -6], [-7, 7], [6, 6]], "o": [[-7, -7], [0, 14], [2, -3], [0, 0]], "v": [[522, 928], [510, 926], [529, 945], [522, 928]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 42", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [59, -32], [-37, -72], [-158, 56]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-67, -22], [-95, 52], [36, 70], [0, 0]], "v": [[357, 887], [450, 855], [450, 768], [450, 682], [413, 669], [211, 685], [125, 870], [357, 887]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 43", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 3], [0, -5], [-5, 0], [0, 2]], "o": [[0, -3], [-5, -3], [0, 6], [6, 0], [0, 0]], "v": [[2100, 926], [2090, 915], [2080, 919], [2090, 930], [2100, 926]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 44", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [0, -8], [-6, 0], [3, 8]], "o": [[-4, -8], [-2, 0], [0, 8], [5, 0], [0, 0]], "v": [[524, 895], [514, 880], [510, 895], [520, 910], [524, 895]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 45", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [13, -5], [11, 5], [-20, -4], [0, 27], [3, -78], [-2, 6], [-10, -3], [23, 25], [-8, 0], [-6, -14], [17, -17], [5, 4], [-4, -7], [-15, 24]], "o": [[3, -5], [-13, 5], [-18, -8], [29, 5], [0, -78], [-1, 18], [2, -7], [25, 7], [-10, -11], [6, 0], [10, 22], [-12, 10], [-4, -4], [16, 27], [0, 0]], "v": [[666, 839], [648, 839], [605, 838], [607, 834], [680, 778], [564, 782], [567, 804], [590, 797], [593, 758], [589, 740], [612, 765], [603, 810], [574, 822], [574, 828], [666, 839]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 46", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[2087, 838], [2081, 844], [2086, 857], [2087, 838]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 47", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [30, 33], [26, -26], [-29, -20], [-30, 30]], "o": [[28, -27], [-23, -26], [-28, 28], [36, 25], [0, 0]], "v": [[1956, 794], [1953, 698], [1830, 700], [1832, 804], [1956, 794]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 48", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[2087, 798], [2081, 804], [2086, 817], [2087, 798]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 49", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[2087, 758], [2081, 764], [2086, 777], [2087, 758]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 50", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [-3, -8], [-2, 0], [0, 8]], "o": [[0, -8], [-5, 0], [4, 8], [2, 0], [0, 0]], "v": [[520, 745], [510, 730], [506, 745], [516, 760], [520, 745]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 51", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, -13], [-4, 11], [3, 3]], "o": [[-10, -10], [6, 11], [2, -7], [0, 0]], "v": [[2086, 713], [2076, 731], [2088, 731], [2086, 713]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 52", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-9, -13], [-4, 11], [3, 3]], "o": [[-10, -10], [6, 11], [2, -7], [0, 0]], "v": [[516, 693], [506, 711], [518, 711], [516, 693]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 53", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2, 0], [0, -8], [-6, 0], [3, 8]], "o": [[-4, -8], [-2, 0], [0, 8], [5, 0], [0, 0]], "v": [[2084, 685], [2074, 670], [2070, 685], [2080, 700], [2084, 685]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 54", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[507, 658], [501, 664], [506, 677], [507, 658]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 55", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 33], [43, 0], [0, -50], [-43, 0], [-18, 17]], "o": [[23, -23], [0, -50], [-43, 0], [0, 50], [16, 0], [0, 0]], "v": [[1701, 641], [1730, 570], [1645, 470], [1560, 570], [1645, 670], [1701, 641]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 56", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [23, 0], [0, 16], [-24, 0], [3, 13], [18, 0], [0, 35], [16, 0], [0, -88], [0, 0], [0, 0], [0, 17]], "o": [[0, -16], [-23, 0], [0, -16], [24, 0], [-2, -12], [-25, 1], [0, -31], [-19, 0], [0, 0], [0, 0], [43, 0], [0, 0]], "v": [[1850, 640], [1820, 620], [1790, 600], [1821, 580], [1848, 563], [1817, 545], [1790, 508], [1770, 470], [1750, 565], [1750, 660], [1800, 660], [1850, 640]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 57", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [25, -4], [0, 15], [-16, 0], [-4, 3], [22, 0], [3, 31], [16, 0], [-1, -12], [-1, -41], [-65, 7], [0, 15]], "o": [[0, -17], [-24, 4], [0, -12], [13, 0], [12, -12], [-20, 0], [-2, -30], [-13, 0], [0, 9], [2, 82], [24, -3], [0, 0]], "v": [[1970, 628], [1940, 612], [1910, 598], [1933, 580], [1963, 574], [1935, 540], [1908, 503], [1885, 465], [1866, 483], [1868, 574], [1938, 652], [1970, 628]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 58", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[2077, 638], [2071, 644], [2076, 657], [2077, 638]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 59", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[507, 618], [501, 624], [506, 637], [507, 618]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 60", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[2077, 598], [2071, 604], [2076, 617], [2077, 598]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 61", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[507, 578], [501, 584], [506, 597], [507, 578]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 62", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[2077, 528], [2071, 534], [2076, 547], [2077, 528]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 63", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[2080, 490], [2070, 480], [2060, 490], [2070, 500], [2080, 490]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 64", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[515, 470], [511, 460], [500, 470], [504, 480], [515, 470]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 65", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [5, 0], [0, -5], [-2, 0], [-3, 6]], "o": [[3, -5], [-6, 0], [0, 6], [3, 0], [0, 0]], "v": [[685, 440], [681, 430], [670, 440], [674, 450], [685, 440]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 66", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[727, 434], [708, 433], [714, 439], [727, 434]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 67", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[797, 434], [778, 433], [784, 439], [797, 434]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 68", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[900, 430], [890, 420], [880, 430], [890, 440], [900, 430]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 69", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[977, 424], [958, 423], [964, 429], [977, 424]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 70", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, -2], [-19, 0], [9, 3]], "o": [[-10, -2], [-10, 3], [19, 0], [0, 0]], "v": [[1038, 423], [1003, 423], [1020, 428], [1038, 423]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 71", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1087, 424], [1068, 423], [1074, 429], [1087, 424]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 72", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[1120, 420], [1110, 410], [1100, 420], [1110, 430], [1120, 420]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 73", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -11], [-3, 4], [3, 7]], "o": [[-3, -8], [-1, 11], [3, -3], [0, 0]], "v": [[2067, 408], [2061, 414], [2066, 427], [2067, 408]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 74", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1157, 414], [1138, 413], [1144, 419], [1157, 414]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 75", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1227, 414], [1208, 413], [1214, 419], [1227, 414]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 76", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1267, 414], [1248, 413], [1254, 419], [1267, 414]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 77", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1337, 414], [1318, 413], [1324, 419], [1337, 414]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 78", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[1370, 410], [1360, 400], [1350, 410], [1360, 420], [1370, 410]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 79", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1447, 404], [1428, 403], [1434, 409], [1447, 404]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 80", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1517, 404], [1498, 403], [1504, 409], [1517, 404]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 81", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [9, 0], [-3, -5], [-6, 0], [0, 6]], "o": [[0, -5], [-8, 0], [3, 6], [5, 0], [0, 0]], "v": [[1590, 400], [1574, 390], [1565, 400], [1581, 410], [1590, 400]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 82", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1697, 394], [1678, 393], [1684, 399], [1697, 394]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 83", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1767, 394], [1748, 393], [1754, 399], [1767, 394]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 84", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [6, 0], [0, -5], [-5, 0], [0, 6]], "o": [[0, -5], [-5, 0], [0, 6], [6, 0], [0, 0]], "v": [[1800, 390], [1790, 380], [1780, 390], [1790, 400], [1800, 390]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 85", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1837, 384], [1818, 383], [1824, 389], [1837, 384]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 86", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1877, 384], [1858, 383], [1864, 389], [1877, 384]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 87", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [7, -3], [-11, 0], [4, 3]], "o": [[-3, -3], [-8, 3], [11, 1], [0, 0]], "v": [[1947, 384], [1928, 383], [1934, 389], [1947, 384]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.9686, 0.0157, 0.0157]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 3, "parent": 11}, {"ty": 4, "nm": "<PERSON><PERSON><PERSON> - SVG", "sr": 1, "st": 0, "op": 180, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [259.2334899902344, 99.6235990524292]}, "s": {"a": 0, "k": [3.467172655999999, 3.5444983199999993]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [23.7067, 34.6268]}, "r": {"a": 0, "k": -360}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "gr", "bm": 0, "hd": false, "nm": "Logo1", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 1", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[442.972, 45.0332], [411.961, 135], [393.212, 135], [432.278, 31.2031], [444.255, 31.2031], [442.972, 45.0332]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 2", "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[468.9210000000001, 134.9999999999999], [437.839, 45.03320000000008], [436.484, 31.20309999999995], [448.53199999999987, 31.20309999999995], [487.74099999999993, 134.9999999999999]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 3", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[467.424, 96.5039], [467.424, 110.69], [410.963, 110.69], [410.963, 96.5039], [467.424, 96.5039]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 4", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[518.467, 31.2031], [518.467, 135], [500.573, 135], [500.573, 31.2031], [518.467, 31.2031]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.0118, 0.3961, 0.9804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tm", "bm": 0, "hd": false, "nm": "Trim Path", "e": {"a": 0, "k": 100}, "o": {"a": 0, "k": 0}, "s": {"a": 0, "k": 0}, "m": 1}, {"ty": "tr", "a": {"a": 0, "k": [446.8925018310549, 83.1235990524292]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [446.8925018310549, 83.1235990524292]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Logo2", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 5", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.608000000000004, 1.608000000000004], [-2.751000000000033, 0], [-1.566000000000031, -1.6499999999999915], [0, -2.3700000000000045], [1.6079999999999472, -1.608000000000004], [2.7930000000000064, 0], [1.608000000000004, 1.608000000000004], [0, 2.369999999999976]], "o": [[0, -2.3700000000000045], [1.608000000000004, -1.6499999999999915], [2.7930000000000064, 0], [1.6079999999999472, 1.608000000000004], [0, 2.369999999999976], [-1.566000000000031, 1.608000000000004], [-2.751000000000033, 0], [-1.608000000000004, -1.608000000000004], [0, 0]], "v": [[362.506, 130.383], [364.918, 124.416], [371.456, 121.94], [377.994, 124.416], [380.406, 130.383], [377.994, 136.35], [371.456, 138.762], [364.918, 136.35], [362.506, 130.383]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.0118, 0.3961, 0.9804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [362.5060119628904, 138.76217651367188]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [362.5060119628904, 138.76217651367188]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Logo3", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 6", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-4.006, 2.1310000000000002], [-5.199000000000012, 0], [-4.8589999999999804, -2.983399999999996], [-2.8130000000000166, -5.540700000000001], [0, -7.245499999999993], [2.899000000000001, -5.626000000000005], [4.944000000000017, -3.0690000000000026], [6.052999999999997, 0], [4.007000000000005, 2.132000000000005], [2.4720000000000084, 3.2390000000000043], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[2.3009999999999877, -3.2391999999999967], [4.092000000000013, -2.216299999999997], [6.052999999999997, 0], [4.944000000000017, 2.9835000000000065], [2.899000000000001, 5.455500000000001], [0, 7.245400000000004], [-2.8130000000000166, 5.540999999999997], [-4.8589999999999804, 3.0689999999999884], [-5.199000000000012, 0], [-3.9209999999999923, -2.1310000000000002], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[183.709, 73.6374], [193.17, 65.5821], [207.107, 62.2576], [223.474, 66.7328], [235.109, 79.5191], [239.457, 98.5706], [235.109, 117.878], [223.474, 130.792], [207.107, 135.395], [193.298, 132.198], [183.709, 124.143], [183.709, 168], [165.808, 168], [165.808, 63.4084], [183.709, 63.4084], [183.709, 73.6374]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 7", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.789999999999992, 3.068700000000007], [2.897999999999996, 1.6195999999999913], [3.323999999999984, 0], [2.899000000000001, -1.7048000000000059], [1.7900000000000205, -3.1539999999999964], [0, -4.262100000000004], [-1.704999999999984, -3.1539999999999964], [-2.812999999999988, -1.7049999999999983], [-3.240000000000009, 0], [-2.812999999999988, 1.7049999999999983], [-1.704999999999984, 3.153000000000006], [0, 4.3474000000000075]], "o": [[0, -4.262100000000004], [-1.704999999999984, -3.153899999999993], [-2.812999999999988, -1.6196000000000055], [-3.240000000000009, 0], [-2.812999999999988, 1.6196000000000055], [-1.704999999999984, 3.1539000000000073], [0, 4.2616999999999905], [1.7900000000000205, 3.1539999999999964], [2.899000000000001, 1.6189999999999998], [3.323999999999984, 0], [2.897999999999996, -1.7049999999999983], [1.789999999999992, -3.1539999999999964], [0, 0]], "v": [[221.172, 98.5706], [218.487, 87.5744], [211.583, 80.4141], [202.377, 77.9847], [193.17, 80.542], [186.266, 87.7023], [183.709, 98.8263], [186.266, 109.95], [193.17, 117.239], [202.377, 119.668], [211.583, 117.111], [218.487, 109.823], [221.172, 98.5706]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.0118, 0.3961, 0.9804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [202.63249969482445, 115.15084838867188]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [202.63249969482445, 115.15084838867188]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Logo4", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 8", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2.130999999999972, 1.9605999999999995], [0, 2.9835000000000065], [-2.0460000000000207, 1.9605999999999995], [-3.1539999999999964, 0], [-2.0459999999999923, -2.0458], [0, -2.983399999999996], [2.1310000000000002, -2.0458], [3.1539999999999964, 0]], "o": [[-3.1539999999999964, 0], [-2.0460000000000207, -2.0458], [0, -2.983399999999996], [2.130999999999972, -2.0458], [3.1539999999999964, 0], [2.1310000000000002, 1.9605999999999995], [0, 2.9835000000000065], [-2.0459999999999923, 1.9605999999999995], [0, 0]], "v": [[139.299, 54.9695], [131.372, 52.0286], [128.303, 44.4847], [131.372, 37.0687], [139.299, 34], [147.099, 37.0687], [150.295, 44.4847], [147.099, 52.0286], [139.299, 54.9695]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 9", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[148.122, 63.4084], [148.122, 134.244], [130.221, 134.244], [130.221, 63.4084], [148.122, 63.4084]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.0118, 0.3961, 0.9804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [129.26199340820335, 94.65085029602051]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [129.26199340820335, 94.65085029602051]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Logo5", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 10", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-3.665999999999997, 2.1310000000000002], [-4.774000000000001, 0], [0, 0], [0, 0], [2.897999999999996, -2.6424999999999983], [0, -6.563599999999994], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[2.30149999999999, -3.750700000000009], [3.75, -2.1310999999999964], [0, 0], [0, 0], [-5.626000000000005, 0], [-2.8131000000000057, 2.6424999999999983], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[96.4274, 74.4046], [105.378, 65.5821], [118.164, 62.3855], [118.164, 81.1813], [113.433, 81.1813], [100.647, 85.145], [96.4274, 98.9542], [96.4274, 134.244], [78.5266, 134.244], [78.5266, 63.4084], [96.4274, 63.4084], [96.4274, 74.4046]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.0118, 0.3961, 0.9804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [78.5266036987307, 98.33680152893066]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [78.5266036987307, 98.33680152893066]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Logo6", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 11", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[65.4657, 44.9962], [65.4657, 59.4447], [41.6832, 59.4447], [41.6832, 134.244], [23.7824, 134.244], [23.7824, 59.4447], [0, 59.4447], [0, 44.9962], [65.4657, 44.9962]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.0118, 0.3961, 0.9804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [32.73284912109398, 89.64215087890625]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [32.73284912109398, 89.64215087890625]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Logo7", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 12", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.8485000000000014], [-2.848000000000013, 0], [0, 2.8478999999999957], [2.8479999999999563, 0]], "o": [[-2.848000000000013, 0], [0, 2.847699999999996], [2.8479999999999563, 0], [0, -2.848700000000001], [0, 0]], "v": [[305.052, 49.2773], [299.895, 54.4346], [305.052, 59.5908], [310.209, 54.4346], [305.052, 49.2773]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 13", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.347999999999999], [-2.3479999999999563, 0], [0, 2.347999999999999], [2.348000000000013, 0]], "o": [[-2.3479999999999563, 0], [0, 2.346999999999994], [2.348000000000013, 0], [0, -2.3490000000000038], [0, 0]], "v": [[318.799, 112.228], [314.548, 116.479], [318.799, 120.729], [323.05, 116.479], [318.799, 112.228]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 14", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.354999999999997], [-2.3559999999999945, 0], [0, 2.354300000000002], [2.355000000000018, 0]], "o": [[-2.355000000000018, 0], [0, 2.354600000000005], [2.3559999999999945, 0], [0, -2.3547999999999973], [0, 0]], "v": [[318.81, 49.5774], [314.546, 53.8413], [318.81, 58.1062], [323.074, 53.8413], [318.81, 49.5774]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 15", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.6599999999999966], [-1.6589999999999918, 0], [0, 1.6589999999999918], [1.660000000000025, 0]], "o": [[-1.660000000000025, 0], [0, 1.6589999999999918], [1.6589999999999918, 0], [0, -1.6599999999999966], [0, 0]], "v": [[331.512, 108.203], [328.507, 111.209], [331.512, 114.214], [334.517, 111.209], [331.512, 108.203]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 16", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.8400000000000034], [-2.840000000000032, 0], [0, 2.840999999999994], [2.839999999999975, 0]], "o": [[-2.840000000000032, 0], [0, 2.840999999999994], [2.839999999999975, 0], [0, -2.840999999999994], [0, 0]], "v": [[305.04, 110.738], [299.897, 115.881], [305.04, 121.025], [310.184, 115.881], [305.04, 110.738]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 17", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -3.1845], [-3.184000000000026, 0], [0, 3.1848000000000027], [3.183999999999969, 0]], "o": [[-3.1850000000000023, 0], [0, 3.184899999999999], [3.1829999999999927, 0], [0, -3.1843000000000004], [0, 0]], "v": [[292.845, 55.0249], [287.079, 60.7913], [292.845, 66.5567], [298.61, 60.7913], [292.845, 55.0249]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 18", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 1.6646], [1.6649999999999636, 0], [0, -1.6651999999999987], [-1.6650000000000205, 0]], "o": [[1.6649999999999636, 0], [0, -1.6651999999999987], [-1.6650000000000205, 0], [0, 1.6646], [0, 0]], "v": [[331.528, 62.1357], [334.543, 59.1219], [331.528, 56.1071], [328.513, 59.1219], [331.528, 62.1357]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 19", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.3674000000000035], [3.3670000000000186, 0], [0, -3.368300000000005], [-3.367999999999995, 0]], "o": [[3.367999999999995, 0], [0, -3.3684999999999974], [-3.367999999999995, 0], [0, 3.3673], [0, 0]], "v": [[284.452, 77.8064], [290.55, 71.7081], [284.452, 65.6099], [278.354, 71.7081], [284.452, 77.8064]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 20", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -3.3633999999999986], [-3.3629999999999995, 0], [0, 3.363200000000006], [3.3640000000000327, 0]], "o": [[-3.3629999999999995, 0], [0, 3.363200000000006], [3.3640000000000327, 0], [0, -3.3633999999999986], [0, 0]], "v": [[284.445, 92.5055], [278.355, 98.5958], [284.445, 104.686], [290.536, 98.5958], [284.445, 92.5055]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 21", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -3.1779999999999973], [-3.1779999999999973, 0], [0, 3.1770000000000067], [3.1779999999999973, 0]], "o": [[-3.1779999999999973, 0], [0, 3.1770000000000067], [3.1790000000000305, 0], [0, -3.1779999999999973], [0, 0]], "v": [[292.832, 103.763], [287.079, 109.517], [292.832, 115.271], [298.586, 109.517], [292.832, 103.763]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 22", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [3.4329999999999927, 0], [0, -3.4324999999999903], [-3.433999999999969, 0], [0, 3.433800000000005]], "o": [[0, -3.432699999999997], [-3.433999999999969, 0], [0, 3.4335999999999984], [3.4329999999999927, 0], [0, 0]], "v": [[287.691, 85.151], [281.474, 78.9335], [275.257, 85.151], [281.474, 91.3685], [287.691, 85.151]]}}}, {"ty": "st", "bm": 0, "hd": false, "nm": "Stroke", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2.5}, "c": {"a": 0, "k": [0.0118, 0.3961, 0.9804]}}, {"ty": "tr", "a": {"a": 0, "k": [301.8849945068357, 89.5656013488765]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [301.8849945068357, 89.5656013488765]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Logo8", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 23", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.158300000000004], [-2.1580000000000155, 0], [0, 2.1572999999999993], [2.1579999999999586, 0]], "o": [[-2.1580000000000155, 0], [0, 2.1572999999999993], [2.1579999999999586, 0], [0, -2.158300000000004], [0, 0]], "v": [[305.052, 50.5273], [301.145, 54.4346], [305.052, 58.3408], [308.959, 54.4346], [305.052, 50.5273]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 24", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.6580000000000013], [-1.6579999999999586, 0], [0, 1.6569999999999965], [1.6580000000000155, 0]], "o": [[-1.6579999999999586, 0], [0, 1.6569999999999965], [1.6580000000000155, 0], [0, -1.6580000000000013], [0, 0]], "v": [[318.799, 113.478], [315.798, 116.479], [318.799, 119.479], [321.8, 116.479], [318.799, 113.478]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 25", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.6643999999999934], [-1.6650000000000205, 0], [0, 1.6645000000000039], [1.6650000000000205, 0]], "o": [[-1.6650000000000205, 0], [0, 1.6645000000000039], [1.6650000000000205, 0], [0, -1.6643999999999934], [0, 0]], "v": [[318.81, 50.8274], [315.796, 53.8413], [318.81, 56.8562], [321.824, 53.8413], [318.81, 50.8274]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 26", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.9699999999999989], [-0.9689999999999941, 0], [0, 0.9689999999999941], [0.9689999999999941, 0]], "o": [[-0.9689999999999941, 0], [0, 0.9689999999999941], [0.9689999999999941, 0], [0, -0.9699999999999989], [0, 0]], "v": [[331.512, 109.453], [329.757, 111.209], [331.512, 112.964], [333.267, 111.209], [331.512, 109.453]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 27", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.1500000000000057], [-2.150000000000034, 0], [0, 2.1509999999999962], [2.1499999999999773, 0]], "o": [[-2.150000000000034, 0], [0, 2.1509999999999962], [2.1499999999999773, 0], [0, -2.1500000000000057], [0, 0]], "v": [[305.04, 111.988], [301.147, 115.881], [305.04, 119.775], [308.934, 115.881], [305.04, 111.988]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 28", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.4941999999999993], [-2.494000000000028, 0], [0, 2.4941999999999993], [2.492999999999995, 0]], "o": [[-2.494000000000028, 0], [0, 2.4941999999999993], [2.492999999999995, 0], [0, -2.4941999999999993], [0, 0]], "v": [[292.845, 56.2749], [288.329, 60.7913], [292.845, 65.3067], [297.36, 60.7913], [292.845, 56.2749]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 29", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0.9738000000000042], [0.9749999999999659, 0], [0, -0.9747999999999948], [-0.9750000000000227, 0]], "o": [[0.9749999999999659, 0], [0, -0.9747999999999948], [-0.9750000000000227, 0], [0, 0.9738000000000042], [0, 0]], "v": [[331.528, 60.8857], [333.293, 59.1219], [331.528, 57.3571], [329.763, 59.1219], [331.528, 60.8857]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 30", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.677099999999996], [2.677000000000021, 0], [0, -2.6779999999999973], [-2.6779999999999973, 0]], "o": [[2.677000000000021, 0], [0, -2.6779999999999973], [-2.6779999999999973, 0], [0, 2.677099999999996], [0, 0]], "v": [[284.452, 76.5564], [289.3, 71.7081], [284.452, 66.8599], [279.604, 71.7081], [284.452, 76.5564]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 31", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.673099999999991], [-2.673000000000002, 0], [0, 2.6732000000000085], [2.673000000000002, 0]], "o": [[-2.673000000000002, 0], [0, 2.6732000000000085], [2.673000000000002, 0], [0, -2.673099999999991], [0, 0]], "v": [[284.445, 93.7555], [279.605, 98.5958], [284.445, 103.436], [289.286, 98.5958], [284.445, 93.7555]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 32", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.4879999999999995], [-2.4869999999999663, 0], [0, 2.487000000000009], [2.4879999999999995, 0]], "o": [[-2.4869999999999663, 0], [0, 2.487000000000009], [2.4879999999999995, 0], [0, -2.4879999999999995], [0, 0]], "v": [[292.832, 105.013], [288.329, 109.517], [292.832, 114.021], [297.336, 109.517], [292.832, 105.013]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 33", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2.742999999999995, 0], [0, -2.742599999999996], [-2.7439999999999714, 0], [0, 2.7436000000000007]], "o": [[0, -2.742599999999996], [-2.7439999999999714, 0], [0, 2.7436000000000007], [2.742999999999995, 0], [0, 0]], "v": [[286.441, 85.151], [281.474, 80.1835], [276.507, 85.151], [281.474, 90.1185], [286.441, 85.151]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.0118, 0.3961, 0.9804]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [303.1349945068357, 88.31560134887695]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [303.1349945068357, 88.31560134887695]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": true, "nm": "Logo9", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 34", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.158300000000004], [-2.1580000000000155, 0], [0, 2.1572999999999993], [2.1579999999999586, 0]], "o": [[-2.1580000000000155, 0], [0, 2.1572999999999993], [2.1579999999999586, 0], [0, -2.158300000000004], [0, 0]], "v": [[305.052, 50.5273], [301.145, 54.4346], [305.052, 58.3408], [308.959, 54.4346], [305.052, 50.5273]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 35", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.6580000000000013], [-1.6579999999999586, 0], [0, 1.6569999999999965], [1.6580000000000155, 0]], "o": [[-1.6579999999999586, 0], [0, 1.6569999999999965], [1.6580000000000155, 0], [0, -1.6580000000000013], [0, 0]], "v": [[318.799, 113.478], [315.798, 116.479], [318.799, 119.479], [321.8, 116.479], [318.799, 113.478]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 36", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.6643999999999934], [-1.6650000000000205, 0], [0, 1.6645000000000039], [1.6650000000000205, 0]], "o": [[-1.6650000000000205, 0], [0, 1.6645000000000039], [1.6650000000000205, 0], [0, -1.6643999999999934], [0, 0]], "v": [[318.81, 50.8274], [315.796, 53.8413], [318.81, 56.8562], [321.824, 53.8413], [318.81, 50.8274]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 37", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.9699999999999989], [-0.9689999999999941, 0], [0, 0.9689999999999941], [0.9689999999999941, 0]], "o": [[-0.9689999999999941, 0], [0, 0.9689999999999941], [0.9689999999999941, 0], [0, -0.9699999999999989], [0, 0]], "v": [[331.512, 109.453], [329.757, 111.209], [331.512, 112.964], [333.267, 111.209], [331.512, 109.453]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 38", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.1500000000000057], [-2.150000000000034, 0], [0, 2.1509999999999962], [2.1499999999999773, 0]], "o": [[-2.150000000000034, 0], [0, 2.1509999999999962], [2.1499999999999773, 0], [0, -2.1500000000000057], [0, 0]], "v": [[305.04, 111.988], [301.147, 115.881], [305.04, 119.775], [308.934, 115.881], [305.04, 111.988]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 39", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.4941999999999993], [-2.494000000000028, 0], [0, 2.4941999999999993], [2.492999999999995, 0]], "o": [[-2.494000000000028, 0], [0, 2.4941999999999993], [2.492999999999995, 0], [0, -2.4941999999999993], [0, 0]], "v": [[292.845, 56.2749], [288.329, 60.7913], [292.845, 65.3067], [297.36, 60.7913], [292.845, 56.2749]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 40", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0.9738000000000042], [0.9749999999999659, 0], [0, -0.9747999999999948], [-0.9750000000000227, 0]], "o": [[0.9749999999999659, 0], [0, -0.9747999999999948], [-0.9750000000000227, 0], [0, 0.9738000000000042], [0, 0]], "v": [[331.528, 60.8857], [333.293, 59.1219], [331.528, 57.3571], [329.763, 59.1219], [331.528, 60.8857]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 41", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.677099999999996], [2.677000000000021, 0], [0, -2.6779999999999973], [-2.6779999999999973, 0]], "o": [[2.677000000000021, 0], [0, -2.6779999999999973], [-2.6779999999999973, 0], [0, 2.677099999999996], [0, 0]], "v": [[284.452, 76.5564], [289.3, 71.7081], [284.452, 66.8599], [279.604, 71.7081], [284.452, 76.5564]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 42", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.673099999999991], [-2.673000000000002, 0], [0, 2.6732000000000085], [2.673000000000002, 0]], "o": [[-2.673000000000002, 0], [0, 2.6732000000000085], [2.673000000000002, 0], [0, -2.673099999999991], [0, 0]], "v": [[284.445, 93.7555], [279.605, 98.5958], [284.445, 103.436], [289.286, 98.5958], [284.445, 93.7555]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 43", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.4879999999999995], [-2.4869999999999663, 0], [0, 2.487000000000009], [2.4879999999999995, 0]], "o": [[-2.4869999999999663, 0], [0, 2.487000000000009], [2.4879999999999995, 0], [0, -2.4879999999999995], [0, 0]], "v": [[292.832, 105.013], [288.329, 109.517], [292.832, 114.021], [297.336, 109.517], [292.832, 105.013]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 44", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2.742999999999995, 0], [0, -2.742599999999996], [-2.7439999999999714, 0], [0, 2.7436000000000007]], "o": [[0, -2.742599999999996], [-2.7439999999999714, 0], [0, 2.7436000000000007], [2.742999999999995, 0], [0, 0]], "v": [[286.441, 85.151], [281.474, 80.1835], [276.507, 85.151], [281.474, 90.1185], [286.441, 85.151]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0, 0, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [303.1349945068357, 88.31560134887695]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [303.1349945068357, 88.31560134887695]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Logo10", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 45", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.627999999999986], [-2.627999999999986, 0], [0, 2.6270000000000095], [2.6280000000000427, 0]], "o": [[-2.627999999999986, 0], [0, 2.6270000000000095], [2.6280000000000427, 0], [0, -2.627999999999986], [0, 0]], "v": [[313.727, 128.037], [308.969, 132.795], [313.727, 137.553], [318.485, 132.795], [313.727, 128.037]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 46", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.900999999999982], [-2.9010000000000105, 0], [0, 2.9020000000000152], [2.902999999999963, 0]], "o": [[-2.9010000000000105, 0], [0, 2.9020000000000152], [2.902999999999963, 0], [0, -2.901999999999987], [0, 0]], "v": [[299.773, 126.208], [294.521, 131.462], [299.773, 136.715], [305.027, 131.462], [299.773, 126.208]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 47", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.634999999999998], [2.634999999999991, 0], [0, -2.6351000000000013], [-2.634999999999991, 0]], "o": [[2.634999999999991, 0], [0, -2.6351000000000013], [-2.634999999999991, 0], [0, 2.634999999999998], [0, 0]], "v": [[313.687, 42.2934], [318.459, 37.5217], [313.687, 32.75], [308.916, 37.5217], [313.687, 42.2934]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 48", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.2860000000000014], [-2.285000000000025, 0], [0, 2.2849999999999966], [2.284999999999968, 0]], "o": [[-2.285000000000025, 0], [0, 2.2849999999999966], [2.284999999999968, 0], [0, -2.2860000000000014], [0, 0]], "v": [[327.471, 125.897], [323.332, 130.036], [327.471, 134.174], [331.61, 130.036], [327.471, 125.897]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 49", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.3400000000000034], [-1.336999999999989, 0], [0, 1.3389999999999986], [1.3389999999999986, 0]], "o": [[-1.336999999999989, 0], [0, 1.3400000000000034], [1.339999999999975, 0], [0, -1.3389999999999986], [0, 0]], "v": [[349.687, 111.014], [347.263, 113.438], [349.687, 115.862], [352.112, 113.438], [349.687, 111.014]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 50", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 1.3438999999999979], [1.342999999999961, 0], [0, -1.3415999999999997], [-1.3430000000000177, 0]], "o": [[1.3419999999999845, 0], [0, -1.3421999999999983], [-1.3430000000000177, 0], [0, 1.3432999999999993], [0, 0]], "v": [[349.663, 59.2777], [352.094, 56.8472], [349.663, 54.4167], [347.233, 56.8472], [349.663, 59.2777]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 51", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 1.858699999999999], [1.858000000000004, 0], [0, -1.8592999999999975], [-1.8559999999999945, 0]], "o": [[1.856999999999971, 0], [0, -1.8585999999999956], [-1.858000000000004, 0], [0, 1.8595000000000041], [0, 0]], "v": [[339.776, 50.2758], [343.141, 46.9122], [339.776, 43.5466], [336.41, 46.9122], [339.776, 50.2758]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 52", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.8519999999999897], [-1.8519999999999754, 0], [0, 1.8520000000000039], [1.8520000000000323, 0]], "o": [[-1.8519999999999754, 0], [0, 1.8520000000000039], [1.8520000000000323, 0], [0, -1.8519999999999897], [0, 0]], "v": [[339.808, 120.029], [336.454, 123.383], [339.808, 126.736], [343.162, 123.383], [339.808, 120.029]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 53", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.293700000000001], [2.2939999999999827, 0], [0, -2.293199999999999], [-2.2940000000000396, 0]], "o": [[2.294999999999959, 0], [0, -2.2935000000000016], [-2.2930000000000064, 0], [0, 2.2933999999999983], [0, 0]], "v": [[327.432, 44.4219], [331.585, 40.2682], [327.432, 36.1167], [323.281, 40.2682], [327.432, 44.4219]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 54", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.4213999999999913], [3.420000000000016, 0], [0, -3.4213000000000022], [-3.4209999999999923, 0]], "o": [[3.420000000000016, 0], [0, -3.4213000000000022], [-3.4209999999999923, 0], [0, 3.4213999999999913], [0, 0]], "v": [[264.084, 84.3618], [270.279, 78.1671], [264.084, 71.9725], [257.888, 78.1671], [264.084, 84.3618]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 55", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.276299999999999], [3.276999999999987, 0], [0, -3.2768000000000015], [-3.2749999999999773, 0]], "o": [[3.2760000000000105, 0], [0, -3.2768000000000015], [-3.2760000000000105, 0], [0, 3.276299999999999], [0, 0]], "v": [[275.947, 59.0393], [281.879, 53.1089], [275.947, 47.1766], [270.016, 53.1089], [275.947, 59.0393]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 56", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [3.4190000000000396, 0], [0, -3.4192000000000036], [-3.419999999999959, 0], [0, 3.4210999999999956]], "o": [[0, -3.418999999999997], [-3.419999999999959, 0], [0, 3.421199999999999], [3.4190000000000396, 0], [0, 0]], "v": [[270.281, 92.1883], [264.09, 85.9966], [257.897, 92.1883], [264.09, 98.381], [270.281, 92.1883]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 57", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.3744000000000085], [3.3740000000000236, 0], [0, -3.374299999999991], [-3.375, 0]], "o": [[3.3740000000000236, 0], [0, -3.374299999999991], [-3.375, 0], [0, 3.3744000000000085], [0, 0]], "v": [[268.149, 70.8614], [274.259, 64.7511], [268.149, 58.6409], [262.039, 64.7511], [268.149, 70.8614]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 58", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.9092999999999947], [2.908999999999992, 0], [0, -2.9092000000000056], [-2.910000000000025, 0]], "o": [[2.908999999999992, 0], [0, -2.9092000000000056], [-2.910000000000025, 0], [0, 2.9092999999999947], [0, 0]], "v": [[299.735, 44.1337], [305.002, 38.8661], [299.735, 33.5986], [294.467, 38.8661], [299.735, 44.1337]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 59", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [3.3700000000000045, 0], [0, -3.3710000000000093], [-3.3700000000000045, 0], [0, 3.370999999999995]], "o": [[0, -3.3710000000000093], [-3.3700000000000045, 0], [0, 3.370999999999995], [3.3700000000000045, 0], [0, 0]], "v": [[274.265, 105.596], [268.163, 99.4931], [262.06, 105.596], [268.163, 111.699], [274.265, 105.596]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 60", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -3.11399999999999], [-3.113999999999976, 0], [0, 3.1140000000000043], [3.115000000000009, 0]], "o": [[-3.113999999999976, 0], [0, 3.1140000000000043], [3.115000000000009, 0], [0, -3.1129999999999995], [0, 0]], "v": [[286.816, 120.479], [281.176, 126.118], [286.816, 131.758], [292.455, 126.118], [286.816, 120.479]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 61", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 3.1218000000000004], [3.120999999999981, 0], [0, -3.122100000000003], [-3.1229999999999905, 0]], "o": [[3.120999999999981, 0], [0, -3.122100000000003], [-3.1229999999999905, 0], [0, 3.1218000000000004], [0, 0]], "v": [[286.786, 49.8713], [292.438, 44.2182], [286.786, 38.5661], [281.133, 44.2182], [286.786, 49.8713]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 62", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -3.271000000000001], [-3.2690000000000055, 0], [0, 3.2700000000000102], [3.269999999999982, 0]], "o": [[-3.2690000000000055, 0], [0, 3.2700000000000102], [3.269999999999982, 0], [0, -3.269999999999996], [0, 0]], "v": [[275.971, 111.316], [270.051, 117.237], [275.971, 123.157], [281.892, 117.237], [275.971, 111.316]]}}}, {"ty": "st", "bm": 0, "hd": false, "nm": "Stroke", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2.5}, "c": {"a": 0, "k": [1, 0.7804, 0.2196]}}, {"ty": "tr", "a": {"a": 0, "k": [302.575500488281, 89.94524574279785]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [302.575500488281, 89.94524574279785]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": false, "nm": "Logo11", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 63", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.9379999999999882], [-1.9379999999999882, 0], [0, 1.9370000000000118], [1.938000000000045, 0]], "o": [[-1.9379999999999882, 0], [0, 1.9370000000000118], [1.938000000000045, 0], [0, -1.9379999999999882], [0, 0]], "v": [[313.727, 129.287], [310.219, 132.795], [313.727, 136.303], [317.235, 132.795], [313.727, 129.287]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 64", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.2109999999999843], [-2.2110000000000127, 0], [0, 2.2110000000000127], [2.211999999999989, 0]], "o": [[-2.2110000000000127, 0], [0, 2.2110000000000127], [2.211999999999989, 0], [0, -2.2109999999999843], [0, 0]], "v": [[299.773, 127.458], [295.771, 131.462], [299.773, 135.465], [303.777, 131.462], [299.773, 127.458]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 65", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 1.9446999999999974], [1.9449999999999932, 0], [0, -1.9447000000000045], [-1.9440000000000168, 0]], "o": [[1.9449999999999932, 0], [0, -1.9447000000000045], [-1.9440000000000168, 0], [0, 1.9446999999999974], [0, 0]], "v": [[313.687, 41.0434], [317.209, 37.5217], [313.687, 34], [310.166, 37.5217], [313.687, 41.0434]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 66", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.5949999999999989], [-1.5950000000000273, 0], [0, 1.593999999999994], [1.5949999999999704, 0]], "o": [[-1.5950000000000273, 0], [0, 1.593999999999994], [1.5949999999999704, 0], [0, -1.5949999999999989], [0, 0]], "v": [[327.471, 127.147], [324.582, 130.036], [327.471, 132.924], [330.36, 130.036], [327.471, 127.147]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 67", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.6479999999999961], [-0.6480000000000246, 0], [0, 0.6490000000000009], [0.6490000000000009, 0]], "o": [[-0.6480000000000246, 0], [0, 0.6490000000000009], [0.6490000000000009, 0], [0, -0.6479999999999961], [0, 0]], "v": [[349.687, 112.264], [348.513, 113.438], [349.687, 114.612], [350.862, 113.438], [349.687, 112.264]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 68", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0.6529000000000025], [0.6519999999999868, 0], [0, -0.6518000000000015], [-0.6519999999999868, 0]], "o": [[0.6519999999999868, 0], [0, -0.6518000000000015], [-0.6519999999999868, 0], [0, 0.6529000000000025], [0, 0]], "v": [[349.663, 58.0277], [350.844, 56.8472], [349.663, 55.6667], [348.483, 56.8472], [349.663, 58.0277]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 69", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 1.1676000000000002], [1.1680000000000064, 0], [0, -1.1685999999999979], [-1.1680000000000064, 0]], "o": [[1.1680000000000064, 0], [0, -1.1685999999999979], [-1.1680000000000064, 0], [0, 1.1676000000000002], [0, 0]], "v": [[339.776, 49.0258], [341.891, 46.9122], [339.776, 44.7966], [337.66, 46.9122], [339.776, 49.0258]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 70", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.161999999999992], [-1.1619999999999777, 0], [0, 1.1610000000000014], [1.1610000000000014, 0]], "o": [[-1.1619999999999777, 0], [0, 1.1610000000000014], [1.1610000000000014, 0], [0, -1.161999999999992], [0, 0]], "v": [[339.808, 121.279], [337.704, 123.383], [339.808, 125.486], [341.912, 123.383], [339.808, 121.279]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 71", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 1.603900000000003], [1.603999999999985, 0], [0, -1.602800000000002], [-1.6030000000000086, 0]], "o": [[1.603999999999985, 0], [0, -1.602800000000002], [-1.6030000000000086, 0], [0, 1.603900000000003], [0, 0]], "v": [[327.432, 43.1719], [330.335, 40.2682], [327.432, 37.3667], [324.531, 40.2682], [327.432, 43.1719]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 72", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.730699999999999], [2.730000000000018, 0], [0, -2.730699999999999], [-2.7309999999999945, 0]], "o": [[2.730000000000018, 0], [0, -2.730699999999999], [-2.7309999999999945, 0], [0, 2.730699999999999], [0, 0]], "v": [[264.084, 83.1118], [269.029, 78.1671], [264.084, 73.2225], [259.138, 78.1671], [264.084, 83.1118]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 73", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.5855999999999995], [2.5860000000000127, 0], [0, -2.586599999999997], [-2.5849999999999795, 0]], "o": [[2.5860000000000127, 0], [0, -2.586599999999997], [-2.5849999999999795, 0], [0, 2.5855999999999995], [0, 0]], "v": [[275.947, 57.7893], [280.629, 53.1089], [275.947, 48.4266], [271.266, 53.1089], [275.947, 57.7893]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 74", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2.7280000000000086, 0], [0, -2.7287000000000035], [-2.7299999999999613, 0], [0, 2.730699999999999]], "o": [[0, -2.7287000000000035], [-2.7299999999999613, 0], [0, 2.730699999999999], [2.7280000000000086, 0], [0, 0]], "v": [[269.031, 92.1883], [264.09, 87.2466], [259.147, 92.1883], [264.09, 97.131], [269.031, 92.1883]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 75", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.6840000000000117], [2.684000000000026, 0], [0, -2.6839999999999904], [-2.684000000000026, 0]], "o": [[2.684000000000026, 0], [0, -2.6839999999999904], [-2.684000000000026, 0], [0, 2.6840000000000117], [0, 0]], "v": [[268.149, 69.6114], [273.009, 64.7511], [268.149, 59.8909], [263.289, 64.7511], [268.149, 69.6114]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 76", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.218999999999994], [2.218999999999994, 0], [0, -2.218900000000005], [-2.218999999999994, 0]], "o": [[2.218999999999994, 0], [0, -2.218900000000005], [-2.218999999999994, 0], [0, 2.218999999999994], [0, 0]], "v": [[299.735, 42.8837], [303.752, 38.8661], [299.735, 34.8486], [295.717, 38.8661], [299.735, 42.8837]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 77", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2.680000000000007, 0], [0, -2.6809999999999974], [-2.680000000000007, 0], [0, 2.6799999999999926]], "o": [[0, -2.6809999999999974], [-2.680000000000007, 0], [0, 2.6799999999999926], [2.680000000000007, 0], [0, 0]], "v": [[273.015, 105.596], [268.163, 100.743], [263.31, 105.596], [268.163, 110.449], [273.015, 105.596]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 78", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.423000000000002], [-2.423999999999978, 0], [0, 2.4240000000000066], [2.424000000000035, 0]], "o": [[-2.423999999999978, 0], [0, 2.4240000000000066], [2.424000000000035, 0], [0, -2.423000000000002], [0, 0]], "v": [[286.816, 121.729], [282.426, 126.118], [286.816, 130.508], [291.205, 126.118], [286.816, 121.729]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 79", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.431599999999996], [2.430000000000007, 0], [0, -2.431600000000003], [-2.4329999999999927, 0]], "o": [[2.430000000000007, 0], [0, -2.431600000000003], [-2.4329999999999927, 0], [0, 2.431599999999996], [0, 0]], "v": [[286.786, 48.6213], [291.188, 44.2182], [286.786, 39.8161], [282.383, 44.2182], [286.786, 48.6213]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 80", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.5799999999999983], [-2.5780000000000314, 0], [0, 2.5790000000000077], [2.579999999999984, 0]], "o": [[-2.5780000000000314, 0], [0, 2.5790000000000077], [2.579999999999984, 0], [0, -2.5799999999999983], [0, 0]], "v": [[275.971, 112.566], [271.301, 117.237], [275.971, 121.907], [280.642, 117.237], [275.971, 112.566]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [1, 0.7804, 0.2196]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [303.825500488281, 88.69524574279785]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [303.825500488281, 88.69524574279785]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "bm": 0, "hd": true, "nm": "Logo12", "it": [{"ty": "sh", "bm": 0, "hd": false, "nm": "Path 81", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.9379999999999882], [-1.9379999999999882, 0], [0, 1.9370000000000118], [1.938000000000045, 0]], "o": [[-1.9379999999999882, 0], [0, 1.9370000000000118], [1.938000000000045, 0], [0, -1.9379999999999882], [0, 0]], "v": [[313.727, 129.287], [310.219, 132.795], [313.727, 136.303], [317.235, 132.795], [313.727, 129.287]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 82", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.2109999999999843], [-2.2110000000000127, 0], [0, 2.2110000000000127], [2.211999999999989, 0]], "o": [[-2.2110000000000127, 0], [0, 2.2110000000000127], [2.211999999999989, 0], [0, -2.2109999999999843], [0, 0]], "v": [[299.773, 127.458], [295.771, 131.462], [299.773, 135.465], [303.777, 131.462], [299.773, 127.458]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 83", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 1.9446999999999974], [1.9449999999999932, 0], [0, -1.9447000000000045], [-1.9440000000000168, 0]], "o": [[1.9449999999999932, 0], [0, -1.9447000000000045], [-1.9440000000000168, 0], [0, 1.9446999999999974], [0, 0]], "v": [[313.687, 41.0434], [317.209, 37.5217], [313.687, 34], [310.166, 37.5217], [313.687, 41.0434]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 84", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.5949999999999989], [-1.5950000000000273, 0], [0, 1.593999999999994], [1.5949999999999704, 0]], "o": [[-1.5950000000000273, 0], [0, 1.593999999999994], [1.5949999999999704, 0], [0, -1.5949999999999989], [0, 0]], "v": [[327.471, 127.147], [324.582, 130.036], [327.471, 132.924], [330.36, 130.036], [327.471, 127.147]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 85", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.6479999999999961], [-0.6480000000000246, 0], [0, 0.6490000000000009], [0.6490000000000009, 0]], "o": [[-0.6480000000000246, 0], [0, 0.6490000000000009], [0.6490000000000009, 0], [0, -0.6479999999999961], [0, 0]], "v": [[349.687, 112.264], [348.513, 113.438], [349.687, 114.612], [350.862, 113.438], [349.687, 112.264]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 86", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0.6529000000000025], [0.6519999999999868, 0], [0, -0.6518000000000015], [-0.6519999999999868, 0]], "o": [[0.6519999999999868, 0], [0, -0.6518000000000015], [-0.6519999999999868, 0], [0, 0.6529000000000025], [0, 0]], "v": [[349.663, 58.0277], [350.844, 56.8472], [349.663, 55.6667], [348.483, 56.8472], [349.663, 58.0277]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 87", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 1.1676000000000002], [1.1680000000000064, 0], [0, -1.1685999999999979], [-1.1680000000000064, 0]], "o": [[1.1680000000000064, 0], [0, -1.1685999999999979], [-1.1680000000000064, 0], [0, 1.1676000000000002], [0, 0]], "v": [[339.776, 49.0258], [341.891, 46.9122], [339.776, 44.7966], [337.66, 46.9122], [339.776, 49.0258]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 88", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -1.161999999999992], [-1.1619999999999777, 0], [0, 1.1610000000000014], [1.1610000000000014, 0]], "o": [[-1.1619999999999777, 0], [0, 1.1610000000000014], [1.1610000000000014, 0], [0, -1.161999999999992], [0, 0]], "v": [[339.808, 121.279], [337.704, 123.383], [339.808, 125.486], [341.912, 123.383], [339.808, 121.279]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 89", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 1.603900000000003], [1.603999999999985, 0], [0, -1.602800000000002], [-1.6030000000000086, 0]], "o": [[1.603999999999985, 0], [0, -1.602800000000002], [-1.6030000000000086, 0], [0, 1.603900000000003], [0, 0]], "v": [[327.432, 43.1719], [330.335, 40.2682], [327.432, 37.3667], [324.531, 40.2682], [327.432, 43.1719]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 90", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.730699999999999], [2.730000000000018, 0], [0, -2.730699999999999], [-2.7309999999999945, 0]], "o": [[2.730000000000018, 0], [0, -2.730699999999999], [-2.7309999999999945, 0], [0, 2.730699999999999], [0, 0]], "v": [[264.084, 83.1118], [269.029, 78.1671], [264.084, 73.2225], [259.138, 78.1671], [264.084, 83.1118]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 91", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.5855999999999995], [2.5860000000000127, 0], [0, -2.586599999999997], [-2.5849999999999795, 0]], "o": [[2.5860000000000127, 0], [0, -2.586599999999997], [-2.5849999999999795, 0], [0, 2.5855999999999995], [0, 0]], "v": [[275.947, 57.7893], [280.629, 53.1089], [275.947, 48.4266], [271.266, 53.1089], [275.947, 57.7893]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 92", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2.7280000000000086, 0], [0, -2.7287000000000035], [-2.7299999999999613, 0], [0, 2.730699999999999]], "o": [[0, -2.7287000000000035], [-2.7299999999999613, 0], [0, 2.730699999999999], [2.7280000000000086, 0], [0, 0]], "v": [[269.031, 92.1883], [264.09, 87.2466], [259.147, 92.1883], [264.09, 97.131], [269.031, 92.1883]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 93", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.6840000000000117], [2.684000000000026, 0], [0, -2.6839999999999904], [-2.684000000000026, 0]], "o": [[2.684000000000026, 0], [0, -2.6839999999999904], [-2.684000000000026, 0], [0, 2.6840000000000117], [0, 0]], "v": [[268.149, 69.6114], [273.009, 64.7511], [268.149, 59.8909], [263.289, 64.7511], [268.149, 69.6114]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 94", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.218999999999994], [2.218999999999994, 0], [0, -2.218900000000005], [-2.218999999999994, 0]], "o": [[2.218999999999994, 0], [0, -2.218900000000005], [-2.218999999999994, 0], [0, 2.218999999999994], [0, 0]], "v": [[299.735, 42.8837], [303.752, 38.8661], [299.735, 34.8486], [295.717, 38.8661], [299.735, 42.8837]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 95", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [2.680000000000007, 0], [0, -2.6809999999999974], [-2.680000000000007, 0], [0, 2.6799999999999926]], "o": [[0, -2.6809999999999974], [-2.680000000000007, 0], [0, 2.6799999999999926], [2.680000000000007, 0], [0, 0]], "v": [[273.015, 105.596], [268.163, 100.743], [263.31, 105.596], [268.163, 110.449], [273.015, 105.596]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 96", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.423000000000002], [-2.423999999999978, 0], [0, 2.4240000000000066], [2.424000000000035, 0]], "o": [[-2.423999999999978, 0], [0, 2.4240000000000066], [2.424000000000035, 0], [0, -2.423000000000002], [0, 0]], "v": [[286.816, 121.729], [282.426, 126.118], [286.816, 130.508], [291.205, 126.118], [286.816, 121.729]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 97", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 2.431599999999996], [2.430000000000007, 0], [0, -2.431600000000003], [-2.4329999999999927, 0]], "o": [[2.430000000000007, 0], [0, -2.431600000000003], [-2.4329999999999927, 0], [0, 2.431599999999996], [0, 0]], "v": [[286.786, 48.6213], [291.188, 44.2182], [286.786, 39.8161], [282.383, 44.2182], [286.786, 48.6213]]}}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "Path 98", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -2.5799999999999983], [-2.5780000000000314, 0], [0, 2.5790000000000077], [2.579999999999984, 0]], "o": [[-2.5780000000000314, 0], [0, 2.5790000000000077], [2.579999999999984, 0], [0, -2.5799999999999983], [0, 0]], "v": [[275.971, 112.566], [271.301, 117.237], [275.971, 121.907], [280.642, 117.237], [275.971, 112.566]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "Fill", "c": {"a": 0, "k": [0.0078, 0.0078, 0.0078]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [303.825500488281, 88.69524574279785]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [303.825500488281, 88.69524574279785]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 4, "parent": 5}, {"ty": 4, "nm": "plane", "sr": 1, "st": 0, "op": 300.00001221925, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [69, 38.5, 0], "ix": 1}, "s": {"a": 0, "k": [100.00000000000003, 100.00000000000003], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [120.00000000000001, 100], "t": -1}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [120.00000000000001, 40], "t": 45}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [121.59970000000001, 47.578], "t": 55}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [120.00000000000001, 100], "t": 90}, {"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [120.00000000000001, 40], "t": 137}, {"s": [120.00000000000001, 100], "t": 177.000007209358}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.448, -0.009], [0, 0], [-3.309, 3.748], [0, 0], [13.367, -0.007]], "o": [[-0.362, 1.317], [0, 0], [4.679, -0.064], [0, 0], [-13.367, 0.006], [0, 0]], "v": [[-25.611, 14.476], [-23.335, 17.506], [-9.736, 17.029], [2.648, 10.797], [25.973, -17.506], [-14.464, -17.393]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [69.674, 58.685], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 2", "ix": 2, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.005, -0.704], [0, 0], [-0.701, -0.004], [0, 0], [-0.005, 0.701], [0, 0]], "o": [[0, 0], [-0.705, -0.004], [0, 0], [-0.004, 0.701], [0, 0], [0.701, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.249, -2.15], [-1.532, -0.883], [-1.542, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.529, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [-0.000500023365020752, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [104.08949997663498, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 3", "ix": 3, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.7, 0.004], [0, 0], [0.004, -0.704], [0, 0], [-0.701, -0.004], [0, 0], [-0.005, 0.701], [0, 0]], "o": [[0, 0], [-0.705, -0.004], [0, 0], [-0.005, 0.701], [0, 0], [0.701, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.248, -2.15], [-1.531, -0.883], [-1.541, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.528, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [99.114, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 4", "ix": 4, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.004, -0.704], [0, 0], [-0.701, -0.004], [0, 0], [-0.005, 0.701], [0, 0]], "o": [[0, 0], [-0.705, -0.004], [0, 0], [-0.004, 0.701], [0, 0], [0.701, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.249, -2.15], [-1.532, -0.883], [-1.542, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.528, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [94.138, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 5", "ix": 5, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.005, -0.704], [0, 0], [-0.7, -0.004], [0, 0], [-0.005, 0.701], [0, 0]], "o": [[0, 0], [-0.705, -0.004], [0, 0], [-0.004, 0.701], [0, 0], [0.701, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.249, -2.15], [-1.532, -0.883], [-1.542, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.529, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [89.162, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 6", "ix": 6, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.005, -0.704], [0, 0], [-0.701, -0.004], [0, 0], [-0.005, 0.701], [0, 0]], "o": [[0, 0], [-0.704, -0.004], [0, 0], [-0.005, 0.701], [0, 0], [0.701, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.249, -2.15], [-1.532, -0.883], [-1.54, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.529, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [84.185, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 7", "ix": 7, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.004, -0.704], [0, 0], [-0.701, -0.004], [0, 0], [-0.005, 0.701], [0, 0]], "o": [[0, 0], [-0.705, -0.004], [0, 0], [-0.005, 0.701], [0, 0], [0.702, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.248, -2.15], [-1.531, -0.883], [-1.541, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.528, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [79.209, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 8", "ix": 8, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.004, -0.704], [0, 0], [-0.701, -0.004], [0, 0], [-0.005, 0.701], [0, 0]], "o": [[0, 0], [-0.705, -0.004], [0, 0], [-0.004, 0.701], [0, 0], [0.701, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.249, -2.15], [-1.532, -0.883], [-1.542, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.529, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [74.233, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 9", "ix": 9, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.005, -0.704], [0, 0], [-0.701, -0.004], [0, 0], [-0.005, 0.701], [0, 0]], "o": [[0, 0], [-0.704, -0.004], [0, 0], [-0.004, 0.701], [0, 0], [0.701, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.249, -2.15], [-1.532, -0.883], [-1.542, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.529, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [69.257, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 10", "ix": 10, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.005, -0.704], [0, 0], [-0.701, -0.004], [0, 0], [-0.004, 0.701], [0, 0]], "o": [[0, 0], [-0.705, -0.004], [0, 0], [-0.004, 0.701], [0, 0], [0.701, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.249, -2.15], [-1.531, -0.883], [-1.542, 0.865], [-0.275, 2.146], [0.245, 2.15], [1.528, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [64.28, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 11", "ix": 11, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.004, -0.704], [0, 0], [-0.7, -0.004], [0, 0], [-0.004, 0.701], [0, 0]], "o": [[0, 0], [-0.704, -0.004], [0, 0], [-0.005, 0.701], [0, 0], [0.701, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.249, -2.15], [-1.531, -0.883], [-1.541, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.528, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [59.304, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 12", "ix": 12, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.005, -0.704], [0, 0], [-0.701, -0.004], [0, 0], [-0.004, 0.701], [0, 0]], "o": [[0, 0], [-0.704, -0.004], [0, 0], [-0.004, 0.701], [0, 0], [0.701, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.249, -2.15], [-1.532, -0.883], [-1.542, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.527, 0.883], [1.54, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [54.328, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 13", "ix": 13, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.004, -0.704], [0, 0], [-0.7, -0.004], [0, 0], [-0.004, 0.701], [0, 0]], "o": [[0, 0], [-0.705, -0.004], [0, 0], [-0.005, 0.701], [0, 0], [0.702, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.248, -2.15], [-1.531, -0.883], [-1.541, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.528, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [49.351, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 14", "ix": 14, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.005, -0.704], [0, 0], [-0.701, -0.004], [0, 0], [-0.005, 0.701], [0, 0]], "o": [[0, 0], [-0.704, -0.004], [0, 0], [-0.004, 0.701], [0, 0], [0.701, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.249, -2.15], [-1.532, -0.883], [-1.542, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.529, 0.883], [1.54, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [-0.001000046730041504, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [44.37399995326996, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": true, "mn": "ADBE Vector Group", "nm": "Group 15", "ix": 15, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.336, -0.006], [0, 0], [0, 0], [0, 0], [0, -0.337]], "o": [[0, 0], [0, 0], [0, 0], [0.336, 0.006], [0, 0.337]], "v": [[2.345, -0.087], [1.286, -0.087], [1.286, -1.324], [2.345, -1.324], [2.952, -0.708]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0.921], [0.92, 0.006], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -0.282], [-0.278, -0.016], [0, 0], [0, 0], [0, 0], [-0.292, 0], [0, 0.293], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.292, 0], [0, 0.293], [0, 0], [0, 0]], "o": [[0, -0.921], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.278, 0.016], [0, 0.282], [0, 0], [0, 0], [0, 0], [0, 0.293], [0.292, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0.293], [0.293, 0], [0, 0], [0, 0], [0.92, -0.007]], "v": [[4.011, -0.708], [2.345, -2.384], [1.286, -2.384], [0.226, -2.384], [-0.833, -2.384], [-1.893, -2.384], [-3.513, -2.384], [-3.513, -2.382], [-4.011, -1.854], [-3.513, -1.326], [-3.513, -1.324], [-1.893, -1.324], [-1.893, 1.854], [-1.363, 2.384], [-0.833, 1.854], [-0.833, -1.324], [0.226, -1.324], [0.226, -0.087], [0.226, 0.973], [0.226, 1.854], [0.756, 2.384], [1.286, 1.854], [1.286, 0.973], [2.345, 0.973]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.1098, 0.6784, 0.8784], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [21.84, 34.637], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": true, "mn": "ADBE Vector Group", "nm": "Group 16", "ix": 16, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.39, 0], [0, 0.39], [0.39, 0], [0, -0.39]], "o": [[0.39, 0], [0, -0.39], [-0.39, 0], [0, 0.39]], "v": [[0.001, 0.706], [0.706, 0.001], [0.001, -0.706], [-0.706, 0.001]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.1098, 0.6784, 0.8784], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [-0.7059999704360962, 0.7059999704360962], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [21.890000029563904, 31.721999970436094], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": true, "mn": "ADBE Vector Group", "nm": "Group 17", "ix": 17, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.39, 0], [0, 0.39], [0.39, 0], [0, -0.39]], "o": [[0.39, 0], [0, -0.39], [-0.39, 0], [0, 0.39]], "v": [[-0.001, 0.706], [0.706, 0.001], [-0.001, -0.706], [-0.706, 0.001]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.1098, 0.6784, 0.8784], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [-0.7059999704360962, 0.7059999704360962], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [19.772000029563905, 31.721999970436094], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 18", "ix": 18, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.701, 0.004], [0, 0], [0.005, -0.704], [0, 0], [-0.7, -0.004], [0, 0], [-0.004, 0.701], [0, 0]], "o": [[0, 0], [-0.705, -0.004], [0, 0], [-0.005, 0.701], [0, 0], [0.702, 0.005], [0, 0], [0.005, -0.701]], "v": [[0.275, -2.148], [-0.248, -2.15], [-1.531, -0.883], [-1.541, 0.865], [-0.275, 2.146], [0.244, 2.15], [1.528, 0.883], [1.541, -0.865]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [39.399, 34.079], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 19", "ix": 19, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.476, -5.572], [-0.648, -5.514], [-0.476, 5.536], [0.648, 5.573]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.749, 0.9098, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [-0.6480000019073486, 0.0004999637603759766], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [80.25299999809265, 59.586499963760374], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 20", "ix": 20, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.485, -4.915], [-0.536, -4.861], [-0.639, -4.854], [-0.486, 4.88], [-0.383, 4.885], [0.639, 4.915]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.749, 0.9098, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [64.89, 59.767], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 21", "ix": 21, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.348, -0.071], [0, 0], [-0.02, -1.352], [0, 0], [-1.349, -0.039], [0, 0], [0.02, 1.352], [0, 0]], "o": [[0, 0], [-1.349, 0.07], [0, 0], [0.023, 1.35], [0, 0], [1.352, 0.042], [0, 0], [-0.02, -1.351]], "v": [[8.812, -5.587], [-8.987, -4.658], [-11.404, -2.161], [-11.329, 2.674], [-8.834, 5.09], [8.986, 5.617], [11.404, 3.122], [11.305, -3.171]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.3843, 0.7529, 0.9961], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [73.34, 59.562], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 22", "ix": 22, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[-0.498, -1.021], [0, 0], [0.29, 1.342], [0, 0]], "o": [[0, 0], [-1.758, -0.975], [0, 0], [1.077, 0.637]], "v": [[4.062, 0.505], [-0.831, 2.426], [-4.062, -1.01], [1.206, -2.426]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [124.304, 32.448], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 23", "ix": 23, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[2.046, 1.858], [9.385, -0.367], [10.489, -0.01], [11.693, 0.135], [9.323, 2.897], [-1.329, -0.82], [-1.309, -0.585], [-4.496, -0.985], [-5.051, -0.228], [-4.945, 0.108], [-6.322, 0.138], [-6.463, 0.141], [0, 0], [-2.029, 0.045], [-3.613, 2.431]], "o": [[-8.751, 3.731], [-10.482, 0.412], [-11.694, 0.014], [-9.722, -0.121], [1.027, 1.158], [1.22, 0.753], [4.216, 1.885], [4.927, 1.078], [4.947, 0.223], [6.321, -0.137], [6.463, -0.14], [0, 0], [2.028, -0.044], [4.656, -0.412], [3.337, -2.244]], "v": [[60.964, -6.208], [33.179, -1.538], [1.718, -0.908], [-33.361, -1.171], [-62.423, -5.126], [-58.812, -2.171], [-55.007, -0.174], [-41.78, 3.818], [-26.907, 5.986], [-12.067, 5.63], [6.895, 5.217], [26.284, 4.794], [41.384, 4.467], [47.471, 4.333], [59.792, -0.232]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.749, 0.9098, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [72.154, 42.209], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 24", "ix": 24, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[4.656, -0.412], [2.028, -0.044], [0, 0], [6.463, -0.141], [6.321, -0.138], [4.947, 0.223], [4.927, 1.079], [4.216, 1.886], [1.22, 0.753], [0.768, 3.171], [-0.552, 0.04], [-34.268, -0.995], [-4.725, -3.946], [-0.456, -0.806], [5.137, -3.455]], "o": [[-2.029, 0.044], [0, 0], [-6.462, 0.141], [-6.321, 0.138], [-4.945, 0.107], [-5.051, -0.227], [-4.496, -0.985], [-1.309, -0.585], [-2.696, -1.662], [-0.117, -0.481], [34.053, -2.466], [4.078, 0.12], [0.778, 0.653], [5.694, 2.993], [-3.613, 2.43]], "v": [[48.912, 10.105], [42.825, 10.238], [27.725, 10.566], [8.336, 10.988], [-10.626, 11.402], [-25.466, 11.757], [-40.339, 9.589], [-53.566, 5.598], [-57.371, 3.6], [-63.24, -3.54], [-63.312, -8.798], [37.188, -10.984], [55.805, -5.665], [57.654, -3.484], [61.233, 5.54]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8471, 0.949, 0.9961], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0.13512802124023438, 0.1996455192565918], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [70.84812802124023, 36.63664551925659], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 25", "ix": 25, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-3.418, -0.735], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [2.467, 3.025], [0, 0], [0, 0]], "v": [[-6.389, 9.184], [-13.627, -11.174], [-9.471, -11.105], [3.424, 3.781], [13.627, 9.055], [-5.331, 11.174]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [13.877, 19.03], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 26", "ix": 26, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0.442, 0.141], [0, 0], [0, 0], [1.35, 0.622], [1.49, 1.311], [0, 0], [2.387, 2.1], [0.675, 0.015], [0, 0], [0, 0], [0, 0], [-0.001, 0], [0.294, 0.094]], "o": [[0, 0], [0, 0], [-1.417, -0.377], [-1.811, -0.834], [0, 0], [-2.386, -2.1], [-0.44, -0.511], [0, 0], [0, 0], [0, 0], [0, 0], [-0.315, 0.035], [-0.442, -0.14]], "v": [[16.566, 7.662], [19.082, 7.453], [8.172, 5.055], [3.998, 3.687], [-0.906, 0.31], [-5.469, -3.705], [-12.629, -10.005], [-14.381, -10.832], [-19.082, -10.912], [-11.844, 9.445], [-2.853, 10.912], [19.074, 8.46], [17.893, 8.084]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.749, 0.9098, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [19.332, 18.768], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 27", "ix": 27, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-11.915, -0.006], [0, 0], [4.171, 0.057], [0, 0], [-0.323, -1.174]], "o": [[11.915, 0.006], [0, 0], [-2.95, -3.341], [0, 0], [-1.291, 0.008], [0, 0]], "v": [[-12.893, 15.503], [23.152, 15.604], [2.361, -9.624], [-8.679, -15.18], [-20.801, -15.604], [-22.829, -12.904]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.5451, 0.8235, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [71.69, 15.855], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 5}, {"ty": 4, "nm": "Ã«Â ÂÃ¬ÂÂ´Ã¬ÂÂ´ 1/cloud Outlines 2", "sr": 1, "st": 0, "op": 300.00001221925, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [107.5, 50.5, 0], "ix": 1}, "s": {"a": 0, "k": [18.922, 16.916, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [265.394, 122, 0], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [195, 122, 0], "t": 32}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [84, 122, 0], "t": 50.948}, {"h": 1, "o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [-22, 122, 0], "t": 72}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [269, 122, 0], "t": 115}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [177, 122, 0], "t": 137}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [83.5, 122, 0], "t": 158}, {"s": [-23.5, 122, 0], "t": 177.000007209358}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [20], "t": 45}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30], "t": 92}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [20], "t": 136}, {"s": [30], "t": 176.000007168627}], "ix": 11}}, "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[2.02, 3.365], [22.213, -26.252], [3.365, 0], [6.731, -20.193], [4.039, 0], [-28.945, 0.673], [0, 0], [49.138, -3.366]], "o": [[-16.828, -29.617], [-2.02, 2.692], [-20.867, -4.039], [-0.673, 3.367], [-28.271, 2.692], [0, 0], [49.138, 0], [-4.039, 0]], "v": [[60.155, -25.262], [-22.638, -32.666], [-31.388, -29.301], [-79.853, -1.031], [-87.93, 5.028], [-83.891, 50.127], [71.598, 50.127], [69.579, -19.877]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [1, 1, 1], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [116.451, 59.168], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 6}, {"ty": 4, "nm": "Ã«Â ÂÃ¬ÂÂ´Ã¬ÂÂ´ 1/cloud Outlines 3", "sr": 1, "st": 0, "op": 300.00001221925, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [107.5, 50.5, 0], "ix": 1}, "s": {"a": 0, "k": [15, 15, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [304.067, 34.5, 0], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [232.50000000000003, 34.5, 0], "t": 45}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [71.5, 34.5, 0], "t": 111}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [-28.499999999999996, 34.5, 0], "t": 147}, {"s": [-102.49999999999999, 34.5, 0], "t": 177.000007209358}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [20], "t": 45}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30], "t": 111}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [20], "t": 147}, {"s": [30], "t": 177.000007209358}], "ix": 11}}, "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[2.02, 3.365], [22.213, -26.252], [3.365, 0], [6.731, -20.193], [4.039, 0], [-28.945, 0.673], [0, 0], [49.138, -3.366]], "o": [[-16.828, -29.617], [-2.02, 2.692], [-20.867, -4.039], [-0.673, 3.367], [-28.271, 2.692], [0, 0], [49.138, 0], [-4.039, 0]], "v": [[60.155, -25.262], [-22.638, -32.666], [-31.388, -29.301], [-79.853, -1.031], [-87.93, 5.028], [-83.891, 50.127], [71.598, 50.127], [69.579, -19.877]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [1, 1, 1], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [116.451, 59.168], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 7}, {"ty": 4, "nm": "Ã«Â ÂÃ¬ÂÂ´Ã¬ÂÂ´ 1/cloud Outlines", "sr": 1, "st": 0, "op": 300.00001221925, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [119, 55.5, 0], "ix": 1}, "s": {"a": 0, "k": [25.598, 22.883, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [352.067, 14.000000000000002, 0], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [280.5, 14.000000000000002, 0], "t": 45}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [119.5, 14.000000000000002, 0], "t": 111}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [19.5, 14.000000000000002, 0], "t": 147}, {"s": [-54.50000000000001, 14.000000000000002, 0], "t": 177.000007209358}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [20], "t": 45}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30], "t": 111}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [20], "t": 147}, {"s": [30], "t": 177.000007209358}], "ix": 11}}, "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[2.02, 3.365], [22.213, -26.252], [3.365, 0], [6.731, -20.193], [4.039, 0], [-28.945, 0.673], [0, 0], [49.138, -3.366]], "o": [[-16.828, -29.617], [-2.02, 2.692], [-20.867, -4.039], [-0.673, 3.367], [-28.271, 2.692], [0, 0], [49.138, 0], [-4.039, 0]], "v": [[60.155, -25.262], [-22.638, -32.666], [-31.388, -29.301], [-79.853, -1.031], [-87.93, 5.028], [-83.891, 50.127], [71.598, 50.127], [69.579, -19.877]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [1, 1, 1], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [116.451, 59.168], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 8}, {"ty": 4, "nm": "loader1 Outlines", "sr": 1, "st": 0, "op": 300.00001221925, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [49, 13.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [310.738, 126, 0], "t": 23}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [232.73799999999997, 126, 0], "t": 39}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [140.738, 126, 0], "t": 55}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [60.738, 126, 0], "t": 71}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [2.738, 126, 0], "t": 87}, {"h": 1, "o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [-64.262, 126, 0], "t": 104}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [297.738, 126, 0], "t": 121}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [182.738, 126, 0], "t": 137}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [75.738, 126, 0], "t": 152}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [-23.262, 126, 0], "t": 165}, {"s": [-84.262, 126, 0], "t": 177.000007209358}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 50, "ix": 11}}, "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.104, 0], [0, 0], [0, 1.104], [-1.104, 0], [0, 0], [0, -1.104]], "o": [[0, 0], [-1.104, 0], [0, -1.104], [0, 0], [1.104, 0], [0, 1.104]], "v": [[26.5, 2], [-26.5, 2], [-28.5, 0], [-26.5, -2], [26.5, -2], [28.5, 0]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8471, 0.949, 0.9961], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [28.5, 25], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 2", "ix": 2, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.104, 0], [0, 0], [0, 1.104], [-1.104, 0], [0, 0], [0, -1.104]], "o": [[0, 0], [-1.104, 0], [0, -1.104], [0, 0], [1.104, 0], [0, 1.104]], "v": [[35, 2], [-35, 2], [-37, 0], [-35, -2], [35, -2], [37, 0]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.749, 0.9098, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [61, 11], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 3", "ix": 3, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.104, 0], [0, 0], [0, 1.104], [-1.104, 0], [0, 0], [0, -1.104]], "o": [[0, 0], [-1.104, 0], [0, -1.104], [0, 0], [1.104, 0], [0, 1.104]], "v": [[28.5, 2], [-28.5, 2], [-30.5, 0], [-28.5, -2], [28.5, -2], [30.5, 0]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8471, 0.949, 0.9961], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [41.5, 2], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 9}, {"ty": 4, "nm": "loader1 Outlines", "sr": 1, "st": 0, "op": 300.00001221925, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [49, 13.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [310.738, 126, 0], "t": 23}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [232.73799999999997, 126, 0], "t": 39}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [140.738, 126, 0], "t": 55}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [60.738, 126, 0], "t": 71}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [2.738, 126, 0], "t": 87}, {"h": 1, "o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [-64.262, 126, 0], "t": 104}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [297.738, 126, 0], "t": 121}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [182.738, 126, 0], "t": 137}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [75.738, 126, 0], "t": 152}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [-23.262, 126, 0], "t": 165}, {"s": [-84.262, 126, 0], "t": 177.000007209358}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 50, "ix": 11}}, "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.104, 0], [0, 0], [0, 1.104], [-1.104, 0], [0, 0], [0, -1.104]], "o": [[0, 0], [-1.104, 0], [0, -1.104], [0, 0], [1.104, 0], [0, 1.104]], "v": [[26.5, 2], [-26.5, 2], [-28.5, 0], [-26.5, -2], [26.5, -2], [28.5, 0]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8471, 0.949, 0.9961], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [28.5, 25], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 2", "ix": 2, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.104, 0], [0, 0], [0, 1.104], [-1.104, 0], [0, 0], [0, -1.104]], "o": [[0, 0], [-1.104, 0], [0, -1.104], [0, 0], [1.104, 0], [0, 1.104]], "v": [[35, 2], [-35, 2], [-37, 0], [-35, -2], [35, -2], [37, 0]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.749, 0.9098, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [61, 11], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 3", "ix": 3, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.104, 0], [0, 0], [0, 1.104], [-1.104, 0], [0, 0], [0, -1.104]], "o": [[0, 0], [-1.104, 0], [0, -1.104], [0, 0], [1.104, 0], [0, 1.104]], "v": [[28.5, 2], [-28.5, 2], [-30.5, 0], [-28.5, -2], [28.5, -2], [30.5, 0]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8471, 0.949, 0.9961], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [41.5, 2], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 10}, {"ty": 4, "nm": "wind Outlines", "sr": 1, "st": 0, "op": 300.00001221925, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [49, 13.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [309.5, 17, 0], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [226.5, 17, 0], "t": 14}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [122.50000000000001, 17, 0], "t": 30}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [25.5, 17, 0], "t": 46}, {"h": 1, "o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [-54.50000000000001, 17, 0], "t": 63}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [303.5, 17, 0], "t": 80}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [223.5, 17, 0], "t": 97}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [125.49999999999999, 17, 0], "t": 114}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [26.5, 17, 0], "t": 132}, {"s": [-55.50000000000001, 17, 0], "t": 149.000006068894}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 50, "ix": 11}}, "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.104, 0], [0, 0], [0, 1.104], [-1.104, 0], [0, 0], [0, -1.104]], "o": [[0, 0], [-1.104, 0], [0, -1.104], [0, 0], [1.104, 0], [0, 1.104]], "v": [[26.5, 2], [-26.5, 2], [-28.5, 0], [-26.5, -2], [26.5, -2], [28.5, 0]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8471, 0.949, 0.9961], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [28.5, 25], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 2", "ix": 2, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.104, 0], [0, 0], [0, 1.104], [-1.104, 0], [0, 0], [0, -1.104]], "o": [[0, 0], [-1.104, 0], [0, -1.104], [0, 0], [1.104, 0], [0, 1.104]], "v": [[35, 2], [-35, 2], [-37, 0], [-35, -2], [35, -2], [37, 0]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.749, 0.9098, 0.9922], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [61, 11], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 3", "ix": 3, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.104, 0], [0, 0], [0, 1.104], [-1.104, 0], [0, 0], [0, -1.104]], "o": [[0, 0], [-1.104, 0], [0, -1.104], [0, 0], [1.104, 0], [0, 1.104]], "v": [[28.5, 2], [-28.5, 2], [-30.5, 0], [-28.5, -2], [28.5, -2], [30.5, 0]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.8471, 0.949, 0.9961], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [41.5, 2], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 11}], "v": "5.7.0", "fr": 30, "op": 180, "ip": 0, "assets": [], "markers": [{"cm": "0", "tm": 48, "dr": 2}, {"cm": "5", "tm": 60, "dr": 2}, {"cm": "1", "tm": 76, "dr": 2}, {"cm": "2", "tm": 176, "dr": 2}]}