{"project_info": {"project_number": "801144692639", "project_id": "tripc-dev", "storage_bucket": "tripc-dev.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:801144692639:android:70c169eb105180b9aaa7aa", "android_client_info": {"package_name": "com.tripc.ai.app.dev"}}, "oauth_client": [{"client_id": "801144692639-18t3118ffaqh4i7j0q3qfrig301md7je.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.tripc.ai.app.dev", "certificate_hash": "eef0627382ae209b0864b6866ba2bbafdb1ee023"}}, {"client_id": "801144692639-oncl1dhsbn4vvpa43fsj1ma2trba4968.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.tripc.ai.app.dev", "certificate_hash": "0753b489f2c3ede1ded975483eb1c827161ccee0"}}, {"client_id": "801144692639-m4jrht9m6v6d2kss6r6951jhhfg3ger3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBqcMtQYcepcYTEbZ6XGu4p-uKtqtLeEcI"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "801144692639-m4jrht9m6v6d2kss6r6951jhhfg3ger3.apps.googleusercontent.com", "client_type": 3}, {"client_id": "801144692639-jngqur56t0qbqp0o90uoe32u657fotl0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.tripc.ai.app.dev"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:801144692639:android:17c32c664518f6a6aaa7aa", "android_client_info": {"package_name": "com.tripc.app.dev"}}, "oauth_client": [{"client_id": "801144692639-m4jrht9m6v6d2kss6r6951jhhfg3ger3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBqcMtQYcepcYTEbZ6XGu4p-uKtqtLeEcI"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "801144692639-m4jrht9m6v6d2kss6r6951jhhfg3ger3.apps.googleusercontent.com", "client_type": 3}, {"client_id": "801144692639-jngqur56t0qbqp0o90uoe32u657fotl0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.tripc.ai.app.dev"}}]}}}], "configuration_version": "1"}