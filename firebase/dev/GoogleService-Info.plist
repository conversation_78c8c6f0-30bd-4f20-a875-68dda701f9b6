<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>801144692639-jngqur56t0qbqp0o90uoe32u657fotl0.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.801144692639-jngqur56t0qbqp0o90uoe32u657fotl0</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>801144692639-18t3118ffaqh4i7j0q3qfrig301md7je.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyDMNNpdJrDh2GItS41rsGa46nR1E4KE9Kg</string>
	<key>GCM_SENDER_ID</key>
	<string>801144692639</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.tripc.ai.app.dev</string>
	<key>PROJECT_ID</key>
	<string>tripc-dev</string>
	<key>STORAGE_BUCKET</key>
	<string>tripc-dev.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:801144692639:ios:0c46a48be86ebcaeaaa7aa</string>
</dict>
</plist>