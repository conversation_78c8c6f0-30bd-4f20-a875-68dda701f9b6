<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>504724078390-7bbvdpesip5bcmuvra0bpt2ft99tjumf.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.504724078390-7bbvdpesip5bcmuvra0bpt2ft99tjumf</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>504724078390-k1lrdn59nucj7v99jpp0jg34hj62p9jv.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyCUiSbMhsV1njFr88vvxuq8wsOMRfIKtbk</string>
	<key>GCM_SENDER_ID</key>
	<string>504724078390</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.tripc.ai.app</string>
	<key>PROJECT_ID</key>
	<string>tripctripc-1f600</string>
	<key>STORAGE_BUCKET</key>
	<string>tripctripc-1f600.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:504724078390:ios:f710da5dc37d1f55103d97</string>
</dict>
</plist>