#!/usr/bin/env bash
#
# copy_firebase_config.sh
#
# Usage:
#   ./copy_firebase_config.sh dev
#   ./copy_firebase_config.sh prod
#
# Assumptions (adjust paths if your project structure differs):
#   ├─ firebase/
#   │   ├─ dev/
#   │   │   ├─ google-services.json
#   │   │   └─ GoogleService-Info.plist
#   │   └─ prod/
#   │       ├─ google-services.json
#   │       └─ GoogleService-Info.plist
#   ├─ android/
#   │   └─ app/
#   │       └─ google-services.json        ← (will be overwritten)
#   └─ ios/
#       └─ Runner/
#           └─ GoogleService-Info.plist     ← (will be overwritten)

set -euo pipefail

FLAVOR="$1"

# Root of your Flutter project (this script assumes you run it from project root)
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

case "$FLAVOR" in
  dev)
    SRC_JSON="$PROJECT_ROOT/firebase/dev/google-services.json"
    ;;
  prod)
    SRC_JSON="$PROJECT_ROOT/firebase/prod/google-services.json"
    ;;
  *)
    echo "Usage: $0 {dev|prod}"
    exit 1
    ;;
esac

DEST_ANDROID="$PROJECT_ROOT/android/app/google-services.json"

echo "Copying Firebase config for flavor '$FLAVOR'..."
echo "  → Android:  $SRC_JSON  →  $DEST_ANDROID"
cp "$SRC_JSON" "$DEST_ANDROID"
echo "Done."
