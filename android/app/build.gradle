plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

// Default debug keystore info
def keyAliasDefault = 'tripc'
def keyPasswordDefault = 'com.tripc.app'
def storePasswordDefault = 'com.tripc.app'
def storeFileDefault = 'keystore/release.keystore'

def debugKeyAliasDefault = 'androiddebugkey'
def debugKeyPasswordDefault = 'android'
def debugStorePasswordDefault = 'android'
// def debugStoreFileDefault = 'keystore/debug.keystore'
def debugStoreFileDefault = "${System.properties['user.home']}/.android/debug.keystore"

// Load a specific properties file to access the keystore if exists
def keystoreProperties = new Properties()
def keystorePropertiesFile = file('../key.properties')
if (keystorePropertiesFile.exists()) {
    println "Apply key.properties file"
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
} else {
    // Load keystore properties from environment or set their default values
    println "key.properties file was not found. Use env or default value instead."
    def env = System.getenv()
    if (env['ANDROID_KEY_ALIAS']) keystoreProperties.put('keyAlias', env['ANDROID_KEY_ALIAS'])
    else keystoreProperties.put('keyAlias', keyAliasDefault)
    if (env['ANDROID_KEY_PASSWORD']) keystoreProperties.put('keyPassword', env['ANDROID_KEY_PASSWORD'])
    else keystoreProperties.put('keyPassword', keyPasswordDefault)
    if (env['ANDROID_STORE_PASSWORD']) keystoreProperties.put('storePassword', env['ANDROID_STORE_PASSWORD'])
    else keystoreProperties.put('storePassword', storePasswordDefault)
    if (env['ANDROID_STORE_FILE']) keystoreProperties.put('storeFile', env['ANDROID_STORE_FILE'])
    else keystoreProperties.put('storeFile', storeFileDefault)
}

android {
    namespace = "com.tripc.ai.app"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.tripc.ai.app"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        // minSdk = flutter.minSdkVersion
        minSdkVersion 24
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        debug {
            keyAlias debugKeyAliasDefault
            keyPassword debugKeyPasswordDefault
            storePassword debugStorePasswordDefault
            storeFile file(debugStoreFileDefault)
        }
        release {
            keyAlias keystoreProperties.keyAlias
            keyPassword keystoreProperties.keyPassword
            storePassword keystoreProperties.storePassword
            storeFile file(keystoreProperties.storeFile)
        }
    }

    buildTypes {
        // release {
        //     // TODO: Add your own signing config for the release build.
        //     // Signing with the debug keys for now, so `flutter run --release` works.
        //     signingConfig = signingConfigs.debug
        // }
        debug {
            debuggable true
            signingConfig signingConfigs.debug
        }
        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }

    flavorDimensions "app"

    productFlavors {
        dev {
            dimension "app"
            applicationIdSuffix ".dev"
            resValue "string", "app_name", "TripC App Dev"
        }

        prod {
            dimension "app"
            resValue "string", "app_name", "TripC App"
        }
    }

    applicationVariants.all { variant ->
        def flavor = variant.flavorName
        def buildType = variant.buildType.name
        def capitalizedVariant = "${flavor.capitalize()}${buildType.capitalize()}"
        def copyTaskName = "copy${capitalizedVariant}FirebaseConfig"

        if (tasks.findByName(copyTaskName) == null) {
            tasks.create(name: copyTaskName, type: Exec) {
                def os = org.gradle.internal.os.OperatingSystem.current()
                def scriptFile
                if (os.isWindows()) {
                    scriptFile = "${rootProject.rootDir}/copy_firebase_config.bat"
                    println "Running .bat script: ${scriptFile}"
                    commandLine "cmd", "/c", scriptFile, flavor
                } else {
                    // scriptFile = "${rootProject.rootDir}/copy_firebase_config.sh"
                    scriptFile = "${rootDir}/../copy_firebase_config.sh"
                    println "🐧 Running .sh script: ${scriptFile}"
                    commandLine "bash", scriptFile, flavor
                }
            }
        }
        variant.preBuild.dependsOn(copyTaskName)
    }

//    applicationVariants.all { variant ->
//        def flavor = variant.flavorName
//        def buildType = variant.buildType.name
//        def variantName = "${flavor}${buildType.capitalize()}"
//        def copyTaskName = "copy${variantName}GoogleServices"
//
//        def copyTask = tasks.register(copyTaskName, Copy) {
//            def sourceFile = file("${rootDir}/firebase/${flavor}/google-services.json")
//            def targetFile = file("${projectDir}/google-services.json")
//            from sourceFile.parent
//            include sourceFile.name
//            into targetFile.parent
//            rename sourceFile.name, "google-services.json"
//        }
//
//        tasks.whenTaskAdded { task ->
//            if (task.name == "compileFlutterBuild${variantName.capitalize()}") {
//                task.dependsOn(copyTask)
//            }
//        }
//    }
}

flutter {
    source = "../.."
}

apply plugin: 'com.google.gms.google-services'
dependencies {
    implementation 'androidx.appcompat:appcompat:1.4.0'
}