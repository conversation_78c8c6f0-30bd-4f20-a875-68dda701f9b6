allprojects {
    repositories {
        google()
        mavenCentral()
    }
    subprojects {
        project.buildDir = "${rootProject.buildDir}/${project.name}"
        afterEvaluate {
            // check if android block is available

            if (it.hasProperty('android')) {

                if (it.android.namespace == null) {
                    def manifest = new XmlSlurper().parse(file(it.android.sourceSets.main.manifest.srcFile))
                    def packageName = <EMAIL>()
                    println("Setting ${packageName} as android namespace")
                    android.namespace = packageName
                }

                def javaVersion = JavaVersion.VERSION_1_8
                android {
                    def androidApiVersion = 34
                    compileSdkVersion androidApiVersion
                    defaultConfig {
                        targetSdkVersion androidApiVersion
                    }
                    compileOptions {
                        sourceCompatibility javaVersion
                        targetCompatibility javaVersion
                    }
                    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                        kotlinOptions {
                            jvmTarget = javaVersion.toString()
                        }
                    }
                    println("Setting java version to ${javaVersion.toString()} which is $javaVersion")
                    println("Setting compileSdkVersion and targetSdkVersion to $androidApiVersion")
                }
            }

        }

    }
    subprojects {
        project.evaluationDependsOn(':app')
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        // START: FlutterFire Configuration
        classpath 'com.google.gms:google-services:4.3.14'
        // END: FlutterFire Configuration
    }
}