@echo off
setlocal enabledelayedexpansion

REM Get the flavor argument (e.g., dev or prod)
set FLAVOR=%1

REM Resolve script location
set SCRIPT_DIR=%~dp0
REM Move up 1 level to project root (because script is in android/)
cd /d "%SCRIPT_DIR%\.."

set PROJECT_ROOT=%cd%

REM Select source file based on flavor
if "%FLAVOR%"=="dev" (
    set SRC_JSON=%PROJECT_ROOT%\firebase\dev\google-services.json
) else if "%FLAVOR%"=="prod" (
    set SRC_JSON=%PROJECT_ROOT%\firebase\prod\google-services.json
) else (
    echo Invalid flavor: %FLAVOR%
    exit /b 1
)

set DEST_JSON=%PROJECT_ROOT%\android\app\google-services.json

echo Copying Firebase config for flavor '%FLAVOR%'...
echo   → Android:  %SRC_JSON% → %DEST_JSON%

if not exist "%SRC_JSON%" (
    echo ❌ Source file does not exist: %SRC_JSON%
    exit /b 1
)

copy /Y "%SRC_JSON%" "%DEST_JSON%" >nul

echo ✅ Copy Firebase Config Done.
exit /b 0
