import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tripc_app/generated/l10n.dart';
import 'package:tripc_app/services/apis/interceptors/authentication_interceptor.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/app/app_theme.dart';
import 'package:tripc_app/services/providers/provider_locale.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/status_display_notifier.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/extensions/theme_extension.dart';

Future<void> myMain() async {
  /// Start services later
  WidgetsFlutterBinding.ensureInitialized();

  /// Firebase initialize
  await Firebase.initializeApp();

  /// Force portrait mode
  await SystemChrome.setPreferredOrientations(
      <DeviceOrientation>[DeviceOrientation.portraitUp]);
  // final SharedPreferences prefs = await SharedPreferences.getInstance();

  /// Cache
  // await CacheSearch.instance.ensureInitialized();
  // await CacheReportAndBlock.instance.ensureInitialized();
  // await CachePush.instance.ensureInitialized();
  // await CacheRetention.instance.ensureInitialized();
  // await CacheDoppleCreation.instance.ensureInitialized();

  /// Time zone
  // await AppPush.instance.configureLocalTimeZone();

  /// Run Application
  runApp(
    const ProviderScope(child: MyApp()),
  );
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});
  static AppCustomColor customSelectedColor = customThemes.first;

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  bool _isPushedNoInternetScreen = false;

  @override
  void initState() {
    super.initState();

    // TODO: change this stream when internet has problems not change connect systems state
    Connectivity().onConnectivityChanged.listen((result) {
      final hasInternet = result.last != ConnectivityResult.none;
      AuthenticationInterceptor.internetMonitoringStream.add(!hasInternet);
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      AuthenticationInterceptor.internetMonitoringStream.stream
          .listen((isNoInternet) {
        if (!isNoInternet || _isPushedNoInternetScreen) {
          return;
        }
        AppRoute.navigateToRoute(AppRoute.I.navigatorKey.currentContext!,
            routeName: AppRoute.routeNoInternet, resultHandler: (result) {
          setState(() {
            _isPushedNoInternetScreen = false;
          });
        });
        setState(() {
          _isPushedNoInternetScreen = true;
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final AppRoute appRoute = AppRoute.I;
    globalReleaseStatusNotifier.setDisplay(false);
    final LocaleProvider localeProvider = ref.watch(pLocaleProvider);
    appRoute.routeObserver.ref = ref;
    final AppTheme appTheme = ref.appTheme();
    return Portal(
      child: ScreenUtilInit(
        designSize: const Size(393, 852),
        builder: (BuildContext context, __) => MediaQuery(
          data:
              MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
          child: MaterialApp(
            // themeMode: ThemeMode.system,
            navigatorKey: appRoute.navigatorKey,
            locale: localeProvider.locale,
            supportedLocales: S.delegate.supportedLocales,
            localizationsDelegates: const <LocalizationsDelegate<dynamic>>[
              S.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
            ],
            debugShowCheckedModeBanner: false,
            theme: appTheme.buildThemeData().copyWith(
              extensions: <ThemeExtension<dynamic>>[_appCustomColor],
            ),
            initialRoute: AppRoute.routeSplashScreen,
            //  isFirstTimeOnApp ?? true
            //     ? AppRoute.routeSplashScreen
            //     : !globalCacheAuth.isLogged()
            //         ? AppRoute.routeHome
            //         : hasNullPasscode
            //             ? hasInviteCode
            //                 ? AppRoute.routeMembershipCode
            //                 : AppRoute.routeMembershipSignIn
            //             : AppRoute.routeHome,
            onGenerateRoute: (RouteSettings settings) {
              return appRoute.generateRoute(
                settings: settings,
                isAuthenticated: true,
              );
            },
            navigatorObservers: <NavigatorObserver>[appRoute.routeObserver],
          ),
        ),
      ),
    );
  }

  /// Determines the first screen after splash
  // Widget _getInitialScreen() {
  //   return isFirstTimeOnApp ?? true
  //       ? Navigator(
  //           key: GlobalKey<NavigatorState>(),
  //           initialRoute: AppRoute.routeTutorial,
  //           onGenerateRoute: (settings) => AppRoute.I.generateRoute(
  //             settings: settings,
  //             isAuthenticated: true,
  //           ),
  //         )
  //       : Navigator(
  //           key: GlobalKey<NavigatorState>(),
  //           initialRoute: AppRoute.routeSignIn,
  //           onGenerateRoute: (settings) => AppRoute.I.generateRoute(
  //             settings: settings,
  //             isAuthenticated: true,
  //           ),
  //         );
  // }

  /// App custom color
  AppCustomColor get _appCustomColor {
    // final search = ref.watch(pChatSettingProvider);
    // final configs = ref.watch(pTabbarProvider);
    // if (configs.currentIndex == 3) {
    //   MyApp.customSelectedColor = search.chatTheme.getThemeByName;
    //   return MyApp.customSelectedColor;
    // }
    MyApp.customSelectedColor = customThemes.last;
    return customThemes.last;
  }
}
