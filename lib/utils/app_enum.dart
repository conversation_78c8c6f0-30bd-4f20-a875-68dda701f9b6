enum FerryType {
  catBaTuanChau,
  tuanChauCatBa,
}

extension FerryTypeHelpers on FerryType {
  static FerryType? fromIndex(int? i) {
    if (i == null) return null;
    return (i >= 0 && i < FerryType.values.length)
        ? FerryType.values[i]
        : null;
  }

  String get slug {
    switch (this) {
      case FerryType.catBaTuanChau:
        return 'tuyen-pha-cat-ba-tuan-chau';
      case FerryType.tuanChauCatBa:
        return 'tuyen-pha-tuan-chau-cat-ba';
      }
  }
}
