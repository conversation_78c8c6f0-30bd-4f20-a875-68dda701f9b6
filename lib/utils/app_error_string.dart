import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

class ErrorParser {
  static String getErrorMessage(String error, BuildContext context) {
    switch (error) {
      case 'errors.phone.alreadyinuse':
        return context.strings.text_phone_already_issue;
      case 'errors.email.alreadyinuse':
        return context.strings.text_email_already_issue;
      case 'errors.otp.expired':
        return context.strings.text_otp_expired;
      case 'errors.email.notfound':
        return context.strings.text_email_not_found;
      case 'errors.membership_passcode_attempt.attempts_exhausted':
        return context.strings.text_block_type_passcode;
      case 'errors.otp.incorrect':
        return context.strings.text_otp_incorrect;
      case 'errors.order.out_of_stock':
        return context.strings.text_out_of_stock;
      default:
        return error;
    }
  }
}
