import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:tripc_app/generated/l10n.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/app/tripc_persistent_tab_type.dart';
import 'package:tripc_app/pages/homepage/views/tripc_homepage.dart';
import 'package:tripc_app/pages/profile/tripc_profile.dart';
import 'package:tripc_app/pages/my_trip/views/my_trip_main_page.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_config.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/app/app_theme.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/comming_soon/tripc_comming_soon.dart';
import 'package:tripc_app/widgets/extensions/common_extension.dart';

/// This function's used importing app_extension.dart file quickly.
/// Just type appEx and enter, the IDE will import this file automatically,
/// then you can delete this line, it's up to you.
void appExtension() {}

class AppExtension {
  const AppExtension._();
  static AppExtension get instanche => const AppExtension._();

  void unfocusKeyBoard() => FocusManager.instance.primaryFocus?.unfocus();

  static final _emailRegex = RegExp(
      r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$');
  static final _validCharacters = RegExp(r'^[a-zA-Z0-9_\-=@,\.;]+$');

  static bool isEmailAndUsernameValidated(
      {required String email, required String userName}) {
    final emailValidated = email.isNotEmpty && _emailRegex.hasMatch(email);
    final userNameValidated =
        userName.isNotEmpty && _validCharacters.hasMatch(userName);

    return emailValidated && userNameValidated;
  }

  static String? getValidateErrorMessage(
      {required String email, required String userName}) {
    if (email.isNotEmpty) {
      if (!_emailRegex.hasMatch(email)) {
        return 'Email must valid';
      }
    }
    if (userName.isNotEmpty) {
      if (!_validCharacters.hasMatch(userName)) {
        return 'Username must not contain special characters';
      }
    }
    return null;
  }

  static String? isEmailPassed(String email) {
    if (email.isNotEmpty) {
      if (!_emailRegex.hasMatch(email)) {
        return 'Email must valid';
      }
    }
    return null;
  }

  static bool isEmailValidated(String email) {
    return email.isNotEmpty && _emailRegex.hasMatch(email);
  }

  static String? isNoSpecialCharPassed(String input) {
    if (input.isNotEmpty) {
      if (!_validCharacters.hasMatch(input)) {
        return 'Username must not contain special characters';
      }
    }
    return null;
  }

  static String convertPhoneNumber(String phoneNumber) {
    if (phoneNumber.contains(' ')) {
      return phoneNumber.replaceAll(' ', '');
    } else if (phoneNumber.contains('-')) {
      return phoneNumber.replaceAll('-', '');
    } else {
      return phoneNumber;
    }
  }
}

extension TruncateDoublesX on double {
  double truncateToDecimalPlaces(int fractionalDigits) =>
      (this * pow(10, fractionalDigits)).truncate() / pow(10, fractionalDigits);
}

extension StringParseUriX on String {
  Uri toUri() => Uri.parse(this);
}

extension StringX on String {
  String get capitalizeFirstLetter {
    if (isEmpty) return this;
    return this[0].toUpperCase() + substring(1);
  }

  String get firstCharacter => isNotEmpty ? this[0] : '';

  String get removeLastCharacter =>
      isNotEmpty && endsWith('s') ? substring(0, length - 1) : this;

  bool isGreetingMessage(String text) => this == text;

  bool get isSvgImage => endsWith('.svg');
}

extension StringCasingExtension on String {
  String toSentenceCase() {
    if (isEmpty) return this;
    final lowercased = toLowerCase();
    return lowercased[0].toUpperCase() + lowercased.substring(1);
  }
}

extension StringXOrNull on String? {
  Widget buildAvatarThumbnailByTypeImage() {
    if (this == null || this?.isEmpty == true) {
      return const SizedBox.shrink();
    }
    return this!.contains('.svg')
        ? SvgPicture.network(this!)
        : BaseCachedNetworkImage(imageUrl: this!);
  }
}

extension ImageUrlX on String {
  String get getUrlByPattern => contains('builtin-avatar')
      ? 'https://dopple-site-dev.vercel.app/${replaceAll('.svg', '.png')}'
      : this;

  String get svgToPng => replaceAll('.svg', '.png');
}

extension ScrollControllerX on ScrollController {
  void scrollToTop() {
    animateTo(
      0,
      curve: Curves.decelerate,
      duration: const Duration(milliseconds: 300),
    );
  }
}

extension ContentRatingX on int {
  String getNameRating() {
    switch (this) {
      case 0:
        return 'Everyone';
      case 1:
        return 'Teen';
      default:
        return 'Max';
    }
  }
}

extension StringHashing on String {
  String get sha256HexDecode {
    try {
      final cleanString = replaceAll(RegExp(r'[^a-fA-F0-9]'), '');
      final bytes = List<int>.generate(cleanString.length ~/ 2,
          (i) => int.parse(cleanString.substring(i * 2, i * 2 + 2), radix: 16));
      return utf8.decode(bytes);
    } catch (e) {
      return '';
    }
  }
}

extension StringXtension on String {
  String shortenID({
    int prefixLength = 5,
  }) {
    if (length < prefixLength) {
      return this;
    }
    return replaceRange(prefixLength, length, '***');
  }

  DateTime? parseExactlyTime() {
    try {
      return DateTime.parse(this);
    } catch (e) {
      return null;
    }
  }

  String getTextByCasing(TextCaseType type) => switch (type) {
        TextCaseType.normal => toCapitalized(),
        TextCaseType.upper => toUpperCase(),
        TextCaseType.lower => toLowerCase(),
        TextCaseType.title => toTitleCase(),
        TextCaseType.none => this,
      };

  String get domainUrl {
    return FlavorConfig.instance.env.apiBaseUrl;
  }

  DateTime get hhmm => DateFormat('HH:mm').parse(this);
}

extension WidgetRefExt on WidgetRef {
  AppTheme appTheme() {
    return read(pAppThemeProvider).theme;
  }

  NavigatorState? navigator() {
    return AppRoute.I.navigatorKey.currentState;
  }
}

extension BuildContextExt on BuildContext {
  S get strings => S.of(this);
  MediaQueryData get mediaQuery => MediaQuery.of(this);
  ThemeData get theme => Theme.of(this);
  double get spacingBottom => Platform.isAndroid
      ? mediaQuery.padding.bottom + 15.H
      : mediaQuery.padding.bottom;
}

extension IntX on int {
  String formatCount({
    bool upperCaseSuffix = false,
  }) {
    double value = toDouble();
    String suffix;

    if (value >= 1e9) {
      value /= 1e9;
      suffix = upperCaseSuffix ? 'b'.toUpperCase() : 'b';
    } else if (value >= 1e6) {
      value /= 1e6;
      suffix = upperCaseSuffix ? 'm'.toUpperCase() : 'm';
    } else if (value >= 1e3) {
      value /= 1e3;
      suffix = upperCaseSuffix ? 'k'.toUpperCase() : 'k';
    } else {
      suffix = '';
    }

    final String formattedValue = value.toStringAsFixed(suffix.isEmpty ? 0 : 1);
    return '$formattedValue$suffix';
  }

  String get dayLeft {
    if (this > 1) {
      return '$this days left';
    }
    return '$this day left';
  }

  String get hourLeft {
    if (this > 1) {
      return '$this hours left';
    }
    return '$this hour left';
  }

  TripCPersistentTabType get toPersistentTabType =>
      TripCPersistentTabType.values[this];
}

/// Extension for screen util
extension SizeExtension on num {
  /// setWidth
  double get W => w.toDouble();

  /// setHeight
  double get H => h.toDouble();

  /// Set sp with default allowFontScalingSelf = false
  /// ignore: non_constant_identifier_names
  double get SP => sp.toDouble();

  /// % of screen width
  /// ignore: non_constant_identifier_names
  double get SW => sw.toDouble();

  /// % of screen height
  /// ignore: non_constant_identifier_names
  double get SH => sh.toDouble();
}

extension DoubleEx on double {
  String get formatMoney {
    return '${NumberFormat('#,##0.00').format(toDouble())} MYR';
  }

  String get secondformatMoney {
    return '${NumberFormat('#,##0.00').format(toDouble())} MYR';
  }

  String get separator {
    return NumberFormat('#,##0.00').format(toDouble());
  }
}

extension IntEx on int {
  String get formatTripcId {
    final text = toString();
    if (text.length == 10) {
      return '${text.substring(0, 2)} ${text.substring(2, 6)} ${text.substring(6, 10)}';
    }
    return text;
  }

  String get covertResultCountDown {
    final minute = this ~/ 60;
    final second = this % 60;
    return '${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}s';
  }

  String get covertResultCountDown2 {
    final minute = this ~/ 60;
    final second = this % 60;
    return '${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
  }

  String get qttText {
    return toString().padLeft(2, '0');
  }

  String get plusTcent => '+ $separator TCent';
  String get tcent => '$separator TCent';

  int get tcentToVND {
    final value = this * 1000;
    return value;
  }

  int get vndToTcent {
    final value = (this / 10000).toInt();
    return value;
  }

  String get vnd => '$separator VND';
  String get vndong => '$separator VNĐ';
  String get vndLower => '${separator}vnd';
  String get vndongLower => '${separator}vnđ';
  String get d => '$separatorđ';
  double get dollaFromPoint {
    return double.parse(NumberFormat("#.##").format(this / 200));
  }

  String get convertNumberToVietnamese {
    if (this == 0) return "không";

    List<String> parts = [];
    int billions = this ~/ 1000000000;
    int millions = (this % 1000000000) ~/ 1000000;
    int thousands = (this % 1000000) ~/ 1000;
    int remainder = this % 1000;

    if (billions > 0) parts.add("${convertThreeDigits(billions)} tỷ");
    if (millions > 0) parts.add("${convertThreeDigits(millions)} triệu");
    if (thousands > 0) parts.add("${convertThreeDigits(thousands)} nghìn");
    if (remainder > 0) parts.add(convertThreeDigits(remainder));
    return '${parts.join(" ")} đồng'.capitalizeFirstLetter;
  }

  String convertThreeDigits(int number) {
    Map<int, String> units = {
      0: "không",
      1: "một",
      2: "hai",
      3: "ba",
      4: "bốn",
      5: "năm",
      6: "sáu",
      7: "bảy",
      8: "tám",
      9: "chín"
    };
    int hundreds = number ~/ 100;
    int tens = (number % 100) ~/ 10;
    int ones = number % 10;

    List<String> parts = [];
    if (hundreds > 0) parts.add("${units[hundreds]} trăm");
    if (tens > 1) {
      parts.add("${units[tens]} mươi");
    } else if (tens == 1) {
      parts.add('mười');
    }

    if (tens > 1 && ones == 1) {
      parts.add("mốt");
    } else if (ones == 5 && tens > 0) {
      parts.add("lăm");
    } else {
      parts.add(units[ones]!);
    }

    return parts.join(" ");
  }

  String get discountSymbol {
    return '$this%';
  }

  String get thousand {
    if (this < 1000) {
      return toString();
    }
    return '${(this / 1000).toStringAsFixed(1)}k';
  }

  String get formatPoint {
    return '${NumberFormat.decimalPattern().format(this)} P';
  }

  int disCountPrice(int percent) {
    return this - this * percent ~/ 100;
  }

  int disCount(int percent) {
    return this * percent ~/ 100;
  }

  String get separator {
    return NumberFormat('#,##0', 'de_DE').format(toDouble());
  }

  String get separatorNoDigit {
    return NumberFormat.decimalPattern().format(this);
  }

  String get percent {
    return '${toString().padLeft(2, '0')}%';
  }

  String get formatMoney {
    return '${NumberFormat('#,##0.00').format(toDouble())} MYR';
  }

  String get secondformatMoney {
    return '${NumberFormat('#,##0.00').format(toDouble())} MYR';
  }

  String get secondFormatPoint {
    return '${NumberFormat.decimalPattern().format(this)} points';
  }
}

extension doubleEx on double {
  String get disCount {
    return '${toStringAsFixed(1)}%';
  }
}

enum TimeZone { utc, local }

extension DatetimeEX on DateTime? {
  String get toHHmm {
    if (isNotNull) {
      return DateFormat('HH:mm').format(this!);
    }
    return '';
  }

  String get formatddMMYYY {
    if (this == null) {
      return '';
    }
    return DateFormat('dd/MM/yyyy').format(this!);
  }

  String get ddYY {
    if (this == null) {
      return '';
    }
    return DateFormat('MM/yy').format(this!);
  }
}

/// Extension for DateTime
extension DateTimeExtension on DateTime {
  int countDayLeft(DateTime target) {
    final days = difference(target).inDays >= 0 ? difference(target).inDays : 0;
    return days;
  }

  String get ddYY {
    return DateFormat('MM/yy').format(this);
  }

  String get ddMMyyyy {
    return DateFormat('dd-MM-yyyy').format(this);
  }

  String get mmmmYYYY {
    return DateFormat('MMMM yyyy').format(this);
  }

  String get hourAndDate {
    return DateFormat("HH:mm, d 'thg' M, yyyy").format(this);
  }

  String get hhmmAndDate {
    return DateFormat("HH:mm dd/MM/yyyy").format(this);
  }

  String get dateTimeVi {
    return DateFormat("EEE, d 'thg' M", 'vi').format(this);
  }

  String get weekday {
    return DateFormat('E').format(this);
  }

  String toTime(BuildContext context) =>
      TimeOfDay.fromDateTime(asLocal()).format(context);

  /// Return DateTime with zero millisecond and microsecond
  DateTime resetMillisecond() {
    return DateTime(year, month, day, hour, minute, second);
  }

  String get hhmm => DateFormat('HH:mm').format(this);

  DateTime daysBefore(int days) => subtract(Duration(days: days));

  DateTime daysAfter(int days) => add(Duration(days: days));

  DateTime nextDayStart() => onlyDate().daysAfter(1);

  DateTime localTimeToday() => DateTime.now().let((DateTime now) => DateTime(
      now.year,
      now.month,
      now.day,
      hour,
      minute,
      second,
      millisecond,
      microsecond));

  DateTime clone() => DateTime(
      year, month, day, hour, minute, second, millisecond, microsecond);

  DateTime atDate({int? y, int? m, int? d}) =>
      DateTime(y ?? year, m ?? month, d ?? day);

  DateTime nextYear() => clone().atDate(y: year + 1);

  DateTime nextMonth() => clone().atDate(m: month + 1);

  DateTime onlyDate() =>
      isUtc ? DateTime.utc(year, month, day) : DateTime(year, month, day);

  DateTime onlyMonth() =>
      isUtc ? DateTime.utc(year, month) : DateTime(year, month);

  DateTime onlyTime([int? hourLocal, int? minuteLocal]) =>
      DateTime.utc(1970, 1, 1, hourLocal ?? hour, minuteLocal ?? minute, 0, 0, 0);

  DateTime atTime(int hour, int minute, [int? second]) =>
      DateTime(year, month, day, hour, minute, second ?? 0, 0, 0);

  DateTime utcTimeFirstDaySinceEpoch() =>
      DateTime.utc(1970, 1, 1, hour, minute, second, millisecond, microsecond);

  // Convert local time as current utc
  // DateTime.now() = 2021-01-25 18:49:03.049422
  // DateTime.asUtc() = 2021-01-25 18:49:03.049422
  // DateTime.toUtc() = 2021-01-25 11:49:03.056208Z
  DateTime asUtc() => isUtc
      ? this
      : DateTime.utc(
          year, month, day, hour, minute, second, millisecond, microsecond);

  DateTime asLocal() => !isUtc
      ? this
      : DateTime(
          year, month, day, hour, minute, second, millisecond, microsecond);

  /// 2020-04-03T11:57:00
  String toDateTimeString() {
    return DateFormat('yyyy-MM-ddThh:mm:ss').format(this);
  }

  String toYYYY_MM_DD() {
    return DateFormat('yyyy.MM.dd').format(this);
  }

  String toMM_DD() {
    return DateFormat('MM-dd').format(this);
  }

  String toMM_DD_hhmm() {
    return DateFormat('MM-dd-EEE HH:mm').format(this);
  }

  String toMMMdyyyy() {
    return DateFormat('MMM d, yyyy').format(this);
  }

  String toYY_MM_DD() {
    return DateFormat('yy.MM.dd').format(this);
  }

  String formatyyyyMMdd() {
    return DateFormat('yyyy-MM-dd').format(this);
  }

  String formatDDMMYYY() {
    return DateFormat('dd-MM-yyyy').format(this);
  }

  String formatddMMYYY() {
    return DateFormat('dd/MM/yyyy').format(this);
  }

  String formatHHmmssddMMyy() {
    return DateFormat('HH:mm:ss dd/MM/yy').format(this);
  }

  /// 11:00 AM
  String toHmaa() {
    return DateFormat('hh:mm aaaa').format(this);
  }

  String toHourMinuteString() {
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }
}

/// Extension for DateTime from String
extension DateTimeStringExtendsion on String {
  /// Check Null or Empty
  bool get isNullOrEmpty => isNull || isEmpty;

  /// Convert from 24h UTC to local DateTime by pattern
  /// https://docs.oracle.com/javase/7/docs/api/java/text/SimpleDateFormat.html
  DateTime? toDateTime(String s, {String? pattern = 'yyyy-MM-dd HH:mm:ss'}) {
    return isNullOrEmpty
        ? null
        : DateFormat(pattern).parse(this, true).toLocal();
  }

  DateTime? toDateTimeISO({String? pattern = 'yyyy-MM-ddTHH:mm:ss.SSSSSSZ'}) {
    return isNullOrEmpty
        ? null
        : DateFormat(pattern).parse(this, true).toLocal();
  }

  String get toFormattedDate {
    final dateTime = DateTime.parse(this);
    return DateFormat('dd/MM/yyyy').format(dateTime);
  }

  String get convertHHMMSSddMMYYYY {
    final dateTime = DateTime.parse(this);
    return DateFormat('HH:mm:ss dd/MM/yyyy').format(dateTime);
  }

  String get convertToDDMMYYY {
    return replaceAll('-', '.');
  }

  DateTime get convertToDateTime {
    return DateFormat('yyyy-MM-dd').parse(this);
  }

  DateTime get convertToDateTimeWithTime {
    return DateFormat('HH:mm:ss').parse(this);
  }

  String zeroPrefix(int count) {
    if (length >= count) {
      return this;
    } else {
      String builder = '';
      for (int i = 0; i < count - length; i++) {
        builder += '0';
      }
      builder += this;
      return builder;
    }
  }

  String removeLeadingZero() {
    if (length == 10 && RegExp(r'^\d+$').hasMatch(this)) {
      if (startsWith('0')) {
        return substring(1);
      }
    }
    return this;
  }

  String addLeadingZero() {
    if (length == 9 && RegExp(r'^\d+$').hasMatch(this)) {
      return '0$this';
    }
    return this;
  }

  int? parseInt() => int.tryParse(this);

  double? parseDouble() => double.tryParse(this);

  String truncate(int limit) {
    return length > limit
        ? '${substring(0, min(length, limit)).trim()}...'
        : this;
  }
}

/// Extension for duration
extension DurationExtension on Duration {
  String format() {
    return toString().split('.').first.padLeft(8, '0');
  }

  /// Add zero padding
  String _twoDigits(int n) {
    if (n >= 10) {
      return '$n';
    }
    return '0${max(n, 0)}';
  }

  /// hours:minutes:seconds
  String toHms() {
    final String twoDigitHours = _twoDigits(inHours.remainder(24));
    final String twoDigitMinutes = _twoDigits(inMinutes.remainder(60));
    final String twoDigitSeconds = _twoDigits(inSeconds.remainder(60));
    return '$twoDigitHours:$twoDigitMinutes:$twoDigitSeconds';
  }

  /// minutes:seconds
  String toMs() {
    final String twoDigitMinutes = _twoDigits(inMinutes.remainder(60));
    final String twoDigitSeconds = _twoDigits(inSeconds.remainder(60));
    return '$twoDigitMinutes:$twoDigitSeconds';
  }
}

/// Extension for int
extension DateExtensions on int {
  DateTime localDateTime() =>
      DateTime.fromMillisecondsSinceEpoch(this, isUtc: false);

  DateTime utcDateTime() =>
      DateTime.fromMillisecondsSinceEpoch(this, isUtc: true);

  DateTime asDateTime({TimeZone from = TimeZone.utc}) {
    switch (from) {
      case TimeZone.local:
        return localDateTime();
      case TimeZone.utc:
      default:
        return utcDateTime();
    }
  }

  DateTime asLocal({TimeZone from = TimeZone.utc}) =>
      asDateTime(from: from).asLocal();

  String toTime(BuildContext context, {TimeZone from = TimeZone.utc}) =>
      asDateTime(from: from).toTime(context);

  int localTimeToday({TimeZone from = TimeZone.utc}) =>
      asDateTime(from: from).localTimeToday().millisecondsSinceEpoch;

  int onlyDate({TimeZone from = TimeZone.utc}) =>
      asDateTime(from: from).onlyDate().millisecondsSinceEpoch;

  int onlyTime({TimeZone from = TimeZone.utc}) =>
      asDateTime(from: from).utcTimeFirstDaySinceEpoch().millisecondsSinceEpoch;

  int utcTimeFirstDaySinceEpoch({TimeZone from = TimeZone.utc}) =>
      asDateTime(from: from).utcTimeFirstDaySinceEpoch().millisecondsSinceEpoch;

  Duration asDuration() => Duration(milliseconds: this);
}

extension StringEXT on String {
  DateTime parseDateTime() {
    DateTime utcDateTime = DateTime.parse(this).toLocal();
    return utcDateTime;
  }

  String get hiddenEmail {
    final split = this.split('@');
    if (split.isNotEmpty) {
      if (split.first.length > 2) {
        return '${split.first.substring(0, 2)}****@${split.last}';
      }
      return this;
    }
    return this;
  }

  String get firstName {
    return splitMapJoin(
      RegExp(r'\S+$'),
      onMatch: (match) => "",
      onNonMatch: (nonMatch) {
        return nonMatch.trim();
      },
    );
  }

  String get lastName {
    return splitMapJoin(
      RegExp(r'\S+$'),
      onNonMatch: (nonMatch) => "",
    );
  }

  String get spilitName {
    if (firstName.isEmpty || lastName.isEmpty) {
      return this;
    }
    return '$firstName / $lastName';
  }

  String get formatPhoneNumber {
    if (length == 10) {
      return '${substring(0, 4)}.${substring(4, 7)}.${substring(7, 10)}';
    }
    return this;
  }

  String get getFormatBirthday {
    return replaceAll('-', '');
  }

  String get secondFormatBirthday {
    if (isEmpty) {
      return '';
    } else if (length != 8) {
      return this;
    }
    String year = substring(0, 4);
    String month = substring(4, 6);
    String day = substring(6, 8);

    return '$year.$month.$day';
  }
}

/// Extension for num
extension NumExtensions<T extends num> on T {
  T plus(T value) {
    return this + value as T;
  }
}

/// Extension for T
extension AnyExtensions<T> on T {
  /// Check Null
  bool get isNull => this == null;

  bool get isNotNull => this != null;

  T safe(T Function() supplier) => this ?? supplier();
}

/// Extension for Object
extension Lambdas<T> on T {
  T also(void Function(T it) fun) {
    fun(this);
    return this;
  }

  R let<R>(R Function(T it) fun) => fun(this);
}

/// Extension for Comparable on Iterable
extension ComparableIterableExtensions<E extends Comparable<E>> on Iterable<E> {
  List<E> sorted() => toList()..sort();

  E? min() {
    final Iterator<E> iterator = this.iterator;
    if (!iterator.moveNext()) {
      return null;
    }
    E min = iterator.current;
    while (iterator.moveNext()) {
      final E e = iterator.current;
      if (min > e) {
        min = e;
      }
    }
    return min;
  }

  E? max() {
    final Iterator<E> iterator = this.iterator;
    if (!iterator.moveNext()) {
      return null;
    }
    E max = iterator.current;
    while (iterator.moveNext()) {
      final E e = iterator.current;
      if (max < e) {
        max = e;
      }
    }
    return max;
  }
}

/// Extension for Set
extension SetExtensions<E> on Set<E> {
  Set<E> filterNotNull() => where((E it) => it != null).toSet();
}

/// Extension for Map
extension MapExtensions<K, V> on Map<K, V> {
  V? get(K key) => this[key];

  V? getOrDefault(K key, V value) => containsKey(key) ? this[key] : value;
}

/// Extension for Iterable of Iterable
extension IterableIterableExtensions<E> on Iterable<Iterable<E>> {
  List<E> flatten() => expand((Iterable<E> it) => it).toList(growable: false);
}

/// Extension for Comparable
extension ComparableExtensions<T> on Comparable<T> {
  bool operator >(T value) => compareTo(value) == 1;

  bool operator >=(T value) =>
      compareTo(value).let((int it) => it == 1 || it == 0);

  bool operator <(T value) => compareTo(value) == -1;

  bool operator <=(T value) =>
      compareTo(value).let((int it) => it == -1 || it == 0);
}

/// Extension for Iterable
extension IterableExtensions<E> on Iterable<E> {
  /// Returns the first element.
  ///
  /// Returns `null` if `this` is empty.
  /// Otherwise returns the first element in the iteration order
  // E? get firstOrNull => iterator.let((Iterator<E> it) => !it.moveNext() ? null : it.current);

  // E? get lastOrNull {
  //   final Iterator<E> it = iterator;
  //   if (!it.moveNext()) {
  //     return null;
  //   }
  //   E result;
  //   do {
  //     result = it.current;
  //   } while (it.moveNext());
  //   return result;
  // }

  /// Returns the first element that satisfies the given predicate [test].
  ///
  /// Iterates through elements and returns the first to satisfy [test].
  ///
  /// If no element satisfies [test], the result returns `null`
  E? find(bool Function(E element) test) {
    for (final E element in this) {
      if (test(element)) {
        return element;
      }
    }
    return null;
  }

  List<E> filter(bool Function(E element) test) => where(test).toList(growable: false);

  List<E> filterNotNull() =>
      where((E it) => it != null).toList(growable: false);

  E? minBy<R extends Comparable<R>>(R Function(E element) selector) {
    final Iterator<E> iterator = this.iterator;
    if (!iterator.moveNext()) {
      return null;
    }
    E minElem = iterator.current;
    if (!iterator.moveNext()) {
      return minElem;
    }
    R minValue = selector(minElem);
    do {
      final E e = iterator.current;
      final R v = selector(e);
      if (minValue > v) {
        minElem = e;
        minValue = v;
      }
    } while (iterator.moveNext());
    return minElem;
  }

  E? minWith(int Function(E a, E b) compare) {
    final Iterator<E> iterator = this.iterator;
    if (!iterator.moveNext()) {
      return null;
    }
    E min = iterator.current;
    while (iterator.moveNext()) {
      final E e = iterator.current;
      if (compare(min, e) > 0) {
        min = e;
      }
    }
    return min;
  }

  /// Returns the first element yielding the largest value of the given function or `null` if there are no elements.
  ///
  /// @sample samples.collections.Collections.Aggregates.maxBy
  E? maxBy<R extends Comparable<R>>(R Function(E element) selector) {
    final Iterator<E> iterator = this.iterator;
    if (!iterator.moveNext()) {
      return null;
    }
    E maxElem = iterator.current;
    if (!iterator.moveNext()) {
      return maxElem;
    }
    R maxValue = selector(maxElem);
    do {
      final E e = iterator.current;
      final R v = selector(e);
      if (maxValue < v) {
        maxElem = e;
        maxValue = v;
      }
    } while (iterator.moveNext());
    return maxElem;
  }

  /// Returns the first element having the largest value according to the provided [comparator] or `null` if there are no elements.
  E? maxWith(int Function(E a, E b) compare) {
    final Iterator<E> iterator = this.iterator;
    if (!iterator.moveNext()) {
      return null;
    }
    E max = iterator.current;
    while (iterator.moveNext()) {
      final E e = iterator.current;
      if (compare(max, e) < 0) {
        max = e;
      }
    }
    return max;
  }

  int sumBy(int Function(E element) fun) {
    int sum = 0;
    forEach((E it) => sum += fun(it));
    return sum;
  }

  String joinToString(
      {String separator = '',
      String prefix = '',
      String postfix = '',
      String Function(E element)? transform}) {
    String result = prefix;

    bool first = true;
    forEach((E it) {
      if (!first) {
        result += separator;
      } else {
        first = false;
      }
      result += transform == null ? '$it' : transform(it);
    });

    result += postfix;
    return result;
  }

  Future<List<R>> asyncMap<R>(FutureOr<R> Function(E element) fun) async {
    final List<R> items = <R>[];
    for (final E it in this) {
      items.add(await fun(it));
    }
    return items;
  }
}

/// Extension for List
extension ListExtensions<E> on List<E> {
  E get(int index) => this[index];

  void set(int index, E element) => this[index] = element;

  E? getOrNull(int index) => index < 0 || index >= length ? null : this[index];

  void forEachIndexed(void Function(int index, E element) f) {
    for (int i = 0; i < length; i++) {
      f(i, this[i]);
    }
  }

  List<R> mapIndexed<R>(R Function(int index, E value) transform) =>
      mapIndexedTo<R, List<R>>(<R>[], transform);

  C mapIndexedTo<R, C extends List<R>>(
      C destination, R Function(int index, E value) transform) {
    forEachIndexed(
        (int index, E element) => destination.add(transform(index, element)));
    return destination;
  }

  int get lastIndex => length - 1;

  List<E> reversedList() => reversed.toList(growable: false);

  List<E> shuffled([Random? random]) =>
      toList(growable: false)..shuffle(random);

  List<E> sorted(int Function(E a, E b) compare) =>
      toList(growable: false)..sort(compare);

  List<E> sortedBy<T extends Comparable<T>>(T Function(E value) selector) =>
      toList(growable: false)
        ..sort((E a, E b) => selector(a).compareTo(selector(b)));

  void moveAt(int oldIndex, int index) {
    final E item = this[oldIndex];
    removeAt(oldIndex);
    insert(index, item);
  }

  void move(int index, E item) {
    remove(item);
    insert(index, item);
  }

  int indexOfItem(E element, Iterable<int> exclude) {
    for (int i = 0; i < length; i++) {
      if (!exclude.contains(i) && this[i] == element) {
        return i;
      }
    }
    return -1;
  }

  int indexOfWhere(bool Function(int index, E element) test) {
    for (int i = 0; i < length; i++) {
      if (test(i, this[i])) {
        return i;
      }
    }
    return -1;
  }

  static List<E> insertBetween<E>(E delimiter, List<E> tokens) {
    final List<E> sb = <E>[];
    bool firstTime = true;
    for (final E token in tokens) {
      if (firstTime) {
        firstTime = false;
      } else {
        sb.add(delimiter);
      }
      sb.add(token);
    }
    return sb;
  }
}

void unfocusKeyboard() {
  FocusManager.instance.primaryFocus?.unfocus();
}

extension TripCPersistentTabTypeX on TripCPersistentTabType {
  Widget getInactiveIcon() => switch (this) {
        TripCPersistentTabType.home => AppAssets.init.icHome.widget(
            color: AppAssets.init.tabbarDisableColor,
            fit: BoxFit.cover,
            width: 30.W,
            height: 30.H,
          ),
        TripCPersistentTabType.explore => AppAssets.init.icExplore.widget(
            color: AppAssets.init.tabbarDisableColor,
            fit: BoxFit.cover,
            width: 30.W,
            height: 30.H,
          ),
        TripCPersistentTabType.mytrip => AppAssets.init.icMyTrip.widget(
            color: AppAssets.init.tabbarDisableColor,
            fit: BoxFit.cover,
            width: 30.W,
            height: 30.H,
          ),
        TripCPersistentTabType.loyalty => AppAssets.init.icLoyalty.widget(
            color: AppAssets.init.tabbarDisableColor,
            fit: BoxFit.cover,
            width: 30.W,
            height: 30.H,
          ),
        TripCPersistentTabType.profile => AppAssets.init.icProfile.widget(
            color: AppAssets.init.tabbarDisableColor,
            fit: BoxFit.cover,
            width: 30.W,
            height: 30.H,
          ),
      };

  Widget getIcon() => switch (this) {
        TripCPersistentTabType.home => AppAssets.init.icHomeFilled.widget(
            color: AppAssets.init.tabbarEnableColor,
            fit: BoxFit.cover,
            width: 30.W,
            height: 30.H,
          ),
        TripCPersistentTabType.explore => AppAssets.init.icExploreFilled.widget(
            color: AppAssets.init.tabbarEnableColor,
            fit: BoxFit.cover,
            width: 30.W,
            height: 30.H,
          ),
        TripCPersistentTabType.mytrip => AppAssets.init.icMyTrip.widget(
            color: Colors.white,
            fit: BoxFit.cover,
            width: 30.W,
            height: 30.H,
          ),
        TripCPersistentTabType.loyalty => AppAssets.init.icLoyaltyFilled.widget(
            color: AppAssets.init.tabbarEnableColor,
            fit: BoxFit.cover,
            width: 30.W,
            height: 30.H,
          ),
        TripCPersistentTabType.profile => AppAssets.init.icProfileFilled.widget(
            color: AppAssets.init.tabbarEnableColor,
            fit: BoxFit.cover,
            width: 30.W,
            height: 30.H,
          ),
      };

  Widget getScreen() => switch (this) {
        TripCPersistentTabType.home => const TripcHomepage(),
        TripCPersistentTabType.explore => const TripcCommingSoon(),
        TripCPersistentTabType.mytrip => const TripcMyTripPage(),
        TripCPersistentTabType.loyalty => const TripcCommingSoon(),
        TripCPersistentTabType.profile => const TripcProfilePage(),
      };

  GlobalKey get globalKey => switch (this) {
        TripCPersistentTabType.home =>
          GlobalKey(debugLabel: 'BottomNavigationBarItemType.home'),
        TripCPersistentTabType.explore =>
          GlobalKey(debugLabel: 'BottomNavigationBarItemType.explore'),
        TripCPersistentTabType.mytrip =>
          GlobalKey(debugLabel: 'BottomNavigationBarItemType.mytrip'),
        TripCPersistentTabType.loyalty =>
          GlobalKey(debugLabel: 'BottomNavigationBarItemType.loyalty'),
        TripCPersistentTabType.profile =>
          GlobalKey(debugLabel: 'BottomNavigationBarItemType.profile'),
      };
}

String timeOfDayText(TimeOfDay time) {
  final now = DateTime.now();
  final dateTime =
      DateTime(now.year, now.month, now.day, time.hour, time.minute);
  return DateFormat('hh:mm a').format(dateTime);
}

String timeOfDayFromTimeFrame(String timeFrame) {
  final now = DateTime.now();
  final hour = timeFrame.substring(0, 2).parseInt() ?? 0;
  final min = timeFrame.substring(3, 5).parseInt() ?? 0;
  final dateTime = DateTime(now.year, now.month, now.day, hour, min);
  return DateFormat('hh:mm a').format(dateTime);
}

String generateRandom11DigitNumber() {
  Random random = Random();
  int firstDigit = random.nextInt(9) + 1;
  int remainingDigits = random.nextInt(1000000000);
  return '$firstDigit${remainingDigits.toString().padLeft(10, '0')}';
}

extension BottomReachExtension on ScrollController {
  void onBottomReach(VoidCallback callback,
      {double sensitivity = 200.0, Duration? throttleDuration}) {
    final duration = throttleDuration ?? const Duration(milliseconds: 200);
    Timer? timer;

    addListener(() {
      if (timer != null) {
        return;
      }

      timer = Timer(duration, () => timer = null);

      final maxScroll = position.maxScrollExtent;
      final currentScroll = position.pixels;
      if (maxScroll - currentScroll <= sensitivity) {
        callback();
      }
    });
  }
}

extension RelativeTimeExtension on String {
  String toRelativeTime() {
    if (isEmpty) return this;

    try {
      final dateTime = DateTime.parse(this).toLocal();
      final now = DateTime.now();
      final diff = now.difference(dateTime);

      if (diff.inMinutes < 1) {
        return S.current.min_ago(1);
      } else if (diff.inMinutes < 60) {
        return S.current.min_ago(diff.inMinutes);
      } else if (diff.inHours < 24) {
        return S.current.hour_ago(diff.inHours);
      } else if (diff.inDays < 30) {
        return S.current.day_ago(diff.inDays);
      } else if (diff.inDays < 365) {
        final months = (diff.inDays / 30).floor();
        return S.current.month_ago(months);
      } else {
        final years = (diff.inDays / 365).floor();
        return S.current.year_ago(years);
      }
    } catch (e) {
      return this;
    }
  }
}
