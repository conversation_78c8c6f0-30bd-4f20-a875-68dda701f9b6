import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/constants/error_text_const.dart';

class ValidationAccount {
  const ValidationAccount._();

  static final _emailRegex = RegExp(
      r'^(([^<>()[\]\\.,;:\s@\"!#$%^&*]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$');
  static final _validCharacters = RegExp(r'^[a-zA-Z0-9_\-=@,\.;]+$');
  static final _validPassword =
      RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])(?!.*\s).{8,30}$');
  static final _simpleValidPass =
      RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d].{5,11}$');
  static final _validPhoneNumber = RegExp(r'^\d{9}$');
  static final _validNumber = RegExp(r'^\d+$');
  static final _validFullName = RegExp(
      r'^(?!.*\s{2})(?![A-Z\s]+$)[A-Za-zÀ-ỹ][A-Za-zÀ-ỹ\s]{0,48}[A-Za-zÀ-ỹ]$');
  static final _specialCharacters = RegExp(r'[!@#$%^&*(),.?\":{}|<>]');
  static final _checkSpace = RegExp(r'^\s|\s$|\s{2,}');
  static final _validPhoneNumberWithZeroStart = RegExp(r'^0\d{9}$');

  static bool isEmailAndUsernameValidated(
      {required String email, required String userName}) {
    final emailValidated = email.isNotEmpty && _emailRegex.hasMatch(email);
    final userNameValidated =
        userName.isNotEmpty && _validCharacters.hasMatch(userName);

    return emailValidated && userNameValidated;
  }

  static String? getValidateErrorMessage(
      {required String email, required String userName}) {
    if (email.isNotEmpty) {
      if (!_emailRegex.hasMatch(email)) {
        return 'Email must valid';
      }
    }
    if (userName.isNotEmpty) {
      if (!_validCharacters.hasMatch(userName)) {
        return 'Username must not contain special characters';
      }
    }
    return null;
  }

  static String? isEmailPassed(String email) {
    if (email.isNotEmpty) {
      if (!_emailRegex.hasMatch(email)) {
        return 'Email must valid';
      }
    }
    return null;
  }

  static isEmailPassedAndNotSameOld(BuildContext context,
      {required String newEmail, required String oldEmail}) {
    if (newEmail.isNotEmpty) {
      if (!_emailRegex.hasMatch(newEmail)) {
        return ErrorMessage.emailInvalid(context);
      }
      return newEmail == oldEmail
          ? context.strings.text_email_must_be_different
          : '';
    }
    return null;
  }

  static bool isEmailValidated(String email) {
    return email.isNotEmpty && _emailRegex.hasMatch(email);
  }

  static String? isNoSpecialCharPassed(String input) {
    if (input.isNotEmpty) {
      if (!_validCharacters.hasMatch(input)) {
        return 'Username must not contain special characters';
      }
    }
    return null;
  }

  static String isPasswordPassed(BuildContext context, String password,
      {String? error}) {
    if (password.isNotEmpty && !_validPassword.hasMatch(password)) {
      return error ?? ErrorMessage.passInvalid(context);
    }
    return '';
  }

  static bool isMyPasswordPassed(String password) {
    return password.isEmpty ? true : _simpleValidPass.hasMatch(password);
  }

  static bool isMyPhoneNumberPassed(String phoneNumber) {
    final numberPhoneConverted = AppExtension.convertPhoneNumber(phoneNumber);
    return numberPhoneConverted.isEmpty
        ? true
        : _validPhoneNumber.hasMatch(numberPhoneConverted);
  }

  static String phoneNumberValidation(
      BuildContext context, String phoneNumber) {
    final numberPhoneConverted = AppExtension.convertPhoneNumber(phoneNumber);
    return numberPhoneConverted.isEmpty
        ? ''
        : _validPhoneNumber.hasMatch(numberPhoneConverted)
            ? ''
            : ErrorMessage.typeValidPhoneNumber(context);
  }

  static String phoneNumberWithZeroValidation(
      BuildContext context, String phoneNumber) {
    if (_validPhoneNumberWithZeroStart.hasMatch(phoneNumber)) {
      return '';
    }
    return ErrorMessage.plsTypeValidPhoneNumber10(context);
  }

  static String fullNameValidation(BuildContext context, String fullName) {
    if (_validFullName.hasMatch(fullName) || fullName.isEmpty) {
      return '';
    }
    if (_checkSpace.hasMatch(fullName)) {
      return ErrorMessage.fullNameWithoutSpace(context);
    }
    if (RegExp(r"\d").hasMatch(fullName)) {
      return ErrorMessage.fullNameWithoutNumber(context);
    }
    if (_specialCharacters.hasMatch(fullName)) {
      return ErrorMessage.fullNameWithoutSpecialSign(context);
    }
    if (fullName.length < 2) {
      return ErrorMessage.plsTypeFullName(context);
    }
    if (fullName.length > 50) {
      return ErrorMessage.fullNameOver50(context);
    }
    return '';
  }

  static String emailValidation(BuildContext context, String email) {
    if (_emailRegex.hasMatch(email) || email.isEmpty) {
      return '';
    }
    return ErrorMessage.plsTypeInvalidEmail(context);
  }

  static String createPasswordValidation(
      BuildContext context, String password) {
    if (_validPassword.hasMatch(password) || password.isEmpty) {
      return '';
    }
    if (password.length < 8) {
      return ErrorMessage.passwordMustBe8Characters(context);
    }
    if (password.contains(" ")) {
      return ErrorMessage.passwordMustBeWithoutSpace(context);
    }
    if (!RegExp(r"[A-Z]").hasMatch(password)) {
      return ErrorMessage.passwordMustBe1Upper(context);
    }
    if (!RegExp(r"[a-z]").hasMatch(password)) {
      return ErrorMessage.passwordMustBe1Lower(context);
    }
    if (!RegExp(r"\d").hasMatch(password)) {
      return ErrorMessage.passwordMustBe1Number(context);
    }
    if (!_specialCharacters.hasMatch(password)) {
      return ErrorMessage.passwordMustBe1Special(context);
    }
    return '';
  }

  static String passwordValidation(BuildContext context, String password) {
    if (_validPassword.hasMatch(password) || password.isEmpty) {
      return '';
    }
    return ErrorMessage.passwordValidation(context);
  }

  static bool isValidNumber(String value) {
    return value.isEmpty ? true : _validNumber.hasMatch(value);
  }
}
