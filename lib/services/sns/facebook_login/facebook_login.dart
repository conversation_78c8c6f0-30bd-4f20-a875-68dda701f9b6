import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';

class FacebookServiceLogin {
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  String generateNonce([int length = 32]) {
    // Define the character set to be used in the nonce
    const charset =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-.';

    // Create a secure random number generator
    final random = Random.secure();

    // Generate a string of the specified length using random characters from the charset
    return String.fromCharCodes(List.generate(
        length, (index) => charset.codeUnitAt(random.nextInt(charset.length))));
  }

  Future<String?> signIn() async {
    try {
      // Trigger the Facebook login flow
      final rawNonce = generateNonce();
      final nonce = sha256ofString(rawNonce);
      final result = await FacebookAuth.instance.login(
        loginTracking: LoginTracking.limited,
        nonce: nonce,
      );

      if (result.status == LoginStatus.success) {
        final token = result.accessToken as LimitedToken;
        OAuthCredential credential = OAuthCredential(
          providerId: 'facebook.com',
          signInMethod: 'oauth',
          idToken: token.tokenString,
          rawNonce: rawNonce,
        );

        // Sign in to Firebase
        final UserCredential userCredential =
            await _auth.signInWithCredential(credential);
        final User? user = userCredential.user;
        return await user?.getIdToken(true);
      } else {
        print("Facebook Login Failed: ${result.message}");
        return null;
      }
    } catch (e) {
      print("Error during Facebook Sign-In: $e");
      return null;
    }
  }

  Future<String?> signInWithAndroid() async {
    try {
      // Trigger the Facebook login flow
      final result = await FacebookAuth.instance.login();

      if (result.status == LoginStatus.success) {
        final AuthCredential credential = FacebookAuthProvider.credential(
          result.accessToken!.tokenString,
        );

        // Sign in to Firebase
        final UserCredential userCredential =
            await _auth.signInWithCredential(credential);
        final User? user = userCredential.user;
        return await user?.getIdToken(true);
      } else {
        print("Facebook Login Failed: ${result.message}");
        return null;
      }
    } catch (e) {
      print("Error during Facebook Sign-In: $e");
      return null;
    }
  }

  Future<void> signOut() async {
    await FacebookAuth.instance.logOut();
    await FirebaseAuth.instance.signOut();
  }
  // Future<void> signOut() async {
  //   final checkSignIn = await checkGoogleSignInStatus();
  //   if (checkSignIn) {
  //     await googleSignIn.signOut();
  //   }
  // }
}
