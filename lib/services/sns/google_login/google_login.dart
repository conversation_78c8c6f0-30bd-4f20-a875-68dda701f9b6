import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:tripc_app/services/app/app_config.dart';

class GoogleServiceLogin {
  final GoogleSignIn googleSignIn = GoogleSignIn(
      scopes: ['email'], serverClientId: FlavorConfig.instance.env.serverClientId);

  Future<bool> checkGoogleSignInStatus() async {
    return await googleSignIn.isSignedIn();
  }

  Future<String?> signIn() async {
    try {
      if (Platform.isAndroid) {
        await signOut();
      }
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();
      if (googleUser != null) {
        GoogleSignInAuthentication googleAuth = await googleUser.authentication;
        final AuthCredential credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );
        final UserCredential userCredential =
            await FirebaseAuth.instance.signInWithCredential(credential);
        final User? user = userCredential.user;
        return await user?.getIdToken(true);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> signOut() async {
    final checkSignIn = await checkGoogleSignInStatus();
    if (checkSignIn) {
      await googleSignIn.signOut();
    }
  }
}
