import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/app/app_theme.dart';
import 'package:tripc_app/services/cache/cache_auth.dart';
import 'package:tripc_app/services/providers/provider_locale.dart';
import 'package:tripc_app/widgets/commons/helpers/bottom_sheet_helper.dart';
import 'package:tripc_app/widgets/commons/helpers/debounce_helper.dart';
import 'package:tripc_app/widgets/commons/helpers/dialog.helper.dart';
import 'package:tripc_app/widgets/commons/helpers/toast_notification_helper.dart';

final globalCacheAuth = CacheAuth();
bool? isFirstTimeOnApp;
bool? isFirstTimeLogin;
final ProviderContainer globalcontainer = ProviderContainer();
final pAppThemeProvider = ChangeNotifierProvider((_) => AppThemeProvider());
final pLocaleProvider = ChangeNotifierProvider((_) => LocaleProvider());
final bottomSheetHelpers = BottomSheetHelperImpl();
final dialogHelpers = DialogHelperImpl();
final toastHelpers = ToastificationHelperImpl();
final debounceHelpers = DebounceHelperImpl();
