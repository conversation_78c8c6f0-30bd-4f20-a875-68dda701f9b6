import 'package:flutter/material.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum AppLocaLe {
  // en(1),
  vi(2);

  const AppLocaLe(this.value);
  final int value;

  factory AppLocaLe.fromValue(int value) {
    return AppLocaLe.values
        .firstWhere((element) => element.value == value, orElse: () => vi);
  }

  Locale get toLocale {
    switch (this) {
      // case AppLocaLe.en:
      //   return const Locale('en', 'US');
      case AppLocaLe.vi:
        return const Locale('vi', 'VN');
      // default:
      //   return const Locale('en', 'US');
    }
  }

  String name(BuildContext context) {
    switch (this) {
      // case AppLocaLe.en:
      //   return context.strings.text_english;
      case AppLocaLe.vi:
        return context.strings.text_vietnamese;
      // default:
      //   return context.strings.text_english;
    }
  }
}

class LocaleProvider with ChangeNotifier {
  /// Save locale
  Locale _locale = AppLocaLe.vi.toLocale;

  /// Get current locale
  //  AppLocaLe _appLocaLe =
  //     AppLocaLe.fromValue(globalCacheAuth.appLocaleId ?? AppLocaLe.en.value);
  AppLocaLe _appLocaLe = AppLocaLe.fromValue(AppLocaLe.vi.value);
  AppLocaLe get applocale => _appLocaLe;

  Locale get locale => _appLocaLe.toLocale;

  /// Update new locale
  set locale(Locale value) {
    _locale = value;
    notifyListeners();
  }

  void changeLocale(AppLocaLe? value) async {
    if (value == null) return;
    _appLocaLe = value;
    globalCacheAuth.saveCurentLanguageId(value: value.value);
    notifyListeners();
  }
}
