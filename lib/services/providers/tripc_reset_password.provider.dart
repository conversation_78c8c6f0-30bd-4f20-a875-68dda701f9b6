import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/reset_password.request.dart';
import 'package:tripc_app/models/app/verify_otp_request.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/apis/auth/api_otp.dart';
import 'package:tripc_app/services/apis/auth/api_reset_password.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/utils/app_validation.dart';

import '../../utils/app_error_string.dart';

class ResetPasswordModel {
  final bool loading;
  final String? errorMessage;
  final String emailGetOtp;
  final String errorEmailGetOtp;
  final String otp;
  final String tokenFromSendOtp;
  final String tokenFromVerifyOtp;
  final String newPass;
  final String reEnterNewPass;
  final String errorNewPass;
  final String errorReNewPass;

  static ResetPasswordModel getDefault() {
    return ResetPasswordModel();
  }

  ResetPasswordModel({
    this.loading = false,
    this.emailGetOtp = '',
    this.errorMessage,
    this.errorEmailGetOtp = '',
    this.otp = '',
    this.tokenFromSendOtp = '',
    this.tokenFromVerifyOtp = '',
    this.errorNewPass = '',
    this.newPass = '',
    this.reEnterNewPass = '',
    this.errorReNewPass = '',
  });

  ResetPasswordModel copyWith({
    bool? loading,
    String? errorMessage,
    String? emailGetOtp,
    String? errorEmailGetOtp,
    String? otp,
    String? tokenFromSendOtp,
    String? newPass,
    String? reEnterNewPass,
    String? errorNewPass,
    String? errorReNewPass,
    String? tokenFromVerifyOtp,
  }) {
    return ResetPasswordModel(
      loading: loading ?? this.loading,
      errorMessage: errorMessage ?? this.errorMessage,
      errorEmailGetOtp: errorEmailGetOtp ?? this.errorEmailGetOtp,
      tokenFromVerifyOtp: tokenFromVerifyOtp ?? this.tokenFromVerifyOtp,
      emailGetOtp: emailGetOtp ?? this.emailGetOtp,
      otp: otp ?? this.otp,
      tokenFromSendOtp: tokenFromSendOtp ?? this.tokenFromSendOtp,
      errorNewPass: errorNewPass ?? this.errorNewPass,
      reEnterNewPass: reEnterNewPass ?? this.reEnterNewPass,
      errorReNewPass: errorReNewPass ?? this.errorReNewPass,
      newPass: newPass ?? this.newPass,
    );
  }

  bool get isSendOTPDisable {
    return !(emailGetOtp.isNotEmpty && errorEmailGetOtp.isEmpty);
  }

  bool get isEnableResetPass {
    return (errorNewPass.isEmpty &&
        errorReNewPass.isEmpty &&
        newPass.isNotEmpty &&
        reEnterNewPass.isNotEmpty);
  }
}

class TripcResetPasswordProvider extends StateNotifier<ResetPasswordModel> {
  TripcResetPasswordProvider(super.state);

  final ApiOtp _apiOtp = ApiOtp();
  final ApiResetPassword _apiResetPass = ApiResetPassword();

  void forceLoading(bool value) {
    state = state.copyWith(loading: value);
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  Future<bool> sendOtp(BuildContext context) async {
    setErrorMessage('');
    try {
      forceLoading(true);
      final result = await _apiOtp
          .sendOtp(
            email: state.emailGetOtp,
          )
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      forceLoading(false);
      state = state.copyWith(tokenFromSendOtp: result.token);
      return result.status;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(ErrorParser.getErrorMessage(exceptionMessage.error ?? '', context));
      }
      forceLoading(false);
      return false;
    }
  }

  Future<bool> verifyOtp() async {
    setErrorMessage('');
    final request = VerifyOtpRequest(
        email: state.emailGetOtp,
        otp: state.otp,
        token: state.tokenFromSendOtp,
        password: '');
    try {
      forceLoading(true);
      final result = await _apiOtp.verifyOtp(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      forceLoading(false);
      state = state.copyWith(tokenFromVerifyOtp: result.token);
      return result.status;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  Future<bool> resetPassword() async {
    setErrorMessage('');
    final request = ResetPassRequest(
        token: state.tokenFromVerifyOtp, password: state.newPass);
    try {
      forceLoading(true);
      final result =
          await _apiResetPass.resetPassword(request: request).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      forceLoading(false);
      return result.status;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  void setErrorEmailGetOtp(String value){
    state = state.copyWith(errorEmailGetOtp: value);
  }

  void setEmailGetOtp(BuildContext context, String value) {
    state = state.copyWith(
        emailGetOtp: value,
        errorEmailGetOtp: ValidationAccount.emailValidation(context, value));
  }

  void typeOtp(String value) {
    state = state.copyWith(
      otp: value,
    );
  }

  void typeNewPass(String value, BuildContext context) {
    final errorNewPassword = ValidationAccount.isPasswordPassed(context, value,
        error: context.strings.text_password_verification);
    state = state.copyWith(
        newPass: value,
        errorNewPass: errorNewPassword,
        errorReNewPass: errorNewPassword.isEmpty
            ? value != state.reEnterNewPass
                ? context.strings.text_newly_entered_password_is_incorrect
                : ''
            : '');
  }

  void reTypeNewPass(String value, BuildContext context) {
    state = state.copyWith(
        reEnterNewPass: value,
        errorReNewPass: value.isNotEmpty && value != state.newPass
            ? context.strings.text_newly_entered_password_is_incorrect
            : '');
  }

  void resetPageResetPass() {
    state = state.copyWith(
        newPass: '', reEnterNewPass: '', errorNewPass: '', errorReNewPass: '');
  }

  void resetState() {
    state = ResetPasswordModel.getDefault();
  }
}

final pResetPasswordProvider =
    StateNotifierProvider<TripcResetPasswordProvider, ResetPasswordModel>(
        (ref) {
  final resetPassProvider =
      TripcResetPasswordProvider(ResetPasswordModel.getDefault());
  return resetPassProvider;
});
