import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/update_user_request.dart';
import 'package:tripc_app/models/app/verify_otp_request.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/models/remote/user_response.dart';
import 'package:tripc_app/services/apis/auth/api_auth.dart';
import 'package:tripc_app/services/apis/auth/api_otp.dart';
import 'package:tripc_app/services/apis/s3/api_upload_s3.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_error_string.dart';
import 'package:tripc_app/utils/app_validation.dart';
import 'package:tripc_app/widgets/commons/constants/error_text_const.dart';

class AccountModel {
  AccountModel(
      {this.user,
      this.errorMessage,
      this.verifyPhoneNumber = '',
      this.isLoading = false,
      this.tokenFromSendOtp = '',
      this.tokenFromVerifyOtp = '',
      this.currentLinkEmailIndex = 0,
      this.timeCountDown = 60,
      this.errorNewEmail = '',
      this.errorSignTripc = '',
      this.currentGender});

  final UserResponse? user;
  final String? errorMessage;
  final String verifyPhoneNumber;
  final int? currentGender;
  final bool isLoading;
  final String tokenFromSendOtp;
  final String tokenFromVerifyOtp;
  final int currentLinkEmailIndex;
  final int timeCountDown;
  final String errorNewEmail;
  final String errorSignTripc;

  static AccountModel getDefault() {
    return AccountModel(user: globalCacheAuth.user, errorMessage: '');
  }

  AccountModel copyWith(
      {UserResponse? user,
      String? errorMessage,
      String? verifyPhoneNumber,
      String? tokenFromSendOtp,
      String? tokenFromVerifyOtp,
      bool? isLoading,
      int? currentLinkEmailIndex,
      int? timeCountDown,
      String? errorNewEmail,
      String? errorSignTripc,
      int? currentGender}) {
    return AccountModel(
        user: user ?? this.user,
        errorMessage: errorMessage ?? this.errorMessage,
        verifyPhoneNumber: verifyPhoneNumber ?? this.verifyPhoneNumber,
        currentGender: currentGender ?? this.currentGender,
        isLoading: isLoading ?? this.isLoading,
        timeCountDown: timeCountDown ?? this.timeCountDown,
        currentLinkEmailIndex:
            currentLinkEmailIndex ?? this.currentLinkEmailIndex,
        tokenFromSendOtp: tokenFromSendOtp ?? this.tokenFromSendOtp,
        errorNewEmail: errorNewEmail ?? this.errorNewEmail,
        errorSignTripc: errorSignTripc ?? this.errorSignTripc,
        tokenFromVerifyOtp: tokenFromVerifyOtp ?? this.tokenFromVerifyOtp);
  }
}

class AccountProvider extends StateNotifier<AccountModel> {
  AccountProvider(super._state);
  final ApiAuth _api = ApiAuth();
  final ApiOtp _apiOtp = ApiOtp();
  final ApiUploadS3 _apiUploadS3 = ApiUploadS3();

  void setCountDown({bool isRefresh = false}) {
    if (isRefresh) {
      state = state.copyWith(timeCountDown: 60);
      return;
    }
    if (state.timeCountDown > 0) {
      state = state.copyWith(timeCountDown: state.timeCountDown - 1);
    }
  }

  void setIndexLinkEmailPage(int index) {
    state = state.copyWith(currentLinkEmailIndex: index);
  }

  void setCurrentGender({int? gender}) {
    state = state.copyWith(currentGender: gender ?? state.user?.gender);
  }

  Future<void> getme({bool needLoading = false}) async {
    setErrorMessage('');
    if (needLoading) setLoading(true);
    try {
      final result = await _api.getMe().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (needLoading) setLoading(false);
      globalCacheAuth.saveAuthWithNewUserResponse(newUserResponse: result.data);
      state = state.copyWith(user: result.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      if (needLoading) setLoading(false);
    }
  }

  Future<bool> verifyPhoneNumber(BuildContext context,
      {required String phoneNumber, bool setEmpty = false}) async {
    if (setEmpty) {
      state = state.copyWith(verifyPhoneNumber: '');
      return false;
    }
    if (phoneNumber.isEmpty) {
      state = state.copyWith(
          verifyPhoneNumber: ErrorMessage.plsTypePhoneNumber(context));
      return false;
    } else {
      state = state.copyWith(
          verifyPhoneNumber:
              ValidationAccount.isMyPhoneNumberPassed(phoneNumber)
                  ? ''
                  : ErrorMessage.typeValidPhoneNumber(context));
      if (ValidationAccount.isMyPhoneNumberPassed(phoneNumber)) {
        return await handleLinkPhoneNumber(context,
            phoneNumber: '0$phoneNumber');
      }
      return false;
    }
  }

  Future<bool> handleLinkPhoneNumber(BuildContext context,
      {required String phoneNumber}) async {
    try {
      setLoading(true);
      final result = await _api.linkPhone(phone: phoneNumber).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      return result;
    } catch (exceptionMessage) {
      setLoading(false);
      if (exceptionMessage is CommonErrorResponse) {
        // setErrorMessage(exceptionMessage.error);
        state = state.copyWith(
            verifyPhoneNumber: ErrorParser.getErrorMessage(
                exceptionMessage.error ?? '', context));
      }
      return false;
    }
  }

  Future<bool> updateUserInfo(
      {String? fullname, String? city, String? avatarUrl}) async {
    setErrorMessage('');
     final cityToUpdate = (city != null && city.isNotEmpty)
      ? city
      : null;
    final request = UpdateUserRequest(
      fullname: fullname ?? state.user?.fullname,
      gender: state.currentGender,
      city: cityToUpdate,
      avatarUrl: avatarUrl ?? state.user?.avatarUrl,
    );
    try {
      setLoading(true);
      final result = await _api.updateUserInfo(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result) {
        await getme();
      }
      setLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  Future<bool> unLinkSocial({required String provider}) async {
    setErrorMessage('');
    try {
      setLoading(true);
      final result = await _api.unLinkSocial(provider: provider).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result) {
        await getme();
      }
      setLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  void setErrorTripcIdSignIn(String error) {
    state = state.copyWith(errorSignTripc: error);
  }

  Future<bool> loginMemberShip(BuildContext context,
      {required String number, required String passcode}) async {
    if (number.isEmpty || passcode.isEmpty) {
      setErrorTripcIdSignIn(ErrorMessage.wrongTripcIdAndPasscode(context));
      return false;
    }
    setErrorMessage('');
    try {
      setLoading(true);
      final result = await _api
          .loginMemberShip(number: number, passcode: passcode)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result) {
        await getme();
      }
      setLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorTripcIdSignIn(ErrorMessage.wrongTripcIdAndPasscode(context));
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  Future<bool> linkSocial(
      {required String provider, required String token}) async {
    setLoading(true);
    try {
      final result = await _api
          .linkSocial(
            provider: provider,
            token: token,
          )
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result) {
        await getme();
      }
      setLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  Future<bool> sendOtp({required String email}) async {
    setErrorMessage('');
    try {
      setLoading(true);
      final result = await _apiOtp.sendOtp(email: email).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      state = state.copyWith(tokenFromSendOtp: result.token);
      return result.status;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  Future<bool> verifyOtp({required String email, required String otp}) async {
    setErrorMessage('');
    final request = VerifyOtpRequest(
        email: email, otp: otp, token: state.tokenFromSendOtp, password: '');
    try {
      setLoading(true);
      final result = await _apiOtp.verifyOtp(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      state = state.copyWith(tokenFromVerifyOtp: result.token);
      return result.status;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  Future<bool> sendOtpLinkEmail({required String email}) async {
    setErrorMessage('');
    try {
      setLoading(true);
      final result = await _apiOtp.sendOtpLinkEmail(email: email).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      state = state.copyWith(tokenFromSendOtp: result.token);
      return true;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  Future<bool> verifyOtpLinkEmail(
      {required String email, required String otp}) async {
    setErrorMessage('');
    final request = VerifyOtpRequest(
        email: email, otp: otp, token: state.tokenFromSendOtp, password: '');
    try {
      setLoading(true);
      final result = await _apiOtp.verifyOtpLinkEmail(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      state = state.copyWith(tokenFromVerifyOtp: result.token);
      return result.status;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  Future<String?> uploadAvatar({required File file}) async {
    setErrorMessage('');
    try {
      setLoading(true);
      final result = await _apiUploadS3.uploadImageToS3(file: file).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.status) {
        await getme();
      }
      setLoading(false);
      return result.data?.url;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return null;
    }
  }

  Future<bool> markSkipInviteCode() async {
    try {
      final result = await _api.markSkipInviteCode().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      return result;
    } catch (exceptionMessage) {
      return false;
    }
  }

  Future<bool> markSkipTripC() async {
    try {
      final result = await _api.markSkipTripC().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      return result;
    } catch (exceptionMessage) {
      return false;
    }
  }

  void validateNewLinkedEmail(BuildContext context, {required String email}) {
    state = state.copyWith(
        errorNewEmail: ValidationAccount.isEmailPassedAndNotSameOld(context,
                newEmail: email, oldEmail: globalCacheAuth.user?.email ?? '') ??
            '');
  }

  Future<bool> deleteAccount(BuildContext context) async {
     try {
      setLoading(true);
      final result = await _api.deleteAccount().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      return result;
    } catch (exceptionMessage) {
       if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(
            ErrorParser.getErrorMessage(exceptionMessage.error ?? '', context));
      }
      setLoading(false);
      return false;
    }
  }

  void resetLinkEmailPage() {
    state = state.copyWith(
        currentLinkEmailIndex: 0, tokenFromSendOtp: '', tokenFromVerifyOtp: '');
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  void resetState() {
    state = AccountModel.getDefault();
  }
}

final pAccountProvider = StateNotifierProvider<AccountProvider, AccountModel>(
    (ref) => AccountProvider(AccountModel.getDefault()));
