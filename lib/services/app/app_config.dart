import 'package:tripc_app/services/app/app_theme.dart';

enum EnvType {
  dev,
  prod,
}

/// Environment declare here
class Env {
  Env._({
    required this.envType,
    required this.apiBaseUrl,
    required this.serverClientId,
  });

  /// Dev mode
  factory Env.dev() {
    return Env._(
        envType: EnvType.dev,
        apiBaseUrl: 'https://tripc-api.allyai.ai',
        serverClientId:
            '801144692639-m4jrht9m6v6d2kss6r6951jhhfg3ger3.apps.googleusercontent.com');
  }

  /// Prod mode
  factory Env.prod() {
    return Env._(
        envType: EnvType.prod,
        apiBaseUrl: 'https://api.tripc.ai',
        serverClientId:
            '504724078390-06gbrjoo464at8jspr1dukqji311i8u0.apps.googleusercontent.com');
  }

  final EnvType envType;
  final String apiBaseUrl;
  final String serverClientId;
}

/// Config env
class FlavorConfig {
  final Env env;
  AppTheme? theme;

  static FlavorConfig? _instance;

  FlavorConfig._({required this.env, this.theme});

  factory FlavorConfig({required Env env, AppTheme? theme}) {
    _instance ??= FlavorConfig._(env: env, theme: theme);
    return _instance!;
  }

  static FlavorConfig get instance {
    if (_instance == null) {
      throw Exception('FlavorConfig must be initialized before setting env');
    }
    return _instance!;
  }

  static void setTheme(AppTheme newTheme) {
    if (_instance == null) {
      throw Exception('FlavorConfig must be initialized before setting theme');
    }
    _instance!.theme = newTheme;
  }

  static bool isDev() => instance.env.envType == EnvType.dev;

  static bool isProd() => instance.env.envType == EnvType.prod;
}
