// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  /// Dev configuration for Android
  static const FirebaseOptions android = FirebaseOptions(
      apiKey: 'AIzaSyDx3DHtvxJ3XkcK_QsWBnb1LH4pQ8t7V1s',
      appId: '1:504724078390:android:4f5bd619df719f7a103d97',
      messagingSenderId: '504724078390',
      projectId: 'tripctripc-1f600',
      storageBucket: 'tripctripc-1f600.firebasestorage.app');

  /// Dev configuration for iOS
  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCUiSbMhsV1njFr88vvxuq8wsOMRfIKtbk',
    appId: '1:504724078390:ios:60965ea4ef827509103d97',
    messagingSenderId: '504724078390',
    projectId: 'tripctripc-1f600',
    storageBucket: 'tripctripc-1f600.firebasestorage.app',
    iosClientId:
        '801144692639-saaaqpbn029ejrqcg0bvgl1gr06g254m.apps.googleusercontent.com',
    iosBundleId: 'com.tripc.app',
  );
}
