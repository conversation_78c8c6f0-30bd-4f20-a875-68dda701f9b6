import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_config.dart';
import 'package:tripc_app/utils/app_extension.dart';

class AppTheme {
  AppTheme._();

  /// Default theme
  factory AppTheme.origin() {
    return AppTheme._();
  }

  /// Default theme
  factory AppTheme.light() {
    return AppTheme._()
      ..assets = AppAssets.origin()
      ..isDark = false
      ..primaryColor = Colors.white
      ..accentColor = Colors.white
      ..backgroundColor = Colors.white
      ..headerBgColor = Colors.white
      ..titleColor = const Color(0xff141518);
  }

  factory AppTheme.dark() {
    return AppTheme._()
      ..assets = AppAssets.origin()
      ..isDark = false
      ..primaryColor = const Color(0xff141518)
      ..accentColor = const Color(0xff141518)
      ..backgroundColor = const Color(0xff141518)
      ..headerBgColor = const Color(0xff141518)
      ..titleColor = Colors.white;
  }

  AppAssets assets = AppAssets.origin();
  bool isDark = true;
  Color primaryColor = Colors.blueGrey;
  Color accentColor = Colors.blueGrey;
  Color backgroundColor = const Color(0xFFF2F2F2);
  Color headerBgColor = Colors.blueGrey;
  Color titleColor = Colors.black;

  /// Build theme data
  ThemeData buildThemeData({int sizeFactor = 0}) {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
      ),
      useMaterial3: true,
      scaffoldBackgroundColor: AppAssets.init.backGroundColor,
      primaryColor: primaryColor,
      secondaryHeaderColor: accentColor,
      fontFamily: assets.fontRoboto,
      //  pageTransitionsTheme: _buildPageTransitionsTheme(),
      buttonTheme: _buildButtonTheme(),
      textTheme: _buildTextTheme(sizeFactor: sizeFactor),
    );
  }

  /// Custom button theme full width
  ButtonThemeData _buildButtonTheme() {
    return ButtonThemeData(
      minWidth: double.infinity,
      shape: const Border(),
      buttonColor: accentColor,
      textTheme: ButtonTextTheme.primary,
      padding: const EdgeInsets.all(16),
    );
  }

  /// Custom text theme
  TextTheme _buildTextTheme({int sizeFactor = 1}) {
    return TextTheme(
      headlineLarge: TextStyle(
        fontFamily: assets.fontRoboto,
        fontWeight: FontWeight.bold,
        fontSize: 40.H,
      ),
      titleLarge: TextStyle(
        fontFamily: assets.fontRoboto,
        fontSize: 28.H + sizeFactor * 0.5,
      ),
      titleMedium: TextStyle(
        fontFamily: assets.fontRoboto,
        fontSize: 24.H + sizeFactor * 0.5,
      ),
      titleSmall: TextStyle(
        fontFamily: assets.fontRoboto,
        fontSize: 22.H + sizeFactor * 0.5,
        fontWeight: FontWeight.w600,
      ),
      bodyLarge: TextStyle(
        fontFamily: assets.fontRoboto,
        fontSize: 16.H + sizeFactor * 0.5,
      ),
      bodyMedium: TextStyle(
        fontFamily: assets.fontRoboto,
        fontSize: 14.H + sizeFactor * 0.5,
      ),
      bodySmall: TextStyle(
        fontFamily: assets.fontRoboto,
        fontSize: 12.H + sizeFactor * 0.5,
      ),
      labelMedium: TextStyle(
        fontFamily: assets.fontRoboto,
        fontSize: 18.H + sizeFactor * 0.5,
      ),
    );
  }
}

class AppThemeProvider with ChangeNotifier {
  AppTheme get theme => FlavorConfig.instance.theme ?? AppTheme.origin();

  set theme(AppTheme value) {
    FlavorConfig.setTheme(value);
    notifyListeners();
  }
}
