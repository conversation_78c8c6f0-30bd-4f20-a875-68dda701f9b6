import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';
import 'package:tripc_app/widgets/presentationals/app_shimmer_loading/image_shimmer_loading.dart';

class AppAssets {
  AppAssets._();

  /// Default theme
  factory AppAssets.origin() {
    return AppAssets._();
  }

  /// Default access
  static final AppAssets init = AppAssets._();

  ///#region FONTS
  /// -----------------
  String fontPretendard = 'Pretendard';
  String fontRoboto = 'Roboto';

  ///#endregion

  //#region Style
  /// -----------------
  final normalTextStyle = const TextStyle(
      fontFamily: 'Roboto',
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: Color(0xff070707));

  final mediumTextStyle = const TextStyle(
      fontFamily: 'Roboto',
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: Color(0xff070707));

  final boldTextStyle = const TextStyle(
      fontFamily: 'Roboto',
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: Color(0xff070707));

  final superBoldTextStyle = const TextStyle(
      fontFamily: 'Roboto',
      fontSize: 14,
      fontWeight: FontWeight.w700,
      color: Color(0xff070707));

  final itemDecoration = BoxDecoration(
      borderRadius: BorderRadius.circular(10),
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          color: const Color(0xff000000).withOpacity(0.15),
          spreadRadius: 0,
          blurRadius: 4,
          offset: const Offset(0, 4),
        )
      ]);

  final itemShadow = const BoxShadow(
    color: Color(0x14000000),
    spreadRadius: 0,
    blurRadius: 20,
    offset: Offset(0, 4),
  );

  final infoBoxShadow = const BoxShadow(
    color: Color(0x1A000000),
    spreadRadius: 0,
    blurRadius: 4,
    offset: Offset(0, 4),
  );

  final innnerShadowTop = const BoxShadow(
    color: Color(0x1FFFFFFF),
    spreadRadius: 0,
    blurRadius: 12,
    offset: Offset(0, 12),
  );
  final innnerShadowBottom = const BoxShadow(
    color: Color(0x1A303030),
    spreadRadius: 0,
    blurRadius: 2,
    offset: Offset(0, -2),
  );

  final itemShadow2 = BoxShadow(
    color: const Color(0xff000000).withOpacity(0.15),
    spreadRadius: 0,
    blurRadius: 16,
    offset: const Offset(0, -2),
  );

  final dragBottomSheetShadow = BoxShadow(
    color: const Color(0xff000000).withOpacity(0.15),
    spreadRadius: 0,
    blurRadius: 16,
    offset: const Offset(0, -2),
  );

  final membershipSignUpOptionShadow = const BoxShadow(
    color: Color(0xffEAEAEA),
    spreadRadius: 0,
    blurRadius: 20,
    offset: Offset(0, 4),
  );

  final keywordsBoxHeader = const LinearGradient(
    colors: [
      Color(0xff8AC3FF),
      Color(0xffFFFFFF),
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: [0, 1],
  );

  final keywordsBox = const LinearGradient(
    colors: [
      Color(0xffFFFFFF),
      Color.fromARGB(255, 168, 210, 255),
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: [0, 1],
  );

  final buttonGradient = const LinearGradient(
    colors: [
      Color(0x33FFFFFF),
      Color(0x33FFFFFF),
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: [0, 1],
  );

  final inputBoxHeaderGradient = const LinearGradient(
    colors: [
      Color(0xffDEEEFF),
      Color(0xff87C1FF),
    ],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    stops: [0, 1],
  );
  final inputBoxPinkHeaderGradient = const LinearGradient(
    colors: [
      Color(0xffFFECEB),
      Color(0xffFFC3BE),
    ],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    stops: [0, 1],
  );

  final signUpOptionGradient1 = const LinearGradient(
    colors: [
      Color(0xffDAECFF),
      Color(0xff9BCCFF),
    ],
    begin: Alignment.topRight,
    end: Alignment.bottomCenter,
    stops: [0.44, 1],
  );

  final signUpOptionGradient2 = const LinearGradient(
    colors: [
      Color(0xffFFEBA7),
      Color(0xffFFF6DA),
    ],
    begin: Alignment.bottomCenter,
    end: Alignment.topRight,
    stops: [0, 0.8],
  );

  final circularGradient = const LinearGradient(
    colors: [
      Color(0xff0365FA),
      Color(0xff003B95),
    ],
    stops: [0.0, 1.0],
  );

  final strokeGradient = const LinearGradient(
    colors: [
      Color(0x33FFFFFF),
      Color(0xffFFFFFF),
    ],
    stops: [0.0, 1.0],
  );

  final slideButtonGradient = const LinearGradient(
    colors: [
      Color(0xFFFFFFFF),
      Color(0x00FFFFFF),
      Color(0x00FFFFFF),
    ],
    stops: [0.0, 0.6, 1.0],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );

  final homePageGradient = LinearGradient(
    colors: [
      const Color(0xff0F1015).withValues(alpha: 0.34),
      const Color(0xffF4F6F9),
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: const [0.0, 1],
  );

  final itemImageCard = LinearGradient(
    colors: [
      const Color(0xff000000).withValues(alpha: 0),
      const Color(0xff000000),
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: const [0.0, 0.69],
  );

  final searchGradient = LinearGradient(
    colors: [
      const Color(0xff3778F6),
      const Color(0xffF4F6F9).withValues(alpha: 0.5),
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: const [0.0, 1],
  );

  final balanceGradient = const LinearGradient(
    colors: [
      Color(0xffFFF1CE),
      Color(0xffFFF8E4),
    ],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    stops: [0.0, 1],
  );

  final promoTicketGradient = const LinearGradient(
    colors: [
      Color(0xffFFB4AE),
      Color(0xffFFECEB),
    ],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    stops: [0.0, 1],
  );

  final profileServiceAreaShadow = BoxShadow(
    color: const Color(0xff070707).withAlpha(20),
    spreadRadius: 1,
    blurRadius: 4,
    offset: const Offset(0, 4),
  );

  ///#endregion

  //////#region color
  /// -----------------
  final secondaryTextColor = const Color(0xffB48246);
  final grayColor = const Color(0xffB9B9B9);
  final backGroundColor = const Color(0xffFAFAF0);
  final backGroundCardColor = const Color(0xffF8F8F6);
  final backGroundCardBoderColor =
      const Color(0xffF1F1F1).withValues(alpha: 0.5);
  final lightGreenColor = const Color(0xff4BB441);
  final greenColor = const Color(0xff0C641E);
  final gray30percent = const Color(0xff000000).withValues(alpha: 0.3);
  final grayBorderColor = const Color(0xff585858);
  final gray2Color = const Color(0xffF4F4ED);
  final gray4Color = const Color(0xffE3E3E3);
  final gray5Color = const Color(0xff909090);
  final superGrayColor = const Color(0xff666666);
  final grayTextColor = const Color(0xff767676);
  final mainBlackColor = const Color(0xff121212);
  final subColor = const Color(0xffFAFAF0);
  final disableButtonColor = const Color(0xffEAEAEA);
  final loginBackGroundColor = const Color(0xffFFFDFD);
  final mainblackColor = const Color(0xff121212);
  final tabbarDisableColor = const Color(0xff9DB2CE);
  final searchPlaceHolderColor = const Color(0xffB9B9B9);
  final dividerColor = const Color(0xff767676);
  final secondDividerColor = const Color(0xFFDADADA);
  final bubleSentColor = const Color(0xffE6C896);
  final bubleReceiveColor = const Color(0xffF4F4ED);
  final timeSectionColor = const Color(0xffF4F5F6);
  final recentChatColor = const Color(0xff48484A);
  final redColor = const Color(0xffFF4A4A);
  final secondaryRedColor = const Color(0xffFF0000);
  final barrierColor = const Color(0xff121212).withValues(alpha: 0.4);
  final whiteBackgroundColor = const Color(0xffFFFFFF);
  final tabbarEnableColor = const Color(0xff3778F6);
  final tierAndInfoCardBg = const Color(0xff76A3FF).withValues(alpha: 0.2);
  final borderGlassTextField = const Color(0xffDFE0E2);
  final textColorGlassTextField = const Color(0xff737982);
  final bgTcentServiceItemColor = const Color(0x4DFFDA44);
  final bgDetailTourColorV2 = const Color(0xFFF4F6F9);
  final textColorForNameTourV2 = const Color(0xFF1E40A3);
  final textForLocationTourV2 = const Color(0xFF0365FA);
  final cardColorTourV2 = const Color(0xFFD9E9FE);
  final subTextColorTourV2 = const Color(0xFF43474C);
  final colorTextOffer = const Color(0xFFFF8A00);
  final colorTextBooking = const Color(0xFFEAA000);
  final colorTextFoodName = const Color(0xFF001142);
  final colorIconViewAllProducts = const Color(0xFF292D32);
  final colorIconSuccess = const Color(0xFF3778F6);

  ///Tripc Color
  final primaryColor = const Color(0xFFBEE4F1);
  final primaryColorV2 = const Color(0xFF4291F8);
  final disableColorV2 = const Color(0xFF535353);
  final secondaryColor = const Color(0xFF50A4FD);
  final whiteSmokeColor = const Color(0xffF5F5F5);
  final buttonStrokeColor = const Color(0xEDE0E0E0);
  final blackColor = const Color(0xff070707);
  final error = const Color(0xFFFF6969);
  final greyTextColorC8 = const Color(0xFFC8C8C8);
  final darkGreyTextColor = const Color(0xFF5A5A5A);
  final secondDarkGreyTextColor = const Color(0xFF838383);
  final lightBlueColor = const Color(0xFFBEE4F1);
  final slideButtonColor = const Color(0x33C4C4C4);
  final whiteColor20 = const Color(0x33FFFFFF);
  final black = const Color(0xff000000);
  final blackTextColor = const Color(0xff2C2C2C);
  final lightPink = const Color(0xffFFC7C3);
  final lightYellow = const Color(0xffFFEEB6);
  final secondLightYellow = const Color(0xffFFF1CE);
  final searchStrokeColor = const Color(0xFFD2D9E8);
  final darkYellow = const Color(0xFFFFB700);
  final secondDarkYellow = const Color(0xFFFFC738);
  final neutralN7 = const Color(0xFF696969);
  final titleServiceItemColor = const Color(0xFF0A2151);
  final priceServiceItemColor = const Color(0xFF3160F4);
  final priceSaleServiceItemColor = const Color(0xFFFF7066);
  final tcentServiceItemColor = const Color(0xFFE29700);
  final black12 = const Color(0x1F000000);
  final black10 = const Color(0x1A000000);
  final black25 = const Color(0x40000000);
  final lightRedColor = const Color(0xffF14336);
  final redDotColor = const Color(0xffFF3434);
  final blackStroke = const Color(0xFF2E383F);
  final darkBlueColor = const Color(0xff0365FA);
  final darkOrange = const Color(0xFFFF9D00);
  final secondDarkOrange = const Color(0xFFF58700);
  final lightOrange = const Color(0xFFFFEDC0);
  final black81F = const Color(0xFF14181F);
  final stroke0E5Color = const Color(0xFFDCE0E5);
  final dayOfWeekColor = const Color(0xFF6F7C8E);
  final darkBlue8FF = const Color(0xff3348FF);
  final lightBlueDFF = const Color(0xffE0EDFF);
  final lightGrayDD4 = const Color(0xffC4CDD4);
  final lightBlueCFF = const Color(0xffDAECFF);
  final appBarUnderline = const Color(0xffD9D9D9);
  final balanceBorderColor = const Color(0xffFFE8AE);
  final promoTicketBorderColor = const Color(0xffFF9C94);
  final lightGreen = const Color(0xffC1FFCF);
  final darkGreenColor = const Color(0xff1FC342);
  final darkRedColor = const Color(0xffFE1200);
  final settingValueColor = const Color(0xff636363);
  final settingDividerColor = const Color(0xffE6E6E6);
  final yellow444 = const Color(0xffFFB444);
  final lightDarkE1 = const Color(0xffE1E1E1);
  final darkBlueText = const Color(0xff003B95);
  final lightBlueBg = const Color(0xff87C1FF);
  final grayDivider = const Color(0xffE6E6E6);
  final gray63Color = const Color(0xff636363);
  final barrier80 = const Color(0xCCBEBEBE);
  final black70 = const Color(0xFF000000).withValues(alpha: 0.7);
  final grayEFColor = const Color(0xffEFEFEF);
  final pink5B5Color = const Color(0xffFFB5B5);
  final buttonStrokeColor93 = const Color(0xFFE0E0E0).withValues(alpha: 0.93);
  final blackColor70 = const Color(0xB3000000);
  final darkBlue5FF = const Color(0xff0065FF);
  final darkBrownRedColor = const Color(0xFFC32418);
  final lightBlueFD = const Color(0xff50A4FD);
  final gray1F1 = const Color(0xffF1F1F1);
  final blueAFF = const Color(0xff007AFF);
  final grayE9 = const Color(0xffE9E9E9);
  final redFF0 = const Color(0xffFF0000);
  final grayF7 = const Color(0xffF7F7F7);
  final blueCAE = const Color(0xFFCAE4FF);
  final lightGray = const Color(0xFFEAEAEA);
  final lightCream = const Color(0xFFFFF8E5);
  final neutralColor = const Color(0xFFFEFEFE);
  final lightBlue = const Color(0xFFE7F5FF);
  final grayF2 = const Color(0xFFF2F2F2);
  final yellowCB = const Color(0xFFFFF0CB);
  final grayF4F6 = const Color(0xFFF4F6F9);
  final blue1E40 = const Color(0xFF1E40A3);
  final blue0365 = const Color(0xFF0365FA);
  final blueD9E9 = const Color(0XFFD9E9FE);
  final grey53 = const Color(0xFF535353);
  final gray8D = const Color(0xff8D8D8D);
  final grayE5 = const Color(0xffE5E5E5);
  final blue191 = const Color(0xff191D88);
  final disableDot = const Color(0xFFE5E5E5);
  final blue001 = const Color(0xff001142);
  final gray4D = const Color(0xff4D4D4D);
  final yellow7E5 = const Color(0xffFFF7E5);
  final yellowEAA = const Color(0xffEAA000);
  final gray656 = const Color(0xff656E5E);
  final black3C4 = const Color(0xff3C4049);
  final grayC4C8 = const Color(0xffC4C8CF);
  final gray474 = const Color(0xff474C57);

  // Stay color
  final backgroundSearchStay = const Color(0xffF4F6F9);
  final hotelSearchNameColor = const Color(0xff30343B);
  final hotelSearchAddressColor = const Color(0xff535965);
  final refundCardColor = const Color(0xffEDFAFF);

  final refundStatusColorText = const Color(0xff169200);

  final requestedStatusColorBG = const Color(0xffFFFEE0);
  final processingStatusColorBG = const Color(0xffE1EEFF);
  final refundedStatusColorBG = const Color(0xffCCFFD1);
  final cancelRefundStatusColorBG = const Color(0xffFFE2E2);

  final colorInformationTextRefundItem = const Color(0xff5E6573);
  final colorBorderTextFieldRefund = const Color(0xff8F96A3);

  ///#endregion

  ///#region IMAGES, ICONS
  /// -----------------

  static const String _svgRootDirectory = 'assets/app/svg/';
  static const String _pngRootDirectory = 'assets/app/png/';

  /// ICONS
  AppAssetBuilder get homeFilledIcon =>
      AppAssetBuilder('${_svgRootDirectory}ic_tabbar_home.svg');
  AppAssetBuilder get close =>
      AppAssetBuilder('${_svgRootDirectory}ic_tabbar_home.svg');

  /// Tripc Assets
  AppAssetBuilder get iconBack =>
      AppAssetBuilder('${_svgRootDirectory}ic_back.svg');

  AppAssetBuilder get iconApple =>
      AppAssetBuilder('${_svgRootDirectory}ic_apple.svg');

  AppAssetBuilder get iconGoogle =>
      AppAssetBuilder('${_svgRootDirectory}ic_google.svg');

  AppAssetBuilder get iconFacebook =>
      AppAssetBuilder('${_svgRootDirectory}ic_facebook.svg');

  AppAssetBuilder get iconEye =>
      AppAssetBuilder('${_svgRootDirectory}ic_eye.svg');

  AppAssetBuilder get splashBackGround =>
      AppAssetBuilder('${_pngRootDirectory}im_splash.png');

  AppAssetBuilder get iconRight =>
      AppAssetBuilder('${_svgRootDirectory}ic_right.svg');

  AppAssetBuilder get tutorialBG1 =>
      AppAssetBuilder('${_pngRootDirectory}im_tutorialBG1.png');

  AppAssetBuilder get tutorialBG2 =>
      AppAssetBuilder('${_pngRootDirectory}im_tutorialBG2.png');

  AppAssetBuilder get imPlane =>
      AppAssetBuilder('${_pngRootDirectory}im_plane.png');

  AppAssetBuilder get imIslands =>
      AppAssetBuilder('${_pngRootDirectory}im_islands.png');

  AppAssetBuilder get tutorialBG3 =>
      AppAssetBuilder('${_pngRootDirectory}im_tutorialBG3.png');

  AppAssetBuilder get iconArrowRight =>
      AppAssetBuilder('${_svgRootDirectory}ic_arrow_right.svg');

  AppAssetBuilder get iconDoubleArrowRight =>
      AppAssetBuilder('${_svgRootDirectory}ic_double_arrow_right.svg');

  AppAssetBuilder get logo =>
      AppAssetBuilder('${_svgRootDirectory}ic_logo.svg');

  AppAssetBuilder get membershipBg =>
      AppAssetBuilder('${_svgRootDirectory}ic_membership_bg.svg');

  AppAssetBuilder get iconMoreDetail =>
      AppAssetBuilder('${_svgRootDirectory}ic_more_detail.svg');

  AppAssetBuilder get iconBook =>
      AppAssetBuilder('${_svgRootDirectory}ic_book.svg');

  AppAssetBuilder get iconCat =>
      AppAssetBuilder('${_svgRootDirectory}ic_cat.svg');

  AppAssetBuilder get icCoin =>
      AppAssetBuilder('${_svgRootDirectory}ic_money_coin.svg');

  AppAssetBuilder get iconSearch =>
      AppAssetBuilder('${_svgRootDirectory}ic_search.svg');

  AppAssetBuilder get iconCircleClose =>
      AppAssetBuilder('${_svgRootDirectory}ic_circle_close.svg');

  AppAssetBuilder get icBill =>
      AppAssetBuilder('${_svgRootDirectory}ic_bill.svg');

  AppAssetBuilder get iconArrowleft =>
      AppAssetBuilder('${_svgRootDirectory}ic_arrow_left.svg');

  AppAssetBuilder get icSuccess =>
      AppAssetBuilder('${_svgRootDirectory}ic_success.svg');

  AppAssetBuilder get icHome =>
      AppAssetBuilder('${_svgRootDirectory}ic_home.svg');
  AppAssetBuilder get icHomeFilled =>
      AppAssetBuilder('${_svgRootDirectory}ic_home_filled.svg');
  AppAssetBuilder get icExplore =>
      AppAssetBuilder('${_svgRootDirectory}ic_explore.svg');
  AppAssetBuilder get icExploreFilled =>
      AppAssetBuilder('${_svgRootDirectory}ic_explore_filled.svg');
  AppAssetBuilder get icLoyalty =>
      AppAssetBuilder('${_svgRootDirectory}ic_loyalty.svg');
  AppAssetBuilder get icLoyaltyFilled =>
      AppAssetBuilder('${_svgRootDirectory}ic_loyalty_filled.svg');
  AppAssetBuilder get icProfile =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile.svg');
  AppAssetBuilder get icProfileFilled =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_filled.svg');
  AppAssetBuilder get icMyTrip =>
      AppAssetBuilder('${_svgRootDirectory}ic_travel.svg');

  AppAssetBuilder get icVoucher =>
      AppAssetBuilder('${_svgRootDirectory}ic_voucher.svg');

  AppAssetBuilder get icEntertainment =>
      AppAssetBuilder('${_svgRootDirectory}icon_entertainment.svg');

  AppAssetBuilder get icParkOutlineRight =>
      AppAssetBuilder('${_svgRootDirectory}icon-park-outline_right.svg');

  AppAssetBuilder get icGroup =>
      AppAssetBuilder('${_svgRootDirectory}ic_group.svg');

  AppAssetBuilder get icOtherServicesMenu =>
      AppAssetBuilder('${_svgRootDirectory}ic_other_menu.svg');

  AppAssetBuilder get icBed =>
      AppAssetBuilder('${_svgRootDirectory}ic_bed.svg');

  AppAssetBuilder get icTaxi =>
      AppAssetBuilder('${_svgRootDirectory}ic_taxi.svg');

  AppAssetBuilder get icBoatTour =>
      AppAssetBuilder('${_svgRootDirectory}ic_boat_tour.svg');

  AppAssetBuilder get icPlaneCar =>
      AppAssetBuilder('${_svgRootDirectory}ic_plane_car.svg');

  AppAssetBuilder get icDish =>
      AppAssetBuilder('${_svgRootDirectory}ic_dish.svg');

  AppAssetBuilder get icWalkingLuggage =>
      AppAssetBuilder('${_svgRootDirectory}ic_walking_luggage.svg');

  AppAssetBuilder get icPlaneSolid =>
      AppAssetBuilder('${_svgRootDirectory}ic_plane_solid.svg');

  AppAssetBuilder get icStar =>
      AppAssetBuilder('${_svgRootDirectory}ic_star.svg');

  AppAssetBuilder get icStarV2 =>
      AppAssetBuilder('${_svgRootDirectory}ic_star_v2.svg');

  AppAssetBuilder get icTcent =>
      AppAssetBuilder('${_svgRootDirectory}ic_tcent.svg');

  AppAssetBuilder get icTcentV2 =>
      AppAssetBuilder('${_pngRootDirectory}ic_tcent_v2.png');

  AppAssetBuilder get icDiamond =>
      AppAssetBuilder('${_svgRootDirectory}ic_diamond.svg');

  AppAssetBuilder get icChatNoti =>
      AppAssetBuilder('${_svgRootDirectory}ic_chat_noti.svg');

  AppAssetBuilder get icNotification =>
      AppAssetBuilder('${_svgRootDirectory}ic_small_notification.svg');

  AppAssetBuilder get icReadNotification =>
      AppAssetBuilder('${_svgRootDirectory}ic_small_read_notification.svg');

  AppAssetBuilder get imHomePage =>
      AppAssetBuilder('${_pngRootDirectory}im_home_page.png');

  AppAssetBuilder get imBeachLandScape =>
      AppAssetBuilder('${_svgRootDirectory}im_beach_landscape.svg');

  AppAssetBuilder get icArrowDown =>
      AppAssetBuilder('${_svgRootDirectory}ic_arrow_down.svg');

  AppAssetBuilder get imBlueGradient =>
      AppAssetBuilder('${_svgRootDirectory}im_blue_gradient.svg');

  AppAssetBuilder get imYellowGradient =>
      AppAssetBuilder('${_svgRootDirectory}im_yellow_gradient.svg');

  AppAssetBuilder get icShare =>
      AppAssetBuilder('${_svgRootDirectory}ic_share.svg');

  AppAssetBuilder get icTag =>
      AppAssetBuilder('${_svgRootDirectory}ic_tag.svg');

  AppAssetBuilder get icDotMore =>
      AppAssetBuilder('${_svgRootDirectory}ic_dot_more.svg');

  AppAssetBuilder get icBlueRight =>
      AppAssetBuilder('${_svgRootDirectory}ic_blue_right.svg');

  AppAssetBuilder get icCalendar =>
      AppAssetBuilder('${_svgRootDirectory}ic_calendar.svg');

  AppAssetBuilder get icICircle =>
      AppAssetBuilder('${_svgRootDirectory}ic_iCircle.svg');

  AppAssetBuilder get icAdd =>
      AppAssetBuilder('${_svgRootDirectory}ic_add.svg');

  AppAssetBuilder get icMinus =>
      AppAssetBuilder('${_svgRootDirectory}ic_minus_circle.svg');

  AppAssetBuilder get icClose =>
      AppAssetBuilder('${_svgRootDirectory}ic_close.svg');

  AppAssetBuilder get icBlackRight =>
      AppAssetBuilder('${_svgRootDirectory}ic_black_right.svg');

  AppAssetBuilder get icRoundNoseLeft =>
      AppAssetBuilder('${_svgRootDirectory}ic_round_nose_left.svg');

  AppAssetBuilder get icEditPen =>
      AppAssetBuilder('${_svgRootDirectory}ic_edit_pen.svg');

  AppAssetBuilder get icIOutLine =>
      AppAssetBuilder('${_svgRootDirectory}ic_iOutline.svg');

  AppAssetBuilder get icDiscount =>
      AppAssetBuilder('${_svgRootDirectory}ic_discount.svg');

  AppAssetBuilder get imEmptyView =>
      AppAssetBuilder('${_svgRootDirectory}im_empty_view.svg');

  AppAssetBuilder get icSetting =>
      AppAssetBuilder('${_svgRootDirectory}ic_setting.svg');

  AppAssetBuilder get icArrowRightGray =>
      AppAssetBuilder('${_svgRootDirectory}ic_arrow_right_gray.svg');

  // Category icon

  AppAssetBuilder get iconFoodCategory =>
      AppAssetBuilder('${_svgRootDirectory}icon_food_category.svg');

  AppAssetBuilder get iconEntertainmentCategory =>
      AppAssetBuilder('${_svgRootDirectory}icon_entertainment_category.svg');

  AppAssetBuilder get iconHealthBeautyCategory =>
      AppAssetBuilder('${_svgRootDirectory}icon_health_beauty_category.svg');

  AppAssetBuilder get iconEventCategory =>
      AppAssetBuilder('${_svgRootDirectory}icon_event_category.svg');

  AppAssetBuilder get iconStayCategory =>
      AppAssetBuilder('${_svgRootDirectory}icon_stay_category.svg');

  AppAssetBuilder get iconTourExperiencesCategory =>
      AppAssetBuilder('${_svgRootDirectory}icon_tour_experiences_category.svg');

  AppAssetBuilder get iconHaveFunCategory =>
      AppAssetBuilder('${_svgRootDirectory}icon_have_fun_category.svg');

  AppAssetBuilder get iconGolfSportCategory =>
      AppAssetBuilder('${_svgRootDirectory}icon_golf_sport_category.svg');

  AppAssetBuilder get iconShoppingCategory =>
      AppAssetBuilder('${_svgRootDirectory}icon_shopping_category.svg');

  AppAssetBuilder get iconMovingCategory =>
      AppAssetBuilder('${_svgRootDirectory}icon_moving_category.svg');
  AppAssetBuilder get icListBoard =>
      AppAssetBuilder('${_svgRootDirectory}ic_list_board.svg');
  AppAssetBuilder get icArrange =>
      AppAssetBuilder('${_svgRootDirectory}ic_arrange.svg');

  AppAssetBuilder get bgCard03 =>
      AppAssetBuilder('${_pngRootDirectory}bg_card_3.png');
  AppAssetBuilder get bgCard01 =>
      AppAssetBuilder('${_pngRootDirectory}bg_card_1.png');
  AppAssetBuilder get bgCard02 =>
      AppAssetBuilder('${_pngRootDirectory}bg_card_2.png');
  AppAssetBuilder get imBookingCuisine =>
      AppAssetBuilder('${_pngRootDirectory}im_booking_cuisine.png');
  AppAssetBuilder get imBookingSpa =>
      AppAssetBuilder('${_pngRootDirectory}im_booking_spa.png');
  AppAssetBuilder get imBookingKaraoke =>
      AppAssetBuilder('${_pngRootDirectory}im_booking_karaoke.png');
  AppAssetBuilder get imStayBanner =>
      AppAssetBuilder('${_pngRootDirectory}im_stay_banner.png');

  AppAssetBuilder get imNoInternet =>
      AppAssetBuilder('${_pngRootDirectory}im_no_internet.png');

  AppAssetBuilder get imageContactTripC =>
      AppAssetBuilder('${_pngRootDirectory}im_contact.png');

  AppAssetBuilder get icSpinningGradient =>
      AppAssetBuilder('${_svgRootDirectory}ic_spinning_gradient.svg');

  AppAssetBuilder get imgCardAvatar =>
      AppAssetBuilder('${_pngRootDirectory}img_card.png');

  AppAssetBuilder get icPromoTicket =>
      AppAssetBuilder('${_svgRootDirectory}ic_promo_ticket.svg');

  AppAssetBuilder get icProfileTourBooked =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_tour_booked.svg');
  AppAssetBuilder get icProfileTourSaved =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_tour_saved.svg');
  AppAssetBuilder get icProfileWallet =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_wallet.svg');
  AppAssetBuilder get icProfileMoment =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_moment.svg');
  AppAssetBuilder get icProfileRecent =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_recent.svg');
  AppAssetBuilder get icProfileContact =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_contact.svg');
  AppAssetBuilder get icProfileGift =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_gift.svg');
  AppAssetBuilder get icProfileSupport =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_support.svg');
  AppAssetBuilder get icProfileInfo =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_info.svg');
  AppAssetBuilder get icProfileReview =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_review.svg');
  AppAssetBuilder get icProfilePravicy =>
      AppAssetBuilder('${_svgRootDirectory}ic_profile_pravicy.svg');
  AppAssetBuilder get icTrain =>
      AppAssetBuilder('${_svgRootDirectory}ic_train.svg');
  AppAssetBuilder get icCarKey =>
      AppAssetBuilder('${_svgRootDirectory}ic_car_key.svg');

  AppAssetBuilder get imSeaBackGround =>
      AppAssetBuilder('${_pngRootDirectory}im_sea_background.png');
  AppAssetBuilder get icSwitchInactive =>
      AppAssetBuilder('${_svgRootDirectory}ic_switch_inactive.svg');
  AppAssetBuilder get icSwitchActive =>
      AppAssetBuilder('${_svgRootDirectory}ic_switch_active.svg');
  AppAssetBuilder get icCheckmark =>
      AppAssetBuilder('${_svgRootDirectory}ic_check_mark.svg');
  AppAssetBuilder get imHalongBay =>
      AppAssetBuilder('${_svgRootDirectory}im_halong_bay.svg');

  AppAssetBuilder get icLocation =>
      AppAssetBuilder('${_svgRootDirectory}ic_location.svg');

  AppAssetBuilder get icTrash =>
      AppAssetBuilder('${_svgRootDirectory}ic_trash.svg');

  AppAssetBuilder get icClock =>
      AppAssetBuilder('${_svgRootDirectory}ic_clock.svg');

  AppAssetBuilder get icRedTag =>
      AppAssetBuilder('${_svgRootDirectory}ic_red_tag.svg');

  AppAssetBuilder get icWhiteEdit =>
      AppAssetBuilder('${_svgRootDirectory}ic_white_edit.svg');

  AppAssetBuilder get icWarning =>
      AppAssetBuilder('${_svgRootDirectory}ic_warning.svg');

  AppAssetBuilder get imCommingSoon =>
      AppAssetBuilder('${_svgRootDirectory}im_comming_soon.svg');

  AppAssetBuilder get icBlueLock =>
      AppAssetBuilder('${_svgRootDirectory}ic_blue_lock.svg');

  AppAssetBuilder get bgCard04 =>
      AppAssetBuilder('${_pngRootDirectory}bg_card_4.png');

  AppAssetBuilder get bgCard05 =>
      AppAssetBuilder('${_pngRootDirectory}bg_card_5.png');

  AppAssetBuilder get icCardStar =>
      AppAssetBuilder('${_svgRootDirectory}ic_card_star.svg');
  AppAssetBuilder get icCancelDoc =>
      AppAssetBuilder('${_svgRootDirectory}ic_cancel_doc.svg');

  AppAssetBuilder get icCube =>
      AppAssetBuilder('${_svgRootDirectory}ic_cube.svg');

  AppAssetBuilder get icHeart =>
      AppAssetBuilder('${_svgRootDirectory}ic_heart.svg');

  AppAssetBuilder get icHeartFill =>
      AppAssetBuilder('${_svgRootDirectory}ic_heart_fill.svg');

  AppAssetBuilder get icFillLocation =>
      AppAssetBuilder('${_svgRootDirectory}ic_fill_location.svg');

  AppAssetBuilder get icDashBoard =>
      AppAssetBuilder('${_svgRootDirectory}ic_dash_board.svg');

  AppAssetBuilder get iconGridView =>
      AppAssetBuilder('${_svgRootDirectory}grid_view_mode.svg');

  AppAssetBuilder get iconListView =>
      AppAssetBuilder('${_svgRootDirectory}list_view_mode.svg');

  AppAssetBuilder get iconFoodType =>
      AppAssetBuilder('${_svgRootDirectory}icon_food_type.svg');

  AppAssetBuilder get iconClock =>
      AppAssetBuilder('${_svgRootDirectory}icon_clock.svg');

  AppAssetBuilder get iconServing =>
      AppAssetBuilder('${_svgRootDirectory}icon_serving.svg');

  AppAssetBuilder get iconSpaType =>
      AppAssetBuilder('${_svgRootDirectory}icon_spa_type.svg');

  AppAssetBuilder get iconKaraokeType =>
      AppAssetBuilder('${_svgRootDirectory}icon_karaoke_type.svg');

  AppAssetBuilder get iconLocation =>
      AppAssetBuilder('${_svgRootDirectory}icon_location.svg');

  AppAssetBuilder get iconBooking =>
      AppAssetBuilder('${_svgRootDirectory}icon_booking.svg');

  AppAssetBuilder get icEqual =>
      AppAssetBuilder('${_svgRootDirectory}ic_equal.svg');

  AppAssetBuilder get iconLocationPin =>
      AppAssetBuilder('${_svgRootDirectory}ic_location_pin.svg');

  AppAssetBuilder get icBroom =>
      AppAssetBuilder('${_svgRootDirectory}ic_broom.svg');

  AppAssetBuilder get icLoundSpeaker =>
      AppAssetBuilder('${_svgRootDirectory}ic_loud_speaker.svg');

  AppAssetBuilder get icSquareGift =>
      AppAssetBuilder('${_svgRootDirectory}ic_square_gift.svg');

  AppAssetBuilder get icGrayClock =>
      AppAssetBuilder('${_svgRootDirectory}ic_gray_clock.svg');

  AppAssetBuilder get icBigNotification =>
      AppAssetBuilder('${_svgRootDirectory}ic_big_notification.svg');

  AppAssetBuilder get icColorClock =>
      AppAssetBuilder('${_svgRootDirectory}ic_color_clock.svg');

  AppAssetBuilder get icContact =>
      AppAssetBuilder('${_svgRootDirectory}ic_contact.svg');

  AppAssetBuilder get icLocationMarker =>
      AppAssetBuilder('${_svgRootDirectory}ic_location_marker.svg');

  AppAssetBuilder get icBin =>
      AppAssetBuilder('${_svgRootDirectory}ic_bin.svg');

  AppAssetBuilder get icBillCheck =>
      AppAssetBuilder('${_svgRootDirectory}ic_bill_check.svg');

  AppAssetBuilder get icNote =>
      AppAssetBuilder('${_svgRootDirectory}ic_note.svg');

  AppAssetBuilder get icBronze =>
      AppAssetBuilder('${_svgRootDirectory}ic_bronze.svg');

  AppAssetBuilder get icSilver =>
      AppAssetBuilder('${_svgRootDirectory}ic_silver.svg');

  AppAssetBuilder get icGold =>
      AppAssetBuilder('${_svgRootDirectory}ic_gold.svg');

  AppAssetBuilder get icPlatinum =>
      AppAssetBuilder('${_svgRootDirectory}ic_platinum.svg');

  AppAssetBuilder get icDiamondTier =>
      AppAssetBuilder('${_svgRootDirectory}ic_tier_diamond.svg');

  AppAssetBuilder get icErrorImg =>
      AppAssetBuilder('${_svgRootDirectory}ic_add_image.svg');

  AppAssetBuilder get icAvatarDetault =>
      AppAssetBuilder('${_svgRootDirectory}ic_avatar_default.svg');

  AppAssetBuilder get icCopy =>
      AppAssetBuilder('${_svgRootDirectory}ic_copy.svg');

  AppAssetBuilder get iconAddGallery =>
      AppAssetBuilder('${_svgRootDirectory}ic_gallery_add.svg');

  AppAssetBuilder get icMyLocation =>
      AppAssetBuilder('${_svgRootDirectory}ic_my_location.svg');

  AppAssetBuilder get icUploadEvidence =>
      AppAssetBuilder('${_svgRootDirectory}ic_upload_evidence.svg');

  AppAssetBuilder get icUploadPhoto =>
      AppAssetBuilder('${_svgRootDirectory}upload_photo.svg');

  AppAssetBuilder get imClock =>
      AppAssetBuilder('${_pngRootDirectory}im_clock.png');

  AppAssetBuilder get imEmpty =>
      AppAssetBuilder('${_pngRootDirectory}im_empty.png');

  AppAssetBuilder get imTutorialBG1New =>
      AppAssetBuilder('${_pngRootDirectory}im_tutorialBG1_new.png');
  AppAssetBuilder get imTutorialBG2New =>
      AppAssetBuilder('${_pngRootDirectory}im_tutorialBG2_new.png');
  AppAssetBuilder get imTutorialBG3New =>
      AppAssetBuilder('${_pngRootDirectory}im_tutorialBG3_new.png');
  AppAssetBuilder get imHomeBanner =>
      AppAssetBuilder('${_pngRootDirectory}im_home_banner.png');

  // LOTTIE
  static const String _jsonRootDirectory = 'assets/app/lottie/';
  AppAssetBuilder get lottieLoadingSweep =>
      AppAssetBuilder('${_jsonRootDirectory}loading_sweep.json');
  AppAssetBuilder get lottieLoadingImage =>
      AppAssetBuilder('${_jsonRootDirectory}loading_image.json');
  AppAssetBuilder get lottieLoadingTripC =>
      AppAssetBuilder('${_jsonRootDirectory}loading_tripc.json');
  AppAssetBuilder get lottieLoadingTripCNew =>
      AppAssetBuilder('${_jsonRootDirectory}loading_tripc_new.json');
  AppAssetBuilder get lottieLoadingSplash =>
      AppAssetBuilder('${_jsonRootDirectory}loading_splash.json');
}

class AppAssetBuilder {
  AppAssetBuilder(this.assetPath);

  final String assetPath;

  static Widget loadAssetImage(String url,
      {bool withWhiteTextureBelow = true,
      double? size,
      double? height,
      double? width,
      double? iconSize,
      double? loadHeight,
      BoxFit? fit,
      Widget}) {
    if (url.isEmpty) {
      return Container(
        height: height,
        color: AppAssets.origin().grayColor,
        child: Center(
            child: AppAssets.init.homeFilledIcon.widget(
                height: iconSize ?? 14.H,
                width: iconSize ?? 14.H,
                color: AppAssets.origin().secondaryTextColor)),
      );
    }
    if (url.contains('.svg')) {
      return Container(
        //color: Colors.white.withOpacity(0.7),
        child: SvgPicture.network(
          url,
          fit: BoxFit.cover,
        ),
      );
    } else {
      return BaseCachedNetworkImage(
        imageUrl: url,
        height: height,
        fit: fit ?? BoxFit.cover,
        placeholder: (context, _) => ImageShimmerLoading(
          height: loadHeight ?? 140.H,
          fit: BoxFit.scaleDown,
          color: context.appCustomPallet.buttonBG,
        ),
        errorWidget: (context, error, object) => Container(
          height: height ?? 60.H,
          color: context.appCustomPallet.buttonBG,
          child: Center(
            child: AppAssets.init.homeFilledIcon
                .widget(height: iconSize ?? 14.H, width: iconSize ?? 14.H),
          ),
        ),
      );
    }
  }

  Widget widget({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    Alignment alignment = Alignment.center,
    Color? color,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    Widget? placeholder,
    String? errorImageUrl,
    int? memCacheHeight,
    final Widget Function(BuildContext, Widget, ImageChunkEvent?)?
        loadingBuilder,
    final Widget Function(BuildContext, Object, StackTrace?)? errorBuilder,
    final Widget Function(BuildContext, Widget, int?, bool)? frameBuilder,
    bool? repeat,
    void Function(LottieComposition)? onLoaded,
    AnimationController? controller,
  }) {
    return AssetBuilder(
      assetPath,
      key: key,
      width: width,
      height: height,
      fit: fit,
      color: color,
      alignment: alignment,
      placeholder: placeholder,
      errorImageUrl: errorImageUrl,
      memCacheHeight: memCacheHeight,
      loadingBuilder: loadingBuilder,
      errorBuilder: errorBuilder,
      frameBuilder: frameBuilder,
      repeat: repeat,
      onLoaded: onLoaded,
      backgroundColor: backgroundColor,
    );
  }
}

class AssetBuilder extends StatelessWidget {
  const AssetBuilder(this.input,
      {super.key,
      this.height,
      this.width,
      this.color,
      this.fit,
      this.memCacheHeight,
      this.alignment = Alignment.center,
      this.borderRadius,
      this.placeholder,
      this.errorImageUrl,
      this.repeat,
      this.onLoaded,
      this.backgroundColor,
      this.controller,
      this.loadingBuilder,
      this.frameBuilder,
      this.errorBuilder});

  final String input;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final Color? color;
  final BorderRadius? borderRadius;
  final Alignment alignment;
  final Widget? placeholder;
  final void Function(LottieComposition)? onLoaded;
  final AnimationController? controller;
  final Widget Function(BuildContext, Widget, ImageChunkEvent?)? loadingBuilder;
  final Widget Function(BuildContext, Object, StackTrace?)? errorBuilder;
  final Widget Function(BuildContext, Widget, int?, bool)? frameBuilder;

  /// This url handle this case can not get image from local
  final String? errorImageUrl;
  final int? memCacheHeight;
  final bool? repeat;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    if (borderRadius != null) {
      return ClipRRect(
        borderRadius: borderRadius!,
        child: _image(),
      );
    }
    return _image();
  }

  Widget _placeholder() {
    return placeholder ?? Container(color: Colors.white);
  }

  Widget _image() {
    try {
      if (input.isEmpty == true) {
        return _placeholder();
      }
      if (input is Uint8List) {
        return Image.memory(input as Uint8List,
            height: height,
            color: color,
            width: width,
            fit: fit,
            alignment: alignment);
      }
      final bool isNetworkMedia = input.startsWith('http');
      if (input.endsWith('svg')) {
        if (isNetworkMedia) {
          return SvgPicture.network(input,
              colorFilter: color != null
                  ? ColorFilter.mode(color!, BlendMode.srcIn)
                  : null,
              height: height,
              width: width,
              fit: fit ?? BoxFit.contain,
              alignment: alignment);
        }
        return SvgPicture.asset(input,
            colorFilter: color != null
                ? ColorFilter.mode(color!, BlendMode.srcIn)
                : null,
            height: height,
            width: width,
            fit: fit ?? BoxFit.contain,
            alignment: alignment);
      }
      if (input.endsWith('json')) {
        return Lottie.asset(
          input,
          repeat: repeat ?? false,
          fit: BoxFit.fill,
          height: height,
          width: width,
          onLoaded: onLoaded,
          controller: controller,
        );
      }
      if (isNetworkMedia) {
        return SizedBox(
          width: width,
          height: height,
          child: Image.network(
            input,
            frameBuilder: frameBuilder,
            loadingBuilder: loadingBuilder,
            errorBuilder: errorBuilder,
            alignment: alignment,
            fit: BoxFit.cover,
          ),
        );
      }
      return Image(
        image: AssetImage(input),
        height: height,
        color: color,
        width: width,
        fit: fit,
        alignment: alignment,
      );
    } catch (_) {
      return Container();
    }
  }
}
