import 'package:tripc_app/services/app/app_config.dart';

class AppApiEndpoint {
  const AppApiEndpoint._();
  static String login = '${FlavorConfig.instance.env.apiBaseUrl}/auth/login';
  static String register =
      '${FlavorConfig.instance.env.apiBaseUrl}/auth/register';
  static String loginSns =
      '${FlavorConfig.instance.env.apiBaseUrl}/auth/login-with-social';
  static String getMe =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/info';
  static String membershipLuckyWeath =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/membership/lucky_wealth';
  static String membershipQuadruple =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/membership/quadruple';
  static String membershipRichNoble =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/membership/rich_noble';
  static String membershipGreatPeace =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/membership/great_peace';
  static String myMembership =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/my-membership';
  static String updatePasscode =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/membership/update-passcode';
  static String membershipDetail(int id) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/membership/$id/detail';
  static String sendOtp =
      '${FlavorConfig.instance.env.apiBaseUrl}/auth/send-otp';
  static String verifyOtp =
      '${FlavorConfig.instance.env.apiBaseUrl}/auth/verify-otp';
  static String linkEmail =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/link-email';
  static String verifyOtpLinkEmail =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/verify-link-email';
  static String verifyOldPassCode =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/membership/verify-passcode';
  static String resetPassword =
      '${FlavorConfig.instance.env.apiBaseUrl}/auth/reset-password';
  static String updateUser =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/update';
  static String unlinkSocial =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/unlink-social';
  static String loginMemberShip =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/membership/enter';
  static String switchMemberShip(int id) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/membership/switch/$id';
  static String deactiveMemberShip(int id) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/membership/deactivate/$id';
  static String linkSocial =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/link-social';
  static String getServiceCategoryBySlug(String slug) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/service-type/slug/$slug';
  static String getTourDealsAroundHere =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/tours/deals-around-here';
  static String getSavedTour =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/my-save-tour';
  static String getNotification =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/notifications';
  static String markReadNotifications =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/mark-read-notifications';
  static String markReadOneNotification(int notificationId) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/mark-read-notifications/$notificationId';
  static String getTourDiscounts =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/tours/discounts';
  static String getTourBestSeller =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/tours/best-seller';
  static String serviceTypeEndpoint =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/service-type';
  static String serviceTypeByParent = '$serviceTypeEndpoint/parent';
  static String serviceTypesList = '$serviceTypeEndpoint/list';
  static String getTourBuyMoreEarnMore =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/tours/by-more-earn-more';
  static String getDetailedTour(int tourId) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/tours/v2/$tourId';
  static String createOrderTripCWithVNPay =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/tripc/create/vnpay';
  static String paymentFailure =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/payment-failure';
  static String inviteCode =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/enter-invite-code';
  static String createOrderTourWithVNPay =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/tour/create/vnpay';
  static String createOrderTourWithPayOS =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/tour/create/vnpay';
  static String createOrderTourWithTcent =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/tour/create/tcent';
  static String createOrderTourPayLater =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/tour/create/pay-later';
  static String savedTour(int tourId) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/save-tour/$tourId';
  static String getSaveTour =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/my-save-tour';
  static String linkPhone =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/link-phone';
  static String markSkipInviteCode =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/mark-enter-invite-code';
  static String markSkipTripC =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/mark-enter-tripc';
  static String getRecentlyViewed =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/my-recently-tour';
  static String deleteRecentlyViewed =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/clear-recently-tour';
  static String getContactList =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/my-contact';
  static String deleteContact(int contactId) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/delete-contact/$contactId';
  static String updateContact(int contactId) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/update-contact/$contactId';
  static String addContact =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/add-contact';
  static String makeS3UploadUrl =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/upload-file';
  static String refreshToken =
      '${FlavorConfig.instance.env.apiBaseUrl}/auth/refresh-token';
  static String getMyContact =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/my-contact';
  static String getPopularKeyword =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/popular-keyword';
  static String getTours =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/tours';
  static String getProductTypes =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/product-type/all';
  static String getBookingSuppliers =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/restaurants';
  static String getToursFerry =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/tours';
  static String getTopSearchTours =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/tours/most-view';
  static String getPopularSearchTours =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/tours/wishlist';
  static String getSearchSuggest =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/tours/search-keywords';
  static String getMyOrder =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/my';
  static String getOrderDetail(int orderId) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/$orderId';
  static String requestRefund =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/tour/request-refund';
  static String getIdByCode(String code) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/get-id-by-code/$code';
  static String forgotPasscode =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/membership/forgot-passcode';
  static String verifyOtpForgotPasscode =
      '${FlavorConfig.instance.env.apiBaseUrl}/auth/verify-otp';
  static String deleteAccount =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/delete-user';
  static String getReservationOrderDetail(int orderId) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/$orderId';
  static String getDetailCuisine(int id) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/services/seating/$id';
  static String createBooking =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/seat/create';
  static String feedback =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/reflect-social-org/create';
  static String getListFeedbacks =
      '${FlavorConfig.instance.env.apiBaseUrl}/api/users/reflect-social-org/list';
  static String getWorkTimeBySupplier(int supplierId) =>
      '${FlavorConfig.instance.env.apiBaseUrl}/api/order/$supplierId';
}
