import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tripc_app/models/remote/auth_response/api_login_response.dart';
import 'package:tripc_app/models/remote/user_response.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum AccountType { email, google, apple }

class AuthSavedInfo {
  AuthSavedInfo({
    this.token,
    this.email = '',
    this.password = '',
    this.isRemember = false,
  });

  String? token;
  String email;
  String password;
  bool isRemember;

  static AuthSavedInfo getDefault() {
    return AuthSavedInfo(
      token: '',
    );
  }

  static AuthSavedInfo fromJson(String input) {
    final Map<String, dynamic> decoded = jsonDecode(input);
    return AuthSavedInfo(
      token: decoded['token'] ?? '',
      email: decoded['email'] ?? '',
      password: decoded['password'] ?? '',
      isRemember: decoded['isRemember'] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
        if (token.isNotNull) 'token': token,
        'email': email,
        'password': password,
        'isRemember': isRemember
      };

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}

AuthSavedInfo authSavedInfoFromJson(String str) => AuthSavedInfo.fromJson(str);

class CacheAuth {
  bool _autoLogin = true;
  bool get autoLogin => _autoLogin;
  final String _keyToCache = 'CacheAuth_savedkey';
  final String _keyCacheAutoLogin = 'CacheAuth_autologin';
  final String _keySaveCacheLogin = 'SavedCacheInfo';
  final String _keySaveFcmToken = 'SavedFcmToken';
  final String _keyCacheAppLocale = 'CacheAuth_appLocale';
  final String _keyCurrentScreen = 'CurrentScreen';
  LoginResponse _savedAuthInfo = LoginResponse.getDefault();
  AuthSavedInfo _savedCacheInfo = AuthSavedInfo.getDefault();
  final AccountType _accountType = AccountType.email;
  int? _appLocaleId;
  int? get appLocaleId => _appLocaleId;

  LoginResponse get savedAuth => _savedAuthInfo;
  AuthSavedInfo get savedCacheInfo => _savedCacheInfo;
  AccountType get accountType => _accountType;
  SharedPreferences? _prefs;
  String deviceIdentifier = '';
  SharedPreferences? getPref() {
    return _prefs;
  }

  Future<void> getDeviceIdentifier() async {
    final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceIdentifier = androidInfo.id;
    } else if (Platform.isIOS) {
      final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceIdentifier = iosInfo.identifierForVendor ?? '';
    }
  }

  void saveCurrentScreen(String currentScreen) {
    _prefs?.setString(_keyCurrentScreen, currentScreen);
  }

  String getCurrentScreen() {
    return _prefs?.getString(_keyCurrentScreen) ?? '';
  }

  String getBearerToken() {
    return 'Bearer ${savedAuth.token ?? ''}';
  }

  String? getRefreshToken() {
    return savedAuth.refreshToken;
  }

  String? getToken() {
    return savedAuth.token;
  }

  Future<void> saveInt(String key, int value) async {
    _prefs?.setInt(
      key,
      value,
    );
  }

  int? getInt(String key) {
    return _prefs?.getInt(
      key,
    );
  }

  Future<void> saveBool(String key, bool value) async {
    _prefs?.setBool(
      key,
      value,
    );
  }

  bool? getBool(String key) {
    return _prefs?.getBool(
      key,
    );
  }

  Future<void> saveString(String key, String value) async {
    _prefs?.setString(
      key,
      value,
    );
  }

  String? getString(String key) {
    return _prefs?.getString(
      key,
    );
  }

  Future<void> getAuth(SharedPreferences prefs) async {
    _prefs = prefs;
    final savedInfo = prefs.getString(_keyToCache) ?? '';
    if (savedInfo.isNotEmpty) {
      _savedAuthInfo = loginResponseFromJson(savedInfo);
    }
    getDeviceIdentifier();
  }

  Future<void> saveAuth({required LoginResponse newInfo}) async {
    _savedAuthInfo = newInfo;
    _prefs?.setString(
      _keyToCache,
      loginResponseToJson(
        _savedAuthInfo,
      ),
    );
  }

  Future<void> getAutoLogin(SharedPreferences prefs) async {
    _prefs = prefs;
    final savedInfo = prefs.getBool(_keyCacheAutoLogin);
    if (savedInfo != null) {
      _autoLogin = savedInfo;
    }
  }

  Future<void> saveAuToLogin({required bool value}) async {
    _autoLogin = value;
    _prefs?.setBool(_keyCacheAutoLogin, value);
  }

  Future<void> getCacheInfoUser(SharedPreferences prefs) async {
    _prefs = prefs;
    final savedCacheInfoString =
        prefs.getString(_keySaveCacheLogin) ?? '';
    if (savedCacheInfoString.isNotEmpty) {
      _savedCacheInfo = authSavedInfoFromJson(savedCacheInfoString);
    }
  }

  Future<void> saveCacheInfoUser({required AuthSavedInfo cacheInfo}) async {
    _savedCacheInfo = cacheInfo;
    _prefs?.setString(
      _keySaveCacheLogin,
      _savedCacheInfo.toString(),
    );
  }

  void resetCacheLogin() {
    _savedCacheInfo = AuthSavedInfo.getDefault();
  }

  Future<void> saveAuthWithNewUserResponse(
      {required UserResponse? newUserResponse}) async {
    if (newUserResponse != null) {
      _savedAuthInfo.data = newUserResponse;
    }
    _prefs?.setString(
      _keyToCache,
      loginResponseToJson(
        _savedAuthInfo,
      ),
    );
  }

  Future<void> getCurentLanguageId(SharedPreferences prefs) async {
    _prefs = prefs;
    final savedInfo = prefs.getInt(_keyCacheAppLocale);
    if (savedInfo != null) {
      _appLocaleId = savedInfo;
    }
  }

  Future<void> saveCurentLanguageId({required int value}) async {
    _appLocaleId = value;
    _prefs?.setInt(_keyCacheAppLocale, value);
  }

  UserResponse? get user => _savedAuthInfo.data;

  Future<bool> getMax18() async {
    return _prefs?.getBool(
          'max18',
        ) ??
        false;
  }

  Future<void> setMax18(bool isAccept) async {
    _prefs?.setBool(
      'max18',
      isAccept,
    );
  }

  Future<List<String>> getTotalChat() async {
    return _prefs?.getStringList(
          'chats',
        ) ??
        [];
  }

  Future<String?> getFcmToken() async {
    return _prefs?.getString(_keySaveFcmToken);
  }

  Future<void> saveFcmToken(String token) async {
    _prefs?.setString(_keySaveFcmToken, token);
  }

  // Future<String?> saveChangesFromTemp({bool canAddPicture = false}) async {
  //   final String? nameToUpdate = _tempChangedName.isNotEmpty
  //       ? _tempChangedName
  //       : savedAuth.user?.username;
  //   final cacheList = globalCacheAuth.savedAuth.data?.pictures?.toList();
  //   if (canAddPicture) {
  //     cacheList?.insert(0, _tempPicture);
  //   }
  //   try {
  //     final resultUpdate = await ApiUpdatedProfile().updatedSelectProfile(
  //       picture: (_tempChangedImageIndex != -1)
  //           ? _tempChangedImageIndex
  //           : (savedAuth.data?.picture ?? 0),
  //       newName: nameToUpdate ?? '',
  //       pictures: cacheList,
  //     );
  //     final afterChangedInfo = savedAuth.copyWith(
  //       data: savedAuth.data?.copyWith(
  //         username: resultUpdate.username,
  //         picture: resultUpdate.picture,
  //         pictures: resultUpdate.pictures,
  //       ),
  //     );

  //     saveAuth(newInfo: afterChangedInfo);
  //     return Future.value();
  //   } catch (exceptionMessage) {
  //     String messageFromServer = '';
  //     if (exceptionMessage is CommonErrorResponse) {
  //       messageFromServer = exceptionMessage.message ?? '';
  //     }
  //     return Future.value(messageFromServer);
  //   }
  // }

  bool isLogged() {
    return _savedAuthInfo.token?.isNotEmpty ?? false;
  }

  void logout({bool isClear = false}) {
    // final currentRequested =
    //     _prefs!.getInt(CachePushKey.notificationRequested.name) ?? 0;
    // final steps = globalCacheAuth.getInt(TutorialPageProvider.tutorialKey) ?? 0;
    _prefs!.setString(_keyToCache, '');
    if (isClear) {
      resetCacheLogin();
      _prefs!.clear();
    }
    // _prefs!.setInt(CachePushKey.notificationRequested.name, currentRequested);
    // _prefs!.setInt(TutorialPageProvider.tutorialKey, steps);
    // globalNSFWChecker.resetCheckState();
    _savedAuthInfo = LoginResponse.getDefault();
    // Future.delayed(const Duration(milliseconds: 500), () async {
    //   final PushNotificationService _pushByAblyService =
    //       AblyPushService.instance.pushNotificationService;
    //   await _pushByAblyService.deactivateDevice();
    // });
  }
}
