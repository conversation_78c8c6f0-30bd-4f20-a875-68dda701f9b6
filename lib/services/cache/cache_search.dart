import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class SearchHistoryCache {
  static const String _keySearchHistory = 'tripc_search_history';
  static const int _maxHistoryItems = 5;

  SharedPreferences? _prefs;
  List<String> _searchHistory = [];

  List<String> get searchHistory => _searchHistory;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    await getSearchHistory();
  }

  Future<void> getSearchHistory() async {
    if (_prefs == null) {
      return;
    }

    final String? savedHistory = _prefs!.getString(_keySearchHistory);
    if (savedHistory != null && savedHistory.isNotEmpty) {
      try {
        final List<dynamic> decoded = jsonDecode(savedHistory);
        _searchHistory = decoded.map((item) => item.toString()).toList();
      } catch (e) {
        _searchHistory = [];
      }
    }
  }

  Future<void> saveSearchHistory() async {
    if (_prefs == null) {
      return;
    }

    final String encodedData = jsonEncode(_searchHistory);
    await _prefs!.setString(_keySearchHistory, encodedData);
  }

  Future<void> addSearchKeyword(String keyword) async {
    if (keyword.isEmpty) {
      return;
    }

    _searchHistory
        .removeWhere((item) => item.toLowerCase() == keyword.toLowerCase());

    bool keywordExists = _searchHistory
        .any((item) => item.toLowerCase() == keyword.toLowerCase());

    if (keywordExists) {
      return;
    }

    _searchHistory.insert(0, keyword.trim());

    if (_searchHistory.length > _maxHistoryItems) {
      _searchHistory = _searchHistory.sublist(0, _maxHistoryItems);
    }

    await saveSearchHistory();
  }

  Future<void> clearSearchHistory() async {
    _searchHistory.clear();
    await saveSearchHistory();
  }

  bool containsKeyword(String keyword) {
    return _searchHistory
        .any((item) => item.toLowerCase() == keyword.toLowerCase());
  }
}

final SearchHistoryCache globalSearchHistoryCache = SearchHistoryCache();
