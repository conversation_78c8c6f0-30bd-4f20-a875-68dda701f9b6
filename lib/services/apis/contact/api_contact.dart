import 'package:dio/dio.dart';
import 'package:tripc_app/models/remote/contact_response.dart';
import '../../../models/app/contact_request.dart';
import '../../../models/remote/api_status_response/api_status_response.dart';
import '../../../models/remote/common_error.dart';
import '../../app/app_api_endpoint.dart';
import '../../providers/providers.dart';
import '../api.dart';

class ApiContact extends Api {
  Future<ListContactResponse> getMyContact() async {
    final result = await wrapE(
          () => dio.get(AppApiEndpoint.getMyContact,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listContactResponseFromJson(result.data.toString());
  }

  Future<bool> addContact({required ContactRequest request}) async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.addContact,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        data: request.toJson(),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }
}
