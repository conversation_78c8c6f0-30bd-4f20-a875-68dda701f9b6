import 'package:dio/dio.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/app/update_passcode_request.dart';
import 'package:tripc_app/models/remote/api_status_response/api_status_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_detailed_response.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_list_numbers_response.dart';
import 'package:tripc_app/services/app/app_api_endpoint.dart';
import 'package:tripc_app/services/providers/providers.dart';
import '../../../models/app/forgot_passcode_request.dart';
import '../../../models/app/verify_otp_forgot_passcode_request.dart';
import '../../../models/remote/membership_repsonse/api_membership_forgot_passcode_response.dart';
import '../api.dart';

class ApiMembership extends Api {
  Future<MembershipListNumbersResponse> getLuckyWeathNumbers(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.membershipLuckyWeath,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return membershipListNumbersFromJson(result.data.toString());
  }

  Future<MembershipListNumbersResponse> getQuadrupleNumbers(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.membershipQuadruple,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return membershipListNumbersFromJson(result.data.toString());
  }

  Future<MembershipListNumbersResponse> getRichNobleNumbers(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.membershipRichNoble,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return membershipListNumbersFromJson(result.data.toString());
  }

  Future<MembershipListNumbersResponse> getGreatPeace(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.membershipGreatPeace,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return membershipListNumbersFromJson(result.data.toString());
  }

  Future<MembershipListNumbersResponse> getMyMembership(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.myMembership,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return membershipListNumbersFromJson(result.data.toString());
  }

  Future<DetailedMembershipResponse> getMembershipDetail(
      {required int id}) async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.membershipDetail(id),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return detailedMembershipFromJson(result.data.toString());
  }

  Future<bool> updatePasscode({required UpdatePasscodeRequest request}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.updatePasscode,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<bool> switchMemberShip({required int id}) async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.switchMemberShip(id),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).data?.success ??
        false;
  }

  Future<bool> deactiveMemberShip({required int id}) async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.deactiveMemberShip(id),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).data?.success ??
        false;
  }

  Future<MembershipForgotPasscodeResponse> sendOtpForgotPasscode(
      {required ForgotPasscodeRequest request}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.forgotPasscode,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return membershipForgotPasscodeFromJson(result.data.toString());
  }

  Future<bool> verifyOtpForgotPasscode(
      {required VerifyOtpForgotPasscodeRequest request}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.verifyOtpForgotPasscode,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }
}
