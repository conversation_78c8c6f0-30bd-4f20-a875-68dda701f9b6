import 'dart:async';
import 'package:curl_logger_dio_interceptor/curl_logger_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:tripc_app/models/local/token.dart';
import 'package:tripc_app/services/apis/interceptors/authentication_interceptor.dart';

class Api {
  Api() {
    // if (!kReleaseMode) {
    // dio.interceptors.add(LogInterceptor(responseBody: true, requestBody: true));
    dio.interceptors.add(CurlLoggerDioInterceptor(printOnSuccess: true));
    dio.interceptors.add(AuthenticationInterceptor(dio));
    //}
  }

  /// Credential info
  Token? token;

  /// Get base url by env
  final Dio dio = Dio();

  /// Get request header options
  Future<Options> getOptions(
      {String? contentType = Headers.jsonContentType}) async {
    final Map<String, String> header = <String, String>{
      Headers.acceptHeader: 'application/json'
    };
    return Options(headers: header, contentType: contentType);
  }

  /// Get auth header options
  Future<Options> getAuthOptions({String? contentType}) async {
    final Options options = await getOptions(contentType: contentType);

    if (token != null) {
      options.headers?.addAll(
          <String, String>{'Authorization': 'Bearer ${token?.accessToken}'});
    }

    return options;
  }

  /// Wrap Dio Exception
  Future<Response<T>> wrapE<T>(Future<Response<T>> Function() dioApi) async {
    try {
      return await dioApi();
    } catch (error) {
      if (error is DioException && error.type == DioExceptionType.badResponse) {
        final Response<dynamic>? response = error.response;
        final statusCodeToCheck = response?.statusCode;
        if ((statusCodeToCheck == 401)) {
          // server return 401 we should going back
          // if ((AppRoute.I.currentRoute != AppRoute.routeLogin) &&
          //     (AppRoute.I.currentRoute != AppRoute.routeSignUp)) {
          //   globalCacheAuth.logout();
          //   firebaseNotification.removeNotificationHandler();
          //   firebaseNotification.refreshToken();
          //   AppRoute.I.navigatorKey.currentState?.pushNamedAndRemoveUntil(
          //     AppRoute.routeLogin,
          //     (route) => false,
          //   );
          // }
        }

        try {
          /// By pass dio header error code to get response content
          /// Try to return response
          if (response != null) {
            final Response<T> res = Response<T>(
              data: response.data as T,
              headers: response.headers,
              requestOptions: response.requestOptions,
              isRedirect: response.isRedirect,
              statusCode: response.statusCode,
              statusMessage: response.statusMessage,
              redirects: response.redirects,
              extra: response.extra,
            );
            return res;
          }
        } catch (e) {
          print(e);
        }

        final String errorMessage =
            'Code ${response?.statusCode} - ${response?.statusMessage} ${response?.data != null ? '\n' : ''} ${response?.data}';
        throw DioException(
            requestOptions: error.requestOptions,
            response: error.response,
            type: error.type,
            error: errorMessage);
      }
      rethrow;
    }
  }
}
