import 'package:dio/dio.dart';
import 'package:tripc_app/models/app/verify_otp_request.dart';
import 'package:tripc_app/models/remote/api_status_response/api_status_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/app/app_api_endpoint.dart';
import '../../../models/remote/otp_token_response.dart';
import '../../providers/providers.dart';
import '../api.dart';

class ApiOtp extends Api {
  Future<ApiStatusResponse> sendOtp({required String email}) async {
    final result = await wrapE(() => dio.post(AppApiEndpoint.sendOtp,
        options: Options(
          responseType: ResponseType.plain,
        ),
        data: {'email': email}));
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString());
  }

  Future<ApiStatusResponse> verifyOtp(
      {required VerifyOtpRequest request}) async {
    final result = await wrapE(() => dio.post(AppApiEndpoint.verifyOtp,
        options: Options(
          responseType: ResponseType.plain,
        ),
        data: request.toJson()));
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString());
  }

  Future<ApiStatusResponse> verifyOtpLinkEmail(
      {required VerifyOtpRequest request}) async {
    final result = await wrapE(() => dio.post(AppApiEndpoint.verifyOtpLinkEmail,
        options: Options(
          responseType: ResponseType.plain,
          headers: {'Authorization': globalCacheAuth.getBearerToken()}
        ),
        data: request.toJson()));
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString());
  }

  Future<OtpTokenResponse> sendOtpLinkEmail({required String email}) async {
    final result = await wrapE(() => dio.post(AppApiEndpoint.linkEmail,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        data: {'email': email}));
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return optTokenResponseFromJson(result.data.toString());
  }
}
