import 'package:dio/dio.dart';
import 'package:tripc_app/models/app/sign_up_request.dart';
import 'package:tripc_app/models/remote/api_status_response/api_status_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/app/app_api_endpoint.dart';
import '../api.dart';

class ApiSignUp extends Api {
  Future<bool> register({required SignUpRequest request}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.register,
          options: Options(
            responseType: ResponseType.plain,
          ),
          data: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }
}
