import 'package:dio/dio.dart';
import 'package:tripc_app/models/app/update_user_request.dart';
import 'package:tripc_app/models/remote/api_status_response/api_status_response.dart';
import 'package:tripc_app/models/remote/auth_response/api_login_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/app/app_api_endpoint.dart';
import 'package:tripc_app/services/providers/providers.dart';
import '../../../models/remote/membership_repsonse/api_membership_verify_passcode_response.dart';
import '../api.dart';

class ApiAuth extends Api {
  Future<LoginResponse> getMe() async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getMe,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return loginResponseFromJson(result.data.toString());
  }

  Future<bool> updateUserInfo({required UpdateUserRequest request}) async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.updateUser,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        data: request.toJson(),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<bool> unLinkSocial({required String provider}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.unlinkSocial,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: {'provider': provider.toLowerCase()}),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<bool> loginMemberShip(
      {required String number, required String passcode}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.loginMemberShip,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: {'number': number, 'passcode': passcode}),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<VerifyPasscodeResponse> verifyOldPassCode(
      {required int? memberShipId, required String passcode}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.verifyOldPassCode,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: {'membership_id': memberShipId, 'passcode': passcode}),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return verifyPasscodeFromJson(result.data.toString());
  }

  Future<bool> linkSocial(
      {required String provider, required String token}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.linkSocial,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: {'provider': provider.toLowerCase(), 'token': token}),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<bool> inviteCode({required String inviteCode}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.inviteCode,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: {'invite_code': inviteCode}),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<bool> linkPhone({required String phone}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.linkPhone,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: {'phone': phone}),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<bool> markSkipInviteCode() async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.markSkipInviteCode,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()})),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<bool> markSkipTripC() async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.markSkipTripC,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()})),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<bool> deleteAccount() async {
    final result = await wrapE(
      () => dio.put(AppApiEndpoint.deleteAccount,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()})),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }
}
