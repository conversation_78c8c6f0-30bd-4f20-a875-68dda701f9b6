import 'package:dio/dio.dart';
import 'package:tripc_app/models/app/reset_password.request.dart';
import 'package:tripc_app/models/remote/api_status_response/api_status_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/app/app_api_endpoint.dart';
import '../api.dart';

class ApiResetPassword extends Api {
  Future<ApiStatusResponse> resetPassword(
      {required ResetPassRequest request}) async {
    final result = await wrapE(() => dio.post(AppApiEndpoint.resetPassword,
        options: Options(
          responseType: ResponseType.plain,
        ),
        data: request.toJson()));
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString());
  }
}
