import 'package:dio/dio.dart';
import 'package:tripc_app/models/remote/auth_response/api_login_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/app/app_api_endpoint.dart';
import 'package:tripc_app/utils/app_extension.dart';
import '../../../models/app/contact_request.dart';
import '../../../models/app/list_data_request.dart';
import '../../../models/remote/api_status_response/api_status_response.dart';
import '../../../models/remote/contact_response.dart';
import '../../../models/remote/notification_response.dart';
import '../../providers/providers.dart';
import '../api.dart';

class ApiUser extends Api {
  Future<LoginResponse> logInWithInputCredential({
    required String email,
    required String password,
    String? firebaseToken,
  }) async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.login,
        options: Options(
          responseType: ResponseType.plain,
        ),
        data: <String, dynamic>{
          'email': email,
          'password': password,
          if (firebaseToken.isNotNull) 'firebase_token': firebaseToken!,
        },
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return loginResponseFromJson(result.data.toString());
  }

  Future<LoginResponse> loginSns({
    required String provider,
    required String token,
    String? firebaseToken,
  }) async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.loginSns,
        options: Options(
          responseType: ResponseType.plain,
        ),
        data: <String, dynamic>{
          'provider': provider,
          'token': token,
          'firebase_token': firebaseToken ?? '1'
        },
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return loginResponseFromJson(result.data.toString());
  }

  Future<ListContactResponse> getContactList() async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getContactList,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listContactResponseFromJson(result.data.toString());
  }

  Future<bool> deleteContact({required int contactId}) async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.deleteContact(contactId),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<bool> updateContact(
      {required int contactId, required ContactRequest request}) async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.updateContact(contactId),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        data: request.toJson(),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<bool> addContact({required ContactRequest request}) async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.addContact,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        data: request.toJson(),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<ListNotificationResponse> getNotification(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.getNotification,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listNotificationResponseFromJson(result.data.toString());
  }

  Future<bool> markReadNotifications() async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.markReadNotifications,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<bool> markReadNotification({required int id}) async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.markReadOneNotification(id),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }
}
