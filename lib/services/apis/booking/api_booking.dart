import 'package:dio/dio.dart';
import 'package:tripc_app/models/remote/booking_response/service_responses/detail_booking_service_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/app/app_api_endpoint.dart';
import 'package:tripc_app/services/providers/providers.dart';
import '../../../models/app/booking_request/booking_request.dart';
import '../../../models/remote/booking_response/order_responses/booking_order_response.dart';
import '../../../models/remote/order_response/api_my_contact.dart';
import '../api.dart';

class ApiBooking extends Api {
  Future<DetailBookingServiceResponse> getDetailCuisine(
      {required int id}) async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getDetailCuisine(id),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return detailBookingServiceResponseFromJson(result.data.toString());
  }

  Future<GetIdByCodeResponse> createBooking(
      {required BookingRequest request}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.createBooking,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return getIdByCodeResponseFromJson(result.data.toString());
  }

  Future<BookingOrderResponse> getDetailBookingOrder(
      {required int orderId}) async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getReservationOrderDetail(orderId),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return bookingOrderResponseFromJson(result.data.toString());
  }
}
