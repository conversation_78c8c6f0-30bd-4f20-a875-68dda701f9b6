import 'package:dio/dio.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/app/refund_request.dart';
import 'package:tripc_app/models/remote/order_response/api_create_order_with_vnpay.dart';
import 'package:tripc_app/models/remote/order_response/api_my_contact.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';

import '../../../models/app/payment_request.dart';
import '../../../models/remote/common_error.dart';
import '../../../models/remote/order_response/create_order_with_payos.dart';
import '../../../models/remote/order_response/status_response.dart';
import '../../app/app_api_endpoint.dart';
import '../../providers/providers.dart';
import '../api.dart';

class ApiOrder extends Api {
  Future<CreateOrderWithVNPayResponse> createOrderTripCWithVNPay(
      {required int tripcId}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.createOrderTripCWithVNPay,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: {
            'tripc_id': tripcId,
          }),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return createOrderVNPayResponseFromJson(result.data.toString());
  }

  Future<bool> paymentFailure({required Map<String, dynamic> request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.paymentFailure,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: {
            'vnp_Amount': request['vnp_Amount'],
            'vnp_BankCode': request['vnp_BankCode'],
            'vnp_CardType': request['vnp_CardType'],
            'vnp_OrderInfo': request['vnp_OrderInfo'],
            'vnp_PayDate': request['vnp_PayDate'],
            'vnp_ResponseCode': request['vnp_ResponseCode'],
            'vnp_TmnCode': request['vnp_TmnCode'],
            'vnp_TransactionNo': request['vnp_TransactionNo'],
            'vnp_TransactionStatus': request['vnp_TransactionStatus'],
            'vnp_TxnRef': request['vnp_TxnRef'],
            'vnp_SecureHash': request['vnp_SecureHash'],
          }),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return statusResponseFromJson(result.data.toString()).status;
  }

  Future<CreateOrderWithVNPayResponse> createOrderTourWithVNPay(
      {required PaymentRequest request}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.createOrderTourWithVNPay,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return createOrderVNPayResponseFromJson(result.data.toString());
  }

  Future<CreateOrderWithPayOSResponse> createOrderTourWithPayOS(
      {required PaymentRequest request}) async {
    final result = await wrapE(
          () => dio.post(AppApiEndpoint.createOrderTourWithPayOS,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return createOrderPayOSResponseFromJson(result.data.toString());
  }

  Future<GetIdByCodeResponse> createOrderTourWithTcent(
      {required PaymentRequest request}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.createOrderTourWithTcent,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return getIdByCodeResponseFromJson(result.data.toString());
  }

  Future<GetIdByCodeResponse> createOrderTourPayLater(
      {required PaymentRequest request}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.createOrderTourPayLater,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return getIdByCodeResponseFromJson(result.data.toString());
  }

  Future<MyOrderResponse> getMyOrders(ListDataRequest request) async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getMyOrder,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        queryParameters: request.toJson(),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return myOrderResponseFromJson(result.data.toString());
  }

  Future<OrderDetailResponse> getOrderDetails({required int orderId}) async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getOrderDetail(orderId),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return orderDetailResponseFromJson(result.data.toString());
  }

  Future<bool> requestRefund({RefundRequest? request}) async {
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.requestRefund,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: request?.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return statusResponseFromJson(result.data.toString()).status;
  }

  Future<GetIdByCodeResponse> getIdByCode({String? txnRef}) async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getIdByCode(txnRef ?? ''),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return getIdByCodeResponseFromJson(result.data.toString());
  }
}
