import 'dart:io';

import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import 'package:tripc_app/models/remote/api_s3_response/upload_s3_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/app/app_api_endpoint.dart';
import 'package:tripc_app/services/providers/providers.dart';
import '../api.dart';

class ApiUploadS3 extends Api {
  Future<UploadS3Response> uploadImageToS3({required File file}) async {
    String fileName = file.path.split('/').last;

    FormData formData = FormData.fromMap({
      "file": await MultipartFile.fromFile(
        file.path,
        filename: fileName,
        contentType: MediaType('image', 'jpeg'), // Optional
      ),
    });
    final result = await wrapE(
      () => dio.post(AppApiEndpoint.makeS3UploadUrl,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          data: formData),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return uploadS3ResponseFromJson(result.data.toString());
  }
}
