import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/remote/api_tour_response/api_tour_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/service_type_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/list_supplier_response.dart';
import 'package:tripc_app/services/app/app_api_endpoint.dart';
import 'package:tripc_app/services/providers/providers.dart';
import '../../../models/remote/api_status_response/api_status_response.dart';
import '../../../models/remote/api_tour_response/category_by_slug.dart';
import '../../../models/remote/booking_response/supplier_responses/detail_supplier_response.dart';
import '../../../models/remote/search_response/keyword_response.dart';
import '../api.dart';

class ApiTours extends Api {
  Future<CategoryBySlugResponse> getServiceCategoryBySlug(
      {required String slug}) async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getServiceCategoryBySlug(slug),
        options: Options(
          responseType: ResponseType.plain,
        ),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return categoryBySlugResponseFromJson(result.data.toString());
  }

  Future<ListTourResponse> getTourDealsAroundHere(
      {required ListDataRequest request, int? categoryId}) async {
    final Map<String, dynamic> queryParams = {
      ...request.toJson(),
      'categories': categoryId,
    };

    final result = await wrapE(
      () => dio.get(AppApiEndpoint.getTourDealsAroundHere,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: queryParams),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<ListTourResponse> getTourDiscounts(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.getTourDiscounts,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<ListTourResponse> getTourBestSeller(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.getTourBestSeller,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<ListTourResponse> getTourBuyMoreEarnMore(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.getTourBuyMoreEarnMore,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<DetailedTourResponse> getDetailedTour({required int tourId}) async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getDetailedTour(tourId),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return detailedTourResponseFromJson(result.data.toString());
  }

  Future<DetailSupplierResponse> getDetailBookingSupplier(
      {required int restaurantId}) async {
    final result = await wrapE(
      () => dio.get(
        '${AppApiEndpoint.getBookingSuppliers}/$restaurantId',
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return detailSupplierResponseFromJson(result.data);
  }

  Future<SavedTourResponse> savedTour({required int tourId}) async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.savedTour(tourId),
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return savedTourResponseFromJson(result.data);
  }

  Future<ListTourResponse> getSaveTour(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.getSaveTour,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<ListTourResponse> getRecentlyViewed(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.getRecentlyViewed,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<bool> deleteRecentlyViewed() async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.deleteRecentlyViewed,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<ListTourResponse> getSavedTours(
      {required ListDataRequest request}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.getSavedTour,
          options: Options(
              responseType: ResponseType.plain,
              headers: {'Authorization': globalCacheAuth.getBearerToken()}),
          queryParameters: request.toJson()),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<ListKeywordResponse> getPopularKeyword() async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.getPopularKeyword,
          options: Options(responseType: ResponseType.plain)),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listKeywordResponseFromJson(result.data.toString());
  }

  Future<ListTourResponse> getTopSearchTours() async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getTopSearchTours,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<ListTourResponse> getPopularSearchTours() async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getPopularSearchTours,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<ListTourResponse> getPopularTours() async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getPopularSearchTours,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<ListKeywordResponse> getSearchSuggest({required String txt}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.getSearchSuggest,
          options: Options(responseType: ResponseType.plain),
          queryParameters: {
            'q': txt,
          }),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listKeywordResponseFromJson(result.data.toString());
  }

  Future<ListTourResponse> searchTours({required String txt}) async {
    final result = await wrapE(
      () => dio.get(AppApiEndpoint.getTourDealsAroundHere,
          options: Options(
            responseType: ResponseType.plain,
          ),
          queryParameters: {
            'keyword': txt,
          }),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<ServiceTypesResponse> getServiceTypeByParentId(
      {required int id}) async {
    final result = await wrapE(
      () => dio.get(
        '${AppApiEndpoint.serviceTypeByParent}/$id',
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return serviceTypesResponseFromJson(result.data.toString());
  }

  Future<ServiceTypesSlugResponse> getServiceTypeByParentSlug(
      {required String slug}) async {
    final result = await wrapE(
      () => dio.get(
        '${AppApiEndpoint.serviceTypeEndpoint}/slug/$slug',
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return serviceTypeResponseFromJson(result.data.toString());
  }

  Future<ServiceTypesResponse> getServiceTypesList() async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.serviceTypesList,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return serviceTypesResponseFromJson(result.data.toString());
  }

  Future<ListTourResponse> getToursListByCategory(
      {required ListDataRequest request, required int categoryId}) async {
    final Map<String, dynamic> queryParams = {
      ...request.toJson(),
      'categories': categoryId,
    };
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getTours,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        queryParameters: queryParams,
      ),
    );

    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<ListSupplierResponse> getBookingSuppliersByTypeId(
      {required ListDataRequest request, required int productTypeId}) async {
    final Map<String, dynamic> queryParams = {
      ...request.toJson(),
      'product_type_id': productTypeId,
    };
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getBookingSuppliers,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        queryParameters: queryParams,
      ),
    );

    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }

    return ListSupplierResponse.fromJson(json.decode(result.data));
  }

  Future<ListSupplierResponse> getRestaurantsBySlug(
      {required ListDataRequest request, required String slug}) async {
    final Map<String, dynamic> queryParams = {
      ...request.toJson(),
      'supplier_type_slug': slug,
    };
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getBookingSuppliers,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        queryParameters: queryParams,
      ),
    );

    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }

    return ListSupplierResponse.fromJson(json.decode(result.data));
  }

  Future<ListTourResponse> getToursFerry(
      {required ListDataRequest request}) async {
    final Map<String, dynamic> queryParams = {
      ...request.toJson(),
    };
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getToursFerry,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        queryParameters: queryParams,
      ),
    );

    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return listTourResponseFromJson(result.data.toString());
  }

  Future<ServiceTypesResponse> getProductTypes(
      {required String supplierTypeSlug}) async {
    final Map<String, dynamic> queryParams = {
      'supplier_type_slug': supplierTypeSlug,
    };

    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getProductTypes,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        queryParameters: queryParams,
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return serviceTypesResponseFromJson(result.data.toString());
  }
}
