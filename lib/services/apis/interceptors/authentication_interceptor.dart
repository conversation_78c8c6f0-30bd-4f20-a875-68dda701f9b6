import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:tripc_app/models/remote/auth_response/api_login_response.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';

import '../../app/app_api_endpoint.dart';

class AuthenticationInterceptor extends Interceptor {
  final Dio dio;
  
  static final StreamController<bool> internetMonitoringStream = StreamController();

  AuthenticationInterceptor(this.dio);

  @override
  Future<void> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    if (!options.path.contains(AppApiEndpoint.refreshToken)) {
      final isNoInternet = await _checkNoInternet();
      if (isNoInternet) {
        internetMonitoringStream.add(true);
        return handler.reject(
          DioException(
            requestOptions: options,
            response: Response(
              requestOptions: options,
              statusMessage: 'No internet connection',
              data: 'No internet connection',
            ),
            message: 'No internet connection',
            error: 'No internet connection',
          ),
        );
      }

      if (_checkCurrentTokenExpired) {
        final newToken = await onRefreshNewToken();
        if (newToken != null) {
          options.headers['Authorization'] = 'Bearer $newToken';
        }
      }
    }

    return handler.next(options);
  }

  void _redirectToLogin() {
    globalCacheAuth.logout();
    final context = AppRoute.I.navigatorKey.currentContext;
    if (context == null) {
      return;
    }
    Navigator.of(context).pushNamedAndRemoveUntil(
        AppRoute.routeSignIn, (Route<dynamic> route) => false);
  }

  Future<bool> _checkNoInternet() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult.contains(ConnectivityResult.none);
  }

  Future<String?> onRefreshNewToken() async {
    try {
      final response = await dio.post(AppApiEndpoint.refreshToken,
          options: Options(
            responseType: ResponseType.plain,
          ),
          data: {
            'refresh_token': globalCacheAuth.getToken(),
          });
      final loginResponse = loginResponseFromJson(response.data.toString());
      LoginResponse auth = globalCacheAuth.savedAuth;
      auth = auth.copyWith(
          token: loginResponse.token, refreshToken: loginResponse.refreshToken);
      return auth.token;
    } catch (e) {
      _redirectToLogin();
    }
    return null;
  }

  bool get _checkCurrentTokenExpired {
    try {
      final token = globalCacheAuth.getToken();
      if (token == null) return false;

      final exp = JWT.decode(token).payload['exp'] as int;
      final expDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      final currentDate = DateTime.now();
      const fiveMinutes = Duration(minutes: 5);

      return currentDate.add(fiveMinutes).isAfter(expDate);
    } catch (e) {
      return true;
    }
  }

}
