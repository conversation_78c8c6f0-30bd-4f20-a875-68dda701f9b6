import 'package:dio/dio.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/app/app_api_endpoint.dart';
import 'package:tripc_app/services/providers/providers.dart';
import '../../../models/app/feedback_request.dart';
import '../../../models/remote/api_status_response/api_status_response.dart';
import '../../../models/remote/feedback_response/list_feedback_response.dart';
import '../api.dart';

class ApiFeedback extends Api {
  Future<bool> createFeedback(FeedbackRequest request) async {
    final result = await wrapE(
      () => dio.post(
        AppApiEndpoint.feedback,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
        data: request.toJson(),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiStatusResponseFromJson(result.data.toString()).status;
  }

  Future<ListFeedBackResponse> feedbacks() async {
    final result = await wrapE(
      () => dio.get(
        AppApiEndpoint.getListFeedbacks,
        options: Options(
            responseType: ResponseType.plain,
            headers: {'Authorization': globalCacheAuth.getBearerToken()}),
      ),
    );
    final error = commonErrorResponseFromJson(result.data.toString());
    if ((result.statusCode ?? 200) > 299) {
      throw error;
    }
    return apiListFeedbackResponseFromJson(result.data.toString());
  }
}
