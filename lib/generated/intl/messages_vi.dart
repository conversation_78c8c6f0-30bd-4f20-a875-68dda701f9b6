// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  static String m0(content) => "Khoảng ${content} ngày trước";

  static String m1(content) => "Số lượng khách (${content})";

  static String m2(content) => "Khoảng ${content} giờ trước";

  static String m3(content) => "Khoảng ${content} phút trước";

  static String m4(content) => "Khoảng ${content} tháng trước";

  static String m5(content) => "Vé người lớn ${content} (Trên 10 tuổi)";

  static String m6(content) => "Mã đặt chỗ: ${content}";

  static String m7(content) => "Vé trẻ em ${content} (5 - 9 tuổi)";

  static String m8(content) => "Còn ${content} ngày";

  static String m9(content) => "${content} ngày trước";

  static String m10(content) => "Mã QR vé ${content}";

  static String m11(content) => "Đã nhận ${content}";

  static String m12(content) =>
      "Bạn đã nhập quá nhiều lần, nên chúng tôi đã tạm thời khóa tài khoản của bạn.\nVui lòng thử lại sau ${content}s.";

  static String m13(content) =>
      "Bạn đang có ${content} mã giảm giá, hãy tận hưởng";

  static String m14(content) => "Khoảng ${content} năm trước";

  static String m15(content) =>
      "(Bạn đang có ${content} mã giảm giá, hãy tận hưởng)";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "baby": MessageLookupByLibrary.simpleMessage("Em bé"),
        "below_5_year_old": MessageLookupByLibrary.simpleMessage("Dưới 5 tuổi"),
        "booking": MessageLookupByLibrary.simpleMessage("Đặt chỗ"),
        "booking_failure":
            MessageLookupByLibrary.simpleMessage("Đặt chỗ thất bại"),
        "booking_information":
            MessageLookupByLibrary.simpleMessage("Thông tin đặt chỗ"),
        "booking_success":
            MessageLookupByLibrary.simpleMessage("Đặt chỗ thành công"),
        "booking_success_dialog_content": MessageLookupByLibrary.simpleMessage(
            "Cảm ơn bạn đã đặt chỗ. Chúng tôi sẽ gửi email xác nhận trong ít phút nữa. Chúc bạn có trải nghiệm tuyệt vời tại nhà hàng!"),
        "business_information":
            MessageLookupByLibrary.simpleMessage("Thông tin doanh nghiệp"),
        "business_information_of_company_own_tripc":
            MessageLookupByLibrary.simpleMessage(
                "Thông tin doanh nghiệp sở hữu nền tảng TripC AI"),
        "business_registration_number": MessageLookupByLibrary.simpleMessage(
            "Mã số doanh nghiệp: 0402251123, cấp bởi Phòng đăng ký kinh doanh – Sở Tài chính TP. Đà Nẵng ngày 09/10/2024"),
        "cat_ba_tuan_chau_ferry_route": MessageLookupByLibrary.simpleMessage(
            "Tuyến phà Cát Bà - Tuần Châu"),
        "children": MessageLookupByLibrary.simpleMessage("Trẻ em"),
        "close": MessageLookupByLibrary.simpleMessage("Đóng cửa"),
        "coming_time": MessageLookupByLibrary.simpleMessage("Thời gian đến"),
        "company_address": MessageLookupByLibrary.simpleMessage(
            "Địa chỉ: 153 Đống Đa, phường Thạch Thang, quận Hải Châu, TP. Đà Nẵng, Việt Nam"),
        "company_name":
            MessageLookupByLibrary.simpleMessage("Công ty cổ phần Ally AI"),
        "company_phone":
            MessageLookupByLibrary.simpleMessage("Điện thoại: +***********"),
        "company_phone_2":
            MessageLookupByLibrary.simpleMessage("Điện thoại: +84963414455"),
        "complete_booking":
            MessageLookupByLibrary.simpleMessage("Hoàn tất đặt chỗ"),
        "confirmed": MessageLookupByLibrary.simpleMessage("Đã xác nhận"),
        "content": MessageLookupByLibrary.simpleMessage("Nội dung"),
        "date": MessageLookupByLibrary.simpleMessage("Ngày"),
        "day_ago": m0,
        "detail_booking":
            MessageLookupByLibrary.simpleMessage("Chi tiết đặt chỗ"),
        "duration": MessageLookupByLibrary.simpleMessage("Thời lượng"),
        "enter_content_no_more_than_250_characters":
            MessageLookupByLibrary.simpleMessage(
                "Nhập nội dung không quá 250 kí tự"),
        "enter_establishment_decision_number":
            MessageLookupByLibrary.simpleMessage(
                "Nhập số quyết định thành lập"),
        "enter_name_of_social_organization":
            MessageLookupByLibrary.simpleMessage("Nhập tên tổ chức xã hội"),
        "enter_number_plate":
            MessageLookupByLibrary.simpleMessage("Nhập biển số xe của bạn"),
        "enter_special_request":
            MessageLookupByLibrary.simpleMessage("Nhập yêu cầu đặt biệt..."),
        "enter_your_business_address":
            MessageLookupByLibrary.simpleMessage("Nhập địa chỉ doanh nghiệp"),
        "enter_your_business_email":
            MessageLookupByLibrary.simpleMessage("Nhập email doanh nghiệp"),
        "enter_your_business_tax_code": MessageLookupByLibrary.simpleMessage(
            "Nhập mã số thuế doanh nghiệp"),
        "entertainment": MessageLookupByLibrary.simpleMessage("Giải trí"),
        "establishment_decision_number":
            MessageLookupByLibrary.simpleMessage("Số quyết định thành lập"),
        "event": MessageLookupByLibrary.simpleMessage("Sự kiện"),
        "every_day": MessageLookupByLibrary.simpleMessage("Hàng ngày"),
        "feedback_success_content": MessageLookupByLibrary.simpleMessage(
            "Nội dung phản ánh đã được cập nhật trên hệ thống của TripC"),
        "feedback_successfully":
            MessageLookupByLibrary.simpleMessage("Phản ánh thành công"),
        "food": MessageLookupByLibrary.simpleMessage("Ẩm thực"),
        "for_partners_of_TripC": MessageLookupByLibrary.simpleMessage(
            "Dành cho đối tác: Quy trình hợp tác với TripC AI"),
        "for_partners_of_TripC_step_one": MessageLookupByLibrary.simpleMessage(
            "Bước 1: Đăng ký & liên hệ: Gửi thông tin đăng ký hoặc liên hệ với TripC AI qua email"),
        "for_partners_of_TripC_step_three": MessageLookupByLibrary.simpleMessage(
            "Bước 3: Triển khai & vận hành: Đối tác xác nhận thông tin và chính thức đưa sản phẩm/dịch vụ lên TripC để tiếp cận hàng ngàn khách hàng tiềm năng."),
        "for_partners_of_TripC_step_two": MessageLookupByLibrary.simpleMessage(
            "Bước 2: Ký hợp đồng: Hoàn thiện thủ tục, ký kết hợp đồng hợp tác để bắt đầu bán sản phẩm/dịch vụ trên nền tảng TripC."),
        "from_5_to_9_year_old":
            MessageLookupByLibrary.simpleMessage("5 - 9 tuổi"),
        "go_back_to_homepage":
            MessageLookupByLibrary.simpleMessage("Trở về trang chủ"),
        "gold_and_sport":
            MessageLookupByLibrary.simpleMessage("Golf và thể thao"),
        "guest_quantity":
            MessageLookupByLibrary.simpleMessage("Số lượng khách"),
        "guest_quantity_with_count": m1,
        "have_fun": MessageLookupByLibrary.simpleMessage("Vui chơi"),
        "health_beauty_category":
            MessageLookupByLibrary.simpleMessage("Sức khỏe & Làm đẹp"),
        "hello": MessageLookupByLibrary.simpleMessage("Hello"),
        "hour_ago": m2,
        "i_agree_with":
            MessageLookupByLibrary.simpleMessage("Tôi đồng ý với các "),
        "introduction": MessageLookupByLibrary.simpleMessage("Giới thiệu"),
        "invoices_for_businesses":
            MessageLookupByLibrary.simpleMessage("Hóa đơn cho doanh nghiệp"),
        "invoices_for_individuals":
            MessageLookupByLibrary.simpleMessage("Hóa đơn cho cá nhân"),
        "issue_electronic_invoice":
            MessageLookupByLibrary.simpleMessage("Xuất hóa đơn điện tử"),
        "legal_representative": MessageLookupByLibrary.simpleMessage(
            "Người đại diện pháp luật: Ông BÙI XUÂN QUYỀN"),
        "list_feedback_social_organization":
            MessageLookupByLibrary.simpleMessage(
                "Danh sách phản ánh tổ chức xã hội"),
        "location": MessageLookupByLibrary.simpleMessage("Địa điểm"),
        "min_ago": m3,
        "ministry_of_industry_notice": MessageLookupByLibrary.simpleMessage(
            "Ứng dụng đang hoạt động thử nghiệm và đang trong quá trình đăng ký với Bộ Công Thương"),
        "month_ago": m4,
        "name_of_social_organization":
            MessageLookupByLibrary.simpleMessage("Tên tổ chức xã hội"),
        "no_deposit_required_when_booking":
            MessageLookupByLibrary.simpleMessage("Không cần cọc khi đặt chỗ"),
        "number_plate": MessageLookupByLibrary.simpleMessage("Biển số xe"),
        "of_TripC": MessageLookupByLibrary.simpleMessage(" của TripC"),
        "optional": MessageLookupByLibrary.simpleMessage("Không bắt buộc"),
        "or_phone_number": MessageLookupByLibrary.simpleMessage(
            "hoặc số điện thoại 0935479122."),
        "order_code": MessageLookupByLibrary.simpleMessage("Mã đơn hàng"),
        "order_information":
            MessageLookupByLibrary.simpleMessage("Thông tin đơn hàng"),
        "other_menu_services":
            MessageLookupByLibrary.simpleMessage("Dịch Vụ Khác"),
        "other_products":
            MessageLookupByLibrary.simpleMessage("Các sản phẩm khác"),
        "our_products":
            MessageLookupByLibrary.simpleMessage("Sản phẩm của chúng tôi"),
        "partnership_contact": MessageLookupByLibrary.simpleMessage(
            "Liên hệ hợp tác và phối hợp với cơ quan quản lý nhà nước: Ông BÙI XUÂN QUYỀN"),
        "quantity": MessageLookupByLibrary.simpleMessage("Số lượng"),
        "receive_feedback_social_organization":
            MessageLookupByLibrary.simpleMessage(
                "Tiếp nhận phản ánh tổ chức xã hội"),
        "regulatory_information":
            MessageLookupByLibrary.simpleMessage("Thông tin quy định"),
        "request_for_electronic_invoice": MessageLookupByLibrary.simpleMessage(
            "Yêu cầu xuất hóa đơn điện tử"),
        "request_order_recorded": MessageLookupByLibrary.simpleMessage(
            "Yêu cầu đặt chỗ đã được ghi nhận!"),
        "reservation_date": MessageLookupByLibrary.simpleMessage("Ngày đến"),
        "reservation_time": MessageLookupByLibrary.simpleMessage("Giờ đến"),
        "select_date": MessageLookupByLibrary.simpleMessage("Lựa chọn ngày"),
        "serving_at": MessageLookupByLibrary.simpleMessage("Phục vụ tại"),
        "shopping": MessageLookupByLibrary.simpleMessage("Mua sắm"),
        "sort": MessageLookupByLibrary.simpleMessage("Sắp xếp"),
        "suggest_for_you":
            MessageLookupByLibrary.simpleMessage("Gợi ý cho bạn"),
        "surcharge_included":
            MessageLookupByLibrary.simpleMessage("Đã bao gồm phụ thu"),
        "taller_than_1_meter":
            MessageLookupByLibrary.simpleMessage("Cao trên 1m"),
        "term_of_use":
            MessageLookupByLibrary.simpleMessage("Điều Khoản Sử dụng"),
        "terms_and_conditions":
            MessageLookupByLibrary.simpleMessage("Điều kiện áp dụng"),
        "terms_and_privacy_policy": MessageLookupByLibrary.simpleMessage(
            "Điều kiện và Chính sách quyền riêng tư"),
        "text_account_linking":
            MessageLookupByLibrary.simpleMessage("Liên Kết Tài Khoản"),
        "text_account_linking_description":
            MessageLookupByLibrary.simpleMessage(
                "Liên kết tài khoản cho phép bạn đăng nhập TripC nhanh chóng"),
        "text_account_management":
            MessageLookupByLibrary.simpleMessage("Quản lý tài khoản"),
        "text_account_privacy": MessageLookupByLibrary.simpleMessage(
            "TripC có quyền tạm khóa hoặc hủy tài khoản nếu phát hiện hành vi gian lận, vi phạm điều khoản."),
        "text_account_responsibility": MessageLookupByLibrary.simpleMessage(
            "Bạn có trách nhiệm bảo mật thông tin đăng nhập và không chia sẻ tài khoản với người khác."),
        "text_account_settings":
            MessageLookupByLibrary.simpleMessage("Cài Đặt Tài Khoản"),
        "text_actual_refund_amount":
            MessageLookupByLibrary.simpleMessage("Số tiền được hoàn thực tế"),
        "text_actual_refund_amount_2":
            MessageLookupByLibrary.simpleMessage("Tiền hoàn thực tế"),
        "text_add_new_contact":
            MessageLookupByLibrary.simpleMessage("Thêm Liên Hệ Mới"),
        "text_add_new_tripc_id":
            MessageLookupByLibrary.simpleMessage("Thêm Mới TripC ID"),
        "text_add_passenger":
            MessageLookupByLibrary.simpleMessage("Thêm hành khách"),
        "text_add_passenger_information":
            MessageLookupByLibrary.simpleMessage("Thêm thông tin hành Khách"),
        "text_add_phone_number":
            MessageLookupByLibrary.simpleMessage("Thêm Số Điện Thoại"),
        "text_add_quantity_passengers":
            MessageLookupByLibrary.simpleMessage("Thêm số lượng hành khách"),
        "text_add_special_request":
            MessageLookupByLibrary.simpleMessage("Thêm yêu cầu đặc biệt"),
        "text_address": MessageLookupByLibrary.simpleMessage("Địa Chỉ:"),
        "text_adult_above_10":
            MessageLookupByLibrary.simpleMessage("Người lớn (trên 10 tuổi)"),
        "text_adult_info":
            MessageLookupByLibrary.simpleMessage("Thông tin người lớn"),
        "text_adult_ticket": m5,
        "text_agree": MessageLookupByLibrary.simpleMessage("Đồng Ý"),
        "text_aldult": MessageLookupByLibrary.simpleMessage("Người lớn"),
        "text_all": MessageLookupByLibrary.simpleMessage("Tất cả"),
        "text_amount_paid":
            MessageLookupByLibrary.simpleMessage("Tiền đã thanh toán"),
        "text_application_activity":
            MessageLookupByLibrary.simpleMessage("Hoạt động trên ứng dụng"),
        "text_application_period":
            MessageLookupByLibrary.simpleMessage("Thời gian áp dụng:"),
        "text_apply": MessageLookupByLibrary.simpleMessage("Áp dụng"),
        "text_as_u_know_delete_account": MessageLookupByLibrary.simpleMessage(
            "Như bạn đã biết, khi bạn xóa tài khoản"),
        "text_as_u_know_remove_tripcid": MessageLookupByLibrary.simpleMessage(
            "Như bạn đã biết, khi bạn gỡ TripC ID"),
        "text_at_last_page":
            MessageLookupByLibrary.simpleMessage("Bạn đã lướt đến cuối trang."),
        "text_available_omorrow":
            MessageLookupByLibrary.simpleMessage("Có Sẵn Ngày Mai"),
        "text_back_to_home":
            MessageLookupByLibrary.simpleMessage("Về Trang Chủ"),
        "text_bad": MessageLookupByLibrary.simpleMessage("Tệ"),
        "text_bad_service_or_product_experience":
            MessageLookupByLibrary.simpleMessage(
                "Trải nghiệm không tốt với dịch vụ/Sản phẩm"),
        "text_bad_weather_or_natural_disaster":
            MessageLookupByLibrary.simpleMessage(
                "Thời tiết xấu hoặc thiên tai"),
        "text_been_term_locked": MessageLookupByLibrary.simpleMessage(
            "Tài khoản của bạn đã bị tạm khóa"),
        "text_before_placing":
            MessageLookupByLibrary.simpleMessage("Trước khi đặt"),
        "text_best_selling":
            MessageLookupByLibrary.simpleMessage("Tour bán chạy"),
        "text_big_deals": MessageLookupByLibrary.simpleMessage("Ưu đãi lớn"),
        "text_block_type_passcode": MessageLookupByLibrary.simpleMessage(
            "Bạn đã nhập sai passcode quá 5 lần và bị khóa trong vòng 24h."),
        "text_book_now": MessageLookupByLibrary.simpleMessage("Đặt ngay"),
        "text_booking_and_payment":
            MessageLookupByLibrary.simpleMessage("2. Đặt Tour & Thanh Toán"),
        "text_booking_code": m6,
        "text_booking_code_title":
            MessageLookupByLibrary.simpleMessage("Mã đặt chỗ"),
        "text_booking_date_time":
            MessageLookupByLibrary.simpleMessage("Ngày giờ đặt:"),
        "text_bronze_rank": MessageLookupByLibrary.simpleMessage("Hạng Đồng"),
        "text_bronze_tier": MessageLookupByLibrary.simpleMessage("Hạng Đồng"),
        "text_business_address":
            MessageLookupByLibrary.simpleMessage("Địa chỉ doanh nghiệp"),
        "text_business_name":
            MessageLookupByLibrary.simpleMessage("Tên doanh nghiệp"),
        "text_buy_more_get_rewards":
            MessageLookupByLibrary.simpleMessage("Mua nhiều thưởng lớn"),
        "text_by_clicking_pay_button": MessageLookupByLibrary.simpleMessage(
            "Bằng việc nhấn vào nút Thanh toán, bạn xác nhận là Tôi đã đồng ý với các "),
        "text_camera": MessageLookupByLibrary.simpleMessage("Máy ảnh"),
        "text_cancel": MessageLookupByLibrary.simpleMessage("Huỷ"),
        "text_cancel_free":
            MessageLookupByLibrary.simpleMessage("Hủy miễn phí"),
        "text_cancel_request_received": MessageLookupByLibrary.simpleMessage(
            "Chúng tôi đã nhận được yêu cầu hủy đơn này của bạn. Trong vòng 7-14 ngày, nhà cung cấp dịch vụ sẽ đưa ra kết quả cho bạn sớm nhất."),
        "text_cancel_request_sent": MessageLookupByLibrary.simpleMessage(
            "Yêu cầu hủy đơn đã được gửi đi"),
        "text_canceled": MessageLookupByLibrary.simpleMessage("Đã hủy"),
        "text_cancellation_policy": MessageLookupByLibrary.simpleMessage(
            "Chính sách hủy tour và hoàn tiền phụ thuộc vào từng nhà cung cấp dịch vụ."),
        "text_cancellation_time":
            MessageLookupByLibrary.simpleMessage("Thời gian hủy"),
        "text_cancle": MessageLookupByLibrary.simpleMessage("Hủy"),
        "text_cannot_be_canceled_one_actived":
            MessageLookupByLibrary.simpleMessage(
                "Không thể hủy sau khi kích hoạt"),
        "text_cannot_remove_default_tripcid": MessageLookupByLibrary.simpleMessage(
            "Mỗi tài khoản bắt buộc phải có một TripC ID, vì vậy bạn không thể gỡ TripC ID khỏi tài khoản của mình."),
        "text_card_balance_account":
            MessageLookupByLibrary.simpleMessage("Số dư:"),
        "text_card_exp_label":
            MessageLookupByLibrary.simpleMessage("Hạn dùng:"),
        "text_card_name_label": MessageLookupByLibrary.simpleMessage("Tên:"),
        "text_change_data_sharing_rights": MessageLookupByLibrary.simpleMessage(
            "Bạn có thể thay đổi quyền này trong Cài đặt thiết bị."),
        "text_change_email_link":
            MessageLookupByLibrary.simpleMessage("Thay đổi Email Liên kết"),
        "text_change_of_plan":
            MessageLookupByLibrary.simpleMessage("Thay đổi kế hoạch"),
        "text_child_info":
            MessageLookupByLibrary.simpleMessage("Thông tin trẻ em"),
        "text_child_name":
            MessageLookupByLibrary.simpleMessage("Họ và Tên của trẻ em:"),
        "text_child_name_hint":
            MessageLookupByLibrary.simpleMessage("Nhập họ và tên của bé"),
        "text_child_ticket": m7,
        "text_children": MessageLookupByLibrary.simpleMessage("trẻ em"),
        "text_children_5_to_9":
            MessageLookupByLibrary.simpleMessage("Trẻ em (5 - 9 tuổi)"),
        "text_choose_a_tour_date":
            MessageLookupByLibrary.simpleMessage("Chọn Ngày Tham Quan"),
        "text_choose_beautiful_number":
            MessageLookupByLibrary.simpleMessage("Chọn TripC ID số đẹp"),
        "text_choose_destination":
            MessageLookupByLibrary.simpleMessage("Chọn điểm đến"),
        "text_choose_destination_content": MessageLookupByLibrary.simpleMessage(
            "Tìm địa điểm nghỉ dưỡng lý tưởng của bạn, Tìm nơi nghỉ dưỡng lý tưởng của bạn và lên kế hoạch cho chuyến đi của bạn"),
        "text_choose_image_source":
            MessageLookupByLibrary.simpleMessage("Chọn nguồn hình ảnh"),
        "text_choose_tripc_point_1": MessageLookupByLibrary.simpleMessage(
            "✅ Đặt tour nhanh chóng, an toàn: Chỉ vài thao tác, bạn có thể tìm kiếm và đặt tour phù hợp với nhu cầu."),
        "text_choose_tripc_point_2": MessageLookupByLibrary.simpleMessage(
            "✅ Ưu đãi hấp dẫn: Cập nhật liên tục các chương trình giảm giá, voucher khuyến mãi dành cho khách hàng."),
        "text_choose_tripc_point_3": MessageLookupByLibrary.simpleMessage(
            "✅ Dịch vụ chất lượng: Lịch trình rõ ràng, hướng dẫn viên chuyên nghiệp, đảm bảo mang lại trải nghiệm tuyệt vời."),
        "text_choose_tripc_point_4": MessageLookupByLibrary.simpleMessage(
            "✅ Hỗ trợ 24/7: Đội ngũ CSKH luôn sẵn sàng giải đáp mọi thắc mắc, giúp bạn an tâm trên mọi hành trình."),
        "text_city_of_residence":
            MessageLookupByLibrary.simpleMessage("Thành phố cư trú"),
        "text_close": MessageLookupByLibrary.simpleMessage("Đóng"),
        "text_collapse": MessageLookupByLibrary.simpleMessage("Thu gọn"),
        "text_combo": MessageLookupByLibrary.simpleMessage("Combo"),
        "text_comming_soon_text": MessageLookupByLibrary.simpleMessage(
            "Tính năng đang phát triển\nSẽ ra mắt trong thời gian sớm nhất"),
        "text_commitment_to_data_protection": MessageLookupByLibrary.simpleMessage(
            "TripC cam kết bảo vệ dữ liệu cá nhân của bạn và cung cấp các tùy chọn kiểm soát quyền riêng tư để bạn có thể sử dụng ứng dụng một cách an toàn và minh bạch."),
        "text_company": MessageLookupByLibrary.simpleMessage("Công Ty"),
        "text_conditional_cancellation":
            MessageLookupByLibrary.simpleMessage("Hủy Có Điều Kiện"),
        "text_conditions_and_privacy": MessageLookupByLibrary.simpleMessage(
            "Điều kiện và Chính sách quyền riêng tư"),
        "text_confirm": MessageLookupByLibrary.simpleMessage("Xác Nhận"),
        "text_confirmation_code_has_been_sent":
            MessageLookupByLibrary.simpleMessage("Mã xác nhận đã được gửi"),
        "text_congratulation_and_make_passcode":
            MessageLookupByLibrary.simpleMessage(
                "Chúc mừng bạn đã sở hữu dãy số đầy ý nghĩa.\nTiếp theo, hãy tạo Passcode cho tài khoản của mình nhé!"),
        "text_congratulation_create_membership_card":
            MessageLookupByLibrary.simpleMessage(
                "Chúc mừng bạn đã khởi tạo thẻ thành viên thành công.\nHãy trải nghiệm các tính năng và ưu đãi mới nhé."),
        "text_congratulations_on_receiving_the_offer":
            MessageLookupByLibrary.simpleMessage(
                "Chúc mừng bạn đã nhận được ưu đãi"),
        "text_contact": MessageLookupByLibrary.simpleMessage("Liên hệ"),
        "text_contact_information":
            MessageLookupByLibrary.simpleMessage("Thông Tin Liên Lạc"),
        "text_contact_message_1": MessageLookupByLibrary.simpleMessage(
            "Mọi thắc mắc hoặc yêu cầu hỗ trợ, vui lòng liên hệ với TripC qua email "),
        "text_contact_message_2":
            MessageLookupByLibrary.simpleMessage("để được tư vấn nhanh chóng!"),
        "text_contact_method": MessageLookupByLibrary.simpleMessage(
            "Phương thức và chi tiết liên hệ"),
        "text_contact_method_hint":
            MessageLookupByLibrary.simpleMessage("Ví dụ: Zalo +0987656484"),
        "text_contact_name":
            MessageLookupByLibrary.simpleMessage("Tên liên hệ"),
        "text_contact_support": MessageLookupByLibrary.simpleMessage(
            "Nếu có yêu cầu hủy, vui lòng liên hệ với TripC để được hỗ trợ."),
        "text_contact_tripc":
            MessageLookupByLibrary.simpleMessage("Liên hệ TripC"),
        "text_contact_tripc_at": MessageLookupByLibrary.simpleMessage(
            "Nếu có bất kỳ câu hỏi nào, vui lòng liên hệ với TripC qua "),
        "text_contact_tripc_email":
            MessageLookupByLibrary.simpleMessage("<EMAIL> "),
        "text_contact_tripc_message": MessageLookupByLibrary.simpleMessage(
            "*Liên hệ TripC ngay nếu bạn muốn thay đổi thông tin."),
        "text_contact_tripc_to_change_your_information":
            MessageLookupByLibrary.simpleMessage(
                "Liên hệ TripC ngay nếu bạn muốn thay đổi thông tin."),
        "text_continue": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
        "text_continue_with_apple":
            MessageLookupByLibrary.simpleMessage("Đăng Nhập Bằng Apple"),
        "text_continue_with_facebook":
            MessageLookupByLibrary.simpleMessage("Đăng Nhập Bằng Facebook"),
        "text_continue_with_google":
            MessageLookupByLibrary.simpleMessage("Đăng Nhập Bằng Google"),
        "text_copied": MessageLookupByLibrary.simpleMessage("Đã sao chép!"),
        "text_country_or_region":
            MessageLookupByLibrary.simpleMessage("Quốc gia hoặc khu vực"),
        "text_create_account":
            MessageLookupByLibrary.simpleMessage("Tạo tài khoản"),
        "text_create_passcode":
            MessageLookupByLibrary.simpleMessage("Tạo passcode"),
        "text_credit_card_method": MessageLookupByLibrary.simpleMessage(
            "Thẻ thanh toán quốc tế ( Visa/ Master)"),
        "text_currency_unit":
            MessageLookupByLibrary.simpleMessage("Đơn vị tiền tệ"),
        "text_danang_2d_1n":
            MessageLookupByLibrary.simpleMessage("Hạ Long 2 ngày 1 đêm"),
        "text_dark_mode":
            MessageLookupByLibrary.simpleMessage("Chế độ tối màu"),
        "text_data_protection": MessageLookupByLibrary.simpleMessage(
            "TripC cam kết bảo vệ dữ liệu cá nhân của người dùng theo chính sách bảo mật."),
        "text_data_sharing": MessageLookupByLibrary.simpleMessage(
            "Chúng tôi không chia sẻ thông tin cá nhân của bạn với bên thứ ba nếu không có sự đồng ý."),
        "text_data_sharing_rights":
            MessageLookupByLibrary.simpleMessage("3. Quyền Chia Sẻ Dữ Liệu"),
        "text_day_before_the_next_use_date":
            MessageLookupByLibrary.simpleMessage(
                "Cho đến 01:00, 1 ngày trước ngày sử dụng sau"),
        "text_day_before_the_previous_use_date":
            MessageLookupByLibrary.simpleMessage(
                "Cho đến 01:00, 1 ngày trước ngày sử dụng trước"),
        "text_days_left": m8,
        "text_delete_account_and_data": MessageLookupByLibrary.simpleMessage(
            "Xóa tài khoản & dữ liệu: Nếu bạn muốn ngừng sử dụng TripC, bạn có thể yêu cầu xóa vĩnh viễn tài khoản cùng toàn bộ dữ liệu liên quan."),
        "text_delete_account_dialog_note_1":
            MessageLookupByLibrary.simpleMessage(
                "Bạn sẽ không thể kiểm tra các yêu cầu đặt chỗ trong quá khứ."),
        "text_delete_account_dialog_note_2":
            MessageLookupByLibrary.simpleMessage(
                "Bạn sẽ không thể đăng nhập vào tài khoản của mình."),
        "text_delete_account_dialog_note_3":
            MessageLookupByLibrary.simpleMessage(
                "Bạn sẽ mất tất cả TCent và mã giảm giá."),
        "text_delete_account_dialog_note_4":
            MessageLookupByLibrary.simpleMessage(
                "Chúng tôi sẽ không hỗ trợ khôi phục tài khoản cho bạn."),
        "text_delete_account_dialog_note_5": MessageLookupByLibrary.simpleMessage(
            "Nếu bạn vẫn muốn xóa tài khoản, vui lòng đảm bảo tất cả yêu cầu đặt chỗ đều đã hoàn thành và bạn không có vấn đề hay thắc mắc nào sau khi xóa tài khoản."),
        "text_delete_browsing_history":
            MessageLookupByLibrary.simpleMessage("Xóa Lịch Sử"),
        "text_delete_browsing_history_note": MessageLookupByLibrary.simpleMessage(
            "Sau khi xóa, bạn không thể xem được lịch sử nữa. Bạn vẫn muốn xóa?"),
        "text_delete_contact":
            MessageLookupByLibrary.simpleMessage("Xóa Liên Hệ"),
        "text_delete_contact_question":
            MessageLookupByLibrary.simpleMessage("Xóa liên hệ này?"),
        "text_delete_my_account":
            MessageLookupByLibrary.simpleMessage("Xóa Tài Khoản Của Tôi"),
        "text_delete_my_account_note": MessageLookupByLibrary.simpleMessage(
            "Sau khi tài khoản bị xóa, mọi thông tin tài khoản cũng sẽ bị xóa.\nBạn sẽ không thể khôi phục các thông tin này."),
        "text_delete_recent_search": MessageLookupByLibrary.simpleMessage(
            "Xóa tất cả các kết quả tìm kiếm gần đây?"),
        "text_departure_date":
            MessageLookupByLibrary.simpleMessage("Ngày khởi hành"),
        "text_departure_location":
            MessageLookupByLibrary.simpleMessage("Địa điểm xuất phát: "),
        "text_detail": MessageLookupByLibrary.simpleMessage("Chi tiết"),
        "text_detailed_information":
            MessageLookupByLibrary.simpleMessage("Thông tin chi tiết"),
        "text_detailed_notification":
            MessageLookupByLibrary.simpleMessage("Thông báo chi tiết"),
        "text_details_included":
            MessageLookupByLibrary.simpleMessage("Chi tiết bao gồm:"),
        "text_diamond": MessageLookupByLibrary.simpleMessage("Kim cương"),
        "text_diamond_rank":
            MessageLookupByLibrary.simpleMessage("Hạng Kim Cương"),
        "text_diamond_tier":
            MessageLookupByLibrary.simpleMessage("Hạng Kim Cương"),
        "text_did_not_receive_otp":
            MessageLookupByLibrary.simpleMessage("Bạn không nhận được mã OTP?"),
        "text_disclaimer":
            MessageLookupByLibrary.simpleMessage("Miễn trừ trách nhiệm"),
        "text_disclaimer_note_1": MessageLookupByLibrary.simpleMessage(
            "Hóa đơn điện tử này cho đơn hàng này sẽ do nhà cung cấp phát hành và được tính trên giá trị ban đầu ( Trước khi áp dụng voucher)."),
        "text_disclaimer_note_2": MessageLookupByLibrary.simpleMessage(
            "Trường hợp người mua không cung cấp thông tin hoặc không gửi yêu cầu xuất hóa đơn. Nhà cung cấp sẽ sử dụng thông tin trên đơn hàng để xuất hóa đơn điện tử."),
        "text_disclaimer_note_3": MessageLookupByLibrary.simpleMessage(
            "Nhà cung cấp sẽ không chịu trách nhiệm nếu có vướng mắc về kê khai thuế đối với những hóa đơn từ 20 triệu đồng trở lên thanh toán bằng tiền mặt hoặc phương thức thanh toán cá nhân nào khác trong TripC."),
        "text_disclaimer_note_4": MessageLookupByLibrary.simpleMessage(
            "Lưu ý: Nếu mã số thuế không chính xác, hóa đơn xuất sẽ không có mã số thuế của người mua để đảm bảo tính pháp lý của hóa đơn."),
        "text_discount": MessageLookupByLibrary.simpleMessage("Giảm giá"),
        "text_discount_code_enjoy":
            MessageLookupByLibrary.simpleMessage("mã giảm giá , hãy tận hưởng"),
        "text_discount_upper": MessageLookupByLibrary.simpleMessage("DISCOUNT"),
        "text_display_name":
            MessageLookupByLibrary.simpleMessage("Tên hiển thị"),
        "text_dissatisfaction_with_experience":
            MessageLookupByLibrary.simpleMessage(
                "Không hài lòng với trải nghiệm"),
        "text_do_have_account":
            MessageLookupByLibrary.simpleMessage("Bạn đã có tài khoản?"),
        "text_do_not_have": MessageLookupByLibrary.simpleMessage("Không có"),
        "text_dont_forget_share_moment": MessageLookupByLibrary.simpleMessage(
            "Đơn đã được đặt thành công. Chúc bạn có một hành trình đầy ắp các trải nghiệm thú vị và an toàn."),
        "text_dont_forget_share_moment_2": MessageLookupByLibrary.simpleMessage(
            "Đừng quên, hãy chia sẽ những khoảnh khắc đó tại TripC để nhận ưu đãi nhé!"),
        "text_dont_have_account":
            MessageLookupByLibrary.simpleMessage("Bạn chưa có tài khoản?"),
        "text_download_personal_data": MessageLookupByLibrary.simpleMessage(
            "Tải xuống dữ liệu cá nhân: Bạn có thể yêu cầu tải về toàn bộ lịch sử giao dịch, tour đã đặt, thông tin cá nhân được lưu trữ."),
        "text_download_ticket": MessageLookupByLibrary.simpleMessage("Tải vé"),
        "text_edit": MessageLookupByLibrary.simpleMessage("Chỉnh sửa"),
        "text_edit_contact":
            MessageLookupByLibrary.simpleMessage("Chỉnh Sửa Liên Hệ"),
        "text_edit_passenger":
            MessageLookupByLibrary.simpleMessage("Chỉnh sửa thông tin"),
        "text_edit_personal_information": MessageLookupByLibrary.simpleMessage(
            "Chỉnh sửa thông tin cá nhân: Cập nhật họ tên, số điện thoại, email, địa chỉ, thông tin thanh toán."),
        "text_electronic_invoice":
            MessageLookupByLibrary.simpleMessage("Hóa đơn điện tử"),
        "text_electronic_invoice_note": MessageLookupByLibrary.simpleMessage(
            "Hóa đơn điện tử chỉ được xuất một lần duy nhất trước khi thanh toán theo thông tin bên dưới. Vui lòng kiểm tra và nhập chính xác thông tin. Sau khi chọn \"Thanh toán\", thông tin hóa đơn sẽ không thể thay đổi!"),
        "text_email": MessageLookupByLibrary.simpleMessage("Email"),
        "text_email_already_in_use": MessageLookupByLibrary.simpleMessage(
            "Email này đã được sử dụng. Hãy đăng nhập vào tài khoản!"),
        "text_email_already_issue": MessageLookupByLibrary.simpleMessage(
            "Email này đã được sử dụng. Vui lòng đăng nhập!"),
        "text_email_has_not_been_registered":
            MessageLookupByLibrary.simpleMessage(
                "Email chưa được sử dụng để đăng kí tài khoản"),
        "text_email_invalid":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập email hợp lệ!"),
        "text_email_link":
            MessageLookupByLibrary.simpleMessage("Liên kết email"),
        "text_email_must_be_different": MessageLookupByLibrary.simpleMessage(
            "Địa chỉ email mới phải khác với địa chỉ email hiện tại!"),
        "text_email_not_found": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy tài khoản với email này. Vui lòng kiểm tra lại hoặc thử một email khác"),
        "text_enable_disable_promotion_emails":
            MessageLookupByLibrary.simpleMessage(
                "Bật/tắt nhận email và thông báo về khuyến mãi, đề xuất tour phù hợp."),
        "text_english": MessageLookupByLibrary.simpleMessage("Tiếng Anh"),
        "text_enjoy_your_trip":
            MessageLookupByLibrary.simpleMessage("Tận hưởng chuyến đi của bạn"),
        "text_enter": MessageLookupByLibrary.simpleMessage("Nhập"),
        "text_enter_address":
            MessageLookupByLibrary.simpleMessage("Nhập địa chỉ của bạn"),
        "text_enter_business_name":
            MessageLookupByLibrary.simpleMessage("Nhập tên doanh nghiệp"),
        "text_enter_contact_phone_num":
            MessageLookupByLibrary.simpleMessage("Nhập số điện thoại liên hệ"),
        "text_enter_email":
            MessageLookupByLibrary.simpleMessage("Nhập email của bạn"),
        "text_enter_email_to_get_passcode":
            MessageLookupByLibrary.simpleMessage("Nhập email để nhận Passcode"),
        "text_enter_fullname":
            MessageLookupByLibrary.simpleMessage("Nhập Họ và tên"),
        "text_enter_mail_reset_pass": MessageLookupByLibrary.simpleMessage(
            "Nhập email của bạn để khôi phục mật khẩu"),
        "text_enter_mail_reset_passcode": MessageLookupByLibrary.simpleMessage(
            "Nhập tài khoản email của bạn để khôi phục passcode"),
        "text_enter_min_2_max_6": MessageLookupByLibrary.simpleMessage(
            "Nhập tối thiểu 2 số và tối đa 6 số"),
        "text_enter_new_pass":
            MessageLookupByLibrary.simpleMessage("Mật Khẩu Mới"),
        "text_enter_new_passcode":
            MessageLookupByLibrary.simpleMessage("Nhập passcode mới"),
        "text_enter_passcode":
            MessageLookupByLibrary.simpleMessage("Nhập Passcode"),
        "text_enter_phone_number": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại di động liên hệ"),
        "text_enter_promotional_code":
            MessageLookupByLibrary.simpleMessage("Nhập mã khuyến mãi"),
        "text_enter_tax_code":
            MessageLookupByLibrary.simpleMessage("Nhập mã số thuế"),
        "text_enter_tripc_id_nice_number":
            MessageLookupByLibrary.simpleMessage("Nhập TripC ID số đẹp"),
        "text_enter_your_name":
            MessageLookupByLibrary.simpleMessage("Nhập tên của bạn"),
        "text_enter_your_password":
            MessageLookupByLibrary.simpleMessage("Nhập mật khẩu"),
        "text_enter_your_referral_code":
            MessageLookupByLibrary.simpleMessage("Nhập mã giới thiệu của bạn"),
        "text_enter_your_referral_code_from_friend":
            MessageLookupByLibrary.simpleMessage(
                "Nhập mã giới thiệu từ người bạn để nhận ngay phần quà đặc biệt từ chúng tôi."),
        "text_enter_your_tripc_id":
            MessageLookupByLibrary.simpleMessage("Nhập TripC ID của bạn"),
        "text_enter_your_tripc_passcode":
            MessageLookupByLibrary.simpleMessage("Nhập TripC ID Passcode"),
        "text_error": MessageLookupByLibrary.simpleMessage("Lỗi"),
        "text_error_phone_10": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập số điện thoại hợp lệ (10 chữ số)!"),
        "text_error_phone_9": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập số điện thoại hợp lệ (9 chữ số)!"),
        "text_error_something_wrong":
            MessageLookupByLibrary.simpleMessage("Có lỗi xảy ra"),
        "text_example": MessageLookupByLibrary.simpleMessage("Ví dụ:"),
        "text_exclusively_for_you":
            MessageLookupByLibrary.simpleMessage("Dành riêng cho bạn"),
        "text_explore": MessageLookupByLibrary.simpleMessage("Khám phá"),
        "text_explore_the_word":
            MessageLookupByLibrary.simpleMessage("Khám phá thế giới"),
        "text_explore_the_word_content": MessageLookupByLibrary.simpleMessage(
            "Khám phá những địa điểm, nền văn hóa và trải nghiệm mới, mở khóa cánh cửa phiêu lưu và đam mê du lịch"),
        "text_explore_viet_nam":
            MessageLookupByLibrary.simpleMessage("Khám phá Việt Nam"),
        "text_explore_viet_nam_content": MessageLookupByLibrary.simpleMessage(
            "Khám phá những địa điểm, nền văn hóa và trải nghiệm mới.\nMở khóa cánh cửa phiêu lưu và đam mê du lịch"),
        "text_extra": MessageLookupByLibrary.simpleMessage("EXTRA"),
        "text_failure_order":
            MessageLookupByLibrary.simpleMessage("Đơn đặt thất bại!"),
        "text_family_combo":
            MessageLookupByLibrary.simpleMessage("Combo gia đình"),
        "text_favorite_combo":
            MessageLookupByLibrary.simpleMessage("Combo được yêu thích"),
        "text_fee": MessageLookupByLibrary.simpleMessage("Phí ( chưa VAT )"),
        "text_fee_text": MessageLookupByLibrary.simpleMessage("Phí"),
        "text_female": MessageLookupByLibrary.simpleMessage("Nữ"),
        "text_fengshui_number":
            MessageLookupByLibrary.simpleMessage("Số phong thủy"),
        "text_fill_in_hotel_address": MessageLookupByLibrary.simpleMessage(
            "Điền tên khách sạn và địa chỉ"),
        "text_fill_in_passenger_info":
            MessageLookupByLibrary.simpleMessage("Điền thông tin hành khách"),
        "text_finish_membership_card": MessageLookupByLibrary.simpleMessage(
            "Hoàn tất khởi tạo thẻ thành viên của bạn"),
        "text_first_name": MessageLookupByLibrary.simpleMessage("Họ"),
        "text_first_name_and_last_name":
            MessageLookupByLibrary.simpleMessage("Họ và Tên"),
        "text_flexible_ticket":
            MessageLookupByLibrary.simpleMessage("Vé linh hoạt"),
        "text_forgot_passcode":
            MessageLookupByLibrary.simpleMessage("Quên passcode"),
        "text_forgot_password":
            MessageLookupByLibrary.simpleMessage("Quên Mật Khẩu"),
        "text_forgot_password_lower":
            MessageLookupByLibrary.simpleMessage("Quên mật khẩu"),
        "text_from": MessageLookupByLibrary.simpleMessage("Từ"),
        "text_full_name": MessageLookupByLibrary.simpleMessage("Tên đầy đủ"),
        "text_full_name_invalid": MessageLookupByLibrary.simpleMessage(
            "Họ và tên không hợp lệ. Vui lòng nhập lại!"),
        "text_full_name_over_50": MessageLookupByLibrary.simpleMessage(
            "Họ và tên không được vượt quá 50 ký tự!"),
        "text_full_name_without_num":
            MessageLookupByLibrary.simpleMessage("Họ tên không được chứa số"),
        "text_full_name_without_space": MessageLookupByLibrary.simpleMessage(
            "Không được nhập khoảng trắng!"),
        "text_full_name_without_special_sign":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng không nhập ký tự đặc biệt!"),
        "text_fullname": MessageLookupByLibrary.simpleMessage("Họ và tên:"),
        "text_gender": MessageLookupByLibrary.simpleMessage("Giới tính"),
        "text_general_information":
            MessageLookupByLibrary.simpleMessage("Thông tin chung"),
        "text_get_now": MessageLookupByLibrary.simpleMessage("GET NOW"),
        "text_get_started": MessageLookupByLibrary.simpleMessage("Hãy bắt đầu"),
        "text_give_a_tripc_id":
            MessageLookupByLibrary.simpleMessage("Tặng bạn một TripC ID"),
        "text_go_to_payment_page":
            MessageLookupByLibrary.simpleMessage("Đến Trang Thanh Toán"),
        "text_gold_rank": MessageLookupByLibrary.simpleMessage("Hạng Vàng"),
        "text_gold_tier": MessageLookupByLibrary.simpleMessage("Hạng Vàng"),
        "text_good": MessageLookupByLibrary.simpleMessage("Tốt"),
        "text_great_peace_number":
            MessageLookupByLibrary.simpleMessage("Số đại thái bình"),
        "text_great_peace_number_meaning": MessageLookupByLibrary.simpleMessage(
            "Biểu tượng cho sự hòa hợp, bình an và phát triển ổn định, thích hợp cho những ai mong muốn cuộc sống an yên."),
        "text_guardian_name":
            MessageLookupByLibrary.simpleMessage("Người giám hộ:"),
        "text_guardian_name_hint": MessageLookupByLibrary.simpleMessage(
            "Nhập Họ và tên người giám hộ"),
        "text_guardian_phone_number": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại người giám hộ:"),
        "text_guardian_phone_number_hint": MessageLookupByLibrary.simpleMessage(
            "Nhập Số điện thoại người giám hộ"),
        "text_have_not_receive_otp":
            MessageLookupByLibrary.simpleMessage("Bạn chưa nhận được mã? "),
        "text_hint_input_city": MessageLookupByLibrary.simpleMessage(
            "Nhập thành phố cư trú của bạn"),
        "text_hire_vehicle": MessageLookupByLibrary.simpleMessage("Thuê Xe"),
        "text_home": MessageLookupByLibrary.simpleMessage("Trang chủ"),
        "text_hot_tour_during_tet_holiday":
            MessageLookupByLibrary.simpleMessage("Tour hot dịp tết"),
        "text_hotel": MessageLookupByLibrary.simpleMessage("Khách Sạn"),
        "text_hotel_address":
            MessageLookupByLibrary.simpleMessage("Khách sạn/Địa chỉ"),
        "text_hotel_membership_offers":
            MessageLookupByLibrary.simpleMessage("Ưu đãi thành viên"),
        "text_hotel_space": MessageLookupByLibrary.simpleMessage("Khách sạn: "),
        "text_how_to_use": MessageLookupByLibrary.simpleMessage("Cách sử dụng"),
        "text_if_a_discount_is_applied": MessageLookupByLibrary.simpleMessage(
            "Nếu áp dụng chiết khấu, phí sẽ được tính theo tỷ lệ của giá trước chiết khấu. Phí này sẽ không vượt quá số tiền thực trả. Sản phẩm không hỗ trợ hoàn tiền một phần"),
        "text_illness_or_bursting_bubble": MessageLookupByLibrary.simpleMessage(
            "Bệnh tật hoặc dịch bệnh bùng phát"),
        "text_in_words": MessageLookupByLibrary.simpleMessage("Bằng chữ"),
        "text_inactive_account":
            MessageLookupByLibrary.simpleMessage("Tài khoản không hoạt động"),
        "text_inactive_timeout_logout": MessageLookupByLibrary.simpleMessage(
            "Nếu bạn không hoạt động trong một khoảng thời gian nhất định (5, 10, 15 phút...), hệ thống sẽ tự động đăng xuất để bảo vệ tài khoản."),
        "text_includes": MessageLookupByLibrary.simpleMessage("Bao gồm"),
        "text_information_to_note":
            MessageLookupByLibrary.simpleMessage("Thông tin cần lưu ý"),
        "text_injury_or_severe_incident": MessageLookupByLibrary.simpleMessage(
            "Chấn thương hoặc tai nạn nghiêm trọng"),
        "text_input_phone_number":
            MessageLookupByLibrary.simpleMessage("Nhập số điện thoại"),
        "text_invalid_referral_code": MessageLookupByLibrary.simpleMessage(
            "Mã giới thiệu không hợp lệ! Vui lòng nhập lại."),
        "text_invoice_email":
            MessageLookupByLibrary.simpleMessage("Email nhận hóa đơn:"),
        "text_invoice_message": MessageLookupByLibrary.simpleMessage(
            "Hóa đơn sẽ được gửi trong vòng 7 ngày làm việc (Không tính T7 - CN)"),
        "text_invoice_message_popup": MessageLookupByLibrary.simpleMessage(
            "Chúng tôi đang xử lý yêu cầu của bạn.\nHóa đơn sẽ được gửi đến email của bạn sau khi bạn hoàn tất đơn hàng."),
        "text_invoice_success": MessageLookupByLibrary.simpleMessage(
            "Gửi yêu cầu Xuất hóa đơn điện từ thành công"),
        "text_invoice_type":
            MessageLookupByLibrary.simpleMessage("Loại hóa đơn:"),
        "text_issue_with_order_or_voucher":
            MessageLookupByLibrary.simpleMessage(
                "Vấn đề về đơn hàng/ đổi voucher"),
        "text_language": MessageLookupByLibrary.simpleMessage("Ngôn ngữ"),
        "text_last_name": MessageLookupByLibrary.simpleMessage("Tên"),
        "text_later": MessageLookupByLibrary.simpleMessage("Để sau"),
        "text_lets_create_passcode": MessageLookupByLibrary.simpleMessage(
            "Hoàn toàn miễn phí với đầy đủ tiện ích và tính năng trong hệ thống TripC.\nTiếp theo, hãy tạo Passcode cho tài khoản của mình nhé!"),
        "text_lets_enter_your_tripc_id": MessageLookupByLibrary.simpleMessage(
            "Mời bạn nhập TripC ID (Nếu có)"),
        "text_link": MessageLookupByLibrary.simpleMessage("Liên kết"),
        "text_link_email_successful_content": MessageLookupByLibrary.simpleMessage(
            "Bạn đã liên kết với email thành công.\nSử dụng email vừa thiết lập để đăng nhập những lần sau."),
        "text_link_email_successfully":
            MessageLookupByLibrary.simpleMessage("Liên kết email thành công"),
        "text_link_phone_description": MessageLookupByLibrary.simpleMessage(
            "Sau khi thêm số điện thoại, bạn có thể sử dụng số đó để liên kết với các dịch vụ khác trong TripC."),
        "text_link_phone_number":
            MessageLookupByLibrary.simpleMessage("Thêm số điện thoại"),
        "text_link_phone_successfully": MessageLookupByLibrary.simpleMessage(
            "Thêm số điện thoại thành công"),
        "text_linked_email":
            MessageLookupByLibrary.simpleMessage("Email liên kết"),
        "text_linked_phone":
            MessageLookupByLibrary.simpleMessage("Số điện thoại"),
        "text_log_out": MessageLookupByLibrary.simpleMessage("Đăng xuất"),
        "text_login_history":
            MessageLookupByLibrary.simpleMessage("Lịch sử đăng nhập"),
        "text_login_to_see_membership_rank":
            MessageLookupByLibrary.simpleMessage(
                "Đăng nhập để xem hạng thành viên"),
        "text_loyalty": MessageLookupByLibrary.simpleMessage("Ưu đãi"),
        "text_male": MessageLookupByLibrary.simpleMessage("Nam"),
        "text_mark_notifications": MessageLookupByLibrary.simpleMessage(
            "Đánh dấu tất cả thông báo là đã đọc"),
        "text_membership_rank":
            MessageLookupByLibrary.simpleMessage("Hạng thành viên"),
        "text_messages": MessageLookupByLibrary.simpleMessage("Tin nhắn"),
        "text_mobile_phone": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại di động liên hệ"),
        "text_momo_method": MessageLookupByLibrary.simpleMessage(
            "Ví điện tử Momo (hoàn TCent lên đến 10% giá trị)"),
        "text_moving": MessageLookupByLibrary.simpleMessage("Di Chuyển"),
        "text_must_purchase_before_six": MessageLookupByLibrary.simpleMessage(
            "Phải được mua trước 18:00, 1 ngày trước ngày sử dụng (Giờ địa phương)"),
        "text_my_account":
            MessageLookupByLibrary.simpleMessage("Quản lý tài khoản của tôi"),
        "text_my_trip": MessageLookupByLibrary.simpleMessage("Chuyến đi"),
        "text_my_tripc_empty_message": MessageLookupByLibrary.simpleMessage(
            "Chưa có chuyến đi nào. \nĐặt vé để trải nghiệm chuyến đi cùng TripC nhé!"),
        "text_national": MessageLookupByLibrary.simpleMessage("Quốc gia"),
        "text_need_e_invoice": MessageLookupByLibrary.simpleMessage(
            "Bạn sẽ cần có “Vé điện tử” để xác minh và sử dụng đơn đặt của bạn"),
        "text_need_to_correct_information": MessageLookupByLibrary.simpleMessage(
            "Cần sửa đổi ngày/giờ/thông tin khách tham gia/ thông tin khác"),
        "text_need_to_correct_order":
            MessageLookupByLibrary.simpleMessage("Cần sửa lại đơn hàng"),
        "text_need_to_correct_package":
            MessageLookupByLibrary.simpleMessage("Cần sửa đổi gói"),
        "text_neutral_and_negative_reviews":
            MessageLookupByLibrary.simpleMessage(
                "Đánh giá trung lập và tiêu cực"),
        "text_new_deal": MessageLookupByLibrary.simpleMessage("Ưu đãi mới"),
        "text_new_email_link":
            MessageLookupByLibrary.simpleMessage("Liên kết email mới"),
        "text_newly_entered_password_is_incorrect":
            MessageLookupByLibrary.simpleMessage(
                "Mật khẩu mới nhập lại không trùng khớp"),
        "text_nguquy_number":
            MessageLookupByLibrary.simpleMessage("Số ngũ quý"),
        "text_no_broswing_history": MessageLookupByLibrary.simpleMessage(
            "Không có lịch sử duyệt.\nVui lòng quay lại trang chủ và bắt đầu khám phá các dịch vụ và tính năng của TripC AI!"),
        "text_no_internet": MessageLookupByLibrary.simpleMessage(
            "Vui lòng kiểm tra cài đặt mạng hoặc thử lại"),
        "text_no_promotional_code": MessageLookupByLibrary.simpleMessage(
            "Hiện không có mã khuyến mãi nào. Nếu có mã\nkhuyến mãi, bạn có thể và áp dụng mã"),
        "text_no_review":
            MessageLookupByLibrary.simpleMessage("Chưa có đánh giá"),
        "text_no_saved_tour":
            MessageLookupByLibrary.simpleMessage("Không có tour đã lưu."),
        "text_non_refundable": MessageLookupByLibrary.simpleMessage(
            "Một số tour có thể không hỗ trợ hoàn tiền hoặc chỉ hoàn lại một phần theo thời gian hủy."),
        "text_not_found":
            MessageLookupByLibrary.simpleMessage("Không tìm thấy kết quả nào."),
        "text_not_same_old_phone_number": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại mới phải khác với số điện thoại hiện tại."),
        "text_not_yet_paid":
            MessageLookupByLibrary.simpleMessage("Chưa thanh toán"),
        "text_note": MessageLookupByLibrary.simpleMessage("Lưu ý:"),
        "text_note_label": MessageLookupByLibrary.simpleMessage("Lưu ý"),
        "text_note_price_at_local_time": MessageLookupByLibrary.simpleMessage(
            "Thời gian địa phương | Giá hiển thị sẽ là giá thấp nhất cho ngày hiện tại"),
        "text_notification": MessageLookupByLibrary.simpleMessage("Thông báo"),
        "text_of": MessageLookupByLibrary.simpleMessage("Của"),
        "text_of_tripc": MessageLookupByLibrary.simpleMessage(" của TripC"),
        "text_open_setting": MessageLookupByLibrary.simpleMessage("Mở Cài đặt"),
        "text_opp_not_logged_in":
            MessageLookupByLibrary.simpleMessage("Oops! Bạn chưa đăng nhập!"),
        "text_or": MessageLookupByLibrary.simpleMessage("Hoặc"),
        "text_or_2": MessageLookupByLibrary.simpleMessage(" hoặc "),
        "text_order_information":
            MessageLookupByLibrary.simpleMessage("Thông tin đơn đặt"),
        "text_order_information_first_row":
            MessageLookupByLibrary.simpleMessage(
                "Nhận xét bên ngoài điểm tham quan"),
        "text_order_information_second_row":
            MessageLookupByLibrary.simpleMessage("Hủy khi có điều kiện"),
        "text_order_information_third_row":
            MessageLookupByLibrary.simpleMessage(
                "Thông tin về các sản phẩm/ dịch vụ."),
        "text_other_external_conditions": MessageLookupByLibrary.simpleMessage(
            "Các tình huống lượng trước khác"),
        "text_other_gender":
            MessageLookupByLibrary.simpleMessage("Không muốn tiết lộ"),
        "text_other_reasons":
            MessageLookupByLibrary.simpleMessage("Lý do khác"),
        "text_otp_code_verification":
            MessageLookupByLibrary.simpleMessage("Xác Thực Mã OTP"),
        "text_otp_expired":
            MessageLookupByLibrary.simpleMessage("Mã OTP của bạn đã hết hạn"),
        "text_otp_has_been_sent": MessageLookupByLibrary.simpleMessage(
            "Mã OTP được gửi lại Email của bạn"),
        "text_otp_incorrect":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập đúng mã OTP"),
        "text_otp_valid_30_minute": MessageLookupByLibrary.simpleMessage(
            "* Mã xác nhận có hiệu lực trong 30 phút sau khi bạn nhận được mã."),
        "text_out_of_stock": MessageLookupByLibrary.simpleMessage(
            "Đơn hàng bạn chọn đang tạm hết. Vui lòng chọn một đơn hàng khác hoặc thử lại sau."),
        "text_outside_the_scope_of_control":
            MessageLookupByLibrary.simpleMessage("Sự cố ngoài tầm kiểm soát"),
        "text_paid": MessageLookupByLibrary.simpleMessage("Đã thanh toán"),
        "text_passcode_reset_successfully":
            MessageLookupByLibrary.simpleMessage(
                "Thiết lập lại Passcode thành công"),
        "text_passenger": MessageLookupByLibrary.simpleMessage("Hành khách"),
        "text_passenger_information":
            MessageLookupByLibrary.simpleMessage("Thông tin hành khách"),
        "text_passenger_information_2":
            MessageLookupByLibrary.simpleMessage("Thông Tin Hành Khách"),
        "text_passenger_name":
            MessageLookupByLibrary.simpleMessage("Tên Hành Khách"),
        "text_passenger_name_rule": MessageLookupByLibrary.simpleMessage(
            "Tên hành khách phải được nhập chính xác như trên giấy tờ tùy thân của bạn"),
        "text_passenger_title":
            MessageLookupByLibrary.simpleMessage("Khách hàng"),
        "text_password": MessageLookupByLibrary.simpleMessage("Mật khẩu"),
        "text_password_invalid":
            MessageLookupByLibrary.simpleMessage("Password không hợp lệ"),
        "text_password_lowercase_valid": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu phải chứa ít nhất một chữ thường!"),
        "text_password_must_be_1_special": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu phải chứa ít nhất một ký tự đặc biệt!"),
        "text_password_must_be_at_last_8_characters":
            MessageLookupByLibrary.simpleMessage(
                "Mật khẩu phải có ít nhất 8 ký tự!"),
        "text_password_one_number_valid": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu phải chứa ít nhất một chữ số!"),
        "text_password_reset_successfully":
            MessageLookupByLibrary.simpleMessage(
                "Thiết lập lại Mật khẩu thành công"),
        "text_password_uppercase_valid": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu phải chứa ít nhất một chữ hoa!"),
        "text_password_validation": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu bắt buộc dài ít nhất 8 ký tự, có chứa chữ hoa, chữ thường, số và ký tự đặc biệt"),
        "text_password_verification": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu bắt buộc dài ít nhất 8 ký tự, có chứa chữ hoa, chữ thường, số và ký tự đặc biệt"),
        "text_password_without_space": MessageLookupByLibrary.simpleMessage(
            "Mật khẩu không được chứa khoảng trắng!"),
        "text_pay": MessageLookupByLibrary.simpleMessage("Thanh toán"),
        "text_payment_confirmation": MessageLookupByLibrary.simpleMessage(
            "Sau khi thanh toán thành công, TripC sẽ gửi xác nhận đặt tour qua email hoặc ứng dụng."),
        "text_payment_content": MessageLookupByLibrary.simpleMessage(
            "ND: Thanh toán hóa đơn TripC ID tại TripC"),
        "text_payment_fail":
            MessageLookupByLibrary.simpleMessage("Thanh toán thất bại!"),
        "text_payment_has_been_successful":
            MessageLookupByLibrary.simpleMessage("Đã thanh toán thành công"),
        "text_payment_later":
            MessageLookupByLibrary.simpleMessage("Thanh toán sau"),
        "text_payment_method":
            MessageLookupByLibrary.simpleMessage("Phương thức thanh toán"),
        "text_payment_method_condition": MessageLookupByLibrary.simpleMessage(
            "Thanh toán được thực hiện qua nhiều hình thức như thẻ ngân hàng, ví điện tử hoặc chuyển khoản."),
        "text_payment_successful":
            MessageLookupByLibrary.simpleMessage("Thanh toán thành công."),
        "text_payment_vn_pay_method": MessageLookupByLibrary.simpleMessage(
            "Ví điện tử VNPay (hoàn TCent lên đến 10% giá trị)"),
        "text_payment_with_payos":
            MessageLookupByLibrary.simpleMessage("Thanh toán với PayOS"),
        "text_people": MessageLookupByLibrary.simpleMessage("Người"),
        "text_people_are_searching":
            MessageLookupByLibrary.simpleMessage("Mọi người đang tìm kiếm"),
        "text_permission_note": MessageLookupByLibrary.simpleMessage(
            "Vui lòng bật quyền trong cài đặt để tiếp tục."),
        "text_permission_required":
            MessageLookupByLibrary.simpleMessage("Cần có Quyền"),
        "text_personal": MessageLookupByLibrary.simpleMessage("Cá Nhân"),
        "text_personal_data_management":
            MessageLookupByLibrary.simpleMessage("1. Quản Lý Dữ Liệu Cá Nhân"),
        "text_personal_info":
            MessageLookupByLibrary.simpleMessage("Thông Tin Cá Nhân"),
        "text_phone": MessageLookupByLibrary.simpleMessage("Điện thoại"),
        "text_phone_already_issue": MessageLookupByLibrary.simpleMessage(
            "Số điện thoại này đã tồn tại!"),
        "text_phone_number":
            MessageLookupByLibrary.simpleMessage("Số điện thoại:"),
        "text_photo": MessageLookupByLibrary.simpleMessage("Ảnh"),
        "text_pick_up_airport":
            MessageLookupByLibrary.simpleMessage("Đưa Đón\nSân Bay"),
        "text_pick_up_time_at_hotel":
            MessageLookupByLibrary.simpleMessage("Thời gian đón ở khách sạn"),
        "text_pick_up_time_at_hotel_hint":
            MessageLookupByLibrary.simpleMessage("Ví dụ: 10:00"),
        "text_platinum_rank":
            MessageLookupByLibrary.simpleMessage("Hạng Bạch Kim"),
        "text_platinum_tier":
            MessageLookupByLibrary.simpleMessage("Hạng Bạch Kim"),
        "text_please_log_in_or_register": MessageLookupByLibrary.simpleMessage(
            "Hãy đăng nhập hoặc đăng ký để khám phá nội dung."),
        "text_please_try_again":
            MessageLookupByLibrary.simpleMessage("Vui lòng thử lại!"),
        "text_pls_check_inbox_email": MessageLookupByLibrary.simpleMessage(
            "Vui lòng kiểm tra hộp thư đến của bạn và nhập mã ở bên dưới"),
        "text_pls_choose_your_own_tripc_id":
            MessageLookupByLibrary.simpleMessage(
                "Hãy chọn TripC ID của riêng mình"),
        "text_pls_enter_6_digit":
            MessageLookupByLibrary.simpleMessage("Hãy nhập Passcode 6 chữ số:"),
        "text_pls_enter_a_new_email": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập địa chỉ email mới. Sau khi liên kết thành công với tài khoản của bạn, bạn có thể sử dụng email này để đăng nhập."),
        "text_pls_enter_correct_password":
            MessageLookupByLibrary.simpleMessage("Sai mật khẩu"),
        "text_pls_enter_details": MessageLookupByLibrary.simpleMessage(
            "Nhập thông tin tài khoản của bạn!"),
        "text_pls_enter_full_name": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập họ và tên có từ 2 đến 50 ký tự!"),
        "text_pls_enter_valid_email": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập một địa chỉ email hợp lệ!"),
        "text_pls_enter_verified_email": MessageLookupByLibrary.simpleMessage(
            "Vui lòng nhập một địa chỉ email đã được xác thực!"),
        "text_pls_set_security_pass": MessageLookupByLibrary.simpleMessage(
            "Hãy đặt một mật khẩu an toàn hơn cho tài khoản của bạn.\nVui lòng tránh các chữ số liên quan đến ngày sinh nhật, các chữ số quá dễ đoán."),
        "text_pls_type_phone_number":
            MessageLookupByLibrary.simpleMessage("Vui lòng nhập số điện thoại"),
        "text_pls_type_valid_phone_number":
            MessageLookupByLibrary.simpleMessage(
                "Vui lòng nhập số điện thoại hợp lệ (9 chữ số)"),
        "text_popular_tour":
            MessageLookupByLibrary.simpleMessage("Tour được yêu thích"),
        "text_post_at": MessageLookupByLibrary.simpleMessage("Đăng vào"),
        "text_powered_by":
            MessageLookupByLibrary.simpleMessage("Được cung cấp bởi "),
        "text_previous_day": m9,
        "text_price": MessageLookupByLibrary.simpleMessage("Giá"),
        "text_principles_on_passenger_names":
            MessageLookupByLibrary.simpleMessage(
                "Nguyên tắc về Họ Tên Hành khách"),
        "text_privacy": MessageLookupByLibrary.simpleMessage("Quyền riêng tư"),
        "text_privacy_changes_history": MessageLookupByLibrary.simpleMessage(
            "Lịch sử thay đổi quyền riêng tư"),
        "text_privacy_policy":
            MessageLookupByLibrary.simpleMessage("6. Bảo Mật Thông Tin"),
        "text_privacy_policy_at_tripc":
            MessageLookupByLibrary.simpleMessage("Quyền Riêng Tư Tại TripC"),
        "text_processed": MessageLookupByLibrary.simpleMessage("Đã xử lý"),
        "text_processing": MessageLookupByLibrary.simpleMessage("Đang xử lý"),
        "text_products_you_have_searched_for":
            MessageLookupByLibrary.simpleMessage("Sản phẩm đã tìm kiếm"),
        "text_profile": MessageLookupByLibrary.simpleMessage("Tài khoản"),
        "text_profile_contact": MessageLookupByLibrary.simpleMessage("Liên hệ"),
        "text_profile_gift": MessageLookupByLibrary.simpleMessage("Quà tặng"),
        "text_profile_holtel_membership":
            MessageLookupByLibrary.simpleMessage("Ưu đã Thành viên Khách sạn"),
        "text_profile_info":
            MessageLookupByLibrary.simpleMessage("Giới Thiệu \nVề TripC.AI"),
        "text_profile_moment":
            MessageLookupByLibrary.simpleMessage("Khoảnh khắc & \nĐánh giá"),
        "text_profile_pravicy":
            MessageLookupByLibrary.simpleMessage("Điều Khoản \n& Điều Kiện"),
        "text_profile_promotion":
            MessageLookupByLibrary.simpleMessage("Mã khuyến mãi"),
        "text_profile_recent":
            MessageLookupByLibrary.simpleMessage("Xem gần đây"),
        "text_profile_review":
            MessageLookupByLibrary.simpleMessage("Đánh Giá \nỨng Dụng"),
        "text_profile_support":
            MessageLookupByLibrary.simpleMessage("Hỗ trợ \nKhách Hàng"),
        "text_profile_tour_booked":
            MessageLookupByLibrary.simpleMessage("Tour đã đặt"),
        "text_profile_tour_saved":
            MessageLookupByLibrary.simpleMessage("Tour đã lưu"),
        "text_profile_wallet":
            MessageLookupByLibrary.simpleMessage("TripC ID của tôi"),
        "text_promotion": MessageLookupByLibrary.simpleMessage("Khuyến Mãi"),
        "text_promotion_2nd":
            MessageLookupByLibrary.simpleMessage("Khuyến mãi"),
        "text_promotion_around_here":
            MessageLookupByLibrary.simpleMessage("Ưu đãi quanh đây"),
        "text_promotion_combo":
            MessageLookupByLibrary.simpleMessage("Combo Ưu Đãi"),
        "text_promotional_empty_message": MessageLookupByLibrary.simpleMessage(
            "Hiện không có mã khuyến mãi nào.\nNếu có mã khuyến mãi, bạn có thể và áp dụng mã"),
        "text_proposed_itinerary":
            MessageLookupByLibrary.simpleMessage("Lịch trình dự kiến"),
        "text_proposed_itinerary_hint":
            MessageLookupByLibrary.simpleMessage("Điền lịch trình dự kiến"),
        "text_proposer_number_meaning": MessageLookupByLibrary.simpleMessage(
            "Mang ý nghĩa may mắn, tiền tài và sự thịnh vượng. Ví dụ: 68, 86, 39, 79,..."),
        "text_prosper_number":
            MessageLookupByLibrary.simpleMessage("Số phát tài phát lộc"),
        "text_qr_code_reservation":
            MessageLookupByLibrary.simpleMessage("Mã QR đặt chỗ"),
        "text_qr_code_ticket": m10,
        "text_qr_reservation":
            MessageLookupByLibrary.simpleMessage("QR đặt chỗ"),
        "text_quantity": MessageLookupByLibrary.simpleMessage("Số lượng:"),
        "text_rating": MessageLookupByLibrary.simpleMessage("Đánh giá"),
        "text_rating_2": MessageLookupByLibrary.simpleMessage("đánh giá"),
        "text_re_enter_new_pass":
            MessageLookupByLibrary.simpleMessage("Xác Nhận Mật Khẩu Mới"),
        "text_re_enter_new_passcode":
            MessageLookupByLibrary.simpleMessage("Nhập lại passcode mới"),
        "text_re_enter_password":
            MessageLookupByLibrary.simpleMessage("Nhập lại mật khẩu"),
        "text_re_entered_new_passcode_incorrectly":
            MessageLookupByLibrary.simpleMessage(
                "Passcode nhập lại không chính xác"),
        "text_read_more": MessageLookupByLibrary.simpleMessage("Đọc thêm"),
        "text_receive": MessageLookupByLibrary.simpleMessage("Nhận"),
        "text_receive_login_alerts": MessageLookupByLibrary.simpleMessage(
            "Nhận cảnh báo khi có đăng nhập bất thường."),
        "text_receive_now": MessageLookupByLibrary.simpleMessage("Nhận ngay"),
        "text_received_tcent": m11,
        "text_recent_searches":
            MessageLookupByLibrary.simpleMessage("Tìm Kiếm Gần Đây"),
        "text_recent_view": MessageLookupByLibrary.simpleMessage("Xem Gần Đây"),
        "text_recently_viewed":
            MessageLookupByLibrary.simpleMessage("Đã xem gần đây"),
        "text_recommended_destination":
            MessageLookupByLibrary.simpleMessage("Điểm Đến Đề Xuất"),
        "text_reduce": MessageLookupByLibrary.simpleMessage("Giảm"),
        "text_referral_code": MessageLookupByLibrary.simpleMessage(
            "Sử dụng mã giới thiệu để nhận ưu đãi"),
        "text_referral_code_define": MessageLookupByLibrary.simpleMessage(
            "Mã giới thiệu là TripC ID do bạn bè hoặc người thân cung cấp khi họ đã sử dụng ứng dụng.\nBạn có thể bỏ qua bước này và nhập mã sau nếu không có mã giới thiệu."),
        "text_refund": MessageLookupByLibrary.simpleMessage("Hoàn tiền"),
        "text_refund_checking": MessageLookupByLibrary.simpleMessage(
            "Chúng tôi đã kiểm tra yêu cầu hoàn tiền về tài khoản của bạn. Trong vòng 7-14 ngày, nhà cung cấp dịch vụ sẽ đưa ra kết quả cho bạn sớm nhất."),
        "text_refund_in_process": MessageLookupByLibrary.simpleMessage(
            "Chúng tôi đã thực hiện hoàn tiền về tài khoản của bạn. Trong thời gian hoàn tiền vào tài khoản của bạn, tùy thuộc vào phương thức thanh toán đã sử dụng để đặt đơn hàng"),
        "text_refund_information":
            MessageLookupByLibrary.simpleMessage("Thông tin hoàn tiền"),
        "text_refund_information_2":
            MessageLookupByLibrary.simpleMessage("Thông Tin Hoàn Tiền"),
        "text_refund_note": MessageLookupByLibrary.simpleMessage(
            "Lưu ý rằng sẽ không hoàn lại tiền cho phí bổ sung và mã khuyến mãi mà bạn đã sử dụng."),
        "text_refund_quantity":
            MessageLookupByLibrary.simpleMessage("Số lượng hoàn"),
        "text_refund_reason":
            MessageLookupByLibrary.simpleMessage("Lý do hoàn"),
        "text_refund_successful":
            MessageLookupByLibrary.simpleMessage("Hoàn tiền thành công"),
        "text_refund_time":
            MessageLookupByLibrary.simpleMessage("Thời gian hoàn tiền:"),
        "text_refund_unit":
            MessageLookupByLibrary.simpleMessage("Đơn vị hoàn lại:"),
        "text_register_and_user_account": MessageLookupByLibrary.simpleMessage(
            "1. Đăng Ký & Tài Khoản Người Dùng"),
        "text_register_new_id":
            MessageLookupByLibrary.simpleMessage("Đăng ký mới TripC ID"),
        "text_register_now":
            MessageLookupByLibrary.simpleMessage("Đăng ký ngay"),
        "text_remember_me": MessageLookupByLibrary.simpleMessage("Ghi nhớ tôi"),
        "text_remove_tripc_id":
            MessageLookupByLibrary.simpleMessage("Gỡ TripC ID khỏi tài khoản"),
        "text_remove_tripcid_dialog_note_1":
            MessageLookupByLibrary.simpleMessage(
                "Bạn sẽ không thể khôi phục được Số TripC ID của mình"),
        "text_remove_tripcid_dialog_note_2":
            MessageLookupByLibrary.simpleMessage(
                "Chúng tôi sẽ phát hành TripC ID này cho một tài khoản khác"),
        "text_request_electronic_invoice":
            MessageLookupByLibrary.simpleMessage("Yêu cầu Hóa đơn điện tử"),
        "text_request_for_electronic_invoice":
            MessageLookupByLibrary.simpleMessage(
                "Yêu cầu xuất hóa đơn điện tử"),
        "text_request_now":
            MessageLookupByLibrary.simpleMessage("Yêu cầu ngay"),
        "text_request_time":
            MessageLookupByLibrary.simpleMessage("Thời gian yêu cầu:"),
        "text_requested": MessageLookupByLibrary.simpleMessage("Đã yêu cầu"),
        "text_resend": MessageLookupByLibrary.simpleMessage("Gửi lại"),
        "text_resend_otp":
            MessageLookupByLibrary.simpleMessage("Gửi lại mã OTP"),
        "text_reservate_at":
            MessageLookupByLibrary.simpleMessage("Đặt chỗ tại "),
        "text_reservation_details":
            MessageLookupByLibrary.simpleMessage("Chi tiết đặt chỗ"),
        "text_reservation_failure":
            MessageLookupByLibrary.simpleMessage("Đặt chỗ thất bại"),
        "text_reservation_success":
            MessageLookupByLibrary.simpleMessage("Đã đặt chỗ thành công"),
        "text_reset": MessageLookupByLibrary.simpleMessage("Đặt lại"),
        "text_reset_2": MessageLookupByLibrary.simpleMessage("Đặt Lại"),
        "text_reset_passcode":
            MessageLookupByLibrary.simpleMessage("Đặt lại Passcode"),
        "text_reset_passcode_note": MessageLookupByLibrary.simpleMessage(
            "Passcode được dùng khi sử dụng dịch vụ và thẻ TripC ID.\nVui lòng tránh các chữ số liên quan đến ngày sinh nhật, các chữ số quá dễ đoán."),
        "text_reset_password":
            MessageLookupByLibrary.simpleMessage("Đặt lại mật khẩu"),
        "text_respectively": MessageLookupByLibrary.simpleMessage("tương ứng"),
        "text_responsibility_notice": MessageLookupByLibrary.simpleMessage(
            "Không sao chép, phát tán hoặc sử dụng nội dung của TripC mà không có sự đồng ý."),
        "text_restaurant": MessageLookupByLibrary.simpleMessage("Nhà Hàng"),
        "text_restaurant_space":
            MessageLookupByLibrary.simpleMessage("Nhà hàng: "),
        "text_review": MessageLookupByLibrary.simpleMessage("Đánh Giá"),
        "text_reviews": MessageLookupByLibrary.simpleMessage("Đánh giá"),
        "text_reviews_with_images":
            MessageLookupByLibrary.simpleMessage("Đánh giá có hình ảnh"),
        "text_sale": MessageLookupByLibrary.simpleMessage("Giảm"),
        "text_sale_refund_10":
            MessageLookupByLibrary.simpleMessage("Sale tết hoàn 10% TCent"),
        "text_same_old_email": MessageLookupByLibrary.simpleMessage(
            "Địa chỉ email mới phải khác với địa chỉ email hiện tại."),
        "text_save": MessageLookupByLibrary.simpleMessage("Lưu"),
        "text_save_qr_to_gallery": MessageLookupByLibrary.simpleMessage(
            "Đã lưu mã QR vào thư viện ảnh của bạn"),
        "text_save_up_to":
            MessageLookupByLibrary.simpleMessage("Tiết kiệm đến"),
        "text_saved": MessageLookupByLibrary.simpleMessage("Đã lưu"),
        "text_saved_tour": MessageLookupByLibrary.simpleMessage("Tour Đã Lưu"),
        "text_scan_qr":
            MessageLookupByLibrary.simpleMessage("Quét mã thanh toán"),
        "text_search": MessageLookupByLibrary.simpleMessage("Tìm kiếm"),
        "text_search_not_found": MessageLookupByLibrary.simpleMessage(
            "Không tìm thấy kết quả nào.\nBạn hãy thử lại nhé."),
        "text_seat_type": MessageLookupByLibrary.simpleMessage("Loại chỗ ngồi"),
        "text_security_activity_and_log": MessageLookupByLibrary.simpleMessage(
            "4. Hoạt Động & Nhật Ký Bảo Mật"),
        "text_security_settings":
            MessageLookupByLibrary.simpleMessage("2. Cài Đặt Bảo Mật"),
        "text_see": MessageLookupByLibrary.simpleMessage("Xem"),
        "text_see_all": MessageLookupByLibrary.simpleMessage("Xem tất cả"),
        "text_see_more": MessageLookupByLibrary.simpleMessage("Xem thêm"),
        "text_select": MessageLookupByLibrary.simpleMessage("Chọn"),
        "text_select_a_nice_account_number":
            MessageLookupByLibrary.simpleMessage("Chọn tài khoản số đẹp"),
        "text_select_promotional_code":
            MessageLookupByLibrary.simpleMessage("Chọn mã khuyến mãi"),
        "text_select_refund_reason":
            MessageLookupByLibrary.simpleMessage("Chọn lý do hoàn"),
        "text_select_service_package":
            MessageLookupByLibrary.simpleMessage("Lựa chọn thời gian"),
        "text_send": MessageLookupByLibrary.simpleMessage("Gửi"),
        "text_send_confirmation_code":
            MessageLookupByLibrary.simpleMessage("Gửi Mã Xác Nhận"),
        "text_send_otp": MessageLookupByLibrary.simpleMessage("Gửi OTP"),
        "text_send_otp_code":
            MessageLookupByLibrary.simpleMessage("Gửi Mã OTP"),
        "text_sent_6_digit_code": MessageLookupByLibrary.simpleMessage(
            "Chúng tôi đã gửi cho bạn một mã gồm 6 chữ số vào email của bạn"),
        "text_service": MessageLookupByLibrary.simpleMessage("Dịch vụ"),
        "text_service_information":
            MessageLookupByLibrary.simpleMessage("Thông tin dịch vụ"),
        "text_service_name":
            MessageLookupByLibrary.simpleMessage("Tên dịch vụ:"),
        "text_service_provision": MessageLookupByLibrary.simpleMessage(
            "Đảm bảo cung cấp dịch vụ đúng như mô tả trên ứng dụng."),
        "text_set_as_default":
            MessageLookupByLibrary.simpleMessage("Đặt làm mặc định"),
        "text_set_default_for_order": MessageLookupByLibrary.simpleMessage(
            "Đặt làm mặc định cho đơn đặt"),
        "text_setting": MessageLookupByLibrary.simpleMessage("Cài đặt"),
        "text_showing_view_history_in_30_days":
            MessageLookupByLibrary.simpleMessage(
                "Đang hiển thị lịch sử xem trong 30 ngày qua"),
        "text_shuttle_information":
            MessageLookupByLibrary.simpleMessage("Thông Tin Đưa Đón (Nếu có)"),
        "text_shuttle_service":
            MessageLookupByLibrary.simpleMessage("Dịch vụ đưa đón:"),
        "text_sign_in": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
        "text_sign_in_now":
            MessageLookupByLibrary.simpleMessage("Đăng nhập ngay!"),
        "text_sign_in_or_sign_up":
            MessageLookupByLibrary.simpleMessage("Đăng nhập/ Đăng ký"),
        "text_sign_up": MessageLookupByLibrary.simpleMessage("Đăng ký"),
        "text_sign_up_now":
            MessageLookupByLibrary.simpleMessage("Đăng ký ngay"),
        "text_sign_up_with_apple":
            MessageLookupByLibrary.simpleMessage("Đăng Ký Bằng Apple"),
        "text_sign_up_with_facebook":
            MessageLookupByLibrary.simpleMessage("Đăng Ký Bằng Facebook"),
        "text_sign_up_with_google":
            MessageLookupByLibrary.simpleMessage("Đăng Ký Bằng Google"),
        "text_silver_rank": MessageLookupByLibrary.simpleMessage("Hạng Bạc"),
        "text_skip": MessageLookupByLibrary.simpleMessage("Bỏ qua"),
        "text_sliver_tier": MessageLookupByLibrary.simpleMessage("Hạng Bạc"),
        "text_social_or_public_disturbances":
            MessageLookupByLibrary.simpleMessage(
                "Biểu tình hoặc bất ổn xã hội"),
        "text_special": MessageLookupByLibrary.simpleMessage("Đặc biệt:"),
        "text_special_requests":
            MessageLookupByLibrary.simpleMessage("Yêu Cầu Đặc Biệt (Nếu có)"),
        "text_state_your_reason":
            MessageLookupByLibrary.simpleMessage("Nêu lý do của bạn."),
        "text_status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
        "text_stay": MessageLookupByLibrary.simpleMessage("Lưu Trú"),
        "text_storage_permission_denied":
            MessageLookupByLibrary.simpleMessage("Quyền lưu trữ bị từ chối"),
        "text_submit_request":
            MessageLookupByLibrary.simpleMessage("Gửi yêu cầu"),
        "text_success_order":
            MessageLookupByLibrary.simpleMessage("Đơn đặt thành công!"),
        "text_success_payment":
            MessageLookupByLibrary.simpleMessage("Thanh toán thành công!"),
        "text_successfully_created_membership_card":
            MessageLookupByLibrary.simpleMessage(
                "Khởi tạo thẻ thành viên thành công"),
        "text_suggest_for_forgot_passcode":
            MessageLookupByLibrary.simpleMessage("Quên passcode?"),
        "text_suggested_keywords":
            MessageLookupByLibrary.simpleMessage("Từ khoá đề xuất"),
        "text_super_cheap_combo":
            MessageLookupByLibrary.simpleMessage("Combo siêu rẻ"),
        "text_super_cheap_tour":
            MessageLookupByLibrary.simpleMessage("Tour siêu rẻ"),
        "text_supplier_response":
            MessageLookupByLibrary.simpleMessage("Phản hồi từ nhà cung cấp"),
        "text_support_during_booking": MessageLookupByLibrary.simpleMessage(
            "Hỗ trợ khách hàng trong quá trình đặt tour và giải quyết các vấn đề phát sinh."),
        "text_surcharge_included":
            MessageLookupByLibrary.simpleMessage("Đã bao gồm phụ phí"),
        "text_tax_code": MessageLookupByLibrary.simpleMessage("Mã số thuế"),
        "text_taxi": MessageLookupByLibrary.simpleMessage("Taxi"),
        "text_tcent": MessageLookupByLibrary.simpleMessage("Tcent"),
        "text_tcent_hint": MessageLookupByLibrary.simpleMessage(
            "Sử dụng TCent để tiết kiệm nhiều hơn"),
        "text_tcent_point_method":
            MessageLookupByLibrary.simpleMessage("Điểm TCent"),
        "text_term_and_condition": MessageLookupByLibrary.simpleMessage(
            "Điều Khoản và Điều Kiện Sử Dụng Ứng Dụng TripC"),
        "text_term_and_condition_title":
            MessageLookupByLibrary.simpleMessage("Điều khoản & Điều kiện"),
        "text_terms_change":
            MessageLookupByLibrary.simpleMessage("7. Thay Đổi Điều Khoản"),
        "text_terms_conditions":
            MessageLookupByLibrary.simpleMessage("Điều khoản & Điều kiện"),
        "text_terms_modification": MessageLookupByLibrary.simpleMessage(
            "TripC có quyền thay đổi hoặc cập nhật điều khoản mà không cần thông báo trước."),
        "text_terms_of_use":
            MessageLookupByLibrary.simpleMessage("Điều Khoản Sử dụng"),
        "text_terms_update_notice": MessageLookupByLibrary.simpleMessage(
            "Việc tiếp tục sử dụng dịch vụ sau khi điều khoản thay đổi đồng nghĩa với việc bạn đồng ý với các cập nhật mới."),
        "text_ticket_change_is_not_required":
            MessageLookupByLibrary.simpleMessage("Không Bắt Buộc Phải Đổi Vé"),
        "text_ticket_id": MessageLookupByLibrary.simpleMessage("ID vé: "),
        "text_ticket_id_no":
            MessageLookupByLibrary.simpleMessage("Ticket ID No "),
        "text_ticket_info":
            MessageLookupByLibrary.simpleMessage("Thông tin vé"),
        "text_ticket_plane": MessageLookupByLibrary.simpleMessage("Vé máy bay"),
        "text_time": MessageLookupByLibrary.simpleMessage("Giờ"),
        "text_time_frame": MessageLookupByLibrary.simpleMessage("Khung giờ"),
        "text_to_be_supported_quick":
            MessageLookupByLibrary.simpleMessage(" để được hỗ trợ nhanh nhất!"),
        "text_today": MessageLookupByLibrary.simpleMessage("Hôm nay"),
        "text_top_search": MessageLookupByLibrary.simpleMessage("Top tìm kiếm"),
        "text_total_payment":
            MessageLookupByLibrary.simpleMessage("Tổng thanh toán"),
        "text_total_price":
            MessageLookupByLibrary.simpleMessage("Tổng giá tiền"),
        "text_total_refunded_amount":
            MessageLookupByLibrary.simpleMessage("Tổng số tiền được hoàn"),
        "text_tour_cancellation":
            MessageLookupByLibrary.simpleMessage("3. Hủy Tour & Hoàn Tiền"),
        "text_tour_guide":
            MessageLookupByLibrary.simpleMessage("Hướng dẫn viên: "),
        "text_tour_price": MessageLookupByLibrary.simpleMessage(
            "Giá tour hiển thị trên ứng dụng có thể thay đổi tùy theo chương trình khuyến mãi và điều kiện từng thời điểm."),
        "text_tour_sightseeing":
            MessageLookupByLibrary.simpleMessage("Tour & Trải Nghiệm"),
        "text_tour_ticket": MessageLookupByLibrary.simpleMessage("Vé tour"),
        "text_tour_time":
            MessageLookupByLibrary.simpleMessage("Thời gian tour: "),
        "text_train_ticket": MessageLookupByLibrary.simpleMessage("Vé Tàu Lửa"),
        "text_transaction_code":
            MessageLookupByLibrary.simpleMessage("Mã giao dịch"),
        "text_transportation_delays_or_accidents":
            MessageLookupByLibrary.simpleMessage(
                "Phương tiện đi chuyển bị chậm trễ/ bị hủy"),
        "text_trip": MessageLookupByLibrary.simpleMessage("Hành trình"),
        "text_trip_review":
            MessageLookupByLibrary.simpleMessage("Đánh giá chuyến đi"),
        "text_tripcID": MessageLookupByLibrary.simpleMessage("TripC ID"),
        "text_tripc_about":
            MessageLookupByLibrary.simpleMessage("Giới thiệu về TripC AI"),
        "text_tripc_id_free":
            MessageLookupByLibrary.simpleMessage("TripC ID miễn phí"),
        "text_tripc_id_free_content": MessageLookupByLibrary.simpleMessage(
            "Hoàn toàn miễn phí với đầy đủ tiện ích và tính năng trong hệ thống TripC"),
        "text_tripc_id_list":
            MessageLookupByLibrary.simpleMessage("Danh sách TripC ID"),
        "text_tripc_id_nice_number":
            MessageLookupByLibrary.simpleMessage("TripC ID số đẹp"),
        "text_tripc_id_nice_number_content": MessageLookupByLibrary.simpleMessage(
            "Chọn ngay số đẹp cho tài lộc liền tay, may mắn đông đầy. Sở hữu ngay!"),
        "text_tripc_id_setting":
            MessageLookupByLibrary.simpleMessage("Cài đặt ID TripC"),
        "text_tripc_intro_description": MessageLookupByLibrary.simpleMessage(
            "TripC AI là nền tảng đặt tour du lịch hàng đầu, mang đến cho bạn trải nghiệm dễ dàng, nhanh chóng và tiết kiệm khi lên kế hoạch cho mọi chuyến đi. Với hàng ngàn tour du lịch trong nước và quốc tế, TripC giúp bạn khám phá những điểm đến tuyệt đẹp với mức giá ưu đãi và dịch vụ chất lượng."),
        "text_tripc_intro_title": MessageLookupByLibrary.simpleMessage(
            "TripC AI – Ứng dụng đặt tour du lịch tiện lợi & ưu đãi hấp dẫn!"),
        "text_tripc_responsibility": MessageLookupByLibrary.simpleMessage(
            "5. Quyền & Trách Nhiệm Của TripC"),
        "text_tripc_term_and_policy":
            MessageLookupByLibrary.simpleMessage("Điều khoản & Điều kiện"),
        "text_try_again": MessageLookupByLibrary.simpleMessage("Thử lại"),
        "text_try_times": MessageLookupByLibrary.simpleMessage("lần thử"),
        "text_tu_quy_number": MessageLookupByLibrary.simpleMessage("Số tứ quý"),
        "text_tu_quy_number_meaning": MessageLookupByLibrary.simpleMessage(
            "Gồm 4 chữ số giống nhau liên tiếp (1111, 2222, 3333,...) thể hiện sự vững chắc, bền vững và đẳng cấp."),
        "text_unauthorized_usage": MessageLookupByLibrary.simpleMessage(
            "Tuân thủ quy định của TripC và các đối tác cung cấp dịch vụ. Không sử dụng ứng dụng cho mục đích bất hợp pháp, gian lận hoặc gây rối."),
        "text_understood": MessageLookupByLibrary.simpleMessage("Đã hiểu"),
        "text_unlink": MessageLookupByLibrary.simpleMessage("Huỷ liên kết"),
        "text_upload_image_title":
            MessageLookupByLibrary.simpleMessage("Tải lên Hình ảnh"),
        "text_upload_photos_and_reviews": MessageLookupByLibrary.simpleMessage(
            "Dùng để tải lên hình ảnh hồ sơ, đánh giá tour."),
        "text_use_now": MessageLookupByLibrary.simpleMessage("Dùng ngay"),
        "text_use_promo_code_issue":
            MessageLookupByLibrary.simpleMessage("Sự cố khi sử dụng mã ưu đãi"),
        "text_use_the_password_you_just_set":
            MessageLookupByLibrary.simpleMessage(
                "Sử dụng mật khẩu vừa thiết lập cho những lần truy cập sau."),
        "text_user_information": MessageLookupByLibrary.simpleMessage(
            "Người dùng cần cung cấp thông tin chính xác khi đăng ký tài khoản."),
        "text_user_rights": MessageLookupByLibrary.simpleMessage(
            "4. Quyền & Trách Nhiệm Của Người Dùng"),
        "text_valid_referral_code": MessageLookupByLibrary.simpleMessage(
            "Mã giới thiệu hợp lệ! Phần thưởng sẽ gửi đến bạn trong kho voucher sau khi hoàn tất."),
        "text_valid_verification_code_for_5_mins":
            MessageLookupByLibrary.simpleMessage(
                "*Mã xác nhận có hiệu lực trong 5 phút sau khi bạn nhận được mã."),
        "text_validity_period":
            MessageLookupByLibrary.simpleMessage("Thời gian hiệu lực:"),
        "text_vat": MessageLookupByLibrary.simpleMessage("VAT (10%)"),
        "text_verify": MessageLookupByLibrary.simpleMessage("Xác Thực"),
        "text_version": MessageLookupByLibrary.simpleMessage("Phiên bản"),
        "text_very_good": MessageLookupByLibrary.simpleMessage("Rất tốt"),
        "text_vietnamese": MessageLookupByLibrary.simpleMessage("Tiếng Việt"),
        "text_vietqr_method": MessageLookupByLibrary.simpleMessage(
            "VietQR (hoàn TCent lên đến 10% giá trị)"),
        "text_view_booked_ticket":
            MessageLookupByLibrary.simpleMessage("Xem vé đã đặt"),
        "text_view_booked_tours":
            MessageLookupByLibrary.simpleMessage("Xem đơn đã đặt"),
        "text_view_booking_payment_cancellation_history":
            MessageLookupByLibrary.simpleMessage(
                "Xem lại lịch sử đặt tour, thanh toán, hủy đơn."),
        "text_view_more": MessageLookupByLibrary.simpleMessage("Xem thêm"),
        "text_view_privacy_changes": MessageLookupByLibrary.simpleMessage(
            "Xem lại các thay đổi về cài đặt quyền riêng tư mà bạn đã thực hiện."),
        "text_view_recent_login": MessageLookupByLibrary.simpleMessage(
            "Xem lịch sử đăng nhập gần nhất, bao gồm thời gian & thiết bị."),
        "text_waiting_for_completion":
            MessageLookupByLibrary.simpleMessage("Chờ hoàn"),
        "text_waiting_for_payment":
            MessageLookupByLibrary.simpleMessage("Đang đợi thanh toán"),
        "text_waiting_for_refund":
            MessageLookupByLibrary.simpleMessage("Đang đợi hoàn tiền"),
        "text_waiting_payment":
            MessageLookupByLibrary.simpleMessage("Đang đợi thanh toán"),
        "text_waiting_receive":
            MessageLookupByLibrary.simpleMessage("Đang chờ nhận"),
        "text_waiting_scan_qr": MessageLookupByLibrary.simpleMessage(
            "Đang đợi quét mã QRCode thanh toán"),
        "text_wanna_go_danang":
            MessageLookupByLibrary.simpleMessage("Tôi muốn đi Hạ Long"),
        "text_warning_identify_email_link": MessageLookupByLibrary.simpleMessage(
            "Tài khoản của bạn hiện đã được liên kết với một địa chỉ email. Nếu bạn muốn đổi sang địa chỉ email mới, bạn sẽ cần xác minh danh tính"),
        "text_warning_lock_sign_in": m12,
        "text_warning_type_passcode": MessageLookupByLibrary.simpleMessage(
            "Passcode không chính xác. Nhập sai quá 5 lần sẽ bị khóa passcode trong vòng 24h."),
        "text_we_have_sent_code_to_email": MessageLookupByLibrary.simpleMessage(
            "Chúng tôi đã gửi mã xác nhận đến địa chỉ"),
        "text_wealthy_number":
            MessageLookupByLibrary.simpleMessage("Số phú quý"),
        "text_wealthy_number_meaning": MessageLookupByLibrary.simpleMessage(
            "Thể hiện sự giàu sang, phú quý với những con số mang ý nghĩa tốt đẹp\nVí dụ: 6886, 8386, 9999."),
        "text_welcome_message": MessageLookupByLibrary.simpleMessage(
            "Chào mừng bạn đến với TripC – ứng dụng đặt tour du lịch hàng đầu! Trước khi sử dụng dịch vụ, vui lòng đọc kỹ Điều khoản và Điều kiện dưới đây. Việc sử dụng ứng dụng đồng nghĩa với việc bạn đồng ý với các điều khoản này."),
        "text_welcome_to_tripC":
            MessageLookupByLibrary.simpleMessage("Chào Mừng Đến Với TripC"),
        "text_why_choose_tripc":
            MessageLookupByLibrary.simpleMessage("Lý do nên chọn TripC AI"),
        "text_wish_after_paid_combo": MessageLookupByLibrary.simpleMessage(
            "Chúc bạn có một hành trình đầy ắp các trãi nghiệm thú vị và an toàn\nCombo có hạn sử dụng nên hãy nhớ sử dụng nhé.\nĐừng quên, hãy chia sẽ những khoảng khắc đó tại TripC để nhận ưu đãi nhé."),
        "text_wishing": MessageLookupByLibrary.simpleMessage(
            "Chúc bạn có một hành trình đầy ắp các trãi nghiệm thú vị và an toàn\nĐừng quên, hãy chia sẽ những khoảng khắc đó tại TripC để nhận ưu đãi nhé"),
        "text_wrong_otp": MessageLookupByLibrary.simpleMessage(
            "Nhập sai mã xác nhận. Vui lòng thử lại!"),
        "text_wrong_tripcId_and_pass_code": MessageLookupByLibrary.simpleMessage(
            "Thông tin không hợp lệ. Vui lòng kiểm tra lại TripC ID và Passcode."),
        "text_yes": MessageLookupByLibrary.simpleMessage("Có"),
        "text_yesterday": MessageLookupByLibrary.simpleMessage("Hôm qua"),
        "text_you_are_choosing":
            MessageLookupByLibrary.simpleMessage("Bạn đang chọn"),
        "text_you_are_having":
            MessageLookupByLibrary.simpleMessage("Bạn hiện có: "),
        "text_you_have": MessageLookupByLibrary.simpleMessage("Bạn còn"),
        "text_you_have_not_set_a_passcode":
            MessageLookupByLibrary.simpleMessage(
                "Bạn chưa cài đặt Passcode cho TripC ID!"),
        "text_you_have_not_set_a_passcode_message":
            MessageLookupByLibrary.simpleMessage(
                "Nếu không cài đặt Passcode, bạn sẽ không thể sử dụng TripC ID để đặt tour, nhận ưu đãi và quản lý tài khoản."),
        "text_you_have_promotional_code": m13,
        "text_you_have_scrolled_to_the_end":
            MessageLookupByLibrary.simpleMessage("Bạn đã lướt đến cuối trang."),
        "text_you_have_successfully_set_pass": MessageLookupByLibrary.simpleMessage(
            "Bạn đã thiết lập Passcode thành công. Sử dụng Passcode vừa thiết lập cho những lần sau. "),
        "text_your_qr": MessageLookupByLibrary.simpleMessage("Mã QR của bạn"),
        "text_your_reservation_code":
            MessageLookupByLibrary.simpleMessage("Mã đặt chỗ của bạn"),
        "text_your_ticket_list":
            MessageLookupByLibrary.simpleMessage("Danh sách vé của bạn"),
        "text_zalo_pay_method": MessageLookupByLibrary.simpleMessage(
            "Ví điện tử ZaloPay (hoàn TCent lên đến 10% giá trị)"),
        "ticket": MessageLookupByLibrary.simpleMessage("vé"),
        "ticket_price": MessageLookupByLibrary.simpleMessage("Giá vé"),
        "time": MessageLookupByLibrary.simpleMessage("Thời gian"),
        "tour_and_experience":
            MessageLookupByLibrary.simpleMessage("Tour và trải nghiệm"),
        "tuan_chau_cat_ba_ferry_route": MessageLookupByLibrary.simpleMessage(
            "Tuyến phà Tuần Châu - Cát Bà"),
        "type_ticket": MessageLookupByLibrary.simpleMessage("Loại vé"),
        "use_the_offer": MessageLookupByLibrary.simpleMessage("Sử dụng ưu đãi"),
        "view_order_history":
            MessageLookupByLibrary.simpleMessage("Xem lại đơn hàng đã đặt"),
        "work_time": MessageLookupByLibrary.simpleMessage("Giờ hoạt động"),
        "year_ago": m14,
        "you_having_x_discount_code_enjoy": m15
      };
}
