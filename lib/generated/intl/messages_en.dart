// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(content) => "About ${content} days ago";

  static String m1(content) => "Number of guests (${content})";

  static String m2(content) => "About ${content} hour ago";

  static String m3(content) => "About ${content} min ago";

  static String m4(content) => "About ${content} month ago";

  static String m5(content) => "Adult ticket ${content} (Over 10 years old)";

  static String m6(content) => "Booking code: ${content}";

  static String m7(content) => "Child ticket ${content} (5 - 9 years old)";

  static String m8(content) => "${content} days left";

  static String m9(content) => "${content} previous days";

  static String m10(content) => "QR code for ticket ${content}";

  static String m11(content) => "Received ${content}";

  static String m12(content) =>
      "You\'ve entered too many times, so we\'ve temporarily locked your account.\nPlease try again after ${content}s.";

  static String m13(content) =>
      "You are having ${content} promotional code, please enjoy";

  static String m14(content) => "About ${content} year ago";

  static String m15(content) =>
      "(You have ${content} coupon code, enjoy it now)";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "amount_available_for_withdrawal": MessageLookupByLibrary.simpleMessage(
            "Amount available for withdrawal"),
        "baby": MessageLookupByLibrary.simpleMessage("Baby"),
        "below_5_year_old":
            MessageLookupByLibrary.simpleMessage("Below 5 years old"),
        "booking": MessageLookupByLibrary.simpleMessage("Booking"),
        "booking_failure":
            MessageLookupByLibrary.simpleMessage("Booking failed"),
        "booking_information":
            MessageLookupByLibrary.simpleMessage("Booking information"),
        "booking_success":
            MessageLookupByLibrary.simpleMessage("Booking successful"),
        "booking_success_dialog_content": MessageLookupByLibrary.simpleMessage(
            "Thank you for your reservation. We will send you a confirmation email shortly. We hope you have a great experience at the restaurant!"),
        "business_information":
            MessageLookupByLibrary.simpleMessage("Business information"),
        "cancel_refund": MessageLookupByLibrary.simpleMessage("Cancel refund"),
        "cat_ba_tuan_chau_ferry_route": MessageLookupByLibrary.simpleMessage(
            "Cat Ba - Tuan Chau ferry route"),
        "children": MessageLookupByLibrary.simpleMessage("Children"),
        "close": MessageLookupByLibrary.simpleMessage("Close"),
        "coming_time": MessageLookupByLibrary.simpleMessage("Reservation time"),
        "complete_booking":
            MessageLookupByLibrary.simpleMessage("Complete booking"),
        "confirmed": MessageLookupByLibrary.simpleMessage("Comfirmed"),
        "content": MessageLookupByLibrary.simpleMessage("Content"),
        "date": MessageLookupByLibrary.simpleMessage("Date"),
        "day_ago": m0,
        "detail_booking":
            MessageLookupByLibrary.simpleMessage("Detail booking"),
        "duration": MessageLookupByLibrary.simpleMessage("Duration"),
        "enter_content_no_more_than_250_characters":
            MessageLookupByLibrary.simpleMessage(
                "Enter content no more than 250 characters"),
        "enter_establishment_decision_number":
            MessageLookupByLibrary.simpleMessage(
                "Enter the establishment decision number"),
        "enter_name_of_social_organization":
            MessageLookupByLibrary.simpleMessage(
                "Enter the name of the social organization"),
        "enter_number_plate":
            MessageLookupByLibrary.simpleMessage("Enter number plate"),
        "enter_place": MessageLookupByLibrary.simpleMessage("Enter place"),
        "enter_special_request":
            MessageLookupByLibrary.simpleMessage("Enter special request..."),
        "enter_your_business_address":
            MessageLookupByLibrary.simpleMessage("Enter your business address"),
        "enter_your_business_email":
            MessageLookupByLibrary.simpleMessage("Enter your business email"),
        "enter_your_business_tax_code": MessageLookupByLibrary.simpleMessage(
            "Enter your business tax code"),
        "entertainment": MessageLookupByLibrary.simpleMessage("Entertainment"),
        "establishment_decision_number": MessageLookupByLibrary.simpleMessage(
            "Establishment decision number"),
        "estimated_refund_time":
            MessageLookupByLibrary.simpleMessage("Estimated refund time"),
        "event": MessageLookupByLibrary.simpleMessage("Event"),
        "every_day": MessageLookupByLibrary.simpleMessage("Every day"),
        "featured_accommodation":
            MessageLookupByLibrary.simpleMessage("Featured accommodations"),
        "feedback_success_content": MessageLookupByLibrary.simpleMessage(
            "The feedback has been updated on TripC\'s system"),
        "feedback_successfully":
            MessageLookupByLibrary.simpleMessage("Feedback successfully"),
        "food": MessageLookupByLibrary.simpleMessage("Food"),
        "from_5_to_9_year_old":
            MessageLookupByLibrary.simpleMessage("5 – 9 years old"),
        "go_back_to_homepage":
            MessageLookupByLibrary.simpleMessage("Back to homepage"),
        "gold_and_sport":
            MessageLookupByLibrary.simpleMessage("Golf and sport"),
        "guest_quantity":
            MessageLookupByLibrary.simpleMessage("Number of guests"),
        "guest_quantity_with_count": m1,
        "have_fun": MessageLookupByLibrary.simpleMessage("Have fun"),
        "health_beauty_category":
            MessageLookupByLibrary.simpleMessage("Health & Beauty"),
        "hello": MessageLookupByLibrary.simpleMessage("Hello"),
        "hotel_name": MessageLookupByLibrary.simpleMessage("Hotel name..."),
        "hour_ago": m2,
        "i_agree_with": MessageLookupByLibrary.simpleMessage("I agree to the "),
        "introduction": MessageLookupByLibrary.simpleMessage("Introduction"),
        "invoices_for_businesses":
            MessageLookupByLibrary.simpleMessage("Invoices for Businesses"),
        "invoices_for_individuals":
            MessageLookupByLibrary.simpleMessage("Invoices for Individuals"),
        "issue_electronic_invoice":
            MessageLookupByLibrary.simpleMessage("Issue electronic invoices"),
        "list_feedback_social_organization":
            MessageLookupByLibrary.simpleMessage(
                "List feedbacks from social organizations"),
        "location": MessageLookupByLibrary.simpleMessage("Location"),
        "min_ago": m3,
        "ministry_of_industry_notice": MessageLookupByLibrary.simpleMessage(
            "The application is in testing phase and is in the process of registration with the Ministry of Industry and Trade"),
        "month_ago": m4,
        "name_of_social_organization":
            MessageLookupByLibrary.simpleMessage("Name of social organization"),
        "no_deposit_required_when_booking":
            MessageLookupByLibrary.simpleMessage(
                "No deposit required when booking"),
        "number_plate": MessageLookupByLibrary.simpleMessage("Number plate"),
        "of_TripC": MessageLookupByLibrary.simpleMessage(" of TripC"),
        "optional": MessageLookupByLibrary.simpleMessage("Optional"),
        "order_code": MessageLookupByLibrary.simpleMessage("Order code"),
        "order_information":
            MessageLookupByLibrary.simpleMessage("Order information"),
        "other_menu_services":
            MessageLookupByLibrary.simpleMessage("Other Services"),
        "other_products":
            MessageLookupByLibrary.simpleMessage("Other products"),
        "our_products": MessageLookupByLibrary.simpleMessage("Our products"),
        "quantity": MessageLookupByLibrary.simpleMessage("Quantity"),
        "receive_feedback_social_organization":
            MessageLookupByLibrary.simpleMessage(
                "Receiving feedback from social organizations"),
        "recent_searches":
            MessageLookupByLibrary.simpleMessage("Recent searches"),
        "refund": MessageLookupByLibrary.simpleMessage("Refund"),
        "refund_history":
            MessageLookupByLibrary.simpleMessage("Refund history"),
        "refund_text": MessageLookupByLibrary.simpleMessage("Refund"),
        "refunded": MessageLookupByLibrary.simpleMessage("Refunded"),
        "regulatory_information":
            MessageLookupByLibrary.simpleMessage("Regulatory Information"),
        "request_for_electronic_invoice": MessageLookupByLibrary.simpleMessage(
            "Request for electronic invoice"),
        "request_order_recorded": MessageLookupByLibrary.simpleMessage(
            "Reservation request has been recorded!"),
        "reservation_date":
            MessageLookupByLibrary.simpleMessage("Reservation date"),
        "reservation_time":
            MessageLookupByLibrary.simpleMessage("Reservation time"),
        "room_amount_including_taxes_fees":
            MessageLookupByLibrary.simpleMessage(
                "Room amount (Including Taxes and Fees)"),
        "select_date": MessageLookupByLibrary.simpleMessage("Select date"),
        "serving_at": MessageLookupByLibrary.simpleMessage("Serving at"),
        "shopping": MessageLookupByLibrary.simpleMessage("Shopping"),
        "sort": MessageLookupByLibrary.simpleMessage("Sort"),
        "suggest_for_you":
            MessageLookupByLibrary.simpleMessage("Suggest for you"),
        "surcharge_included":
            MessageLookupByLibrary.simpleMessage("Surcharge included"),
        "taller_than_1_meter":
            MessageLookupByLibrary.simpleMessage("Taller than 1 m"),
        "term_of_use": MessageLookupByLibrary.simpleMessage("Terms of Use"),
        "terms_and_conditions":
            MessageLookupByLibrary.simpleMessage("Terms and conditions"),
        "terms_and_privacy_policy":
            MessageLookupByLibrary.simpleMessage("Terms and Privacy Policy"),
        "text_account_linking":
            MessageLookupByLibrary.simpleMessage("Account linking"),
        "text_account_linking_description":
            MessageLookupByLibrary.simpleMessage(
                "Account linking allows you to quickly log in to TripC"),
        "text_account_management":
            MessageLookupByLibrary.simpleMessage("Account management"),
        "text_account_privacy": MessageLookupByLibrary.simpleMessage(
            "TripC reserves the right to temporarily lock or cancel accounts if fraudulent activity or violations of the terms are detected."),
        "text_account_responsibility": MessageLookupByLibrary.simpleMessage(
            "You are responsible for securing your login information and not sharing your account with others."),
        "text_account_settings":
            MessageLookupByLibrary.simpleMessage("Account Settings"),
        "text_actual_refund_amount":
            MessageLookupByLibrary.simpleMessage("Actual refund amount"),
        "text_actual_refund_amount_2":
            MessageLookupByLibrary.simpleMessage("Actual refund amount"),
        "text_add_new_contact":
            MessageLookupByLibrary.simpleMessage("Add New Contact"),
        "text_add_new_tripc_id":
            MessageLookupByLibrary.simpleMessage("Add New TripC ID"),
        "text_add_passenger":
            MessageLookupByLibrary.simpleMessage("Add Passenger"),
        "text_add_passenger_information":
            MessageLookupByLibrary.simpleMessage("Add passenger information"),
        "text_add_phone_number":
            MessageLookupByLibrary.simpleMessage("Add Phone Number"),
        "text_add_quantity_passengers":
            MessageLookupByLibrary.simpleMessage("Add quantity of passengers"),
        "text_add_special_request":
            MessageLookupByLibrary.simpleMessage("Add special request"),
        "text_address": MessageLookupByLibrary.simpleMessage("Address:"),
        "text_adult_above_10":
            MessageLookupByLibrary.simpleMessage("Adults (Over 10 years old)"),
        "text_adult_info":
            MessageLookupByLibrary.simpleMessage("Adult Information"),
        "text_adult_ticket": m5,
        "text_agree": MessageLookupByLibrary.simpleMessage("Agree"),
        "text_aldult": MessageLookupByLibrary.simpleMessage("Adult"),
        "text_all": MessageLookupByLibrary.simpleMessage("All"),
        "text_amount_paid": MessageLookupByLibrary.simpleMessage("Amount paid"),
        "text_application_activity":
            MessageLookupByLibrary.simpleMessage("Application activity"),
        "text_application_period":
            MessageLookupByLibrary.simpleMessage("Application period:"),
        "text_apply": MessageLookupByLibrary.simpleMessage("Apply"),
        "text_as_u_know_delete_account": MessageLookupByLibrary.simpleMessage(
            "As you know, when you delete your account"),
        "text_as_u_know_remove_tripcid": MessageLookupByLibrary.simpleMessage(
            "As you know, when you remove TripC ID"),
        "text_at_last_page": MessageLookupByLibrary.simpleMessage(
            "You have reached the bottom of the page."),
        "text_available_omorrow":
            MessageLookupByLibrary.simpleMessage("Available Tomorrow"),
        "text_back_to_home":
            MessageLookupByLibrary.simpleMessage("Back to Home Page"),
        "text_bad": MessageLookupByLibrary.simpleMessage("Bad"),
        "text_bad_service_or_product_experience":
            MessageLookupByLibrary.simpleMessage(
                "Bad experience with service/product"),
        "text_bad_weather_or_natural_disaster":
            MessageLookupByLibrary.simpleMessage(
                "Bad weather or natural disaster"),
        "text_been_term_locked": MessageLookupByLibrary.simpleMessage(
            "Your account has been temporarily locked"),
        "text_before_placing":
            MessageLookupByLibrary.simpleMessage("Before Placing"),
        "text_best_selling":
            MessageLookupByLibrary.simpleMessage("Best-selling tour"),
        "text_big_deals": MessageLookupByLibrary.simpleMessage("Big deals"),
        "text_block_type_passcode": MessageLookupByLibrary.simpleMessage(
            "You have entered the wrong passcode more than 5 times and are locked for 24 hours."),
        "text_book_now": MessageLookupByLibrary.simpleMessage("Book now"),
        "text_booking_and_payment":
            MessageLookupByLibrary.simpleMessage("2. Booking & Payment"),
        "text_booking_code": m6,
        "text_booking_code_title":
            MessageLookupByLibrary.simpleMessage("Booking code"),
        "text_booking_date_time":
            MessageLookupByLibrary.simpleMessage("Booking date:"),
        "text_bronze_rank": MessageLookupByLibrary.simpleMessage("Bronze"),
        "text_bronze_tier": MessageLookupByLibrary.simpleMessage("Bronze Tier"),
        "text_business_address":
            MessageLookupByLibrary.simpleMessage("Business address"),
        "text_business_name":
            MessageLookupByLibrary.simpleMessage("Business name"),
        "text_buy_more_get_rewards": MessageLookupByLibrary.simpleMessage(
            "Buy more and get big rewards"),
        "text_by_clicking_pay_button": MessageLookupByLibrary.simpleMessage(
            "By clicking the Pay button, you confirm that I have agreed to "),
        "text_camera": MessageLookupByLibrary.simpleMessage("Camera"),
        "text_cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "text_cancel_free":
            MessageLookupByLibrary.simpleMessage("Free cancellation"),
        "text_cancel_request_received": MessageLookupByLibrary.simpleMessage(
            "We have received your cancel request. The service provider will give you results within 7-14 days at the earliest."),
        "text_cancel_request_sent": MessageLookupByLibrary.simpleMessage(
            "Cancel request has been sent"),
        "text_canceled": MessageLookupByLibrary.simpleMessage("Canceled"),
        "text_cancellation_policy": MessageLookupByLibrary.simpleMessage(
            "The tour cancellation and refund policy depend on each service provider."),
        "text_cancellation_time":
            MessageLookupByLibrary.simpleMessage("Cancellation time"),
        "text_cancle": MessageLookupByLibrary.simpleMessage("Cancle"),
        "text_cannot_be_canceled_one_actived":
            MessageLookupByLibrary.simpleMessage(
                "Cannot be canceled once activated"),
        "text_cannot_remove_default_tripcid": MessageLookupByLibrary.simpleMessage(
            "Each account is required to have a TripC ID, so you cannot remove the TripC ID from your account."),
        "text_card_balance_account":
            MessageLookupByLibrary.simpleMessage("Balance amount:"),
        "text_card_exp_label": MessageLookupByLibrary.simpleMessage("Exp:"),
        "text_card_name_label": MessageLookupByLibrary.simpleMessage("Name:"),
        "text_change_data_sharing_rights": MessageLookupByLibrary.simpleMessage(
            "You can change this right in the Device Settings."),
        "text_change_email_link":
            MessageLookupByLibrary.simpleMessage("Change Email Link"),
        "text_change_of_plan":
            MessageLookupByLibrary.simpleMessage("Change of plan"),
        "text_child_info":
            MessageLookupByLibrary.simpleMessage("Child Information"),
        "text_child_name":
            MessageLookupByLibrary.simpleMessage("Children\'s Full Name:"),
        "text_child_name_hint":
            MessageLookupByLibrary.simpleMessage("Enter child\'s full name"),
        "text_child_ticket": m7,
        "text_children": MessageLookupByLibrary.simpleMessage("children"),
        "text_children_5_to_9":
            MessageLookupByLibrary.simpleMessage("Children (5 - 9 years old)"),
        "text_choose_a_tour_date":
            MessageLookupByLibrary.simpleMessage("Choose A Tour Date"),
        "text_choose_beautiful_number": MessageLookupByLibrary.simpleMessage(
            "Choose TripC ID beautiful number"),
        "text_choose_destination":
            MessageLookupByLibrary.simpleMessage("Choose Destination"),
        "text_choose_destination_content": MessageLookupByLibrary.simpleMessage(
            "Find your perfect vacation spot, Find your perfect getaway and plan your trip"),
        "text_choose_image_source":
            MessageLookupByLibrary.simpleMessage("Choose an image source"),
        "text_choose_tripc_point_1": MessageLookupByLibrary.simpleMessage(
            "✅ Fast and secure tour booking: With just a few clicks, you can find and book the tour that suits your needs."),
        "text_choose_tripc_point_2": MessageLookupByLibrary.simpleMessage(
            "✅ Attractive discounts: Continuously updated promotional programs and discounts for customers."),
        "text_choose_tripc_point_3": MessageLookupByLibrary.simpleMessage(
            "✅ Quality service: Clear itinerary, ensuring safety for customers."),
        "text_choose_tripc_point_4": MessageLookupByLibrary.simpleMessage(
            "✅ 24/7 support: Our customer service team is always ready to assist and support you on your journey."),
        "text_city_of_residence":
            MessageLookupByLibrary.simpleMessage("City of residence"),
        "text_close": MessageLookupByLibrary.simpleMessage("Close"),
        "text_collapse": MessageLookupByLibrary.simpleMessage("Collapse"),
        "text_combo": MessageLookupByLibrary.simpleMessage("Combo"),
        "text_comming_soon_text": MessageLookupByLibrary.simpleMessage(
            "Feature under development\nWill be released as soon as possible"),
        "text_commitment_to_data_protection": MessageLookupByLibrary.simpleMessage(
            "TripC is committed to protecting your personal data and providing privacy control options so that you can use the application safely and transparently."),
        "text_company": MessageLookupByLibrary.simpleMessage("Company"),
        "text_conditional_cancellation":
            MessageLookupByLibrary.simpleMessage("Conditional Cancellation"),
        "text_conditions_and_privacy": MessageLookupByLibrary.simpleMessage(
            "Conditions and Privacy Policy"),
        "text_confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "text_confirmation_code_has_been_sent":
            MessageLookupByLibrary.simpleMessage(
                "Confirmation code has been sent"),
        "text_congratulation_and_make_passcode":
            MessageLookupByLibrary.simpleMessage(
                "Congratulations on owning a meaningful series of numbers.\nNext, create a Passcode for your account!"),
        "text_congratulation_create_membership_card":
            MessageLookupByLibrary.simpleMessage(
                "Congratulations, you have successfully created your membership card.\nExperience new features and incentives."),
        "text_congratulations_on_receiving_the_offer":
            MessageLookupByLibrary.simpleMessage(
                "Congratulations on receiving the offer"),
        "text_contact": MessageLookupByLibrary.simpleMessage("Contact"),
        "text_contact_information":
            MessageLookupByLibrary.simpleMessage("Contact Information"),
        "text_contact_message_1": MessageLookupByLibrary.simpleMessage(
            "For any questions or support requests, please contact TripC via email "),
        "text_contact_message_2":
            MessageLookupByLibrary.simpleMessage("for quick advice!"),
        "text_contact_method":
            MessageLookupByLibrary.simpleMessage("Contact method and details"),
        "text_contact_method_hint":
            MessageLookupByLibrary.simpleMessage("Example: Zalo +0987656484"),
        "text_contact_name":
            MessageLookupByLibrary.simpleMessage("Contact name"),
        "text_contact_support": MessageLookupByLibrary.simpleMessage(
            "For cancellation requests, please contact TripC for support."),
        "text_contact_tripc":
            MessageLookupByLibrary.simpleMessage("Contact TripC"),
        "text_contact_tripc_at": MessageLookupByLibrary.simpleMessage(
            "If you have any questions, please contact TripC at "),
        "text_contact_tripc_email":
            MessageLookupByLibrary.simpleMessage("<EMAIL> "),
        "text_contact_tripc_message": MessageLookupByLibrary.simpleMessage(
            "*Contact TripC immediately if you want to change information."),
        "text_contact_tripc_to_change_your_information":
            MessageLookupByLibrary.simpleMessage(
                "Contact TripC immediately if you want to change your information."),
        "text_continue": MessageLookupByLibrary.simpleMessage("Continue"),
        "text_continue_with_apple":
            MessageLookupByLibrary.simpleMessage("Continue with Apple"),
        "text_continue_with_facebook":
            MessageLookupByLibrary.simpleMessage("Continue with Facebook"),
        "text_continue_with_google":
            MessageLookupByLibrary.simpleMessage("Continue with Google"),
        "text_copied": MessageLookupByLibrary.simpleMessage("Copied!"),
        "text_country_or_region":
            MessageLookupByLibrary.simpleMessage("Country or region"),
        "text_create_account":
            MessageLookupByLibrary.simpleMessage("Create Account"),
        "text_create_passcode":
            MessageLookupByLibrary.simpleMessage("Create passcode"),
        "text_credit_card_method": MessageLookupByLibrary.simpleMessage(
            "International payment card (Visa/Master)"),
        "text_currency_unit":
            MessageLookupByLibrary.simpleMessage("Currency unit"),
        "text_danang_2d_1n":
            MessageLookupByLibrary.simpleMessage("Ha Long 2 days 1 night"),
        "text_dark_mode": MessageLookupByLibrary.simpleMessage("Dark mode"),
        "text_data_protection": MessageLookupByLibrary.simpleMessage(
            "TripC is committed to protecting users\' personal data according to the privacy policy."),
        "text_data_sharing": MessageLookupByLibrary.simpleMessage(
            "We do not share your personal information with third parties without your consent."),
        "text_data_sharing_rights":
            MessageLookupByLibrary.simpleMessage("3. Data Sharing Rights"),
        "text_day_before_the_next_use_date":
            MessageLookupByLibrary.simpleMessage(
                "Until 01:00, 1 day before the next use date"),
        "text_day_before_the_previous_use_date":
            MessageLookupByLibrary.simpleMessage(
                "Until 01:00, 1 day before the previous use date"),
        "text_days_left": m8,
        "text_deeply_discounted_tour":
            MessageLookupByLibrary.simpleMessage("Deeply discounted tour"),
        "text_delete_account_and_data": MessageLookupByLibrary.simpleMessage(
            "Delete account & data: If you want to stop using TripC, you can request to permanently delete the account and all related data."),
        "text_delete_account_dialog_note_1":
            MessageLookupByLibrary.simpleMessage(
                "You will not be able to check past bookings."),
        "text_delete_account_dialog_note_2":
            MessageLookupByLibrary.simpleMessage(
                "You won\'t be able to sign in to your account."),
        "text_delete_account_dialog_note_3":
            MessageLookupByLibrary.simpleMessage(
                "You will lose all TCent and discount codes."),
        "text_delete_account_dialog_note_4":
            MessageLookupByLibrary.simpleMessage(
                "We will not assist in recovering your account."),
        "text_delete_account_dialog_note_5": MessageLookupByLibrary.simpleMessage(
            "If you still want to delete your account, please ensure that all bookings are completed and that you have no issues or concerns after deleting your account."),
        "text_delete_browsing_history":
            MessageLookupByLibrary.simpleMessage("Delete Browsing History"),
        "text_delete_browsing_history_note": MessageLookupByLibrary.simpleMessage(
            "After deleting, you will no longer be able to view your browsing history. Do you still want to delete?"),
        "text_delete_contact":
            MessageLookupByLibrary.simpleMessage("Delele Contact"),
        "text_delete_contact_question":
            MessageLookupByLibrary.simpleMessage("Delete this contact?"),
        "text_delete_my_account":
            MessageLookupByLibrary.simpleMessage("Delete My Account"),
        "text_delete_my_account_note": MessageLookupByLibrary.simpleMessage(
            "If your account is deleted, all account information will also be deleted.\nYou will not be able to recover this information."),
        "text_delete_recent_search": MessageLookupByLibrary.simpleMessage(
            "Clear all recent search results?"),
        "text_departure_date":
            MessageLookupByLibrary.simpleMessage("Departure date: "),
        "text_departure_location":
            MessageLookupByLibrary.simpleMessage("Departure location: "),
        "text_detail": MessageLookupByLibrary.simpleMessage("Detail"),
        "text_detailed_information":
            MessageLookupByLibrary.simpleMessage("Detailed information"),
        "text_detailed_notification":
            MessageLookupByLibrary.simpleMessage("Detailed notification"),
        "text_details_included":
            MessageLookupByLibrary.simpleMessage("Details included:"),
        "text_diamond": MessageLookupByLibrary.simpleMessage("Diamond"),
        "text_diamond_rank": MessageLookupByLibrary.simpleMessage("Diamond"),
        "text_diamond_tier":
            MessageLookupByLibrary.simpleMessage("Diamond Tier"),
        "text_did_not_receive_otp": MessageLookupByLibrary.simpleMessage(
            "You didn\'t receive the OTP code?"),
        "text_disclaimer": MessageLookupByLibrary.simpleMessage("Disclaimer"),
        "text_disclaimer_note_1": MessageLookupByLibrary.simpleMessage(
            "This electronic invoice for this order will be issued by the supplier and will be calculated based on the original value (before applying the voucher)."),
        "text_disclaimer_note_2": MessageLookupByLibrary.simpleMessage(
            "In case the buyer does not provide information or does not send a request for an invoice. The supplier will use the information on the order to issue an electronic invoice."),
        "text_disclaimer_note_3": MessageLookupByLibrary.simpleMessage(
            "The supplier will not be responsible for any problems with tax declaration for invoices of 20 million VND or more paid in cash or any other personal payment method in TripC."),
        "text_disclaimer_note_4": MessageLookupByLibrary.simpleMessage(
            "Note: If the tax code is incorrect, the issued invoice will not have the buyer\'s tax code to ensure the legality of the invoice."),
        "text_discount": MessageLookupByLibrary.simpleMessage("Discount"),
        "text_discount_code_enjoy":
            MessageLookupByLibrary.simpleMessage("discount code , enjoy"),
        "text_discount_upper": MessageLookupByLibrary.simpleMessage("DISCOUNT"),
        "text_display_name":
            MessageLookupByLibrary.simpleMessage("Display name"),
        "text_dissatisfaction_with_experience":
            MessageLookupByLibrary.simpleMessage(
                "Dissatisfied with the experience"),
        "text_do_have_account":
            MessageLookupByLibrary.simpleMessage("Do you have an account?"),
        "text_do_not_have": MessageLookupByLibrary.simpleMessage("Do not have"),
        "text_dont_forget_share_moment": MessageLookupByLibrary.simpleMessage(
            "Your order has been successful. We wish you a safe and enjoyable journey."),
        "text_dont_forget_share_moment_2": MessageLookupByLibrary.simpleMessage(
            "Don\'t forget to share those moments at TripC to receive incentives!"),
        "text_dont_have_account":
            MessageLookupByLibrary.simpleMessage("You don\'t have an account?"),
        "text_download_personal_data": MessageLookupByLibrary.simpleMessage(
            "Download personal data: You can request to download all transaction history, booked tours, and stored personal information."),
        "text_download_ticket":
            MessageLookupByLibrary.simpleMessage("Download ticket"),
        "text_edit": MessageLookupByLibrary.simpleMessage("Edit"),
        "text_edit_contact":
            MessageLookupByLibrary.simpleMessage("Edit Contact"),
        "text_edit_passenger":
            MessageLookupByLibrary.simpleMessage("Edit information"),
        "text_edit_personal_information": MessageLookupByLibrary.simpleMessage(
            "Edit personal information: Update full name, phone number, email, address, payment information."),
        "text_electronic_invoice":
            MessageLookupByLibrary.simpleMessage("Electronic invoice"),
        "text_electronic_invoice_note": MessageLookupByLibrary.simpleMessage(
            "Electronic invoices can only be issued once before payment according to the information below. Please check and enter the information correctly. After selecting \"Pay\", the invoice information cannot be changed!"),
        "text_email": MessageLookupByLibrary.simpleMessage("Email"),
        "text_email_already_in_use": MessageLookupByLibrary.simpleMessage(
            "This email is already in use. Please log in to your account!"),
        "text_email_already_issue": MessageLookupByLibrary.simpleMessage(
            "This email is already used. Please login!"),
        "text_email_has_not_been_registered":
            MessageLookupByLibrary.simpleMessage(
                "Email has not been used to register an account"),
        "text_email_invalid":
            MessageLookupByLibrary.simpleMessage("Please enter a valid email!"),
        "text_email_link": MessageLookupByLibrary.simpleMessage("Email link"),
        "text_email_must_be_different": MessageLookupByLibrary.simpleMessage(
            "The new email address must be different from the current email address!"),
        "text_email_not_found": MessageLookupByLibrary.simpleMessage(
            "No account found with this email. Please check again or try another email"),
        "text_enable_disable_promotion_emails":
            MessageLookupByLibrary.simpleMessage(
                "Enable/disable receiving promotional emails and notifications about relevant tours."),
        "text_english": MessageLookupByLibrary.simpleMessage("English"),
        "text_enjoy_your_trip":
            MessageLookupByLibrary.simpleMessage("Enjoy Your Trip"),
        "text_enter": MessageLookupByLibrary.simpleMessage("Enter"),
        "text_enter_address":
            MessageLookupByLibrary.simpleMessage("Enter your address"),
        "text_enter_business_name":
            MessageLookupByLibrary.simpleMessage("Enter business name"),
        "text_enter_contact_phone_num":
            MessageLookupByLibrary.simpleMessage("Enter contact phone number"),
        "text_enter_email":
            MessageLookupByLibrary.simpleMessage("Enter your email"),
        "text_enter_email_to_get_passcode":
            MessageLookupByLibrary.simpleMessage("Enter email to get passcode"),
        "text_enter_fullname":
            MessageLookupByLibrary.simpleMessage("Enter fullname"),
        "text_enter_mail_reset_pass": MessageLookupByLibrary.simpleMessage(
            "Enter your Email account to reset your password"),
        "text_enter_mail_reset_passcode": MessageLookupByLibrary.simpleMessage(
            "Enter your Email account to reset your Passcode"),
        "text_enter_min_2_max_6": MessageLookupByLibrary.simpleMessage(
            "Enter min 2 numbers and max 6 numbers"),
        "text_enter_new_pass":
            MessageLookupByLibrary.simpleMessage("New Password"),
        "text_enter_new_passcode":
            MessageLookupByLibrary.simpleMessage("Enter new passcode"),
        "text_enter_passcode":
            MessageLookupByLibrary.simpleMessage("Enter Passcode"),
        "text_enter_phone_number":
            MessageLookupByLibrary.simpleMessage("Mobile contact number"),
        "text_enter_promotional_code":
            MessageLookupByLibrary.simpleMessage("Enter promotional code"),
        "text_enter_referral_code": MessageLookupByLibrary.simpleMessage(
            "Enter the referral code from your friend to immediately receive a special gift from us."),
        "text_enter_tax_code":
            MessageLookupByLibrary.simpleMessage("Enter tax code"),
        "text_enter_tripc_id_nice_number":
            MessageLookupByLibrary.simpleMessage("Enter TripC ID nice number"),
        "text_enter_your_name":
            MessageLookupByLibrary.simpleMessage("Enter your name"),
        "text_enter_your_password":
            MessageLookupByLibrary.simpleMessage("Enter your password"),
        "text_enter_your_referral_code":
            MessageLookupByLibrary.simpleMessage("Enter your referral code"),
        "text_enter_your_referral_code_from_friend":
            MessageLookupByLibrary.simpleMessage(
                "Enter the referral code from your friend to immediately receive a special gift from us."),
        "text_enter_your_tripc_id":
            MessageLookupByLibrary.simpleMessage("Enter your TripC ID"),
        "text_enter_your_tripc_passcode":
            MessageLookupByLibrary.simpleMessage("Enter TripC ID Passcode"),
        "text_error": MessageLookupByLibrary.simpleMessage("Error"),
        "text_error_phone_10": MessageLookupByLibrary.simpleMessage(
            "Please enter a valid phone number (10 digits)!"),
        "text_error_phone_9": MessageLookupByLibrary.simpleMessage(
            "Please enter a valid phone number (9 digits)!"),
        "text_error_something_wrong":
            MessageLookupByLibrary.simpleMessage("Something went wrong"),
        "text_example": MessageLookupByLibrary.simpleMessage("Example:"),
        "text_exclusively_for_you":
            MessageLookupByLibrary.simpleMessage("Exclusively for you"),
        "text_explore": MessageLookupByLibrary.simpleMessage("Explore"),
        "text_explore_the_word":
            MessageLookupByLibrary.simpleMessage("Explore The World"),
        "text_explore_the_word_content": MessageLookupByLibrary.simpleMessage(
            "Discover new places, cultures, and experiences , Unlock the doors to adventure and wanderlust"),
        "text_explore_viet_nam":
            MessageLookupByLibrary.simpleMessage("Explore Việt Nam"),
        "text_explore_viet_nam_content": MessageLookupByLibrary.simpleMessage(
            "Explore new places, cultures and experiences.\nUnlock the doors to adventure and wanderlust"),
        "text_extra": MessageLookupByLibrary.simpleMessage("EXTRA"),
        "text_failure_order":
            MessageLookupByLibrary.simpleMessage("Order failed!"),
        "text_family_combo":
            MessageLookupByLibrary.simpleMessage("Family combo"),
        "text_favorite_combo":
            MessageLookupByLibrary.simpleMessage("Favorite combo"),
        "text_fee":
            MessageLookupByLibrary.simpleMessage("Fees (excluding VAT)"),
        "text_fee_text": MessageLookupByLibrary.simpleMessage("Fee"),
        "text_female": MessageLookupByLibrary.simpleMessage("Female"),
        "text_fengshui_number":
            MessageLookupByLibrary.simpleMessage("Fengshui number"),
        "text_fill_in_hotel_address": MessageLookupByLibrary.simpleMessage(
            "Fill in hotel name and address"),
        "text_fill_in_passenger_info": MessageLookupByLibrary.simpleMessage(
            "Fill in passenger information"),
        "text_finish_membership_card": MessageLookupByLibrary.simpleMessage(
            "Complete initialization of your membership card"),
        "text_first_name": MessageLookupByLibrary.simpleMessage("First name"),
        "text_first_name_and_last_name":
            MessageLookupByLibrary.simpleMessage("Full name"),
        "text_flexible_ticket":
            MessageLookupByLibrary.simpleMessage("Flexible ticket"),
        "text_forgot_passcode":
            MessageLookupByLibrary.simpleMessage("Forgot passcode"),
        "text_forgot_password":
            MessageLookupByLibrary.simpleMessage("Forgot Password"),
        "text_forgot_password_lower":
            MessageLookupByLibrary.simpleMessage("Forgot password"),
        "text_from": MessageLookupByLibrary.simpleMessage("From"),
        "text_full_name": MessageLookupByLibrary.simpleMessage("Full name"),
        "text_full_name_invalid": MessageLookupByLibrary.simpleMessage(
            "Full name is not valid. Please re-enter!"),
        "text_full_name_over_50": MessageLookupByLibrary.simpleMessage(
            "Full name must not exceed 50 characters!"),
        "text_full_name_without_num": MessageLookupByLibrary.simpleMessage(
            "Full names cannot contain numbers"),
        "text_full_name_without_space":
            MessageLookupByLibrary.simpleMessage("Do not enter spaces!"),
        "text_full_name_without_special_sign":
            MessageLookupByLibrary.simpleMessage(
                "Please do not enter special characters!"),
        "text_fullname": MessageLookupByLibrary.simpleMessage("Full name:"),
        "text_gender": MessageLookupByLibrary.simpleMessage("Gender"),
        "text_general_information":
            MessageLookupByLibrary.simpleMessage("General information"),
        "text_get_now": MessageLookupByLibrary.simpleMessage("GET NOW"),
        "text_get_started": MessageLookupByLibrary.simpleMessage("Get started"),
        "text_give_a_tripc_id":
            MessageLookupByLibrary.simpleMessage("Give you a TripC ID"),
        "text_go_to_payment_page":
            MessageLookupByLibrary.simpleMessage("Go to Payment Page"),
        "text_gold_rank": MessageLookupByLibrary.simpleMessage("Gold"),
        "text_gold_tier": MessageLookupByLibrary.simpleMessage("Gold Tier"),
        "text_good": MessageLookupByLibrary.simpleMessage("Good"),
        "text_great_peace_number":
            MessageLookupByLibrary.simpleMessage("Great peace number"),
        "text_great_peace_number_meaning": MessageLookupByLibrary.simpleMessage(
            "Symbol of harmony, peace and stable development, suitable for those who want a peaceful life."),
        "text_guardian_name": MessageLookupByLibrary.simpleMessage("Guardian:"),
        "text_guardian_name_hint":
            MessageLookupByLibrary.simpleMessage("Enter guardian\'s full name"),
        "text_guardian_phone_number":
            MessageLookupByLibrary.simpleMessage("Guardian Phone Number:"),
        "text_guardian_phone_number_hint": MessageLookupByLibrary.simpleMessage(
            "Enter guardian\'s phone number"),
        "text_have_not_receive_otp": MessageLookupByLibrary.simpleMessage(
            "Haven\'t received the code yet? "),
        "text_hint_input_city": MessageLookupByLibrary.simpleMessage(
            "Enter your city of residence"),
        "text_hire_vehicle":
            MessageLookupByLibrary.simpleMessage("Hire Vehicle"),
        "text_home": MessageLookupByLibrary.simpleMessage("Home"),
        "text_hot_tour_during_tet_holiday":
            MessageLookupByLibrary.simpleMessage("Hot tour during Tet holiday"),
        "text_hotel": MessageLookupByLibrary.simpleMessage("Hotel"),
        "text_hotel_address":
            MessageLookupByLibrary.simpleMessage("Hotel/Address"),
        "text_hotel_membership_offers":
            MessageLookupByLibrary.simpleMessage("Membership Offers"),
        "text_hotel_space": MessageLookupByLibrary.simpleMessage("Hotel: "),
        "text_how_to_use": MessageLookupByLibrary.simpleMessage("How to Use"),
        "text_if_a_discount_is_applied": MessageLookupByLibrary.simpleMessage(
            "If a discount is applied, the fee will be calculated as a proportion of the pre-discount price. This fee will not exceed the amount actually paid. The product does not support partial refunds"),
        "text_illness_or_bursting_bubble": MessageLookupByLibrary.simpleMessage(
            "Illness or outbreak of disease"),
        "text_in_words": MessageLookupByLibrary.simpleMessage("In words"),
        "text_inactive_account":
            MessageLookupByLibrary.simpleMessage("Account is inactive"),
        "text_inactive_timeout_logout": MessageLookupByLibrary.simpleMessage(
            "If you remain inactive for a specified period (5, 10, 15 minutes...), the system will automatically log you out to protect your account."),
        "text_includes": MessageLookupByLibrary.simpleMessage("Includes"),
        "text_information_to_note":
            MessageLookupByLibrary.simpleMessage("Information to note"),
        "text_injury_or_severe_incident":
            MessageLookupByLibrary.simpleMessage("Injury or serious incident"),
        "text_input_phone_number":
            MessageLookupByLibrary.simpleMessage("Input phone number"),
        "text_invalid_referral_code": MessageLookupByLibrary.simpleMessage(
            "Invalid referral code! Please re-enter."),
        "text_invoice_email":
            MessageLookupByLibrary.simpleMessage("Invoice email:"),
        "text_invoice_message": MessageLookupByLibrary.simpleMessage(
            "Invoice will be sent within 7 working days (excluding Sat - Sun)"),
        "text_invoice_message_popup": MessageLookupByLibrary.simpleMessage(
            "We are processing your request.\nAn invoice will be sent to your email after you complete your order."),
        "text_invoice_success": MessageLookupByLibrary.simpleMessage(
            "Send request for electronic invoice successfully"),
        "text_invoice_type":
            MessageLookupByLibrary.simpleMessage("Invoice type:"),
        "text_issue_with_order_or_voucher":
            MessageLookupByLibrary.simpleMessage("Issue with order/ voucher"),
        "text_language": MessageLookupByLibrary.simpleMessage("Language"),
        "text_last_name": MessageLookupByLibrary.simpleMessage("Last name"),
        "text_later": MessageLookupByLibrary.simpleMessage("Later"),
        "text_lets_create_passcode": MessageLookupByLibrary.simpleMessage(
            "Completely free with full utilities and features in the TripC system.\nNext, create a Passcode for your account!"),
        "text_lets_enter_your_tripc_id": MessageLookupByLibrary.simpleMessage(
            "Please enter TripC ID (If any)"),
        "text_link": MessageLookupByLibrary.simpleMessage("Link"),
        "text_link_email_successful_content": MessageLookupByLibrary.simpleMessage(
            "You have successfully linked your email.\nUse the email you just set up to log in next time."),
        "text_link_email_successfully":
            MessageLookupByLibrary.simpleMessage("Link email successfully"),
        "text_link_phone_description": MessageLookupByLibrary.simpleMessage(
            "After adding your phone number, you can use it to link with other services in TripC."),
        "text_link_phone_number":
            MessageLookupByLibrary.simpleMessage("Add phone number"),
        "text_link_phone_successfully": MessageLookupByLibrary.simpleMessage(
            "Phone number added successfully"),
        "text_linked_email":
            MessageLookupByLibrary.simpleMessage("Linked email"),
        "text_linked_phone":
            MessageLookupByLibrary.simpleMessage("Phone number"),
        "text_log_out": MessageLookupByLibrary.simpleMessage("Log out"),
        "text_login_history":
            MessageLookupByLibrary.simpleMessage("Login history"),
        "text_login_to_see_membership_rank":
            MessageLookupByLibrary.simpleMessage(
                "Login to see membership rank"),
        "text_loyalty": MessageLookupByLibrary.simpleMessage("Loyalty"),
        "text_male": MessageLookupByLibrary.simpleMessage("Male"),
        "text_mark_notifications": MessageLookupByLibrary.simpleMessage(
            "Mark all notifications as read"),
        "text_membership_rank":
            MessageLookupByLibrary.simpleMessage("Membership rank"),
        "text_messages": MessageLookupByLibrary.simpleMessage("Messages"),
        "text_mobile_phone":
            MessageLookupByLibrary.simpleMessage("Contact mobile phone number"),
        "text_momo_method": MessageLookupByLibrary.simpleMessage(
            "Momo e-wallet (TCent refund up to 10% value)"),
        "text_moving": MessageLookupByLibrary.simpleMessage("Moving"),
        "text_must_purchase_before_six": MessageLookupByLibrary.simpleMessage(
            "Must be purchased before 6:00 p.m., 1 day before use (Local Time)"),
        "text_my_account":
            MessageLookupByLibrary.simpleMessage("Manage My Account"),
        "text_my_trip": MessageLookupByLibrary.simpleMessage("My Trip"),
        "text_my_tripc_empty_message": MessageLookupByLibrary.simpleMessage(
            "No trips yet.\nBook tickets to experience the trip with TripC!"),
        "text_national": MessageLookupByLibrary.simpleMessage("National"),
        "text_need_e_invoice": MessageLookupByLibrary.simpleMessage(
            "You will need an “E-Ticket” to verify and use your booking"),
        "text_need_to_correct_information": MessageLookupByLibrary.simpleMessage(
            "Need to correct date/time/information of the customer/other information"),
        "text_need_to_correct_order":
            MessageLookupByLibrary.simpleMessage("Need to correct order"),
        "text_need_to_correct_package":
            MessageLookupByLibrary.simpleMessage("Need to correct package"),
        "text_neutral_and_negative_reviews":
            MessageLookupByLibrary.simpleMessage(
                "Neutral and negative reviews"),
        "text_new_deal": MessageLookupByLibrary.simpleMessage("New deal"),
        "text_new_email_link":
            MessageLookupByLibrary.simpleMessage("New email link"),
        "text_newly_entered_password_is_incorrect":
            MessageLookupByLibrary.simpleMessage(
                "The newly entered password is incorrect"),
        "text_nguquy_number":
            MessageLookupByLibrary.simpleMessage("Ngu quy number"),
        "text_no_broswing_history": MessageLookupByLibrary.simpleMessage(
            "No browsing history.\nPlease return to the home page and start exploring TripC AI\'s services and features!"),
        "text_no_internet": MessageLookupByLibrary.simpleMessage(
            "Please check your network settings or try again."),
        "text_no_promotional_code": MessageLookupByLibrary.simpleMessage(
            "There are currently no promotional codes available. If you have a promotional code, you can and apply it"),
        "text_no_review":
            MessageLookupByLibrary.simpleMessage("No reviews yet"),
        "text_no_saved_tour":
            MessageLookupByLibrary.simpleMessage("No tours saved."),
        "text_non_refundable": MessageLookupByLibrary.simpleMessage(
            "Some tours may not support refunds or only partially refund based on cancellation time."),
        "text_not_found":
            MessageLookupByLibrary.simpleMessage("No results found."),
        "text_not_same_old_phone_number": MessageLookupByLibrary.simpleMessage(
            "The new phone number must be different from the current phone number."),
        "text_not_yet_paid":
            MessageLookupByLibrary.simpleMessage("Not yet paid"),
        "text_note": MessageLookupByLibrary.simpleMessage("Note:"),
        "text_note_2": MessageLookupByLibrary.simpleMessage("Note:"),
        "text_note_label": MessageLookupByLibrary.simpleMessage("Note"),
        "text_note_price_at_local_time": MessageLookupByLibrary.simpleMessage(
            "Local time | The price displayed will be the lowest price for the current day"),
        "text_notification":
            MessageLookupByLibrary.simpleMessage("Notification"),
        "text_of": MessageLookupByLibrary.simpleMessage("Of"),
        "text_of_tripc": MessageLookupByLibrary.simpleMessage(" of TripC"),
        "text_open_setting":
            MessageLookupByLibrary.simpleMessage("Open Settings"),
        "text_opp_not_logged_in": MessageLookupByLibrary.simpleMessage(
            "Oops! You are not logged in!"),
        "text_or": MessageLookupByLibrary.simpleMessage("Or"),
        "text_or_2": MessageLookupByLibrary.simpleMessage(" or "),
        "text_order_information":
            MessageLookupByLibrary.simpleMessage("Order Information"),
        "text_order_information_first_row":
            MessageLookupByLibrary.simpleMessage(
                "Reviews outside the attractions"),
        "text_order_information_second_row":
            MessageLookupByLibrary.simpleMessage("Cancel when possible"),
        "text_order_information_third_row":
            MessageLookupByLibrary.simpleMessage(
                "Information about products/services."),
        "text_other_external_conditions": MessageLookupByLibrary.simpleMessage(
            "Other conditions beyond control"),
        "text_other_gender":
            MessageLookupByLibrary.simpleMessage("Don\'t want to reveal"),
        "text_other_reasons":
            MessageLookupByLibrary.simpleMessage("Other reasons"),
        "text_otp_code_verification":
            MessageLookupByLibrary.simpleMessage("OTP Code Verification"),
        "text_otp_expired":
            MessageLookupByLibrary.simpleMessage("Your OTP code has expired"),
        "text_otp_has_been_sent": MessageLookupByLibrary.simpleMessage(
            "The OTP code is sent back to your Email"),
        "text_otp_incorrect": MessageLookupByLibrary.simpleMessage(
            "Please enter the correct OTP code"),
        "text_otp_valid_30_minute": MessageLookupByLibrary.simpleMessage(
            "* The confirmation code is valid for 30 minutes after you receive it."),
        "text_out_of_stock": MessageLookupByLibrary.simpleMessage(
            "The order you selected is temporarily out of stock. Please choose another order or try later."),
        "text_outside_the_scope_of_control":
            MessageLookupByLibrary.simpleMessage(
                "Outside the scope of control"),
        "text_paid": MessageLookupByLibrary.simpleMessage("Paid"),
        "text_passcode_reset_successfully":
            MessageLookupByLibrary.simpleMessage("Passcode reset successfully"),
        "text_passenger": MessageLookupByLibrary.simpleMessage("Passenger"),
        "text_passenger_information":
            MessageLookupByLibrary.simpleMessage("Passenger information"),
        "text_passenger_information_2":
            MessageLookupByLibrary.simpleMessage("Passenger Information"),
        "text_passenger_name":
            MessageLookupByLibrary.simpleMessage("Passenger Name"),
        "text_passenger_name_rule": MessageLookupByLibrary.simpleMessage(
            "Passenger name must be entered exactly as it appears on your identification document"),
        "text_passenger_title":
            MessageLookupByLibrary.simpleMessage("Passenger"),
        "text_password": MessageLookupByLibrary.simpleMessage("Password"),
        "text_password_invalid":
            MessageLookupByLibrary.simpleMessage("Password must be valid"),
        "text_password_lowercase_valid": MessageLookupByLibrary.simpleMessage(
            "Password must contain at least one lowercase letter!"),
        "text_password_must_be_1_special": MessageLookupByLibrary.simpleMessage(
            "Password must contain at least one special character!"),
        "text_password_must_be_at_last_8_characters":
            MessageLookupByLibrary.simpleMessage(
                "Password must be at least 8 characters!"),
        "text_password_one_number_valid": MessageLookupByLibrary.simpleMessage(
            "Password must contain at least one digit!"),
        "text_password_reset_successfully":
            MessageLookupByLibrary.simpleMessage("Password reset successfully"),
        "text_password_uppercase_valid": MessageLookupByLibrary.simpleMessage(
            "Password must contain at least one uppercase letter!"),
        "text_password_validation": MessageLookupByLibrary.simpleMessage(
            "Password must be at least 8 characters long, contain uppercase letters, lowercase letters, numbers and special characters"),
        "text_password_verification": MessageLookupByLibrary.simpleMessage(
            "Password must be at least 8 characters long, contain uppercase letters, lowercase letters, numbers and special characters"),
        "text_password_without_space": MessageLookupByLibrary.simpleMessage(
            "Password cannot contain spaces!"),
        "text_pay": MessageLookupByLibrary.simpleMessage("Pay"),
        "text_payment_confirmation": MessageLookupByLibrary.simpleMessage(
            "After successful payment, TripC will send booking confirmation via email or the app."),
        "text_payment_content": MessageLookupByLibrary.simpleMessage(
            "CT: Pay bills TripC ID at TripC"),
        "text_payment_fail":
            MessageLookupByLibrary.simpleMessage("Payment failed!"),
        "text_payment_has_been_successful":
            MessageLookupByLibrary.simpleMessage("Payment has been successful"),
        "text_payment_later": MessageLookupByLibrary.simpleMessage("Pay later"),
        "text_payment_method":
            MessageLookupByLibrary.simpleMessage("Payment method"),
        "text_payment_method_condition": MessageLookupByLibrary.simpleMessage(
            "Payments are processed through various methods such as bank cards, e-wallets, or bank transfers."),
        "text_payment_successful":
            MessageLookupByLibrary.simpleMessage("Payment successful!"),
        "text_payment_vn_pay_method": MessageLookupByLibrary.simpleMessage(
            "VNPay e-wallet (TCent refund up to 10% of value)"),
        "text_payment_with_payos":
            MessageLookupByLibrary.simpleMessage("Payment with PayOS"),
        "text_people": MessageLookupByLibrary.simpleMessage("People"),
        "text_people_are_searching":
            MessageLookupByLibrary.simpleMessage("People are searching for"),
        "text_permission_note": MessageLookupByLibrary.simpleMessage(
            "Please enable permission in settings to continue."),
        "text_permission_required":
            MessageLookupByLibrary.simpleMessage("Permission Required"),
        "text_personal": MessageLookupByLibrary.simpleMessage("Personal"),
        "text_personal_data_management":
            MessageLookupByLibrary.simpleMessage("1. Personal Data Management"),
        "text_personal_info":
            MessageLookupByLibrary.simpleMessage("Personal Information"),
        "text_phone": MessageLookupByLibrary.simpleMessage("Phone"),
        "text_phone_already_issue": MessageLookupByLibrary.simpleMessage(
            "This phone number already exists!"),
        "text_phone_number":
            MessageLookupByLibrary.simpleMessage("Phone number:"),
        "text_photo": MessageLookupByLibrary.simpleMessage("Photos"),
        "text_pick_up_airport":
            MessageLookupByLibrary.simpleMessage("Pick Up\nAirport"),
        "text_pick_up_time_at_hotel":
            MessageLookupByLibrary.simpleMessage("Pick up time at hotel"),
        "text_pick_up_time_at_hotel_hint":
            MessageLookupByLibrary.simpleMessage("Example: 10:00"),
        "text_platinum_rank": MessageLookupByLibrary.simpleMessage("Platinum"),
        "text_platinum_tier":
            MessageLookupByLibrary.simpleMessage("Platinum Tier"),
        "text_please_log_in_or_register": MessageLookupByLibrary.simpleMessage(
            "Please log in or register to explore content."),
        "text_please_try_again":
            MessageLookupByLibrary.simpleMessage("Please try again!"),
        "text_pls_check_inbox_email": MessageLookupByLibrary.simpleMessage(
            "Please check your inbox and enter the code below"),
        "text_pls_choose_your_own_tripc_id":
            MessageLookupByLibrary.simpleMessage(
                "Please choose your own TripC ID"),
        "text_pls_enter_6_digit": MessageLookupByLibrary.simpleMessage(
            "Please enter 6-digit Passcode:"),
        "text_pls_enter_a_new_email": MessageLookupByLibrary.simpleMessage(
            "Please enter a new email address. Once successfully linked to your account, you can use this email to log in."),
        "text_pls_enter_correct_password":
            MessageLookupByLibrary.simpleMessage("Wrong password"),
        "text_pls_enter_details": MessageLookupByLibrary.simpleMessage(
            "Enter your account information!"),
        "text_pls_enter_full_name": MessageLookupByLibrary.simpleMessage(
            "Please enter full name from 2 to 50 characters!"),
        "text_pls_enter_valid_email": MessageLookupByLibrary.simpleMessage(
            "Please enter a valid email address!"),
        "text_pls_enter_verified_email": MessageLookupByLibrary.simpleMessage(
            "Please enter a verified email address!"),
        "text_pls_set_security_pass": MessageLookupByLibrary.simpleMessage(
            "Please set a more secure password for your account.\nPlease avoid numbers related to birthdays, which are too easy to guess."),
        "text_pls_type_phone_number":
            MessageLookupByLibrary.simpleMessage("Please enter phone number"),
        "text_pls_type_valid_phone_number":
            MessageLookupByLibrary.simpleMessage(
                "Please enter a valid phone number (9 digits)"),
        "text_popular_tour":
            MessageLookupByLibrary.simpleMessage("Popular tours"),
        "text_post_at": MessageLookupByLibrary.simpleMessage("Post at"),
        "text_powered_by": MessageLookupByLibrary.simpleMessage("Powered by "),
        "text_previous_day": m9,
        "text_price": MessageLookupByLibrary.simpleMessage("Price"),
        "text_principles_on_passenger_names":
            MessageLookupByLibrary.simpleMessage(
                "Principles on Passenger Names"),
        "text_privacy": MessageLookupByLibrary.simpleMessage("Privacy"),
        "text_privacy_changes_history":
            MessageLookupByLibrary.simpleMessage("History of privacy changes"),
        "text_privacy_policy":
            MessageLookupByLibrary.simpleMessage("6. Privacy Policy"),
        "text_privacy_policy_at_tripc":
            MessageLookupByLibrary.simpleMessage("Privacy Policy at TripC"),
        "text_processed": MessageLookupByLibrary.simpleMessage("Processed"),
        "text_processing": MessageLookupByLibrary.simpleMessage("Processing"),
        "text_products_you_have_searched_for":
            MessageLookupByLibrary.simpleMessage(
                "Products you have searched for"),
        "text_profile": MessageLookupByLibrary.simpleMessage("Profile"),
        "text_profile_contact": MessageLookupByLibrary.simpleMessage("Contact"),
        "text_profile_gift": MessageLookupByLibrary.simpleMessage("Gifts"),
        "text_profile_holtel_membership":
            MessageLookupByLibrary.simpleMessage("Hotel Membership Benefits"),
        "text_profile_info":
            MessageLookupByLibrary.simpleMessage("About \nTripC.AI"),
        "text_profile_moment":
            MessageLookupByLibrary.simpleMessage("Moments & \nReviews"),
        "text_profile_pravicy":
            MessageLookupByLibrary.simpleMessage("Terms \n& Conditions"),
        "text_profile_promotion":
            MessageLookupByLibrary.simpleMessage("Promo Code"),
        "text_profile_recent":
            MessageLookupByLibrary.simpleMessage("Recently \nViewed"),
        "text_profile_review": MessageLookupByLibrary.simpleMessage("Rate App"),
        "text_profile_support":
            MessageLookupByLibrary.simpleMessage("Customer Support"),
        "text_profile_tour_booked":
            MessageLookupByLibrary.simpleMessage("Booked Tours"),
        "text_profile_tour_saved":
            MessageLookupByLibrary.simpleMessage("Saved Tours"),
        "text_profile_wallet":
            MessageLookupByLibrary.simpleMessage("My TripC ID"),
        "text_promotion": MessageLookupByLibrary.simpleMessage("Promotion"),
        "text_promotion_2nd": MessageLookupByLibrary.simpleMessage("Promotion"),
        "text_promotion_around_here":
            MessageLookupByLibrary.simpleMessage("Promotion around here"),
        "text_promotion_combo":
            MessageLookupByLibrary.simpleMessage("Promotion Combo"),
        "text_promotional_empty_message": MessageLookupByLibrary.simpleMessage(
            "There are currently no promotional codes available.\nIf you have a promotional code, you can and apply it"),
        "text_proposed_itinerary":
            MessageLookupByLibrary.simpleMessage("Proposed itinerary"),
        "text_proposed_itinerary_hint":
            MessageLookupByLibrary.simpleMessage("Fill in proposed itinerary"),
        "text_proposer_number_meaning": MessageLookupByLibrary.simpleMessage(
            "Meaning of luck, money and prosperity. For example: 68, 86, 39, 79,..."),
        "text_prosper_number":
            MessageLookupByLibrary.simpleMessage("Prosper number"),
        "text_qr_code_reservation":
            MessageLookupByLibrary.simpleMessage("Reservation QR code"),
        "text_qr_code_ticket": m10,
        "text_qr_reservation":
            MessageLookupByLibrary.simpleMessage("Reservation QR"),
        "text_quantity": MessageLookupByLibrary.simpleMessage("Quantity:"),
        "text_rating": MessageLookupByLibrary.simpleMessage("Rating"),
        "text_rating_2": MessageLookupByLibrary.simpleMessage("review"),
        "text_re_enter_new_pass":
            MessageLookupByLibrary.simpleMessage("Confirm New Password"),
        "text_re_enter_new_passcode":
            MessageLookupByLibrary.simpleMessage("Re-enter new passcode"),
        "text_re_enter_password":
            MessageLookupByLibrary.simpleMessage("Re-enter the password"),
        "text_re_entered_new_passcode_incorrectly":
            MessageLookupByLibrary.simpleMessage(
                "Passcode re-entered incorrectly"),
        "text_read_more": MessageLookupByLibrary.simpleMessage("Read more"),
        "text_receive": MessageLookupByLibrary.simpleMessage("Receive"),
        "text_receive_login_alerts": MessageLookupByLibrary.simpleMessage(
            "Receive alerts for unusual login attempts."),
        "text_receive_now": MessageLookupByLibrary.simpleMessage("Receive now"),
        "text_received_tcent": m11,
        "text_recent_searches":
            MessageLookupByLibrary.simpleMessage("Recent Searches"),
        "text_recent_view":
            MessageLookupByLibrary.simpleMessage("Recently Viewed"),
        "text_recently_viewed":
            MessageLookupByLibrary.simpleMessage("Recently viewed"),
        "text_recommended_destination":
            MessageLookupByLibrary.simpleMessage("Recommended Destination"),
        "text_reduce": MessageLookupByLibrary.simpleMessage("Reduce"),
        "text_referral_code": MessageLookupByLibrary.simpleMessage(
            "Use referral code to receive the offer"),
        "text_referral_code_define": MessageLookupByLibrary.simpleMessage(
            "The referral code is the TripC ID provided by a friend or relative when they have used the application.\nYou can skip this step and enter the code later if you do not have a referral code."),
        "text_refund": MessageLookupByLibrary.simpleMessage("Refund"),
        "text_refund_checking": MessageLookupByLibrary.simpleMessage(
            "We have checked the refund request to your account. The service provider will give you results within 7-14 days at the earliest."),
        "text_refund_in_process": MessageLookupByLibrary.simpleMessage(
            "We have processed the refund to your account. The time for the refund to your account depends on the payment method used to place the order."),
        "text_refund_information":
            MessageLookupByLibrary.simpleMessage("Thông tin hoàn tiền"),
        "text_refund_information_2":
            MessageLookupByLibrary.simpleMessage("Refund Information"),
        "text_refund_note": MessageLookupByLibrary.simpleMessage(
            "Please note that there will be no refunds for additional fees and promotional codes you have used."),
        "text_refund_quantity":
            MessageLookupByLibrary.simpleMessage("Refund quantity"),
        "text_refund_reason":
            MessageLookupByLibrary.simpleMessage("Refund reason"),
        "text_refund_successful":
            MessageLookupByLibrary.simpleMessage("Refund successful"),
        "text_refund_time":
            MessageLookupByLibrary.simpleMessage("Refund time:"),
        "text_refund_unit":
            MessageLookupByLibrary.simpleMessage("Refund unit:"),
        "text_register_and_user_account": MessageLookupByLibrary.simpleMessage(
            "1. Registration & User Account"),
        "text_register_new_id":
            MessageLookupByLibrary.simpleMessage("Register new TripC ID"),
        "text_register_now":
            MessageLookupByLibrary.simpleMessage("Register now"),
        "text_remember_me": MessageLookupByLibrary.simpleMessage("Remember me"),
        "text_remove_tripc_id": MessageLookupByLibrary.simpleMessage(
            "Remove TripC ID from your account"),
        "text_remove_tripcid_dialog_note_1":
            MessageLookupByLibrary.simpleMessage(
                "You will not be able to recover your TripC ID Number"),
        "text_remove_tripcid_dialog_note_2":
            MessageLookupByLibrary.simpleMessage(
                "We will issue this TripC ID to another account"),
        "text_request_electronic_invoice":
            MessageLookupByLibrary.simpleMessage("Request electronic invoice"),
        "text_request_for_electronic_invoice":
            MessageLookupByLibrary.simpleMessage(
                "Request for electronic invoice"),
        "text_request_now": MessageLookupByLibrary.simpleMessage("Request Now"),
        "text_request_time":
            MessageLookupByLibrary.simpleMessage("Request time:"),
        "text_requested": MessageLookupByLibrary.simpleMessage("Requested"),
        "text_resend": MessageLookupByLibrary.simpleMessage("Resend"),
        "text_resend_otp":
            MessageLookupByLibrary.simpleMessage("Resend OTP code"),
        "text_reservate_at":
            MessageLookupByLibrary.simpleMessage("Reservate at "),
        "text_reservation_details":
            MessageLookupByLibrary.simpleMessage("Reservation details"),
        "text_reservation_failure":
            MessageLookupByLibrary.simpleMessage("Booking failed"),
        "text_reservation_success":
            MessageLookupByLibrary.simpleMessage("Booking successful"),
        "text_reset": MessageLookupByLibrary.simpleMessage("Reset"),
        "text_reset_2": MessageLookupByLibrary.simpleMessage("Reset"),
        "text_reset_passcode":
            MessageLookupByLibrary.simpleMessage("Reset Passcode"),
        "text_reset_passcode_note": MessageLookupByLibrary.simpleMessage(
            "Passcode is used when using services and TripC ID cards.\nPlease avoid numbers related to birthdays, numbers that are too easy to guess."),
        "text_reset_password":
            MessageLookupByLibrary.simpleMessage("Reset password"),
        "text_respectively":
            MessageLookupByLibrary.simpleMessage("respectively"),
        "text_responsibility_notice": MessageLookupByLibrary.simpleMessage(
            "Do not copy, distribute, or use TripC\'s content without permission."),
        "text_restaurant": MessageLookupByLibrary.simpleMessage("Restaurant"),
        "text_restaurant_space":
            MessageLookupByLibrary.simpleMessage("Restaurant: "),
        "text_review": MessageLookupByLibrary.simpleMessage("Review"),
        "text_reviews": MessageLookupByLibrary.simpleMessage("Review"),
        "text_reviews_with_images":
            MessageLookupByLibrary.simpleMessage("Reviews with images"),
        "text_sale": MessageLookupByLibrary.simpleMessage("Sale"),
        "text_sale_refund_10":
            MessageLookupByLibrary.simpleMessage("Tet sale 10% TCent refund"),
        "text_same_old_email": MessageLookupByLibrary.simpleMessage(
            "The new email address must be different from the current email address."),
        "text_save": MessageLookupByLibrary.simpleMessage("Save"),
        "text_save_qr_to_gallery":
            MessageLookupByLibrary.simpleMessage("Saved QR to your gallery"),
        "text_save_up_to": MessageLookupByLibrary.simpleMessage("Save up to"),
        "text_saved": MessageLookupByLibrary.simpleMessage("Saved"),
        "text_saved_tour": MessageLookupByLibrary.simpleMessage("Saved Tour"),
        "text_scan_qr": MessageLookupByLibrary.simpleMessage("Scan qr code"),
        "text_search": MessageLookupByLibrary.simpleMessage("Search"),
        "text_search_not_found": MessageLookupByLibrary.simpleMessage(
            "No results found.\nPlease try again."),
        "text_seat_type": MessageLookupByLibrary.simpleMessage("Seat type"),
        "text_security_activity_and_log":
            MessageLookupByLibrary.simpleMessage("4. Activity & Security Log"),
        "text_security_settings":
            MessageLookupByLibrary.simpleMessage("2. Security Settings"),
        "text_see": MessageLookupByLibrary.simpleMessage("See"),
        "text_see_all": MessageLookupByLibrary.simpleMessage("See all"),
        "text_see_more": MessageLookupByLibrary.simpleMessage("See more"),
        "text_select": MessageLookupByLibrary.simpleMessage("Select"),
        "text_select_a_nice_account_number":
            MessageLookupByLibrary.simpleMessage(
                "Select a nice account number"),
        "text_select_promotional_code":
            MessageLookupByLibrary.simpleMessage("Select promotional code"),
        "text_select_refund_reason":
            MessageLookupByLibrary.simpleMessage("Select refund reason"),
        "text_select_service_package":
            MessageLookupByLibrary.simpleMessage("Select time"),
        "text_send": MessageLookupByLibrary.simpleMessage("Send"),
        "text_send_confirmation_code":
            MessageLookupByLibrary.simpleMessage("Send Confirmation Code"),
        "text_send_otp": MessageLookupByLibrary.simpleMessage("Send OTP"),
        "text_send_otp_code":
            MessageLookupByLibrary.simpleMessage("Send OTP Code"),
        "text_sent_6_digit_code": MessageLookupByLibrary.simpleMessage(
            "We\'ve sent you a 6-digit code to your email"),
        "text_service": MessageLookupByLibrary.simpleMessage("Service"),
        "text_service_information":
            MessageLookupByLibrary.simpleMessage("Service information"),
        "text_service_name":
            MessageLookupByLibrary.simpleMessage("Serivce\'s name:"),
        "text_service_provision": MessageLookupByLibrary.simpleMessage(
            "Ensure the provision of services as described in the app."),
        "text_set_as_default":
            MessageLookupByLibrary.simpleMessage("Set as default"),
        "text_set_default_for_order":
            MessageLookupByLibrary.simpleMessage("Set as default for orders"),
        "text_setting": MessageLookupByLibrary.simpleMessage("Setting"),
        "text_showing_view_history_in_30_days":
            MessageLookupByLibrary.simpleMessage(
                "Showing viewing history for the last 30 days"),
        "text_shuttle_information": MessageLookupByLibrary.simpleMessage(
            "Shuttle Information (If applicable)"),
        "text_shuttle_service":
            MessageLookupByLibrary.simpleMessage("Shuttle service:"),
        "text_sign_in": MessageLookupByLibrary.simpleMessage("Sign in"),
        "text_sign_in_now":
            MessageLookupByLibrary.simpleMessage("Sign in now!"),
        "text_sign_in_or_sign_up":
            MessageLookupByLibrary.simpleMessage("Sign in/ Sign up"),
        "text_sign_up": MessageLookupByLibrary.simpleMessage("Sign up"),
        "text_sign_up_now": MessageLookupByLibrary.simpleMessage("Sign up now"),
        "text_sign_up_with_apple":
            MessageLookupByLibrary.simpleMessage("Sign Up With Apple"),
        "text_sign_up_with_facebook":
            MessageLookupByLibrary.simpleMessage("Sign Up With Facebook"),
        "text_sign_up_with_google":
            MessageLookupByLibrary.simpleMessage("Sign Up With Google"),
        "text_silver_rank": MessageLookupByLibrary.simpleMessage("Silver"),
        "text_skip": MessageLookupByLibrary.simpleMessage("Skip"),
        "text_sliver_tier": MessageLookupByLibrary.simpleMessage("Sliver Tier"),
        "text_social_or_public_disturbances":
            MessageLookupByLibrary.simpleMessage("Protests or social unrest"),
        "text_special": MessageLookupByLibrary.simpleMessage("Special:"),
        "text_special_requests":
            MessageLookupByLibrary.simpleMessage("Special Requests (If any)"),
        "text_state_your_reason":
            MessageLookupByLibrary.simpleMessage("State your reason."),
        "text_status": MessageLookupByLibrary.simpleMessage("Status"),
        "text_stay": MessageLookupByLibrary.simpleMessage("Stay"),
        "text_storage_permission_denied":
            MessageLookupByLibrary.simpleMessage("Storage permission denied"),
        "text_submit_request":
            MessageLookupByLibrary.simpleMessage("Submit request"),
        "text_success_order":
            MessageLookupByLibrary.simpleMessage("Order successfully!"),
        "text_success_payment":
            MessageLookupByLibrary.simpleMessage("Successful payment!"),
        "text_successfully_created_membership_card":
            MessageLookupByLibrary.simpleMessage(
                "Successfully created membership card"),
        "text_suggest_for_forgot_passcode":
            MessageLookupByLibrary.simpleMessage("Forgot passcode?"),
        "text_suggested_keywords":
            MessageLookupByLibrary.simpleMessage("Suggested keywords"),
        "text_super_cheap_combo":
            MessageLookupByLibrary.simpleMessage("Super cheap combo"),
        "text_super_cheap_tour":
            MessageLookupByLibrary.simpleMessage("Super cheap tour"),
        "text_supplier_response":
            MessageLookupByLibrary.simpleMessage("Supplier response"),
        "text_support_during_booking": MessageLookupByLibrary.simpleMessage(
            "Provide support to customers during the booking process and resolve any issues that arise."),
        "text_surcharge_included":
            MessageLookupByLibrary.simpleMessage("Surcharge included"),
        "text_tax_code": MessageLookupByLibrary.simpleMessage("Tax code"),
        "text_taxi": MessageLookupByLibrary.simpleMessage("Taxi"),
        "text_tcent": MessageLookupByLibrary.simpleMessage("Tcent"),
        "text_tcent_hint":
            MessageLookupByLibrary.simpleMessage("Use TCent to save more"),
        "text_tcent_point_method":
            MessageLookupByLibrary.simpleMessage("TCent point"),
        "text_term_and_condition": MessageLookupByLibrary.simpleMessage(
            "Terms and Conditions of Using TripC App"),
        "text_term_and_condition_title":
            MessageLookupByLibrary.simpleMessage("Terms & Conditions"),
        "text_terms_change":
            MessageLookupByLibrary.simpleMessage("7. Terms Modification"),
        "text_terms_conditions":
            MessageLookupByLibrary.simpleMessage("Terms & Conditions"),
        "text_terms_modification": MessageLookupByLibrary.simpleMessage(
            "TripC reserves the right to modify or update the terms without prior notice."),
        "text_terms_of_use":
            MessageLookupByLibrary.simpleMessage("Terms of Use"),
        "text_terms_update_notice": MessageLookupByLibrary.simpleMessage(
            "Continuing to use the service after terms changes means you agree to the updated terms."),
        "text_ticket_change_is_not_required":
            MessageLookupByLibrary.simpleMessage(
                "Ticket Change is Not Required"),
        "text_ticket_id": MessageLookupByLibrary.simpleMessage("Ticket ID: "),
        "text_ticket_id_no":
            MessageLookupByLibrary.simpleMessage("Ticket ID No "),
        "text_ticket_info":
            MessageLookupByLibrary.simpleMessage("Ticket Information"),
        "text_ticket_plane":
            MessageLookupByLibrary.simpleMessage("Plane Ticket"),
        "text_time": MessageLookupByLibrary.simpleMessage("Time"),
        "text_time_frame": MessageLookupByLibrary.simpleMessage("Time frame"),
        "text_to_be_supported_quick":
            MessageLookupByLibrary.simpleMessage(" for the quickest support!"),
        "text_today": MessageLookupByLibrary.simpleMessage("Today"),
        "text_top_search": MessageLookupByLibrary.simpleMessage("Top search"),
        "text_total_payment":
            MessageLookupByLibrary.simpleMessage("Total payment"),
        "text_total_price": MessageLookupByLibrary.simpleMessage("Total price"),
        "text_total_refunded_amount":
            MessageLookupByLibrary.simpleMessage("Total refunded amount"),
        "text_tour_cancellation": MessageLookupByLibrary.simpleMessage(
            "3. Tour Cancellation & Refund"),
        "text_tour_guide": MessageLookupByLibrary.simpleMessage("Tour guide: "),
        "text_tour_price": MessageLookupByLibrary.simpleMessage(
            "The tour price displayed in the app may vary depending on promotional programs and time conditions."),
        "text_tour_sightseeing":
            MessageLookupByLibrary.simpleMessage("Tour & SightSeeing"),
        "text_tour_ticket": MessageLookupByLibrary.simpleMessage("Tour ticket"),
        "text_tour_time": MessageLookupByLibrary.simpleMessage("Tour time: "),
        "text_train_ticket":
            MessageLookupByLibrary.simpleMessage("Train Ticket"),
        "text_transaction_code":
            MessageLookupByLibrary.simpleMessage("Transaction code"),
        "text_transportation_delays_or_accidents":
            MessageLookupByLibrary.simpleMessage(
                "Transportation delays or accidents"),
        "text_trip": MessageLookupByLibrary.simpleMessage("Trip"),
        "text_trip_review": MessageLookupByLibrary.simpleMessage("Trip Review"),
        "text_tripcID": MessageLookupByLibrary.simpleMessage("TripC ID"),
        "text_tripc_about":
            MessageLookupByLibrary.simpleMessage("Introduction to TripC AI"),
        "text_tripc_id_free":
            MessageLookupByLibrary.simpleMessage("TripC ID free"),
        "text_tripc_id_free_content": MessageLookupByLibrary.simpleMessage(
            "Completely free with full utilities and features in the TripC system"),
        "text_tripc_id_list":
            MessageLookupByLibrary.simpleMessage("TripC ID list"),
        "text_tripc_id_nice_number":
            MessageLookupByLibrary.simpleMessage("TripC ID nice number"),
        "text_tripc_id_nice_number_content": MessageLookupByLibrary.simpleMessage(
            "Choose a beautiful number now for instant fortune and good luck. Own it now!"),
        "text_tripc_id_setting":
            MessageLookupByLibrary.simpleMessage("TripC ID\'s setting"),
        "text_tripc_intro_description": MessageLookupByLibrary.simpleMessage(
            "TripC is a leading tour booking platform, providing you with an easy, quick, and cost-effective way to plan your trips. With thousands of tours within the country and internationally, TripC helps you explore beautiful destinations at great prices with quality services."),
        "text_tripc_intro_title": MessageLookupByLibrary.simpleMessage(
            "TripC AI – The Application for Convenient Tour Booking & Attractive Discounts!"),
        "text_tripc_responsibility": MessageLookupByLibrary.simpleMessage(
            "5. TripC\'s Rights & Responsibilities"),
        "text_tripc_term_and_policy":
            MessageLookupByLibrary.simpleMessage("Terms & Conditions"),
        "text_try_again": MessageLookupByLibrary.simpleMessage("Try again"),
        "text_try_times": MessageLookupByLibrary.simpleMessage("try times"),
        "text_tu_quy_number":
            MessageLookupByLibrary.simpleMessage("Tu quy number"),
        "text_tu_quy_number_meaning": MessageLookupByLibrary.simpleMessage(
            "Consists of 4 consecutive identical numbers (1111, 2222, 3333,...) representing solidity, sustainability and class."),
        "text_unauthorized_usage": MessageLookupByLibrary.simpleMessage(
            "Comply with TripC\'s rules and the terms of service provided by partners. Do not use the app for illegal, fraudulent, or disruptive purposes."),
        "text_understood": MessageLookupByLibrary.simpleMessage("Understood"),
        "text_unlink": MessageLookupByLibrary.simpleMessage("Unlink"),
        "text_upload_image_title":
            MessageLookupByLibrary.simpleMessage("Upload Image"),
        "text_upload_photos_and_reviews": MessageLookupByLibrary.simpleMessage(
            "Used to upload profile images and rate tours."),
        "text_use_now": MessageLookupByLibrary.simpleMessage("Use now"),
        "text_use_promo_code_issue": MessageLookupByLibrary.simpleMessage(
            "Issue with using the promo code"),
        "text_use_the_password_you_just_set":
            MessageLookupByLibrary.simpleMessage(
                "Use the password you just set for future accesses."),
        "text_user_information": MessageLookupByLibrary.simpleMessage(
            "Users are required to provide accurate information when registering for an account."),
        "text_user_rights": MessageLookupByLibrary.simpleMessage(
            "4. User Rights & Responsibilities"),
        "text_valid_referral_code": MessageLookupByLibrary.simpleMessage(
            "Valid referral code! The reward will be sent to you in your voucher warehouse upon completion."),
        "text_valid_verification_code_for_5_mins":
            MessageLookupByLibrary.simpleMessage(
                "*The verification code is valid for 5 minutes after you receive it."),
        "text_validity_period":
            MessageLookupByLibrary.simpleMessage("Validity period:"),
        "text_vat": MessageLookupByLibrary.simpleMessage("VAT (10%)"),
        "text_verify": MessageLookupByLibrary.simpleMessage("Verify"),
        "text_version": MessageLookupByLibrary.simpleMessage("Version"),
        "text_very_good": MessageLookupByLibrary.simpleMessage("Very good"),
        "text_vietnamese": MessageLookupByLibrary.simpleMessage("Vietnamese"),
        "text_vietqr_method": MessageLookupByLibrary.simpleMessage(
            "VietQR (TCent refund up to 10% value)"),
        "text_view_booked_ticket":
            MessageLookupByLibrary.simpleMessage("View booked tickets"),
        "text_view_booked_tours":
            MessageLookupByLibrary.simpleMessage("View placed order"),
        "text_view_booking_payment_cancellation_history":
            MessageLookupByLibrary.simpleMessage(
                "View the history of tour bookings, payments, and cancellations."),
        "text_view_more": MessageLookupByLibrary.simpleMessage("View more"),
        "text_view_privacy_changes": MessageLookupByLibrary.simpleMessage(
            "Review changes to privacy settings that you have made."),
        "text_view_recent_login": MessageLookupByLibrary.simpleMessage(
            "View recent login history, including time & device."),
        "text_waiting_for_completion":
            MessageLookupByLibrary.simpleMessage("Waiting for completion"),
        "text_waiting_for_payment":
            MessageLookupByLibrary.simpleMessage("Waiting for payment"),
        "text_waiting_for_refund":
            MessageLookupByLibrary.simpleMessage("Waiting for refund"),
        "text_waiting_payment":
            MessageLookupByLibrary.simpleMessage("Waiting for payment"),
        "text_waiting_receive":
            MessageLookupByLibrary.simpleMessage("Waiting for receipt"),
        "text_waiting_scan_qr": MessageLookupByLibrary.simpleMessage(
            "Waiting to scan payment QRCode"),
        "text_wanna_go_danang":
            MessageLookupByLibrary.simpleMessage("I want to go to Ha Long"),
        "text_warning_identify_email_link": MessageLookupByLibrary.simpleMessage(
            "Your account is now associated with an email address. If you want to change to a new email address, you will need to verify your identity"),
        "text_warning_lock_sign_in": m12,
        "text_warning_type_passcode": MessageLookupByLibrary.simpleMessage(
            "Passcode is incorrect. If you enter the wrong password more than 5 times your password will be locked for 24 hours."),
        "text_we_have_sent_code_to_email": MessageLookupByLibrary.simpleMessage(
            "We have sent the confirmation code to the address"),
        "text_wealthy_number":
            MessageLookupByLibrary.simpleMessage("Wealthy number"),
        "text_wealthy_number_meaning": MessageLookupByLibrary.simpleMessage(
            "Express wealth and prosperity with numbers that have good meaning\nFor example: 6886, 8386, 9999."),
        "text_welcome_message": MessageLookupByLibrary.simpleMessage(
            "Welcome to TripC – the leading tour booking app! Before using the service, please read the Terms and Conditions below. Using the app means you agree to these terms."),
        "text_welcome_to_tripC":
            MessageLookupByLibrary.simpleMessage("Welcome To TripC"),
        "text_why_choose_tripc":
            MessageLookupByLibrary.simpleMessage("Why choose TripC"),
        "text_wish_after_paid_combo": MessageLookupByLibrary.simpleMessage(
            "Wishing you a journey full of fun and safe experiences\nCombo has an expiration date so remember to use it.\nDon\'t forget, share those moments at TripC to receive incentives."),
        "text_wishing": MessageLookupByLibrary.simpleMessage(
            "Wishing you a journey full of interesting and safe experiences\nDon\'t forget, share those moments at TripC to receive incentives."),
        "text_wrong_otp": MessageLookupByLibrary.simpleMessage(
            "Wrong confirmation code. Please try again!"),
        "text_wrong_tripcId_and_pass_code": MessageLookupByLibrary.simpleMessage(
            "Invalid information. Please check your TripC ID and Passcode again."),
        "text_yes": MessageLookupByLibrary.simpleMessage("Yes"),
        "text_yesterday": MessageLookupByLibrary.simpleMessage("Yesterday"),
        "text_you_are_choosing":
            MessageLookupByLibrary.simpleMessage("You are choosing"),
        "text_you_are_having":
            MessageLookupByLibrary.simpleMessage("You are having: "),
        "text_you_have": MessageLookupByLibrary.simpleMessage("You have"),
        "text_you_have_not_set_a_passcode":
            MessageLookupByLibrary.simpleMessage(
                "You have not set a Passcode for TripC ID!"),
        "text_you_have_not_set_a_passcode_message":
            MessageLookupByLibrary.simpleMessage(
                "Without setting up a Passcode, you will not be able to use TripC ID to book tours, receive promotions and manage your account."),
        "text_you_have_promotional_code": m13,
        "text_you_have_scrolled_to_the_end":
            MessageLookupByLibrary.simpleMessage(
                "You have scrolled to the end."),
        "text_you_have_successfully_set_pass": MessageLookupByLibrary.simpleMessage(
            "You have successfully reset your Passcode. Use the reset Passcode for the next times."),
        "text_your_qr": MessageLookupByLibrary.simpleMessage("Your QR Code"),
        "text_your_reservation_code":
            MessageLookupByLibrary.simpleMessage("Your reservation code"),
        "text_your_ticket_list":
            MessageLookupByLibrary.simpleMessage("Your ticket list"),
        "text_zalo_pay_method": MessageLookupByLibrary.simpleMessage(
            "ZaloPay e-wallet (TCent refund up to 10% value)"),
        "ticket": MessageLookupByLibrary.simpleMessage("ticket"),
        "ticket_price": MessageLookupByLibrary.simpleMessage("Ticket Price"),
        "time": MessageLookupByLibrary.simpleMessage("Time"),
        "total_refund_amount":
            MessageLookupByLibrary.simpleMessage("Total refund amount"),
        "tour_and_experience":
            MessageLookupByLibrary.simpleMessage("Tour and experience"),
        "tuan_chau_cat_ba_ferry_route": MessageLookupByLibrary.simpleMessage(
            "Tuan Chau - Cat Ba ferry route"),
        "type_ticket": MessageLookupByLibrary.simpleMessage("Ticket type"),
        "use_the_offer": MessageLookupByLibrary.simpleMessage("Use the offer"),
        "view_order_history":
            MessageLookupByLibrary.simpleMessage("Review placed orders"),
        "withdraw_money":
            MessageLookupByLibrary.simpleMessage("Withdraw money"),
        "withdrawn_amount":
            MessageLookupByLibrary.simpleMessage("Withdrawn amount"),
        "work_time": MessageLookupByLibrary.simpleMessage("Work time"),
        "year_ago": m14,
        "you_having_x_discount_code_enjoy": m15
      };
}
