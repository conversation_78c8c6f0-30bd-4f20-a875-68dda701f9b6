enum PayOSCancleStatus {
  cancelled,
  notYetCancelled,
  unknown
}

PayOSCancleStatus getPayOSCancleStatus (String value){
  switch(value){
    case 'true':
      return PayOSCancleStatus.cancelled;
    case 'false':
      return PayOSCancleStatus.notYetCancelled;
    default:
      return PayOSCancleStatus.unknown;
  }
}

enum PayOSPaymentStatus {
  paid,
  pending,
  progressing,
  cancelled,
  unknown
}

PayOSPaymentStatus getPayOSPaymentStatus (String value){
  switch (value){
    case 'PAID':
      return PayOSPaymentStatus.paid;
    case 'PENDING':
      return PayOSPaymentStatus.pending;
    case 'PROGRESSING':
      return PayOSPaymentStatus.progressing;
    case 'CANCELLED':
      return PayOSPaymentStatus.cancelled;
    default:
      return PayOSPaymentStatus.unknown;
  }
}