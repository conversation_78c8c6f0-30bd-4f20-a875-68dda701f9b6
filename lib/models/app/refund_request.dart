class RefundRequest {
  final int? orderId;
  final List<RefundItem>? refundItems;
  final String? reason;

  RefundRequest({this.orderId, this.refundItems, this.reason});

  factory RefundRequest.fromJson(Map<String, dynamic> json) {
    return RefundRequest(
      orderId: json['order_id'],
      refundItems: (json['refund_items'] as List?)
          ?.map((item) => RefundItem.fromJson(item))
          .toList(),
      reason: json['reason'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'order_id': orderId,
      'refund_items': refundItems?.map((item) => item.toJson()).toList(),
      'reason': reason,
    };
  }
}

class RefundItem {
  final int? subProductId;
  final int? quantity;

  RefundItem({this.subProductId, this.quantity});

  factory RefundItem.fromJson(Map<String, dynamic> json) {
    return RefundItem(
      subProductId: json['sub_product_id'],
      quantity: json['quantity'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sub_product_id': subProductId,
      'quantity': quantity,
    };
  }
}