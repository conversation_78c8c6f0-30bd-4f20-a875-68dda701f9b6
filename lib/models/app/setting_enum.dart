import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum SettingEnum {
  language(1),
  currencyUnit(2),
  national(3),
  accountManagement(4),
  darkMode(5),
  termConditions(6),
  privacy(7),
  version(8),
  receiveFeedback(9),
  listFeedback(10);

  const SettingEnum(this.value);
  final int value;
  static SettingEnum? getByValue(int i) {
    try {
      return SettingEnum.values.firstWhere(
        (x) => x.value == i,
      );
    } catch (e) {
      return null;
    }
  }

  String title(BuildContext context) {
    switch (this) {
      case language:
        return context.strings.text_language;
      case currencyUnit:
        return context.strings.text_currency_unit;
      case national:
        return context.strings.text_national;
      case accountManagement:
        return context.strings.text_account_management;
      case darkMode:
        return context.strings.text_dark_mode;
      case termConditions:
        return context.strings.text_terms_conditions;
      case privacy:
        return context.strings.text_privacy;
      case version:
        return context.strings.text_version;
      case receiveFeedback:
        return context.strings.receive_feedback_social_organization;
      case listFeedback:
        return context.strings.list_feedback_social_organization;
    }
  }
}
