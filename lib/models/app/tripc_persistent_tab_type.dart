import '../../generated/l10n.dart';

enum TripCPersistentTabType {
  home,
  explore,
  mytrip,
  loyalty,
  profile;

  String alias() {
    switch (this) {
      case TripCPersistentTabType.home:
        return S.current.text_home;
      case TripCPersistentTabType.explore:
        return S.current.text_explore;
      case TripCPersistentTabType.mytrip:
        return S.current.text_my_trip;
      case TripCPersistentTabType.loyalty:
        return S.current.text_loyalty;
      case TripCPersistentTabType.profile:
        return S.current.text_profile;
    }
  }
}
