import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/utils/app_extension.dart';

class ServiceModel {
  ServiceModel(
      {this.image,
      required this.name,
      required this.nameTour,
      required this.rating,
      required this.score,
      required this.price,
      required this.salePrice,
      required this.discount,
      required this.tcent,
      required this.supplier,
      required this.departureLocation,
      required this.detailInfomation,
      required this.startTime,
      required this.endTime,
      required this.hotel,
      required this.restaurant,
      required this.guide,
      required this.aldultPrice,
      required this.childPrice,
      required this.noteInformation,
      required this.package,
      this.listInfomation = const [],
      this.notes = const [],
      this.specialInfomation = const [],
      this.type,
      this.startTimeCombo,
      this.endTimeCombo,
      this.location = ''});
  String? image;
  final String name;
  final String nameTour;
  final int rating;
  final double score;
  final int price;
  final int salePrice;
  final int discount;
  final int tcent;
  final String supplier;
  final String departureLocation;
  final String detailInfomation;
  final ServicePackage package;
  final String startTime;
  final String endTime;
  final String restaurant;
  final String hotel;
  final String guide;
  final int aldultPrice;
  final int childPrice;
  final List<String> noteInformation;
  final String location;
  final int? type;
  final List<String> listInfomation;
  final List<String> specialInfomation;
  final List<String> notes;
  final DateTime? startTimeCombo;
  final DateTime? endTimeCombo;

  ServiceModel copyWith(
      {String? image,
      String? name,
      String? nameTour,
      int? rating,
      double? score,
      int? price,
      int? salePrice,
      int? discount,
      int? tcent,
      String? supplier,
      String? departureLocation,
      String? detailInfomation,
      ServicePackage? package,
      String? startTime,
      String? endTime,
      String? restaurant,
      String? hotel,
      String? guide,
      int? aldultPrice,
      int? childPrice,
      List<String>? noteInformation,
      String? location,
      int? type,
      List<String>? listInfomation,
      List<String>? specialInfomation,
      DateTime? startTimeCombo,
      DateTime? endTimeCombo,
      List<String>? notes}) {
    return ServiceModel(
      image: image ?? this.image,
      name: name ?? this.name,
      nameTour: nameTour ?? this.nameTour,
      rating: rating ?? this.rating,
      score: score ?? this.score,
      price: price ?? this.price,
      salePrice: salePrice ?? this.salePrice,
      discount: discount ?? this.discount,
      tcent: tcent ?? this.tcent,
      supplier: supplier ?? this.supplier,
      departureLocation: departureLocation ?? this.departureLocation,
      detailInfomation: detailInfomation ?? this.detailInfomation,
      package: package ?? this.package,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      restaurant: restaurant ?? this.restaurant,
      hotel: hotel ?? this.hotel,
      guide: guide ?? this.guide,
      aldultPrice: aldultPrice ?? this.aldultPrice,
      childPrice: childPrice ?? this.childPrice,
      noteInformation: noteInformation ?? this.noteInformation,
      location: location ?? this.location,
      type: type ?? this.type,
      listInfomation: listInfomation ?? this.listInfomation,
      specialInfomation: specialInfomation ?? this.specialInfomation,
      notes: notes ?? this.notes,
      startTimeCombo: startTimeCombo ?? this.startTimeCombo,
      endTimeCombo: endTimeCombo ?? this.endTimeCombo,
    );
  }

  String get tourTime {
    return '$startTime - $endTime';
  }

  int get daysLeft {
    return endTimeCombo?.countDayLeft(DateTime.now()) ?? 0;
  }

  DateTime get rangeStart {
    final availableDates = package.availableDates;
    return availableDates.isNotEmpty ? availableDates.first : DateTime.now();
  }

  DateTime get rangeEnd {
    final availableDates = package.availableDates;
    return availableDates.isNotEmpty ? availableDates.last : DateTime.now();
  }

  String get applicationPeriod {
    return '${startTimeCombo?.hhmmAndDate} - ${endTimeCombo?.hhmmAndDate}';
  }

  TripCServiceCategory get serviceType {
    return TripCServiceCategory.getByValue(type ?? 1);
  }
}

class ServicePackage {
  ServicePackage(
      {required this.seatType,
      required this.availableDates,
      required this.days,
      required this.timeFrame});
  List<String> timeFrame;
  List<DateTime> availableDates;
  List<int> seatType;
  int days;
}
