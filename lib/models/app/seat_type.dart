import 'package:flutter/material.dart';

enum SeatType {
  standard(0),
  luxury(1),
  vip(2);

  const SeatType(this.value);
  final int value;
  static SeatType getByValue(int i) {
    return SeatType.values.firstWhere(
      (x) => x.value == i,
      orElse: () => standard,
    );
  }

  String title(BuildContext context) {
    switch (this) {
      case standard:
        return 'Ghế tiêu chuẩn';
      case luxury:
        return 'Ghế cao cấp';
      case vip:
        return 'Ghế Vip';
    }
  }
}
