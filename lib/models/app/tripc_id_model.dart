import 'package:tripc_app/models/app/tripc_tier.dart';

class TripcIdModel {
  TripcIdModel({
    required this.name,
    required this.id,
    required this.expired,
    required this.balance,
    required this.tier,
    required this.passcode,
  });
  final String name;
  final int id;
  final DateTime expired;
  final int balance;
  final int tier;
  final String passcode;

  TripcIdModel copyWith(
      {String? name,
      int? id,
      int? tier,
      DateTime? expired,
      int? balance,
      String? passcode}) {
    return TripcIdModel(
        name: name ?? this.name,
        tier: tier ?? this.tier,
        id: id ?? this.id,
        expired: expired ?? this.expired,
        balance: balance ?? this.balance,
        passcode: passcode ?? this.passcode);
  }

  TripcTierType get tierType {
    return TripcTierType.getByValue(tier);
  }
}
