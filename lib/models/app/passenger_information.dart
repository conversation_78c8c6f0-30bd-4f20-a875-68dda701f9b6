import 'package:tripc_app/models/app/passenger.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import '../remote/contact_response.dart';

class PassengerInfo {
  PassengerInfo(
      {this.name,
      this.email,
      this.phoneNumber,
      this.specialRequest,
      required this.passengers,
      this.shuttleInfo,
      this.contactResponse,
      this.attributes = const []});
  String? name;
  String? email;
  String? phoneNumber;
  String? specialRequest;
  List<Passenger> passengers;
  ShuttleInfo? shuttleInfo;
  ContactResponse? contactResponse;
  List<AttributeResponse> attributes;
  PassengerInfo copyWith(
      {String? name,
      String? email,
      String? phoneNumber,
      String? specialRequest,
      List<Passenger>? passengers,
      ShuttleInfo? shuttleInfo,
      ContactResponse? contactResponse,
      List<AttributeResponse>? attributes}) {
    return PassengerInfo(
        name: name ?? this.name,
        email: email ?? this.email,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        specialRequest: specialRequest ?? this.specialRequest,
        passengers: passengers ?? this.passengers,
        shuttleInfo: shuttleInfo ?? this.shuttleInfo,
        contactResponse: contactResponse ?? this.contactResponse,
        attributes: attributes ?? this.attributes);
  }
}

class ShuttleInfo {
  ShuttleInfo(
      {this.hotel,
        this.time,
        this.contact,
        this.expected});
  String? hotel;
  String? time;
  String? contact;
  String? expected;
  ShuttleInfo copyWith(
      {String? hotel,
        String? time,
        String? contact,
        String? expected}) {
    return ShuttleInfo(
        hotel: hotel ?? this.hotel,
        time: time ?? this.time,
        contact: contact ?? this.contact,
        expected: expected ?? this.expected);
  }
}
