class ContactRequest {
  ContactRequest({
    required this.fullname,
    required this.email,
    required this.phone,
    required this.isDefault,
  });
  final String email;
  final String phone;
  final String fullname;
  final int isDefault;

  ContactRequest copyWith({
    String? fullname,
    String? email,
    String? phone,
    int? isDefault,
  }) {
    return ContactRequest(
      fullname: fullname ?? this.fullname,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  Map<String, dynamic> toJson() => {
        'email': email,
        'phone': phone,
        'fullname': fullname,
        'is_default': isDefault,
      };
}
