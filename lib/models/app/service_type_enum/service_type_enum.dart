enum ParentServiceTypeEnum {
  tourTraiNghiem(
    slug: 'tour-trai-nghiem',
    name: 'Tour & Trải Nghiệm',
  ),
  vui<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
    slug: 'vui-choi-giai-tri',
    name: '<PERSON><PERSON> & G<PERSON>ả<PERSON>',
  ),
  luu<PERSON><PERSON>(
    slug: 'luu-tru',
    name: '<PERSON><PERSON><PERSON>',
  ),
  di<PERSON><PERSON><PERSON><PERSON>(
    slug: 'di-chuyen',
    name: '<PERSON> Chu<PERSON>',
  ),
  suc<PERSON><PERSON>LamDep(
    slug: 'suc-khoe-lam-dep',
    name: 'Sức khoẻ & Làm đẹp',
  ),
  giai<PERSON><PERSON>(
    slug: 'giai-tri',
    name: 'Gi<PERSON><PERSON> trí',
  ),
  the<PERSON><PERSON>(
    slug: 'golf-the-thao',
    name: 'Golf & Thể thao',
  ),
  mua<PERSON><PERSON>(
    slug: 'mua-sam',
    name: '<PERSON><PERSON> sắm',
  ),
  <PERSON><PERSON><PERSON>(
    slug: 'su-kien',
    name: '<PERSON><PERSON> kiện',
  );

  final String slug;
  final String name;

  const ParentServiceTypeEnum({
    required this.slug,
    required this.name,
  });
}
