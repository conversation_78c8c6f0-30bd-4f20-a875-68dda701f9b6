import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum TripcGoodNumberType {
  prosper(1),
  tuquy(2),
  wealthy(3),
  greatpeace(4);

  const TripcGoodNumberType(this.value);
  final int value;
  static TripcGoodNumberType? getByValue(int i) {
    try {
      return TripcGoodNumberType.values.firstWhere(
        (x) => x.value == i,
      );
    } catch (e) {
      return null;
    }
  }

  String title(BuildContext context) {
    switch (this) {
      case TripcGoodNumberType.prosper:
        return context.strings.text_prosper_number;
      case TripcGoodNumberType.tuquy:
        return context.strings.text_tu_quy_number;
      case TripcGoodNumberType.wealthy:
        return context.strings.text_wealthy_number;
      case TripcGoodNumberType.greatpeace:
        return context.strings.text_great_peace_number;
    }
  }

  String meaning(BuildContext context) {
    switch (this) {
      case TripcGoodNumberType.prosper:
        return context.strings.text_proposer_number_meaning;
      case TripcGoodNumberType.tuquy:
        return context.strings.text_tu_quy_number_meaning;
      case TripcGoodNumberType.wealthy:
        return context.strings.text_wealthy_number_meaning;
      case TripcGoodNumberType.greatpeace:
        return context.strings.text_great_peace_number_meaning;
    }
  }
}

class TripcGoodNumber {
  TripcGoodNumber({
    required this.type,
    required this.number,
    required this.price,
    required this.tcent,
  });
  final int type;
  final int number;
  final int price;
  final int tcent;

  int get vat => (price * 10 / 100).toInt();
  int get total => (price * 110 / 100).toInt();

  TripcGoodNumberType? get numberType {
    return TripcGoodNumberType.getByValue(type);
  }

  String receiveTcentText(BuildContext context) {
    return '${context.strings.text_receive_now} ${tcent.tcent}';
  }
}
