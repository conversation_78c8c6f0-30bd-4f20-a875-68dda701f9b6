import 'package:tripc_app/pages/ticket_tour/providers/providers.dart';

class PaymentRequest {
  final int? productId;
  final List<OrderItem>? order;
  final String? departureTime;
  final String? description;
  final ShuttleService? shuttleService;
  final InvoiceRequest? invoice;
  final List<AttributeRequest> attributes;
  final String? time;
  final String? date;

  PaymentRequest({
    this.productId,
    this.order,
    this.departureTime,
    this.description,
    this.shuttleService,
    this.invoice,
    this.attributes = const [],
    this.time,
    this.date,
  });

  PaymentRequest copyWith({
    int? productId,
    List<OrderItem>? order,
    String? departureTime,
    String? description,
    ShuttleService? shuttleService,
    InvoiceRequest? invoice,
    List<AttributeRequest>? attributes,
    String? time,
    String? date,
  }) {
    return PaymentRequest(
      productId: productId ?? this.productId,
      order: order ?? this.order,
      departureTime: departureTime ?? this.departureTime,
      description: description ?? this.description,
      shuttleService: shuttleService ?? this.shuttleService,
      invoice: invoice ?? this.invoice,
      attributes: attributes ?? this.attributes,
      time: time ?? this.time,
      date: date ?? this.date,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "product_id": productId,
      "order": order?.map((e) => e.toJson()).toList(),
      if (departureTime != null) "departure_time": departureTime,
      "description": description,
      if (!(shuttleService?.isAllFieldEmpty ?? true)) "shuttle_service": shuttleService?.toJson(),
      if (invoice != null) 'invoice': invoice?.toJson(),
      if (attributes.isNotEmpty) 'attributes': attributes.map((e) => e.toJson()).toList(),
      'date': date,
      'time': time,
    };
  }
}

class OrderItem {
  final int? subProductId;
  final int? quantity;
  final List<Customer>? customer;

  OrderItem({this.subProductId, this.quantity, this.customer});

  OrderItem copyWith({
    int? subProductId,
    int? quantity,
    List<Customer>? customer,
  }) {
    return OrderItem(
      subProductId: subProductId ?? this.subProductId,
      quantity: quantity ?? this.quantity,
      customer: customer ?? this.customer,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "sub_product_id": subProductId,
      "quantity": quantity,
      "customer": customer?.map((e) => e.toJson()).toList(),
    };
  }
}

class Customer {
  final String? fullname;
  final String? phoneNumber;
  final String? email;
  final String? guardianName;
  final TicketType type;

  Customer({this.fullname, this.phoneNumber, this.email, this.guardianName,this.type = TicketType.aldult});

  Customer copyWith({
    String? fullname,
    String? phoneNumber,
    String? email,
    String? guardianName,
    TicketType? type,
  }) {
    return Customer(
      fullname: fullname ?? this.fullname,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      guardianName: guardianName ?? this.guardianName,
      type: type ?? this.type,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "fullname": fullname,
      "phone_number": phoneNumber,
      if (email?.isNotEmpty ?? false) "email": email,
      if (guardianName?.isNotEmpty ?? false) "child_guardian_name": guardianName,
    };
  }
}

class ShuttleService {
  final String? pickupLocation;
  final String? pickupTime;
  final String? contact;
  final String? expectedSchedule;

  ShuttleService({this.pickupLocation, this.pickupTime, this.contact, this.expectedSchedule});

  bool get isAllFieldEmpty {
    return (contact?.isEmpty ?? true) && (pickupLocation?.isEmpty ?? true)
    && (pickupTime?.isEmpty ?? true) && (expectedSchedule?.isEmpty ?? true);
  }

  ShuttleService copyWith({
    String? pickupLocation,
    String? pickupTime,
    String? contact,
    String? expectedSchedule,
  }) {
    return ShuttleService(
      pickupLocation: pickupLocation ?? this.pickupLocation,
      pickupTime: pickupTime ?? this.pickupTime,
      contact: contact ?? this.contact,
      expectedSchedule: expectedSchedule ?? this.expectedSchedule,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (pickupLocation?.isNotEmpty ?? false) "pickup_location": pickupLocation,
      if (pickupTime?.isNotEmpty ?? false) "pickup_time": pickupTime,
      if (contact?.isNotEmpty ?? false) "contact": contact,
      if (expectedSchedule?.isNotEmpty ?? false) "expected_schedule": expectedSchedule,
    };
  }
}

class InvoiceRequest {
  final String? type; // "business" hoặc "individual"
  final String? name;
  final String? email;
  final String? address;
  final String? taxCode;
  final String? phoneNo;
  InvoiceRequest({
    this.type,
    this.name,
    this.email,
    this.address,
    this.taxCode,
    this.phoneNo
  });

  factory InvoiceRequest.fromJson(Map<String, dynamic> json) {
    return InvoiceRequest(
      type: json['type'],
      name: json['name'],
      email: json['email'],
      address: json['address'],
      taxCode: json['tax_code'],
      phoneNo: json['phone_no']
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'name': name,
      'email': email,
      'address': address,
      if (phoneNo?.isNotEmpty ?? false) 'phone_no': phoneNo,
      if (taxCode?.isNotEmpty ?? false) 'tax_code': taxCode,
    };
  }
}

class AttributeRequest {
  final int? id;
  final int? valueId;
  AttributeRequest({
    this.id,
    this.valueId,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'value_id': valueId,
    };
  }
}
