class UpdateUserRequest {
  UpdateUserRequest(
      {this.fullname,
      this.gender,
      this.themeMode,
      this.city,
      this.language,
      this.currency,
      this.national,
      this.phone,
      this.avatarUrl});
  final String? fullname;
  final int? gender;
  final String? themeMode;
  final String? city;
  final String? language;
  final String? currency;
  final String? national;
  final String? phone;
  final String? avatarUrl;

  UpdateUserRequest copyWith(
      {String? fullname,
      int? gender,
      String? themeMode,
      String? city,
      String? language,
      String? currency,
      String? national,
      String? phone,
      String? avatarUrl}) {
    return UpdateUserRequest(
        fullname: fullname ?? this.fullname,
        gender: gender ?? this.gender,
        themeMode: themeMode ?? this.themeMode,
        city: city ?? this.city,
        language: language ?? this.language,
        currency: currency ?? this.currency,
        national: national ?? this.national,
        phone: phone ?? this.phone,
        avatarUrl: avatarUrl ?? this.avatarUrl);
  }

  Map<String, dynamic> toJson() {
    return {
      if (fullname != null) 'fullname': fullname,
      if (gender != null) 'gender': gender,
      if (themeMode != null) 'theme_mode': themeMode,
      if (city != null) 'city': city,
      if (language != null) 'language': language,
      if (currency != null) 'currency': currency,
      if (national != null) 'national': national,
      if (phone != null) 'phone': phone,
      if (avatarUrl != null) 'avatar_url': avatarUrl,
    };
  }
}
