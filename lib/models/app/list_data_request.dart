class ListDataRequest {
  ListDataRequest({this.q, this.page, this.pageSize, this.keyWord, this.sku, this.categoryId, this.ferrySlug});
  final String? q;
  final int? page;
  final int? pageSize;
  final String? keyWord;
  final String? sku;
  final int? categoryId;
  final String? ferrySlug;

  ListDataRequest copyWith(
      {String? q, int? page, int? pageSize, String? keyWord, String? sku, int? categoryId, String? ferrySlug}) {
    return ListDataRequest(
        q: q ?? this.q,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize,
        keyWord: keyWord ?? this.keyWord,
        sku: sku ?? this.sku,
        categoryId: categoryId ?? this.categoryId,
        ferrySlug: ferrySlug ?? this.ferrySlug
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (q != null) 'q': q,
      if (page != null) 'page': page,
      if (pageSize != null) 'page_size': pageSize,
      if (keyWord != null) 'keyword': keyWord,
      if (sku != null) 'sku': sku,
      if (categoryId != null) 'categories': categoryId,
      if (ferrySlug != null) 'ferry_route_slug': ferrySlug
    };
  }
}
