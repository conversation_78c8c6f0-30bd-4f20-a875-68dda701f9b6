import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum TourType {
  dealsAroundHere(1),
  hotTetHoliday(2),
  favoriteCombo(3),
  superCheapCombo(4),
  buyMoreEarnMore(5),
  tourSaved(6),
  favoriteTour(7),
  superCheapTour(8);

  const TourType(this.value);
  final int value;
  static TourType getByValue(int i) {
    return TourType.values.firstWhere(
      (x) => x.value == i,
      orElse: () => TourType.dealsAroundHere,
    );
  }

  String title(BuildContext context) {
    switch (this) {
      case dealsAroundHere:
        return context.strings.suggest_for_you;
      case hotTetHoliday:
        return context.strings.text_hot_tour_during_tet_holiday;
      case favoriteCombo:
        return context.strings.text_favorite_combo;
      case superCheapCombo:
        return context.strings.text_super_cheap_combo;
      case buyMoreEarnMore:
        return context.strings.text_buy_more_get_rewards;
      case tourSaved:
        return context.strings.text_profile_tour_saved;
      case favoriteTour:
        return context.strings.text_popular_tour;
      case superCheapTour:
        return context.strings.text_super_cheap_tour;
    }
  }
}
