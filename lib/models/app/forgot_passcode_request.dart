class ForgotPasscodeRequest {
  ForgotPasscodeRequest({
    required this.membershipId,
    required this.email,
  });
  final int membershipId;
  final String email;

  ForgotPasscodeRequest copyWith({int? membershipId, String? email}) {
    return ForgotPasscodeRequest(
      membershipId: membershipId ?? this.membershipId,
      email: email ?? this.email,
    );
  }

  Map<String, dynamic> toJson() {
    return {'membership_id': membershipId, 'email': email};
  }
}
