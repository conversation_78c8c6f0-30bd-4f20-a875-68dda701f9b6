enum TripCLoginSns {
  google(1),
  facebook(2),
  apple(3);

  const TripCLoginSns(this.value);
  final int value;
  static TripCLoginSns getByValue(int i) {
    return TripCLoginSns.values.firstWhere(
      (x) => x.value == i,
      orElse: () => TripCLoginSns.google,
    );
  }

  String get providerValue {
    switch (this) {
      case google:
        return 'google';
      case facebook:
        return 'facebook';
      case apple:
        return 'apple';
    }
  }
}
