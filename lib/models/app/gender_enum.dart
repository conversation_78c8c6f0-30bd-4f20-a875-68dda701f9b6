import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum GenderEnum {
  male(1),
  female(2),
  other(3);

  const GenderEnum(this.value);
  final int value;
  static GenderEnum getByValue(int i) {
    return GenderEnum.values.firstWhere(
      (x) => x.value == i,
      orElse: () => GenderEnum.other,
    );
  }

  String title(BuildContext context) {
    switch (this) {
      case male:
        return context.strings.text_male;
      case female:
        return context.strings.text_female;
      case other:
        return context.strings.text_other_gender;
    }
  }
}
