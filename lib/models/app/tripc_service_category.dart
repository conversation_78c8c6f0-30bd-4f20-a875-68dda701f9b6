import 'package:flutter/material.dart';
import 'package:tripc_app/models/remote/api_tour_response/service_type_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_constants.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'tour_type_enum.dart';

enum TripCServiceCategory {
  combo(11),
  hotel(12),
  food(1),
  entertainment(2),
  healthAndBeauty(3),
  event(4),
  stay(5),
  tourSightSeeing(6),
  haveFun(7),
  golfAndSport(8),
  shopping(9),
  moving(10);

  const TripCServiceCategory(this.value);

  final int value;

  static TripCServiceCategory getByValue(int i) {
    return TripCServiceCategory.values.firstWhere((x) => x.value == i,
        orElse: () => TripCServiceCategory.tourSightSeeing);
  }

  // static List<TripcServiceCategory> firstRowCategories() {
  //   if (globalReleaseStatusNotifier.isDisplayAll) {
  //     return [
  //       combo,
  //       tourSightSeeing,
  //       entertainment,
  //       hotel,
  //     ];
  //   } else {
  //     return [
  //       tourSightSeeing,
  //       entertainment,
  //       moving,
  //     ];
  //   }
  // }
  //
  // static List<TripcServiceCategory> secondRowCategories() {
  //   return [flightTicket, moving, restaurant, other];
  // }

  String name(BuildContext context) {
    switch (this) {
      // case TripcServiceCategory.combo:
      //   return context.strings.text_promotion_combo;
      //   case TripcServiceCategory.hotel:
      //     return context.strings.text_stay;
      case TripCServiceCategory.food:
        return context.strings.food;
      case TripCServiceCategory.entertainment:
        return context.strings.entertainment;
      case TripCServiceCategory.healthAndBeauty:
        return context.strings.health_beauty_category;
      case TripCServiceCategory.event:
        return context.strings.event;
      case TripCServiceCategory.stay:
        return context.strings.text_stay.toSentenceCase();
      case TripCServiceCategory.tourSightSeeing:
        return context.strings.tour_and_experience;
      case TripCServiceCategory.haveFun:
        return context.strings.have_fun;
      case TripCServiceCategory.golfAndSport:
        return context.strings.gold_and_sport;
      case TripCServiceCategory.shopping:
        return context.strings.shopping;
      case TripCServiceCategory.moving:
        return context.strings.text_moving;
      case TripCServiceCategory.combo:
        // TODO: Handle this case.
        throw UnimplementedError();
      case TripCServiceCategory.hotel:
        // TODO: Handle this case.
        throw UnimplementedError();
    }
  }

  AppAssetBuilder get icon {
    switch (this) {
      // case TripcServiceCategory.combo:
      //   return AppAssets.origin().icVoucher;
      case TripCServiceCategory.food:
        return AppAssets.origin().iconFoodCategory;
      case TripCServiceCategory.entertainment:
        return AppAssets.origin().iconEntertainmentCategory;
      case TripCServiceCategory.healthAndBeauty:
        return AppAssets.origin().iconHealthBeautyCategory;
      case TripCServiceCategory.event:
        return AppAssets.origin().iconEventCategory;
      case TripCServiceCategory.stay:
        return AppAssets.origin().iconStayCategory;
      case TripCServiceCategory.tourSightSeeing:
        return AppAssets.origin().iconTourExperiencesCategory;
      case TripCServiceCategory.haveFun:
        return AppAssets.origin().iconHaveFunCategory;
      case TripCServiceCategory.golfAndSport:
        return AppAssets.origin().iconGolfSportCategory;
      case TripCServiceCategory.shopping:
        return AppAssets.origin().iconShoppingCategory;
      case TripCServiceCategory.moving:
        return AppAssets.origin().iconMovingCategory;
      case TripCServiceCategory.combo:
        // TODO: Handle this case.
        throw UnimplementedError();
      case TripCServiceCategory.hotel:
        // TODO: Handle this case.
        throw UnimplementedError();
    }
  }

  Widget get banner {
    switch (this){
      case TripCServiceCategory.food:
        return AppAssets.origin().imBookingCuisine.widget();
      case TripCServiceCategory.healthAndBeauty:
        return AppAssets.origin().imBookingSpa.widget();
      case TripCServiceCategory.entertainment:
        return AppAssets.origin().imBookingKaraoke.widget();
      default: 
        return AppAssets.origin().imBookingCuisine.widget();
    }
  }

  String get serviceTypeSlug {
    return switch (this) {
      TripCServiceCategory.food => AppConstants.slugFood,
      TripCServiceCategory.entertainment => AppConstants.slugEntertainment,
      TripCServiceCategory.healthAndBeauty => AppConstants.slugHealthAndBeauty,
      TripCServiceCategory.event => AppConstants.slugEvent,
      TripCServiceCategory.stay => AppConstants.slugStay,
      TripCServiceCategory.tourSightSeeing => AppConstants.slugTourSightSeeing,
      TripCServiceCategory.haveFun => AppConstants.slugHaveFun,
      TripCServiceCategory.golfAndSport => AppConstants.slugGolfAndSport,
      TripCServiceCategory.shopping => AppConstants.slugShopping,
      TripCServiceCategory.moving => AppConstants.slugMoving,
      TripCServiceCategory.hotel => AppConstants.slugHotel,
      TripCServiceCategory.combo => AppConstants.slugCombo,
    };
  }

  int? getServiceTypeId(List<ServiceType> allServiceTypes) {
    if (serviceTypeSlug.isEmpty) return null;

    int? search(List<ServiceType> list) {
      for (final svc in list) {
        if (svc.slug == serviceTypeSlug) {
          return svc.id;
        }
        if (svc.children != null && svc.children!.isNotEmpty) {
          final foundInChild = search(svc.children!);
          if (foundInChild != null) return foundInChild;
        }
      }
      return null;
    }

    return search(allServiceTypes);
  }

  void onTap(BuildContext context) {
    switch (this) {
      // case TripcServiceCategory.combo:
      //   AppRoute.pushNamed(context,
      //       routeName: AppRoute.routeSearchTicket,
      //       arguments: TripcServiceCategory.combo);
      //   break;
      case TripCServiceCategory.food:
        AppRoute.pushNamed(context,
            routeName: AppRoute.routeBooking,
            arguments: TripCServiceCategory.food);
        break;
      case TripCServiceCategory.entertainment:
        AppRoute.pushNamed(context,
            routeName: AppRoute.routeBooking,
            arguments: TripCServiceCategory.entertainment);
        break;
      case TripCServiceCategory.healthAndBeauty:
        AppRoute.pushNamed(context,
            routeName: AppRoute.routeBooking,
            arguments: TripCServiceCategory.healthAndBeauty);
        break;
      case TripCServiceCategory.tourSightSeeing:
        AppRoute.pushNamed(context,
            routeName: AppRoute.routeSearchTicket,
            arguments: TripCServiceCategory.tourSightSeeing);
        break;
      case TripCServiceCategory.haveFun:
        AppRoute.pushNamed(context,
            routeName: AppRoute.routeEntertainment,
            arguments: TripCServiceCategory.haveFun);
        break;
      case TripCServiceCategory.moving:
        AppRoute.pushNamed(context,
            routeName: AppRoute.routeEntertainment,
            arguments: TripCServiceCategory.moving);
        break;
      default:
        dialogHelpers.show(context,
            child: TripcDialog(
              onTap: () => Navigator.of(context, rootNavigator: true).pop(),
              icon: AppAssets.origin()
                  .imCommingSoon
                  .widget(height: 179.H, fit: BoxFit.cover),
              title: context.strings.text_comming_soon_text,
              titleButton: context.strings.text_understood,
              contentPadding:
                  EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
            ));
    }
  }

  String getSuperCheapTitle(BuildContext context) {
    switch (this) {
      // case TripcServiceCategory.combo:
      //   return context.strings.text_super_cheap_combo;
      case TripCServiceCategory.tourSightSeeing:
        return context.strings.text_super_cheap_tour;
      default:
        return '';
    }
  }

  String getFavouriteTitle(BuildContext context) {
    switch (this) {
      // case TripcServiceCategory.combo:
      //   return context.strings.text_favorite_combo;
      case TripCServiceCategory.tourSightSeeing:
        return context.strings.text_popular_tour;
      default:
        return '';
    }
  }

  TourType getSuperCheapTourType(BuildContext context) {
    switch (this) {
      // case TripcServiceCategory.combo:
      //   return TourType.superCheapCombo;
      case TripCServiceCategory.tourSightSeeing:
        return TourType.superCheapTour;
      default:
        return TourType.superCheapCombo;
    }
  }

  TourType getFavouriteTourType(BuildContext context) {
    switch (this) {
      // case TripcServiceCategory.combo:
      //   return TourType.favoriteCombo;
      case TripCServiceCategory.tourSightSeeing:
        return TourType.favoriteTour;
      default:
        return TourType.favoriteCombo;
    }
  }
}
