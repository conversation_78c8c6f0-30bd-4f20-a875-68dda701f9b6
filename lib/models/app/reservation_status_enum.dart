import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum ReservationOrderStatus {
  success,
  failed;

  static ReservationOrderStatus getByValue(String i) {
    switch (i) {
      case 'success':
        return ReservationOrderStatus.success;
      case 'failed':
        return ReservationOrderStatus.failed;
      default:
        return ReservationOrderStatus.success;
    }
  }

  String statusText(BuildContext context) {
    switch (this) {
      case ReservationOrderStatus.success:
        return context.strings.text_reservation_success;
      case ReservationOrderStatus.failed:
        return context.strings.text_reservation_failure;
    }
  }
}