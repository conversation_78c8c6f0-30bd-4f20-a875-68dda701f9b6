import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/status_display_notifier.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../pages/tabbar/app_tabbar.dart';
import '../../services/providers/providers.dart';
import '../../widgets/commons/app_dialog/tripc_dialog.dart';
import '../../widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'tripc_persistent_tab_type.dart';

enum TripcProfileServiceCategory {
  tourBooked(1),
  tourSaved(2),
  wallet(3),
  moment(4),
  recent(5),
  contact(6),
  gift(7),
  support(8),
  info(9),
  pravicy(10),
  review(11);

  const TripcProfileServiceCategory(this.value);
  final int value;
  static TripcProfileServiceCategory getByValue(int i) {
    return TripcProfileServiceCategory.values.firstWhere((x) => x.value == i,
        orElse: () => TripcProfileServiceCategory.tourBooked);
  }

  String name(BuildContext context) {
    switch (this) {
      case TripcProfileServiceCategory.tourBooked:
        return context.strings.text_profile_tour_booked;
      case TripcProfileServiceCategory.tourSaved:
        return context.strings.text_profile_tour_saved;
      case TripcProfileServiceCategory.wallet:
        return context.strings.text_profile_wallet;
      case TripcProfileServiceCategory.moment:
        return context.strings.text_profile_moment;
      case TripcProfileServiceCategory.recent:
        return context.strings.text_profile_recent;
      case TripcProfileServiceCategory.contact:
        return context.strings.text_profile_contact;
      case TripcProfileServiceCategory.gift:
        return context.strings.text_profile_gift;
      case TripcProfileServiceCategory.support:
        return context.strings.text_profile_support;
      case TripcProfileServiceCategory.info:
        return context.strings.text_profile_info;
      case TripcProfileServiceCategory.review:
        return context.strings.text_profile_review;
      case TripcProfileServiceCategory.pravicy:
        return context.strings.text_profile_pravicy;
    }
  }

  AppAssetBuilder get icon {
    switch (this) {
      case TripcProfileServiceCategory.tourBooked:
        return AppAssets.origin().icProfileTourBooked;
      case TripcProfileServiceCategory.tourSaved:
        return AppAssets.origin().icProfileTourSaved;
      case TripcProfileServiceCategory.wallet:
        return AppAssets.origin().icProfileWallet;
      case TripcProfileServiceCategory.moment:
        return AppAssets.origin().icProfileMoment;
      case TripcProfileServiceCategory.recent:
        return AppAssets.origin().icProfileRecent;
      case TripcProfileServiceCategory.contact:
        return AppAssets.origin().icProfileContact;
      case TripcProfileServiceCategory.gift:
        return AppAssets.origin().icProfileGift;
      case TripcProfileServiceCategory.support:
        return AppAssets.origin().icProfileSupport;
      case TripcProfileServiceCategory.info:
        return AppAssets.origin().icProfileInfo;
      case TripcProfileServiceCategory.review:
        return AppAssets.origin().icProfileReview;
      case TripcProfileServiceCategory.pravicy:
        return AppAssets.origin().icProfilePravicy;
    }
  }

  void onTap(BuildContext context, WidgetRef ref) {
    final isDisplayAll = globalReleaseStatusNotifier.isDisplayAll;
    switch (this) {
      case TripcProfileServiceCategory.tourBooked:
        ref
            .read(pAppBottomNavProvider.notifier)
            .setTab(isDisplayAll ? TripCPersistentTabType.mytrip : TripCPersistentTabType.explore);
        break;
      case TripcProfileServiceCategory.tourSaved:
        AppRoute.pushNamed(context, routeName: AppRoute.routeSavedTour);
        break;
      case TripcProfileServiceCategory.wallet:
        AppRoute.pushNamed(context, routeName: AppRoute.routeListTripcID);
        break;
      case TripcProfileServiceCategory.moment:
        AppRoute.pushNamed(context, routeName: AppRoute.routeRating);
        break;
      case TripcProfileServiceCategory.recent:
        AppRoute.pushNamed(context, routeName: AppRoute.routeRecentlyViewed);
        break;
      case TripcProfileServiceCategory.contact:
        AppRoute.pushNamed(context, routeName: AppRoute.routeListContact);
        break;
      case TripcProfileServiceCategory.gift:
        dialogHelpers.show(context,
            child: TripcDialog(
              onTap: () => Navigator.of(context, rootNavigator: true).pop(),
              icon: AppAssets.origin()
                  .imCommingSoon
                  .widget(height: 179.H, fit: BoxFit.cover),
              title: context.strings.text_comming_soon_text,
              titleButton: context.strings.text_understood,
              contentPadding:
                  EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
            ));
        break;
      case TripcProfileServiceCategory.support:
        dialogHelpers.show(context,
            child: TripcDialog(
              title: context.strings.text_contact_tripc,
              onTap: () => Navigator.of(context, rootNavigator: true).pop(),
              icon: AppAssets.init.imageContactTripC.widget(
                height: 40.H,
                width: 40.H,
              ),
              titleButton: context.strings.text_close,
              contentPadding: EdgeInsets.symmetric(horizontal: 24.W)
                  .copyWith(top: 24.H, bottom: 27.H),
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 16.H),
                child: TripcRichText(
                  text: '',
                  children: [
                    TextSpan(
                        text: context.strings.text_contact_message_1,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w300,
                          color: Colors.black,
                        )),
                    TextSpan(
                        text: context.strings.text_contact_tripc_email,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w300,
                          color: AppAssets.init.darkBlue5FF,
                        )),
                    TextSpan(
                        text: context.strings.text_contact_message_2,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w300,
                          color: Colors.black,
                        ))
                  ],
                ),
              ),
            ));
        break;
      case TripcProfileServiceCategory.info:
        AppRoute.pushNamed(context,
            routeName: AppRoute.routeProfileIntroduceTripcAI);
        break;
      case TripcProfileServiceCategory.review:
        dialogHelpers.show(context,
            child: TripcDialog(
              onTap: () => Navigator.of(context, rootNavigator: true).pop(),
              icon: AppAssets.origin()
                  .imCommingSoon
                  .widget(height: 179.H, fit: BoxFit.cover),
              title: context.strings.text_comming_soon_text,
              titleButton: context.strings.text_understood,
              contentPadding:
                  EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
            ));
        break;
      case TripcProfileServiceCategory.pravicy:
        AppRoute.pushNamed(context,
            routeName: AppRoute.routeProfileTermAndCondition);
        break;
    }
  }
}
