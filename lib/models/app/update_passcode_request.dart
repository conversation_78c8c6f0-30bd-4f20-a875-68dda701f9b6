class UpdatePasscodeRequest {
  UpdatePasscodeRequest({
    required this.membershipId,
    required this.passcode,
  });
  final int membershipId;
  final String passcode;

  UpdatePasscodeRequest copyWith({int? membershipId, String? passcode}) {
    return UpdatePasscodeRequest(
      membershipId: membershipId ?? this.membershipId,
      passcode: passcode ?? this.passcode,
    );
  }

  Map<String, dynamic> to<PERSON>son() {
    return {'membership_id': membershipId, 'passcode': passcode};
  }
}
