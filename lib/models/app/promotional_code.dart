import 'package:tripc_app/utils/app_extension.dart';

class PromotionalCode {
  PromotionalCode(
      {required this.discountPercent,
      required this.code,
      required this.saving,
      required this.startTime,
      required this.note,
      required this.endTime});
  final String code;
  final int discountPercent;
  final int saving;
  final DateTime startTime;
  final DateTime endTime;
  final String note;

  PromotionalCode copyWith(
      {int? discountPercent,
      String? code,
      int? saving,
      DateTime? startTime,
      DateTime? endTime,
      String? note}) {
    return PromotionalCode(
        discountPercent: discountPercent ?? this.discountPercent,
        code: code ?? this.code,
        saving: saving ?? this.saving,
        startTime: startTime ?? this.startTime,
        endTime: endTime ?? this.endTime,
        note: note ?? this.note);
  }

  String get startAndEndTime {
    return '${startTime.hourAndDate} - ${endTime.hourAndDate}';
  }
}
