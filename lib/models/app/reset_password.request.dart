class ResetPassRequest {
  ResetPassRequest({
    required this.token,
    required this.password,
  });
  final String token;
  final String password;

  ResetPassRequest copyWith({String? token, String? password}) {
    return ResetPassRequest(
      token: token ?? this.token,
      password: password ?? this.password,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'password': password,
    };
  }
}
