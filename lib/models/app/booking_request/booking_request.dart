import 'dart:convert';

import '../payment_request.dart';

String bookingRequestToJson(BookingRequest data) => json.encode(data.toJson());

class BookingRequest {
    int? productId;
    String? description;
    List<OrderRequest>? order;
    InvoiceRequest? invoice;
    List<AttributeRequest>? attributes;
    String? date;
    String? time;

    BookingRequest({
        this.productId,
        this.description,
        this.order,
        this.invoice,
        this.attributes,
        this.date,
        this.time,
    });

    BookingRequest copyWith({
        int? productId,
        String? description,
        List<OrderRequest>? order,
        InvoiceRequest? invoice,
        List<AttributeRequest>? attributes,
        String? date,
        String? time,
    }) => 
        BookingRequest(
            productId: productId ?? this.productId,
            description: description ?? this.description,
            order: order ?? this.order,
            invoice: invoice ?? this.invoice,
            attributes: attributes ?? this.attributes,
            date: date ?? this.date,
            time: time ?? this.time,
        );

    Map<String, dynamic> toJson() => {
        "product_id": productId,
        "description": description,
        "order": order == null ? [] : List<dynamic>.from(order!.map((x) => x.toJson())),
        "invoice": invoice?.toJson(),
        "attributes": attributes == null ? [] : List<dynamic>.from(attributes!.map((x) => x.toJson())),
        "date": date,
        "time": time,
    };
}

class OrderRequest {
    int? subProductId;
    int? quantity;

    OrderRequest({
        this.subProductId,
        this.quantity,
    });

    OrderRequest copyWith({
        int? subProductId,
        int? quantity,
    }) => 
        OrderRequest(
            subProductId: subProductId ?? this.subProductId,
            quantity: quantity ?? this.quantity,
        );

    factory OrderRequest.fromJson(Map<String, dynamic> json) => OrderRequest(
        subProductId: json["sub_product_id"],
        quantity: json["quantity"],
    );

    Map<String, dynamic> toJson() => {
        "sub_product_id": subProductId,
        "quantity": quantity,
    };
}
