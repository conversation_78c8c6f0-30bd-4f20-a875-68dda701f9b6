import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum AuthencationErrorKey {
  emailNotFound('errors.user.notfound'),
  wrongPass('errors.login.failed'),
  lockLogin('errors.login.locked'),
  inactiveAccount('errors.login.withdraw'),
  noneError('');

  const AuthencationErrorKey(this.value);
  final String value;
  static AuthencationErrorKey getByValue(String value) {
    if (value.startsWith('errors.login.locked')) {
      return AuthencationErrorKey.lockLogin;
    }

    return AuthencationErrorKey.values.firstWhere((x) => x.value == value,
        orElse: () => AuthencationErrorKey.noneError);
  }

  String message(BuildContext context) {
    switch (this) {
      case AuthencationErrorKey.emailNotFound:
        return context.strings.text_email_has_not_been_registered;
      case AuthencationErrorKey.wrongPass:
        return context.strings.text_pls_enter_correct_password;
      case AuthencationErrorKey.lockLogin:
        return context.strings.text_been_term_locked;
      case AuthencationErrorKey.inactiveAccount:
        return context.strings.text_inactive_account;
      case AuthencationErrorKey.noneError:
        return '';
    }
  }
}
