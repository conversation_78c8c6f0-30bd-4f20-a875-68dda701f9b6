import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum OrderStatus {
  pending,
  confirmed,
  completed,
  waitingForRefund,
  cancelled;

  static OrderStatus byName(String name) {
    return OrderStatus.values.firstWhere((f) => f.name == name,
        orElse: () => OrderStatus.waitingForRefund);
  }
}

enum PaymentStatus {
  pending,
  paid,
  payLater,
  failed,
  cancelled;

  static PaymentStatus getByValue(String i) {
    switch (i) {
      case 'pending':
        return PaymentStatus.pending;
      case 'failed':
        return PaymentStatus.failed;
      case 'pay_later':
        return PaymentStatus.payLater;
      case 'paid':
        return PaymentStatus.paid;
      case 'cancelled':
        return PaymentStatus.cancelled;
      default:
        return PaymentStatus.pending;
    }
  }

  String statusText(BuildContext context) {
    switch (this) {
      case PaymentStatus.paid:
        return context.strings.text_payment_has_been_successful;
      case PaymentStatus.pending:
        return context.strings.text_waiting_for_payment;
      case PaymentStatus.failed:
        return context.strings.text_canceled;
      case PaymentStatus.cancelled:
        return context.strings.text_canceled;
      case PaymentStatus.payLater:
        return context.strings.text_waiting_for_payment;
    }
  }
}

