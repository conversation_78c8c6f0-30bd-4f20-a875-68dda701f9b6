import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum TripcTierType {
  bronze(1),
  silver(2),
  gold(3),
  platinum(4),
  diamond(5);

  const TripcTierType(this.value);
  final int value;
  static TripcTierType getByValue(int i) {
    return TripcTierType.values.firstWhere(
      (x) => x.value == i,
      orElse: () => bronze,
    );
  }

  String title(BuildContext context) {
    switch (this) {
      case bronze:
        return context.strings.text_bronze_tier;
      case silver:
        return context.strings.text_sliver_tier;
      case gold:
        return context.strings.text_gold_tier;
      case platinum:
        return context.strings.text_platinum_tier;
      case diamond:
        return context.strings.text_diamond_tier;
    }
  }

  Color backgroudColor() {
    switch (this) {
      case bronze:
        return const Color(0xffE3C4B1);
      case silver:
        return const Color(0xffEEEEEE);
      case gold:
        return const Color(0xffF6F1B8);
      case platinum:
        return const Color(0xff99D9F2);
      case diamond:
        return const Color(0xff2F1265);
    }
  }

  Color textColor() {
    switch (this) {
      case bronze:
        return const Color(0xff6D4700);
      case silver:
        return const Color(0xff616263);
      case gold:
        return const Color(0xff866100);
      case platinum:
        return const Color(0xff023787);
      case diamond:
        return const Color(0xffFEFEFE);
    }
  }

  String text(BuildContext context) {
    switch (this) {
      case bronze:
        return context.strings.text_bronze_rank;
      case silver:
        return context.strings.text_silver_rank;
      case gold:
        return context.strings.text_gold_rank;
      case platinum:
        return context.strings.text_platinum_rank;
      case diamond:
        return context.strings.text_diamond_rank;
    }
  }

  AppAssetBuilder icon() {
    switch (this) {
      case bronze:
        return AppAssets.origin().icBronze;
      case silver:
        return AppAssets.origin().icSilver;
      case gold:
        return AppAssets.origin().icGold;
      case platinum:
        return AppAssets.origin().icPlatinum;
      case diamond:
        return AppAssets.origin().icDiamondTier;
    }
  }
}
