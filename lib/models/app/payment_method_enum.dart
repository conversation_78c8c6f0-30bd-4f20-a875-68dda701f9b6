import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum PaymentMethod {
  creditcard(1),
  tcent(2),
  vietqr(3),
  momo(4),
  zalopay(5),
  vnpay(6),
  paylate(7),
  payos(8);

  const PaymentMethod(this.value);
  final int value;
  static PaymentMethod getByValue(int i) {
    return PaymentMethod.values.firstWhere(
      (x) => x.value == i,
      orElse: () => PaymentMethod.creditcard,
    );
  }

  String title(BuildContext context) {
    switch (this) {
      case PaymentMethod.creditcard:
        return context.strings.text_credit_card_method;
      case PaymentMethod.tcent:
        return context.strings.text_tcent_point_method;
      case PaymentMethod.vietqr:
        return context.strings.text_vietqr_method;
      case PaymentMethod.momo:
        return context.strings.text_momo_method;
      case PaymentMethod.zalopay:
        return context.strings.text_zalo_pay_method;
      case PaymentMethod.vnpay:
        return context.strings.text_payment_vn_pay_method;
      case PaymentMethod.paylate:
        return context.strings.text_payment_later;
      case PaymentMethod.payos:
        return 'PayOs';
    }
  }
}
