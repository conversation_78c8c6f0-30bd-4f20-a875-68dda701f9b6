import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/passenger_information.dart';
import 'package:tripc_app/models/app/payment_method_enum.dart';
import 'package:tripc_app/models/app/payment_status.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/utils/app_extension.dart';

class Payment {
  Payment(
      {required this.bookingCode,
      required this.aldultQuantity,
      required this.childQuantity,
      required this.bookingDate,
      required this.departureDate,
      required this.passengerInfo,
      required this.totalPrice,
      required this.service,
      this.countdownTime,
      this.transactionCode,
      this.status,
      this.paymentMethod,
      this.adultPrice = 200000,
      this.childPrice = 100000,
      this.selectSeat,
      this.selectHotTime,
      this.selectedDate,
      this.selectedTime});
  final String bookingCode;
  final int aldultQuantity;
  final int childQuantity;
  final DateTime bookingDate;
  final DateTime departureDate;
  final PassengerInfo? passengerInfo;
  final TourResponse? service;
  final int totalPrice;
  final PaymentMethod? paymentMethod;
  final int? status;
  final int? countdownTime;
  final String? transactionCode;
  final int adultPrice;
  final int childPrice;
  final AttributeValue? selectSeat;
  final AttributeValue? selectHotTime;
  final DateTime? selectedDate;
  final String? selectedTime;

  Payment copyWith(
      {String? bookingCode,
      DateTime? bookingDate,
      DateTime? departureDate,
      PassengerInfo? passengerInfo,
      int? aldultQuantity,
      int? childQuantity,
      int? totalPrice,
      PaymentMethod? paymentMethod,
      int? countdownTime,
      int? status,
      String? transactionCode,
      TourResponse? service,
      AttributeValue? selectSeat,
      AttributeValue? selectHotTime,
      DateTime? selectedDate,
      String? selectedTime,
      }) {
    return Payment(
      bookingCode: bookingCode ?? this.bookingCode,
      aldultQuantity: aldultQuantity ?? this.aldultQuantity,
      childQuantity: childQuantity ?? this.childQuantity,
      bookingDate: bookingDate ?? this.bookingDate,
      departureDate: departureDate ?? this.departureDate,
      passengerInfo: passengerInfo ?? this.passengerInfo,
      totalPrice: totalPrice ?? this.totalPrice,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      countdownTime: countdownTime ?? this.countdownTime,
      transactionCode: transactionCode ?? this.transactionCode,
      service: service ?? this.service,
      status: status ?? this.status,
      selectSeat: selectSeat ?? this.selectSeat,
      selectHotTime: selectHotTime ?? this.selectHotTime,
      selectedTime: selectedTime ?? this.selectedTime,
      selectedDate: selectedDate ?? this.selectedDate,
    );
  }

  PaymentStatus get paymentStatus {
    return PaymentStatus.getByValue('pending');
  }

  int get totalQuantity {
    return aldultQuantity + childQuantity;
  }

  String noteText(BuildContext context) {
    if ((passengerInfo?.specialRequest ?? '').isEmpty) {
      return context.strings.text_do_not_have;
    }
    return passengerInfo?.specialRequest ?? '';
  }
}
