import 'package:tripc_app/models/app/gender_enum.dart';

class TripcUser {
  TripcUser(
      {required this.id,
      this.fullName,
      this.gender,
      this.national,
      this.city,
      this.email,
      this.phoneNumber});

  final int id;
  final String? fullName;
  final int? gender;
  final String? national;
  final String? city;
  final String? email;
  final String? phoneNumber;

  TripcUser copyWith(
      {int? id,
      String? fullName,
      int? gender,
      String? national,
      String? city,
      String? email,
      String? phoneNumber}) {
    return TripcUser(
        id: id ?? this.id,
        fullName: fullName ?? this.fullName,
        gender: gender ?? this.gender,
        national: national ?? this.national,
        city: city ?? this.city,
        email: email ?? this.email,
        phoneNumber: phoneNumber ?? this.phoneNumber);
  }

  GenderEnum get userGender {
    return GenderEnum.getByValue(gender ?? 1);
  }
}
