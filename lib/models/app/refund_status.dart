import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum RefundStatus {
  pending(1),
  success(2);

  const RefundStatus(this.value);
  final int value;
  static RefundStatus getByValue(int i) {
    return RefundStatus.values.firstWhere(
      (x) => x.value == i,
      orElse: () => RefundStatus.pending,
    );
  }

  static RefundStatus getByString(String i) {
    switch (i) {
      case 'pending':
        return RefundStatus.pending;
      case 'success':
        return RefundStatus.success;
      default:
        return RefundStatus.pending;
    }
  }

  String statusText(BuildContext context) {
    switch (this) {
      case RefundStatus.pending:
        return context.strings.text_refund_successful;
      case RefundStatus.success:
        return context.strings.text_waiting_for_refund;
    }
  }
}

extension RefundStatusX on RefundStatus {

  String getContent(BuildContext context) {
    switch (this) {
      case RefundStatus.pending:
        return context.strings.text_refund_checking;
      case RefundStatus.success:
        return context.strings.text_refund_in_process;
    }
  }
}
