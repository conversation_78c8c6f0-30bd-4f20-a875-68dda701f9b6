import 'package:tripc_app/services/app/app_assets.dart';

enum TripcMembershipRank {
  one(1),
  two(2),
  three(3),
  four(4),
  five(5);

  const TripcMembershipRank(this.value);
  final int value;
  static TripcMembershipRank getByValue(int i) {
    return TripcMembershipRank.values.firstWhere(
      (x) => x.value == i,
      orElse: () => one,
    );
  }

  AppAssetBuilder get getBackGround {
    switch (this) {
      case one:
        return AppAssets.origin().bgCard01;
      case two:
        return AppAssets.origin().bgCard02;
      case three:
        return AppAssets.origin().bgCard03;
      case four:
        return AppAssets.origin().bgCard04;
      case five:
        return AppAssets.origin().bgCard05;
    }
  }
}
