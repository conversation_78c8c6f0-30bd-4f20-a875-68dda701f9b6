import 'package:collection/collection.dart';

enum ParentSupplierTypeEnum {
  luuTru(
    slug: 'luu-tru',
    name: '<PERSON><PERSON><PERSON> trú',
    children: [
      'khach-san',
      'resort',
      'homestay',
      'villa-biet-thu',
      'can-ho-dich-vu',
      'hostel-dorm',
      'khu-nghi-duong-sinh-thai',
    ],
  ),
  amThuc(
    slug: 'am-thuc',
    name: 'Ẩm thực',
    children: [
      'nha-hang',
      'quan-an-quan-nhau',
      'quan-ca-phe-cafe',
      'buffet-nha-hang-buffet',
      'chuoi-f-b',
      'trai-nghiem-am-thuc',
    ],
  ),
  lu<PERSON><PERSON>h(
    slug: 'lu-hanh',
    name: 'Lữ hành',
    children: [
      'cong-ty-du-lich-lu-hanh',
      'huong-dan-vien-tu-do',
      'to-chuc-ban-dia-cung-cap-trai-nghiem',
      'don-vi-to-chuc-trekking-mao-hiem-camping',
      'tour-noi-thanh-thanh-pho',
      'nha-dieu-hanh-tour-nuoc-ngoai',
    ],
  ),
  di<PERSON>huyen(
    slug: 'di-chuyen',
    name: 'Di chuyển',
    children: [
      'hang-taxi-xe-cong-nghe',
      'xe-dua-don-san-bay',
      'don-vi-thue-xe-tu-lai-co-tai-xe',
      'dich-vu-xe-limousine',
      'tour-xe-may-xe-jeep-xe-co',
      '',
    ],
  ),
  muaSam(
    slug: 'mua-sam',
    name: 'Mua sắm',
    children: [
      'trung-tam-thuong-mai',
      'cua-hang-qua-luu-niem-dac-san',
      'cua-hang-thoi-trang-do-thu-cong',
      'cho-dem-cho-dia-phuong',
      'tiem-thuoc-nhu-yeu-pham',
    ],
  ),
  huongDanVienChuyenGia(
    slug: 'huong-dan-vien-chuyen-gia',
    name: 'Hướng dẫn viên & Chuyên gia',
    children: [
      'huong-dan-vien-ca-nhan',
      'travel-coach-trip-planner',
      'nguoi-ban-dia',
      'photographer-travel-videographer',
    ],
  ),
  traiNghiemHocTapVanHoa(
    slug: 'trai-nghiem-hoc-tap-van-hoa',
    name: 'Trải nghiệm học tập / Văn hoá',
    children: [
      'lop-nau-an-lam-gom-lam-banh',
      'workshop-nghe-thuat-van-hoa',
      'lop-hoc-ngan-han-danh-cho-du-khach',
    ],
  );

  final String slug;
  final String name;
  final List<String> children;

  const ParentSupplierTypeEnum({
    required this.slug,
    required this.name,
    required this.children,
  });

  List<String> getChildrenSlugs() => children;

  static ParentSupplierTypeEnum? fromSlug(String slug) {
    return ParentSupplierTypeEnum.values
        .firstWhereOrNull((e) => e.slug == slug);
  }
}
