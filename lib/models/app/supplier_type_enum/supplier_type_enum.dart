import 'package:collection/collection.dart';

import 'parent_supplier_type_enum.dart';

enum SupplierTypeEnum{
  khachSan(
      slug: 'khach-san',
      name: '<PERSON>h<PERSON>ch sạn',
      parent: ParentSupplierTypeEnum.luuTru),
  resort(
      slug: 'resort',
      name: 'Resort',
      parent: ParentSupplierTypeEnum.luuTru),
  homestay(
      slug: 'homestay',
      name: 'Homestay',
      parent: ParentSupplierTypeEnum.luuTru),
  villa(
      slug: 'villa-biet-thu',
      name: 'Villa/Biệt thự',
      parent: ParentSupplierTypeEnum.luuTru),
  canHoDichVu(
      slug: 'can-ho-dich-vu',
      name: '<PERSON><PERSON><PERSON> hộ dịch vụ',
      parent: ParentSupplierTypeEnum.luuTru),
  hostel(
      slug: 'hostel-dorm',
      name: 'Hostel / Dorm',
      parent: ParentSupplierTypeEnum.luuTru),
  khu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
      slug: 'khu-nghi-duong-sinh-thai',
      name: 'Khu nghỉ dưỡng sinh thái',
      parent: ParentSupplierTypeEnum.luuTru),

  nha<PERSON><PERSON>(
      slug: 'nha-hang',
      name: 'Nhà hàng',
      parent: ParentSupplierTypeEnum.amThuc),
  quanAn(
      slug: 'quan-an-quan-nhau',
      name: 'Quán ăn/Quán nhậu',
      parent: ParentSupplierTypeEnum.amThuc),
  caPhe(
      slug: 'quan-ca-phe-cafe',
      name: 'Quán cà phê/Café',
      parent: ParentSupplierTypeEnum.amThuc),
  buffet(
      slug: 'buffet-nha-hang-buffet',
      name: 'Buffet/Nhà hàng buffet',
      parent: ParentSupplierTypeEnum.amThuc),
  chuoiFB(
      slug: 'chuoi-f-b',
      name: 'Chuỗi F&B',
      parent: ParentSupplierTypeEnum.amThuc),
  traiNghiemAmThuc(
      slug: 'trai-nghiem-am-thuc',
      name: 'Trải nghiệm ẩm thực',
      parent: ParentSupplierTypeEnum.amThuc),

  congTyDuLich(
      slug: 'cong-ty-du-lich-lu-hanh',
      name: 'Công ty du lịch / Lữ hành',
      parent: ParentSupplierTypeEnum.luHanh),
  huongDanVienTuDo(
      slug: 'huong-dan-vien-tu-do',
      name: 'Hướng dẫn viên tự do',
      parent: ParentSupplierTypeEnum.luHanh),
  toChucBanDia(
      slug: 'to-chuc-ban-dia-cung-cap-trai-nghiem',
      name: 'Tổ chức bản địa cung cấp trải nghiệm',
      parent: ParentSupplierTypeEnum.luHanh),
  trekking(
      slug: 'don-vi-to-chuc-trekking-mao-hiem-camping',
      name: 'Đơn vị tổ chức trekking / mạo hiểm / camping',
      parent: ParentSupplierTypeEnum.luHanh),
  tourThanhPho(
      slug: 'tour-noi-thanh-thanh-pho',
      name: 'Tour nội thành / thành phố',
      parent: ParentSupplierTypeEnum.luHanh),
  dieuHanhTour(
      slug: 'nha-dieu-hanh-tour-nuoc-ngoai',
      name: 'Nhà điều hành tour nước ngoài',
      parent: ParentSupplierTypeEnum.luHanh);

  final String slug;
  final String name;
  final ParentSupplierTypeEnum parent;

  const SupplierTypeEnum({
    required this.slug,
    required this.name,
    required this.parent,
  });

  static SupplierTypeEnum? fromSlug(String slug) {
    return SupplierTypeEnum.values.firstWhereOrNull((e) => e.slug == slug);
  }

  static List<SupplierTypeEnum> fromParent(ParentSupplierTypeEnum parent) {
    return SupplierTypeEnum.values.where((e) => e.parent == parent).toList();
  }
}
