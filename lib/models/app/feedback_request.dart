class FeedbackRequest {
  FeedbackRequest({
    required this.content,
    required this.name,
    required this.number,
    required this.image,
  });
  final String? name;
  final String? number;
  final String? content;
  final String? image;

  FeedbackRequest copyWith(
      {String? content, String? name, String? number, String? image}) {
    return FeedbackRequest(
        content: content ?? this.content,
        name: name ?? this.name,
        number: number ?? this.number,
        image: image ?? this.image);
  }

  Map<String, dynamic> toJson() => {
        'name': name,
        'image_url': image,
        'establishment_decision_number': number,
        'content': content,
      };
}
