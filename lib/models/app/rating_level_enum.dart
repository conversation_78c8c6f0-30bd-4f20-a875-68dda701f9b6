import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum RatingLevel {
  bad(1),
  good(2),
  verygood(3);

  const RatingLevel(this.value);
  final int value;
  static RatingLevel getByValue(int i) {
    return RatingLevel.values.firstWhere(
      (x) => x.value == i,
      orElse: () => RatingLevel.verygood,
    );
  }

  String statusText(BuildContext context) {
    switch (this) {
      case RatingLevel.bad:
        return context.strings.text_bad;
      case RatingLevel.good:
        return context.strings.text_good;
      case RatingLevel.verygood:
        return context.strings.text_very_good;
    }
  }
}
