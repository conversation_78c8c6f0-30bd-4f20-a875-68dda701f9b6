class VerifyOtpForgotPasscodeRequest {
  VerifyOtpForgotPasscodeRequest({
    required this.token,
    required this.otp,
    required this.email,
    this.password = ''
  });
  final String email;
  final String token;
  final String otp;
  final String password;

  VerifyOtpForgotPasscodeRequest copyWith({String? token, String? otp, String? email}) {
    return VerifyOtpForgotPasscodeRequest(
      token: token ?? this.token,
      otp: otp ?? this.otp,
      email: email ?? this.email,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'otp': otp,
      'email': email
    };
  }
}