import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum RefundEnum {
  requested('01'),
  processing('02'),
  refunded('03'),
  cancel('04');

  const RefundEnum(this.value);
  final String value;
  static RefundEnum getByValue(String i) {
    return RefundEnum.values.firstWhere(
      (x) => x.value == i,
      orElse: () => RefundEnum.requested,
    );
  }

  String title(BuildContext context) {
    switch (this) {
      case requested:
        return context.strings.text_requested;
      case processing:
        return context.strings.text_processing;
      case refunded:
        return context.strings.refunded;
      case RefundEnum.cancel:
        return context.strings.cancel_refund;
    }
  }

  Color textColor() {
    switch (this) {
      case requested:
        return AppAssets.origin().colorTextBooking;
      case processing:
        return AppAssets.origin().textForLocationTourV2;
      case refunded:
        return AppAssets.origin().refundStatusColorText;
      case RefundEnum.cancel:
        return AppAssets.origin().redDotColor;
    }
  }

  Color backgroundColor() {
    switch (this) {
      case requested:
        return AppAssets.origin().requestedStatusColorBG;
      case processing:
        return AppAssets.origin().processingStatusColorBG;
      case refunded:
        return AppAssets.origin().refundedStatusColorBG;
      case RefundEnum.cancel:
        return AppAssets.origin().cancelRefundStatusColorBG;
    }
  }
}
