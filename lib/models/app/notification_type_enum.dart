import 'package:tripc_app/services/app/app_assets.dart';

enum NotificationType {
  system,
  promotion;

  static NotificationType getByValue(String value) {
    switch (value) {
      case 'system':
        return NotificationType.system;
      case 'promotion':
        return NotificationType.promotion;
      default:
        return NotificationType.system;
    }
  }

  AppAssetBuilder get icon {
    switch (this) {
      case system:
        return AppAssets.origin().icSquareGift;
      case promotion:
        return AppAssets.origin().icLoundSpeaker;
    }
  }
}
