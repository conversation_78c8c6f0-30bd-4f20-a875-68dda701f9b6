class User {
  User({required this.name, required this.avatar});

  final String name;
  final String? avatar;
}

class Rating {
  Rating(
      {required this.user,
      required this.ratingDate,
      required this.ratingScore,
      required this.totalLikes,
      required this.totalDislikes,
      required this.content,
      this.images,
      this.supplierResponse});

  final User user;
  final DateTime ratingDate;
  final int ratingScore;
  final int totalLikes;
  final int totalDislikes;
  final String content;
  final List<String>? images;
  final String? supplierResponse;
}
