import 'package:tripc_app/models/app/gender_enum.dart';

import '../../pages/ticket_tour/providers/ticket_tour_provider.dart';

class Passenger {
  Passenger(
      {required this.gender,
      required this.name,
      required this.id,
      this.email,
      this.phone,
      this.subProductId,
      this.guardianName,
      this.type = TicketType.aldult});
  final int? id;
  final String? name;
  final int? gender;
  final String? email;
  final String? phone;
  final String? guardianName;
  final int? subProductId;
  final TicketType type;

  bool get isAllFieldEmpty {
    return (name?.isEmpty ?? false) && (email?.isEmpty ?? false) && (phone?.isEmpty ?? false);
  }

  Passenger copyWith(
      {int? id, String? name, int? gender, String? email, String? phone, int? subProductId, String? guardianName, TicketType? type}) {
    return Passenger(
        id: id ?? this.id,
        name: name ?? this.name,
        gender: gender ?? this.gender,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        guardianName: guardianName ?? this.guardianName,
        subProductId: subProductId ?? this.subProductId,
        type: type ?? this.type);
  }

  GenderEnum get genderEnum {
    return GenderEnum.getByValue(gender ?? 1);
  }
}
