class SignUpRequest {
  SignUpRequest({
    required this.fullname,
    required this.email,
    required this.password,
  });
  final String fullname;
  final String email;
  final String password;

  SignUpRequest copyWith({String? fullname, String? email, String? password}) {
    return SignUpRequest(
      fullname: fullname ?? this.fullname,
      email: email ?? this.email,
      password: password ?? this.password,
    );
  }

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'full_name': fullname,
      'email': email,
      'password': password,
    };
  }
}
