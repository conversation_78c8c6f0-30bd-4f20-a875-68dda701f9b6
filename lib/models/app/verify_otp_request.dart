class VerifyOtpRequest {
  VerifyOtpRequest(
      {required this.email,
      required this.otp,
      required this.token,
      required this.password});
  final String email;
  final String otp;
  final String token;
  final String password;

  VerifyOtpRequest copyWith(
      {String? email, String? otp, String? token, String? password}) {
    return VerifyOtpRequest(
        email: email ?? this.email,
        otp: otp ?? this.otp,
        token: token ?? this.token,
        password: password ?? this.password);
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'otp': otp,
      'token': token,
      'password': password,
    };
  }
}
