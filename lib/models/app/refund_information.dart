import '../remote/api_tour_response/tour_response.dart';
import 'refund_status.dart';

class RefundInformation {
  RefundInformation(
      {required this.status,
      required this.refundPrice,
      required this.paidPrice,
      required this.tour,
      required this.aldultQuantity,
      required this.childQuantity,
      required this.reason,
      required this.requestDate,
      required this.refundDate,
      });
  final int status;
  final int refundPrice;
  final int paidPrice;
  final TourResponse tour;
  final int aldultQuantity;
  final int childQuantity;
  final String? reason;
  final DateTime requestDate;
  final DateTime? refundDate;

  RefundStatus get refundStatus {
    return RefundStatus.getByValue(status);
  }
}
