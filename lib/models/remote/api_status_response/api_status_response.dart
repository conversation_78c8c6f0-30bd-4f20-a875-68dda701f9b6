import 'dart:convert';
import 'package:tripc_app/models/remote/api_status_response/api_data_response.dart';

ApiStatusResponse apiStatusResponseFromJson(String str) =>
    ApiStatusResponse.fromJson(json.decode(str));

String apiStatusResponseToJson(ApiStatusResponse data) =>
    json.encode(data.toJson());

class ApiStatusResponse {
  ApiStatusResponse({required this.status, this.data, this.token});
  factory ApiStatusResponse.fromJson(Map<String, dynamic> json) =>
      ApiStatusResponse(
          status: json['status'] ?? false,
          token: json['token'],
          data: json['data'] == null
              ? null
              : ApiDataResponse.fromJson(json['data']));
  static ApiStatusResponse getDefault() {
    return ApiStatusResponse(status: false);
  }

  bool status;
  String? token;
  ApiDataResponse? data;

  ApiStatusResponse copyWith(
      {bool? status, ApiDataResponse? data, String? token}) {
    return ApiStatusResponse(
        status: status ?? this.status,
        data: data ?? this.data,
        token: token ?? this.token);
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'token': token,
        'data': data?.toJson(),
      };
}

// VerifyPasscodeResponse verifyPasscodeResponseFromJson(String str) =>
//     VerifyPasscodeResponse.fromJson(json.decode(str));

// String verifyPasscodeResponseToJson(VerifyPasscodeResponse data) =>
//     json.encode(data.toJson());

// class VerifyPasscodeResponse {
//   VerifyPasscodeResponse({required this.status, this.data});
//   factory VerifyPasscodeResponse.fromJson(Map<String, dynamic> json) =>
//       VerifyPasscodeResponse(
//           status: json['status'] ?? false,
//           data: json['data'] == null ? null : VerifyPasscode.fromJson(json['data']));

//   bool status;
//   VerifyPasscode? data;

//   VerifyPasscodeResponse copyWith(
//       {bool? status, VerifyPasscode? data}) {
//     return VerifyPasscodeResponse(
//         status: status ?? this.status,
//         data: data ?? this.data,);
//   }

//   Map<String, dynamic> toJson() => {
//     'status': status,
//     'data': data?.toJson(),
//   };
// }

// class VerifyPasscode {
//   final bool? valid;
//   final int? remainingAttempts;

//   VerifyPasscode({
//     this.valid,
//     this.remainingAttempts,
//   });

//   factory VerifyPasscode.fromJson(Map<String, dynamic> json) {
//     return VerifyPasscode(
//       valid: json['valid'],
//       remainingAttempts: json['remaining_attempts'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'valid': valid,
//       'remaining_attempts': remainingAttempts,
//     };
//   }
// }
