class ApiDataResponse {
  ApiDataResponse({
    required this.success,
  });
  factory ApiDataResponse.fromJson(Map<String, dynamic> json) =>
      ApiDataResponse(success: json['success'] ?? false);
  static ApiDataResponse getDefault() {
    return ApiDataResponse(success: false);
  }

  bool success;

  ApiDataResponse copyWith({
    bool? success,
  }) {
    return ApiDataResponse(
      success: success ?? this.success,
    );
  }

  Map<String, dynamic> toJson() => {'success': success};
}
