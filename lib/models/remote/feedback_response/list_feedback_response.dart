import 'dart:convert';
import 'feedback_response.dart';

ListFeedBackResponse apiListFeedbackResponseFromJson(String str) =>
    ListFeedBackResponse.fromJson(json.decode(str));

class ListFeedBackResponse {
  ListFeedBackResponse({required this.status, this.data, this.token});
  factory ListFeedBackResponse.fromJson(Map<String, dynamic> json) =>
      ListFeedBackResponse(
        status: json['status'] ?? false,
        token: json['token'],
        data: json['data'] == null
            ? null
            : List<FeedbackResponse>.from(
                json["data"].map((x) => FeedbackResponse.fromJson(x))),
      );
  static ListFeedBackResponse getDefault() {
    return ListFeedBackResponse(status: false);
  }

  bool status;
  String? token;
  List<FeedbackResponse>? data;

  ListFeedBackResponse copyWith(
      {bool? status, List<FeedbackResponse>? data, String? token}) {
    return ListFeedBackResponse(
        status: status ?? this.status,
        data: data ?? this.data,
        token: token ?? this.token);
  }
}
