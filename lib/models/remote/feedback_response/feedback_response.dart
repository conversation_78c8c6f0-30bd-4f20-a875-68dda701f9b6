class FeedbackResponse {
  final String name;
  final String number;
  final String content;
  final String time;

  FeedbackResponse({required this.name, required this.number, required this.content, required this.time});

  factory FeedbackResponse.fromJson(Map<String, dynamic> json) {
    return FeedbackResponse(
      name: json['name'] ?? '',
      number: json['establishment_decision_number'] ?? '',
      content: json['content'] ?? '',
      time: json['created_at'] ?? '',
    );
  }
}