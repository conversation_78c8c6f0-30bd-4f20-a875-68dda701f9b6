import 'dart:convert';

import 'package:tripc_app/utils/app_extension.dart';

import '../app/notification_type_enum.dart';

NotificationResponse notificationResponseFromJson(String str) =>
    NotificationResponse.fromJson(json.decode(str));

String notificationResponseToJson(NotificationResponse data) =>
    json.encode(data.toJson());

class NotificationResponse {
  NotificationResponse({
    this.id,
    this.title,
    this.description,
    this.createAt,
    this.type,
    this.read,
  });

  factory NotificationResponse.fromJson(Map<String, dynamic> json) =>
      NotificationResponse(
        id: json['id'],
        title: json['title'],
        description: json['description'],
        createAt: json['created_at'],
        type: json['type'],
        read: json['is_read'],
      );

  NotificationResponse copyWith({
    int? id,
    String? title,
    String? description,
    String? createAt,
    String? type,
    int? read
  }) {
    return NotificationResponse(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      createAt: createAt ?? this.createAt,
      type: type ?? this.type,
      read: read ?? this.read
    );
  }

  int? id;
  String? title;
  String? description;
  String? createAt;
  String? type;
  int? read;

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'description': description,
        'created_at': createAt,
        'type': type,
        'is_read': read
      };

  
  String get hour {
    if (createAt == null) {
      return '';
    }
    return createAt!.parseDateTime().toHHmm;
  }

  DateTime get createAtTime {
    return createAt?.parseDateTime() ?? DateTime.now();
  }

  NotificationType get notiType {
    return NotificationType.getByValue(type ?? '');
  }
}

ListNotificationResponse listNotificationResponseFromJson(String str) =>
    ListNotificationResponse.fromJson(json.decode(str));

String listNotificationResponseToJson(ListNotificationResponse data) =>
    json.encode(data.toJson());

class ListNotificationResponse {
  bool status;
  List<NotificationResponse> data;
  int? total;

  ListNotificationResponse({
    required this.status,
    this.data = const [],
    this.total,
  });

  factory ListNotificationResponse.fromJson(Map<String, dynamic> json) =>
      ListNotificationResponse(
        status: json["status"] ?? false,
        data: json["data"] == null
            ? []
            : List<NotificationResponse>.from(
                json["data"].map((x) => NotificationResponse.fromJson(x))),
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "total": total,
      };
}
