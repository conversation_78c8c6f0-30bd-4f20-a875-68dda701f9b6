import 'dart:convert';
import 'package:tripc_app/models/app/membership_default_status_enum.dart';
import 'package:tripc_app/models/app/tripc_tier.dart';
import 'package:tripc_app/models/remote/auth_provider_response.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_model.dart';
import 'package:tripc_app/utils/app_extension.dart';

UserResponse userResponseFromJson(String str) =>
    UserResponse.fromJson(json.decode(str));

String userResponseToJson(UserResponse data) => json.encode(data.toJson());

class UserResponse {
  UserResponse(
      {this.email,
      this.createdAt,
      this.updatedAt,
      this.id,
      this.fullname,
      this.dateOrBirth,
      this.status,
      this.firebaseToken,
      this.gender,
      this.memberships,
      this.phone,
      this.language,
      this.currency,
      this.national,
      this.themeMode,
      this.authProviders,
      this.city,
      this.isSkipInviteCode,
      this.isSkipTripC,
      this.avatarUrl});

  factory UserResponse.fromJson(Map<String, dynamic> json) => UserResponse(
        email: json['email'],
        createdAt: json['created_at'],
        updatedAt: json['updated_at'],
        id: json['id'],
        fullname: json['full_name'],
        dateOrBirth: json['date_or_birth'],
        status: json['status'],
        firebaseToken: json['firebase_token'],
        gender: json['gender'],
        memberships: json['memberships'] != null
            ? List<MembershipModel>.from(
                json['memberships'].map((x) => MembershipModel.fromJson(x)))
            : [],
        phone: json['phone'],
        language: json['language'],
        currency: json['currency'],
        national: json['national'],
        themeMode: json['theme_mode'],
        city: json['city'],
        authProviders: json['auth_providers'] != null
            ? List<AuthProviderResponse>.from(json['auth_providers']
                .map((x) => AuthProviderResponse.fromJson(x)))
            : [],
        isSkipInviteCode: json['is_skip_invite_code'],
        isSkipTripC: json['is_skip_tripc'],
        avatarUrl: json['avatar_url'],
      );

  UserResponse copyWith(
      {String? email,
      String? createdAt,
      String? updatedAt,
      int? id,
      String? fullname,
      String? dateOrBirth,
      String? status,
      String? firebaseToken,
      String? phone,
      int? gender,
      List<MembershipModel>? memberships,
      String? language,
      String? currency,
      String? national,
      String? themeMode,
      String? city,
      List<AuthProviderResponse>? authProviders,
      int? isSkipInviteCode,
      int? isSkipTripC,
      String? avatarUrl}) {
    return UserResponse(
        email: email ?? this.email,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        fullname: fullname ?? this.fullname,
        dateOrBirth: dateOrBirth ?? this.dateOrBirth,
        status: status ?? this.status,
        firebaseToken: firebaseToken ?? this.firebaseToken,
        gender: gender ?? this.gender,
        memberships: memberships ?? this.memberships,
        phone: phone ?? this.phone,
        language: language ?? this.language,
        currency: currency ?? this.currency,
        national: national ?? this.national,
        themeMode: themeMode ?? this.themeMode,
        city: city ?? this.city,
        authProviders: authProviders ?? this.authProviders,
        isSkipInviteCode: isSkipInviteCode ?? this.isSkipInviteCode,
        isSkipTripC: isSkipTripC ?? this.isSkipTripC,
        avatarUrl: avatarUrl ?? this.avatarUrl);
  }

  int? id;
  String? email;
  String? phone;
  String? status;
  String? firebaseToken;
  String? fullname;
  String? createdAt;
  String? updatedAt;
  String? dateOrBirth;
  int? gender;
  String? language;
  String? currency;
  String? national;
  String? city;
  String? themeMode;
  List<AuthProviderResponse>? authProviders;
  List<MembershipModel>? memberships;
  int? isSkipInviteCode;
  int? isSkipTripC;
  String? avatarUrl;

  Map<String, dynamic> toJson() => {
        'email': email,
        'created_at': createdAt,
        'updated_at': updatedAt,
        'id': id,
        'full_name': fullname,
        'date_or_birth': dateOrBirth,
        'status': status,
        'firebase_token': firebaseToken,
        'gender': gender,
        'memberships': memberships != null
            ? List<dynamic>.from(memberships!.map((x) => x.toJson()))
            : [],
        'phone': phone,
        'language': language,
        'currency': currency,
        'national': national,
        'theme_mode': themeMode,
        'city': city,
        'auth_providers': authProviders != null
            ? List<dynamic>.from(authProviders!.map((x) => x.toJson()))
            : [],
        'is_skip_invite_code': isSkipInviteCode,
        'is_skip_tripc': isSkipTripC,
        'avatar_url': avatarUrl,
      };

  MembershipModel? get firstMembership {
    if (memberships != null && memberships!.isNotEmpty) {
      return memberships!.first;
    }
    return null;
  }

  MembershipModel? get defaultMemberShip {
    try {
      return memberships?.firstWhere(
          (element) => element.defaultStatus == MembershipDefaultStatus.active);
    } catch (e) {
      return null;
    }
  }

  MembershipModel? get selectedMembership {
    try {
      return memberships?.firstWhere((element) => element.isSelectedMbs);
    } catch (e) {
      return null;
    }
  }

  bool get hasPascode {
    final index =
        memberships?.indexWhere((element) => element.isDefaultMbs) ?? -1;
    if (index >= 0) {
      return memberships![index].hasPasscode;
    }
    return false;
  }

  bool isLinkedProvider(String provider) {
    if (authProviders != null && authProviders!.isNotEmpty) {
      return authProviders!
          .any((element) => element.thirdPartyAuth == provider);
    }
    return false;
  }

  String? getPhoneNumber() {
    final phoneNumber = phone;
    if (phoneNumber.isNotNull && phoneNumber!.isNotEmpty) {
      return phoneNumber.substring(1, phoneNumber.length);
    }
    return null;
  }

  TripcTierType getTier() {
    if (memberships != null && memberships!.isNotEmpty) {
      return memberships!.first.tierType;
    }
    return TripcTierType.bronze;
  }

  String getMemberShipID() {
    if (memberships != null && memberships!.isNotEmpty) {
      return memberships!.first.number!.formatTripcId;
    }
    return '-';
  }

  bool get isSkipInviteCodeBool {
    return isSkipInviteCode == 1;
  }
}
