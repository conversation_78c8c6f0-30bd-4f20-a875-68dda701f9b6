import 'dart:convert';

import '../../order_response/my_order_response.dart';
import 'detail_booking_order.dart';

BookingOrderResponse bookingOrderResponseFromJson(String str) =>
    BookingOrderResponse.fromJson(json.decode(str));

String bookingOrderResponseToJson(BookingOrderResponse data) =>
    json.encode(data.toJson());

class BookingOrderResponse {
  bool? status;
  BookingOrder? data;

  BookingOrderResponse({
    this.status,
    this.data,
  });

  BookingOrderResponse copyWith({
    bool? status,
    BookingOrder? data,
  }) =>
      BookingOrderResponse(
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory BookingOrderResponse.fromJson(Map<String, dynamic> json) =>
      BookingOrderResponse(
        status: json["status"],
        data: json["data"] == null ? null : BookingOrder.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
      };
}

class BookingOrder {
  int? id;
  String? orderCode;
  List<BookingDetailOrder>? order;
  String? orderStatus;
  String? description;
  DateTime? departureTime;
  String? sku;
  InvoiceResponse? invoice;
  String? transactionId;
  DateTime? createdAt;
  String? bookingInformation;
  String? generalTerms;
  String? usageInstructions;
  String? condition;
  int? tcentBack;
  ContactResponse? contact;
  String? combinedTicketUrl;

  BookingOrder({
    this.id,
    this.orderCode,
    this.order,
    this.orderStatus,
    this.description,
    this.departureTime,
    this.sku,
    this.invoice,
    this.transactionId,
    this.createdAt,
    this.bookingInformation,
    this.generalTerms,
    this.usageInstructions,
    this.condition,
    this.tcentBack,
    this.contact,
    this.combinedTicketUrl,
  });

  BookingOrder copyWith({
    int? id,
    String? orderCode,
    List<BookingDetailOrder>? order,
    String? orderStatus,
    String? description,
    DateTime? departureTime,
    String? sku,
    InvoiceResponse? invoice,
    String? transactionId,
    DateTime? createdAt,
    String? bookingInformation,
    String? generalTerms,
    String? usageInstructions,
    String? condition,
    int? tcentBack,
    ContactResponse? contact,
    String? combinedTicketUrl,
  }) =>
      BookingOrder(
        id: id ?? this.id,
        orderCode: orderCode ?? this.orderCode,
        order: order ?? this.order,
        orderStatus: orderStatus ?? this.orderStatus,
        description: description ?? this.description,
        departureTime: departureTime ?? this.departureTime,
        sku: sku ?? this.sku,
        invoice: invoice ?? this.invoice,
        transactionId: transactionId ?? this.transactionId,
        createdAt: createdAt ?? this.createdAt,
        bookingInformation: bookingInformation ?? this.bookingInformation,
        generalTerms: generalTerms ?? this.generalTerms,
        usageInstructions: usageInstructions ?? this.usageInstructions,
        condition: condition ?? this.condition,
        tcentBack: tcentBack ?? this.tcentBack,
        contact: contact ?? this.contact,
        combinedTicketUrl: combinedTicketUrl ?? this.combinedTicketUrl,
      );

  factory BookingOrder.fromJson(Map<String, dynamic> json) => BookingOrder(
        id: json["id"],
        orderCode: json["order_code"],
        order: json["order"] == null
            ? []
            : List<BookingDetailOrder>.from(
                json["order"]!.map((x) => BookingDetailOrder.fromJson(x))),
        orderStatus: json["order_status"],
        description: json["description"],
        departureTime: json["departure_time"] == null
            ? null
            : DateTime.parse(json["departure_time"]),
        sku: json["sku"],
        invoice:
            json["invoice"] == null ? null : InvoiceResponse.fromJson(json["invoice"]),
        transactionId: json["transaction_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        bookingInformation: json["booking_information"],
        generalTerms: json["general_terms"],
        usageInstructions: json["usage_instructions"],
        condition: json["condition"],
        tcentBack: json["tcent_back"],
        contact:
            json["contact"] == null ? null : ContactResponse.fromJson(json["contact"]),
        combinedTicketUrl: json["combined_ticket_url"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "order": order == null
            ? []
            : List<dynamic>.from(order!.map((x) => x.toJson())),
        "order_status": orderStatus,
        "description": description,
        "departure_time": departureTime?.toIso8601String(),
        "sku": sku,
        "invoice": invoice?.toJson(),
        "transaction_id": transactionId,
        "created_at": createdAt?.toIso8601String(),
        "booking_information": bookingInformation,
        "general_terms": generalTerms,
        "usage_instructions": usageInstructions,
        "tcent_back": tcentBack,
        // "contact": contact?.toJson(),
        "combined_ticket_url": combinedTicketUrl,
      };
}