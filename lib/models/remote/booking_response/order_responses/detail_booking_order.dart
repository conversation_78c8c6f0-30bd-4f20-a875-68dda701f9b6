import '../../order_response/my_order_response.dart';
import '../supplier_responses/booking_service_response.dart';

class BookingDetailOrder {
  int? quantity;
  BookingServiceResponse? bookingService;

  BookingDetailOrder({
    this.quantity,
    this.bookingService,
  });

  BookingDetailOrder copyWith({
    int? quantity,
    BookingServiceResponse? bookingService,
    List<CustomerTicket>? customerTickets,
  }) =>
      BookingDetailOrder(
        quantity: quantity ?? this.quantity,
        bookingService: bookingService ?? this.bookingService,
      );

  factory BookingDetailOrder.fromJson(Map<String, dynamic> json) =>
      BookingDetailOrder(
        quantity: json["quantity"],
        bookingService: json["product"] == null
            ? null
            : BookingServiceResponse.fromJson(json["product"]),
      );

  Map<String, dynamic> toJson() => {
        "quantity": quantity,
        "product": bookingService?.toJson(),
      };
}
