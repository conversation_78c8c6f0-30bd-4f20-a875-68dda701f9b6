import 'dart:convert';

import '../../api_tour_response/applicable_time_response.dart';
import '../../api_tour_response/sub_product_response.dart';
import '../supplier_responses/detail_supplier_response.dart';
import 'attribute_response.dart';

DetailBookingServiceResponse detailBookingServiceResponseFromJson(String str) =>
    DetailBookingServiceResponse.fromJson(json.decode(str));

String detailBookingServiceResponseToJson(DetailBookingServiceResponse data) =>
    json.encode(data.toJson());

class DetailBookingServiceResponse {
  bool? status;
  DetailBookingService? data;

  DetailBookingServiceResponse({
    this.status,
    this.data,
  });

  DetailBookingServiceResponse copyWith({
    bool? status,
    DetailBookingService? data,
  }) =>
      DetailBookingServiceResponse(
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory DetailBookingServiceResponse.fromJson(Map<String, dynamic> json) =>
      DetailBookingServiceResponse(
        status: json["status"],
        data: json["data"] == null
            ? null
            : DetailBookingService.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
      };
}

class DetailBookingService {
  int? id;
  String? name;

  String? generalTerms;
  String? usageInstructions;
  String? condition;

  DetailSupplier? supplier;

  List<SubProductResponse>? subProducts;
  int? capacity;

  bool? isExportInvoice;

  List<AttributeResponse>? attributes;
  ApplicableTimeResponse? applicableTime;

  DetailBookingService({
    this.id,
    this.name,
    this.generalTerms,
    this.usageInstructions,
    this.condition,
    this.supplier,
    this.subProducts,
    this.capacity,
    this.isExportInvoice,
    this.attributes,
    this.applicableTime,
  });

  DetailBookingService copyWith({
    int? id,
    String? name,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? generalTerms,
    String? usageInstructions,
    String? condition,
    DetailSupplier? supplier,
    List<SubProductResponse>? subProducts,
    int? capacity,
    bool? isExportInvoice,
    List<AttributeResponse>? attributes,
    ApplicableTimeResponse? applicableTime,
  }) =>
      DetailBookingService(
        id: id ?? this.id,
        name: name ?? this.name,
        generalTerms: generalTerms ?? this.generalTerms,
        usageInstructions: usageInstructions ?? this.usageInstructions,
        condition: condition ?? this.condition,
        supplier: supplier ?? this.supplier,
        subProducts: subProducts ?? this.subProducts,
        capacity: capacity ?? this.capacity,
        isExportInvoice: isExportInvoice ?? this.isExportInvoice,
        attributes: attributes ?? this.attributes,
        applicableTime: applicableTime ?? this.applicableTime,
      );

  factory DetailBookingService.fromJson(Map<String, dynamic> json) =>
      DetailBookingService(
        id: json["id"],
        name: json["name"],
        generalTerms: json["general_terms"],
        usageInstructions: json["usage_instructions"],
        condition: json["condition"],
        supplier: json["supplier"] == null
            ? null
            : DetailSupplier.fromJson(json["supplier"]),
        subProducts: json["sub_products"] == null
            ? []
            : List<SubProductResponse>.from(json["sub_products"]!
                .map((x) => SubProductResponse.fromJson(x))),
        capacity: json["capacity"],
        isExportInvoice: json["is_export_invoice"],
        attributes: json["attributes"] == null
            ? []
            : List<AttributeResponse>.from(
                json["attributes"]!.map((x) => AttributeResponse.fromJson(x))),
        applicableTime: json["applicable_time"] == null
            ? null
            : ApplicableTimeResponse.fromJson(json["applicable_time"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "general_terms": generalTerms,
        "usage_instructions": usageInstructions,
        "condition": condition,
        "supplier": supplier?.toJson(),
        "sub_products": subProducts == null
            ? []
            : List<dynamic>.from(subProducts!.map((x) => x.toJson())),
        "is_export_invoice": isExportInvoice,
        // "attributes": attributes == null ? [] : List<dynamic>.from(attributes!.map((x) => x.toJson())),
        "applicable_time": applicableTime?.toJson(),
      };
}
