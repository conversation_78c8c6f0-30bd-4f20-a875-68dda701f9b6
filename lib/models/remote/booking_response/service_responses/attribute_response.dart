import 'dart:convert';

import 'package:collection/collection.dart';

import '../../../app/attribute_enum.dart';

class AttributeResponse {
  final AttributeEnum type;
  final String name;
  final List<dynamic> values;

  AttributeResponse({
    required this.type,
    required this.name,
    required this.values,
  });

  factory AttributeResponse.fromJson(Map<String, dynamic> json) {
    final type = AttributeEnum.fromValue(json['attribute_id']);
    final valuesJson = json['values'] as List;

    List<dynamic> values;

    switch (type) {
      case AttributeEnum.seatType:
        values = valuesJson.map((v) => AttributeSeatType.fromJson(v)).toList();
        break;
      case AttributeEnum.time:
        values = valuesJson.map((v) => AttributeTime.fromJson(v)).toList();
        break;
      case AttributeEnum.insurance:
        values = valuesJson.map((v) => AttributeInsurance.fromJson(v)).toList();
        break;
      case AttributeEnum.tourGuide:
        values = valuesJson.map((v) => AttributeTourGuide.fromJson(v)).toList();
        break;
      case AttributeEnum.buffet:
        values = valuesJson.map((v) => AttributeBuffet.fromJson(v)).toList();
        break;
    }

    return AttributeResponse(
      type: type,
      name: json['name'],
      values: values,
    );
  }
}

extension ListAttributeResponseX on List<AttributeResponse>{
  List<String>? getTimeSlots() {
    return firstWhereOrNull((x) => x.type == AttributeEnum.time)?.values
        .firstWhereOrNull((v) => v is AttributeTime)
        ?.timeSlots;
  }
}


class AttributeSeatType {
  final String value;
  final int price;
  final int id;

  AttributeSeatType({required this.value, required this.price, required this.id});

  factory AttributeSeatType.fromJson(Map<String, dynamic> json) => AttributeSeatType(
    value: json['value'],
    price: int.parse(json['price']),
    id: json['id'],
  );
}

class AttributeInsurance {
  final String value;
  final int price;
  final int id;

  AttributeInsurance({required this.value, required this.price, required this.id});

  factory AttributeInsurance.fromJson(Map<String, dynamic> json) => AttributeInsurance(
    value: json['value'],
    price: int.parse(json['price']),
    id: json['id'],
  );
}

class AttributeBuffet {
  final String value;
  final int price;
  final int id;

  AttributeBuffet({required this.value, required this.price, required this.id});

  factory AttributeBuffet.fromJson(Map<String, dynamic> json) => AttributeBuffet(
    value: json['value'],
    price: int.parse(json['price']),
    id: json['id'],
  );
}

class AttributeTourGuide {
  final String value;
  final int price;
  final int id;

  AttributeTourGuide({required this.value, required this.price, required this.id});

  factory AttributeTourGuide.fromJson(Map<String, dynamic> json) => AttributeTourGuide(
    value: json['value'],
    price: int.parse(json['price']),
    id: json['id'],
  );
}

class AttributeTime {
  final List<String> timeSlots;
  final int price;
  final int id;

  AttributeTime({
    required this.timeSlots,
    required this.price,
    required this.id,
  });

  factory AttributeTime.fromJson(Map<String, dynamic> json) {
    final value = jsonDecode(json['value']);
    return AttributeTime(
      timeSlots: List<String>.from(value['time_slots']),
      price: int.parse(json['price']),
      id: json['id'],
    );
  }
}
