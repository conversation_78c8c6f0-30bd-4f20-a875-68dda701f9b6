class ProductResponse {
  int? id;
  String? name;
  String? description;
  int? price;
  String? thumbnail;
  String? note;

  ProductResponse({
    this.id,
    this.name,
    this.description,
    this.price,
    this.thumbnail,
    this.note
  });

  ProductResponse copyWith({
    int? id,
    String? name,
    String? description,
    int? price,
    String? thumbnail,
    String? note
  }) =>
      ProductResponse(
        id: id ?? this.id,
        name: name ?? this.name,
        description: description ?? this.description,
        price: price ?? this.price,
        thumbnail: thumbnail ?? this.thumbnail,
        note: note ?? this.note
      );

  factory ProductResponse.fromJson(Map<String, dynamic> json) =>
      ProductResponse(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        price: json["price"],
        thumbnail: json["thumbnail"],
        note: json["note"]
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "price": price,
        "thumbnail": thumbnail,
      };
}
