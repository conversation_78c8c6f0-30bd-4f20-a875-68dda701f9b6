import 'package:tripc_app/models/remote/order_response/my_order_response.dart';

import 'detail_supplier_response.dart';

///Each provider offers different table reservation services
///(e.g. VIP table reservation, 2-person table reservation, etc.)
class BookingServiceResponse {
  int? id;
  String? name;
  int? totalPrice;
  String? description;
  DetailSupplier? supplier;
  List<CustomerTicket>? customerTickets;

  BookingServiceResponse(
      {this.id,
      this.name,
      this.totalPrice,
      this.description,
      this.supplier,
      this.customerTickets});

  BookingServiceResponse copyWith(
          {int? id,
          String? name,
          int? totalPrice,
          String? description,
          DetailSupplier? supplier,
          List<CustomerTicket>? customerTickets}) =>
      BookingServiceResponse(
          id: id ?? this.id,
          name: name ?? this.name,
          totalPrice: totalPrice ?? this.totalPrice,
          description: description ?? this.description,
          supplier: supplier ?? this.supplier,
          customerTickets: customerTickets ?? this.customerTickets);

  factory BookingServiceResponse.fromJson(Map<String, dynamic> json) =>
      BookingServiceResponse(
          id: json["id"],
          name: json["name"],
          totalPrice: json["total_price"],
          description: json["description"],
          supplier: json["supplier"] == null
              ? null
              : DetailSupplier.fromJson(json["supplier"]),
          customerTickets: json["customer_tickets"] == null
              ? []
              : List<CustomerTicket>.from(json["customer_tickets"]
                  .map((x) => CustomerTicket.fromJson(x))));

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "total_price": totalPrice,
        "description": description,
        "supplier": supplier
      };
}
