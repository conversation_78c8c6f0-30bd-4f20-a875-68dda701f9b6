class Supplier {
  final int? id;
  final String? name;
  final String? logoUrl;
  final double? rating;
  final int? totalReviews;
  final String? productTypes;
  final String? fullAddress;
  final bool? isLiked;
  final bool? isPublic;

  Supplier(
      {this.id,
      this.name,
      this.logoUrl,
      this.rating,
      this.totalReviews,
      this.productTypes,
      this.fullAddress,
      this.isLiked,
      this.isPublic});

  // From JSON
  factory Supplier.fromJson(Map<String, dynamic> json) {
    return Supplier(
        id: json['id'],
        name: json['name'],
        logoUrl: json['logo_url'],
        rating: json['rating']?.toDouble(),
        totalReviews: json['total_reviews'],
        productTypes: json['product_types'],
        fullAddress: json['full_address'],
        isLiked: json['is_like'],
        isPublic: json['is_published']);
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'logo_url': logoUrl,
      'rating': rating,
      'total_reviews': totalReviews,
      'product_types': productTypes,
      'full_address': fullAddress,
      'is_like': isLiked
    };
  }

  // CopyWith method
  Supplier copyWith(
      {int? id,
      String? name,
      String? logoUrl,
      double? rating,
      int? totalReviews,
      String? productTypes,
      String? fullAddress,
      bool? isLiked,
      bool? isPublic}) {
    return Supplier(
        id: id ?? this.id,
        name: name ?? this.name,
        logoUrl: logoUrl ?? this.logoUrl,
        rating: rating ?? this.rating,
        totalReviews: totalReviews ?? this.totalReviews,
        productTypes: productTypes ?? this.productTypes,
        fullAddress: fullAddress ?? this.fullAddress,
        isLiked: isLiked ?? this.isLiked,
        isPublic: isPublic ?? this.isPublic);
  }
}
