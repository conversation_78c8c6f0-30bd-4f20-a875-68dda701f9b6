import 'product_response.dart';

class ProductGroupResponse {
  int? id;
  String? name;
  List<ProductResponse>? products;

  ProductGroupResponse({
    this.id,
    this.name,
    this.products,
  });

  ProductGroupResponse copyWith({
    int? id,
    String? name,
    List<ProductResponse>? products,
  }) =>
      ProductGroupResponse(
        id: id ?? this.id,
        name: name ?? this.name,
        products: products ?? this.products,
      );

  factory ProductGroupResponse.fromJson(Map<String, dynamic> json) =>
      ProductGroupResponse(
        id: json["id"],
        name: json["name"],
        products: json["foods"] == null
            ? []
            : List<ProductResponse>.from(
                json["foods"]!.map((x) => ProductResponse.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "foods": products == null
            ? []
            : List<dynamic>.from(products!.map((x) => x.toJson())),
      };
}
