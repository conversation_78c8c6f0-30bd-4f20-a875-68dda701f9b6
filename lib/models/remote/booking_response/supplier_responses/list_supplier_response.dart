import 'package:tripc_app/models/remote/booking_response/supplier_responses/supplier_response.dart';

class ListSupplierResponse {
  final bool status;
  final List<Supplier>? data;
  final int total;

  ListSupplierResponse({
    required this.status,
    required this.data,
    required this.total,
  });

  // From JSON
  factory ListSupplierResponse.fromJson(Map<String, dynamic> json) {
    // Parse 'data' if present and not null, otherwise keep it null
    final dataJson = json['data'] as List<dynamic>?;
    return ListSupplierResponse(
      status: json['status'] as bool,
      data: dataJson
          ?.map((item) => Supplier.fromJson(item as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int,
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'data': data?.map((item) => item.toJson()).toList(),
      'total': total,
    };
  }
}
