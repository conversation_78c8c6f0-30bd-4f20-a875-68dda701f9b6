import 'dart:convert';

import 'product_group_response.dart';
import 'booking_service_response.dart';
import 'work_time_response.dart';

DetailSupplierResponse detailSupplierResponseFromJson(String str) =>
    DetailSupplierResponse.fromJson(json.decode(str));

String detailSupplierResponseToJson(DetailSupplierResponse data) =>
    json.encode(data.toJson());

class DetailSupplierResponse {
  bool? status;
  DetailSupplier? data;

  DetailSupplierResponse({
    this.status,
    this.data,
  });

  DetailSupplierResponse copyWith({
    bool? status,
    DetailSupplier? data,
  }) =>
      DetailSupplierResponse(
        status: status ?? this.status,
        data: data ?? this.data,
      );

  factory DetailSupplierResponse.fromJson(Map<String, dynamic> json) =>
      DetailSupplierResponse(
        status: json["status"],
        data:
            json["data"] == null ? null : DetailSupplier.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
      };
}

class DetailSupplier {
  int? id;
  String? name;
  String? logoUrl;
  int? rating;
  int? totalReviews;
  String? productTypes;
  String? fullAddress;
  String? intro;
  String? policy;
  String? note;
  String? startTime;
  String? endTime;
  List<ProductGroupResponse>? productGroups;
  List<BookingServiceResponse>? bookingSevices;
  WorkingTimeResponse? workingTime;

  DetailSupplier(
      {this.id,
      this.name,
      this.logoUrl,
      this.rating,
      this.totalReviews,
      this.productTypes,
      this.fullAddress,
      this.intro,
      this.policy,
      this.note,
      this.startTime,
      this.endTime,
      this.productGroups,
      this.bookingSevices,
      this.workingTime});

  DetailSupplier copyWith(
          {int? id,
          String? name,
          String? logoUrl,
          int? rating,
          int? totalReviews,
          String? productTypes,
          String? fullAddress,
          String? intro,
          String? policy,
          String? note,
          String? startTime,
          String? endTime,
          List<ProductGroupResponse>? productGroups,
          List<BookingServiceResponse>? seatProducts,
          WorkingTimeResponse? workingTime}) =>
      DetailSupplier(
          id: id ?? this.id,
          name: name ?? this.name,
          logoUrl: logoUrl ?? this.logoUrl,
          rating: rating ?? this.rating,
          totalReviews: totalReviews ?? this.totalReviews,
          productTypes: productTypes ?? this.productTypes,
          fullAddress: fullAddress ?? this.fullAddress,
          intro: intro ?? this.intro,
          policy: policy ?? this.policy,
          note: note ?? this.note,
          productGroups: productGroups ?? this.productGroups,
          bookingSevices: seatProducts ?? bookingSevices,
          startTime: startTime ?? this.startTime,
          endTime: endTime ?? this.endTime,
          workingTime: workingTime ?? this.workingTime);

  factory DetailSupplier.fromJson(Map<String, dynamic> json) => DetailSupplier(
      id: json["id"],
      name: json["name"],
      logoUrl: json["logo_url"],
      rating: json["rating"],
      totalReviews: json["total_reviews"],
      productTypes: json["product_types"],
      fullAddress: json["full_address"],
      intro: json["intro"],
      policy: json["policy"],
      note: json['note'],
      startTime: json['start_time'],
      endTime: json['end_time'],
      productGroups: json["product_groups"] == null
          ? []
          : List<ProductGroupResponse>.from(json["product_groups"]!
              .map((x) => ProductGroupResponse.fromJson(x))),
      bookingSevices: json["seat_products"] == null
          ? []
          : List<BookingServiceResponse>.from(json["seat_products"]!
              .map((x) => BookingServiceResponse.fromJson(x))),
      workingTime: json["working_hours"] == null
          ? null
          : WorkingTimeResponse.fromJson(json["working_hours"]));

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "logo_url": logoUrl,
        "rating": rating,
        "total_reviews": totalReviews,
        "product_types": productTypes,
        "full_address": fullAddress,
        "intro": intro,
        "policy": policy,
        "note": note,
        "start_time": startTime,
        "end_time": endTime,
        "product_groups": productGroups == null
            ? []
            : List<dynamic>.from(productGroups!.map((x) => x.toJson())),
        "seat_products": bookingSevices == null
            ? []
            : List<dynamic>.from(bookingSevices!.map((x) => x.toJson())),
      };
}
