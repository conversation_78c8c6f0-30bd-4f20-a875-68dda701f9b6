class WorkingTimeResponse {
  List<WorkTimeInDate>? monday;
  List<WorkTimeInDate>? tuesday;
  List<WorkTimeInDate>? wednesday;
  List<WorkTimeInDate>? thursday;
  List<WorkTimeInDate>? saturday;
  List<WorkTimeInDate>? friday;
  List<WorkTimeInDate>? sunday;

  WorkingTimeResponse({
    this.friday,
    this.monday,
    this.saturday,
    this.sunday,
    this.thursday,
    this.tuesday,
    this.wednesday,
  });

  WorkingTimeResponse copyWith({
    List<WorkTimeInDate>? friday,
    List<WorkTimeInDate>? monday,
    List<WorkTimeInDate>? saturday,
    List<WorkTimeInDate>? sunday,
    List<WorkTimeInDate>? thursday,
    List<WorkTimeInDate>? tuesday,
    List<WorkTimeInDate>? wednesday,
  }) =>
      WorkingTimeResponse(
        friday: friday ?? this.friday,
        monday: monday ?? this.monday,
        saturday: saturday ?? this.saturday,
        sunday: sunday ?? this.sunday,
        thursday: thursday ?? this.thursday,
        tuesday: tuesday ?? this.tuesday,
        wednesday: wednesday ?? this.wednesday,
      );

  factory WorkingTimeResponse.fromJson(Map<String, dynamic> json) =>
      WorkingTimeResponse(
        friday: json["friday"] == null
            ? []
            : List<WorkTimeInDate>.from(
                json["friday"]!.map((x) => WorkTimeInDate.fromJson(x))),
        monday: json["monday"] == null
            ? []
            : List<WorkTimeInDate>.from(
                json["monday"]!.map((x) => WorkTimeInDate.fromJson(x))),
        saturday: json["saturday"] == null
            ? []
            : List<WorkTimeInDate>.from(
                json["saturday"]!.map((x) => WorkTimeInDate.fromJson(x))),
        sunday: json["sunday"] == null
            ? []
            : List<WorkTimeInDate>.from(
                json["sunday"]!.map((x) => WorkTimeInDate.fromJson(x))),
        thursday: json["thursday"] == null
            ? []
            : List<WorkTimeInDate>.from(
                json["thursday"]!.map((x) => WorkTimeInDate.fromJson(x))),
        tuesday: json["tuesday"] == null
            ? []
            : List<WorkTimeInDate>.from(
                json["tuesday"]!.map((x) => WorkTimeInDate.fromJson(x))),
        wednesday: json["wednesday"] == null
            ? []
            : List<WorkTimeInDate>.from(
                json["wednesday"]!.map((x) => WorkTimeInDate.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "friday": friday == null
            ? []
            : List<dynamic>.from(friday!.map((x) => x.toJson())),
        "monday": monday == null
            ? []
            : List<dynamic>.from(monday!.map((x) => x.toJson())),
        "saturday": saturday == null
            ? []
            : List<dynamic>.from(saturday!.map((x) => x.toJson())),
        "sunday":
            sunday == null ? [] : List<dynamic>.from(sunday!.map((x) => x)),
        "thursday": thursday == null
            ? []
            : List<dynamic>.from(thursday!.map((x) => x.toJson())),
        "tuesday": tuesday == null
            ? []
            : List<dynamic>.from(tuesday!.map((x) => x.toJson())),
        "wednesday": wednesday == null
            ? []
            : List<dynamic>.from(wednesday!.map((x) => x.toJson())),
      };
}

extension WorkingTimeResponseExtension on WorkingTimeResponse {
  List<MapEntry<String, List<WorkTimeInDate>>> get orderedDayEntries {
    return [
      MapEntry('monday', monday ?? []),
      MapEntry('tuesday', tuesday ?? []),
      MapEntry('wednesday', wednesday ?? []),
      MapEntry('thursday', thursday ?? []),
      MapEntry('friday', friday ?? []),
      MapEntry('saturday', saturday ?? []),
      MapEntry('sunday', sunday ?? []),
    ];
  }
}

class WorkTimeInDate {
  int? id;
  int? supplierId;
  String? dayOfWeek;
  String? startTime;
  String? endTime;
  int? slot;
  bool? isClosed;
  bool? isCurrent;

  WorkTimeInDate({
    this.id,
    this.supplierId,
    this.dayOfWeek,
    this.startTime,
    this.endTime,
    this.slot,
    this.isClosed,
    this.isCurrent,
  });

  WorkTimeInDate copyWith({
    int? id,
    int? supplierId,
    String? dayOfWeek,
    String? startTime,
    String? endTime,
    int? slot,
    bool? isClosed,
    bool? isCurrent,
  }) =>
      WorkTimeInDate(
        id: id ?? this.id,
        supplierId: supplierId ?? this.supplierId,
        dayOfWeek: dayOfWeek ?? this.dayOfWeek,
        startTime: startTime ?? this.startTime,
        endTime: endTime ?? this.endTime,
        slot: slot ?? this.slot,
        isClosed: isClosed ?? this.isClosed,
        isCurrent: isCurrent ?? this.isCurrent,
      );

  factory WorkTimeInDate.fromJson(Map<String, dynamic> json) => WorkTimeInDate(
        id: json["id"],
        supplierId: json["supplier_id"],
        dayOfWeek: json["day_of_week"],
        startTime: json["start_time"],
        endTime: json["end_time"],
        slot: json["slot"],
        isClosed: json["is_closed"],
        isCurrent: json["is_current"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "supplier_id": supplierId,
        "day_of_week": dayOfWeek,
        "start_time": startTime,
        "end_time": endTime,
        "slot": slot,
        "is_closed": isClosed,
        "is_current": isCurrent,
      };
}
