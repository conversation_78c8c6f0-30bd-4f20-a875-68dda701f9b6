class TokenResponse {
  TokenResponse({
    this.expiresIn,
    this.accessToken,
  });

  factory TokenResponse.fromJson(Map<String, dynamic> json) => TokenResponse(
        expiresIn: json['expiresIn'],
        accessToken: json['accessToken'],
      );
  TokenResponse copyWith({
    int? expiresIn,
    String? accessToken,
  }) {
    return TokenResponse(
      expiresIn: expiresIn ?? this.expiresIn,
      accessToken: accessToken ?? this.accessToken,
    );
  }

  int? expiresIn;
  String? accessToken;

  Map<String, dynamic> toJson() => {
        'expiresIn': expiresIn,
        'accessToken': accessToken,
      };
}
