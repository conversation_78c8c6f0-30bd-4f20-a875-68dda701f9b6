class SeatProduct {
  final int id;
  final String? name;
  final int totalPrice;
  final String? description;

  SeatProduct({
    required this.id,
    this.name,
    required this.totalPrice,
    this.description,
  });

  factory SeatProduct.fromJson(Map<String, dynamic> json) {
    return SeatProduct(
      id: json['id'],
      name: json['name'],
      totalPrice: json['total_price'],
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'total_price': totalPrice,
      'description': description,
    };
  }
}