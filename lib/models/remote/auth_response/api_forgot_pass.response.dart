import 'dart:convert';

ForgotPassResponse forgotPasslResponseFromJson(String str) =>
    ForgotPassResponse.fromJson(json.decode(str));

String forgotPasslResponseToson(ForgotPassResponse data) =>
    json.encode(data.toJson());

class ForgotPassResponse {
  ForgotPassResponse({
    this.email,
  });
  factory ForgotPassResponse.fromJson(Map<String, dynamic> json) =>
      ForgotPassResponse(
        email: json['data']['destination']
      );
  static ForgotPassResponse getDefault() {
    return ForgotPassResponse(email: null);
  }

  String? email;
  ForgotPassResponse copyWith({
    String? email,
  }) {
    return ForgotPassResponse(
      email: email ?? this.email,
    );
  }

  Map<String, dynamic> toJson() => {
        'data': {
        'destination': email
     }
   };
}

ResetPassResponse resetPasslResponseFromJson(String str) =>
    ResetPassResponse.fromJson(json.decode(str));

String resetPasslResponseToson(ResetPassResponse data) =>
    json.encode(data.toJson());

class ResetPassResponse {
  ResetPassResponse({
    this.data,
    this.message,
    required this.errors
  });
  factory ResetPassResponse.fromJson(Map<String, dynamic> json) =>
      ResetPassResponse(
        data: json['data'],
        message: json['message'],
        errors: json['errors'],
      );
  static ResetPassResponse getDefault() {
    return ResetPassResponse(data: null, message: null, errors: []);
  }

  bool? data;
  String? message;
  List<dynamic> errors = [];
  ResetPassResponse copyWith({
    bool? data,
    String? message,
    List<dynamic>? errors
  }) {
    return ResetPassResponse(
        data: data ?? this.data,
        message: message ?? this.message,
        errors: errors ?? this.errors
    );
  }

  Map<String, dynamic> toJson() => {
        'data': data,
        'message': message,
        'errors': errors
   };
}
