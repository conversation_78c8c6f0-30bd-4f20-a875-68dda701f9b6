import 'dart:convert';
import 'package:tripc_app/models/remote/user_response.dart';

LoginResponse loginResponseFromJson(String str) =>
    LoginResponse.fromJson(json.decode(str));

String loginResponseToJson(LoginResponse data) => json.encode(data.toJson());

class LoginResponse {
  LoginResponse(
      {this.data, this.token, this.refreshToken, this.status = false});
  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
      data: json['data'] == null ? null : UserResponse.fromJson(json['data']),
      status: json['status'] ?? false,
      refreshToken: json['refresh_token'],
      token: json['token']);
  static LoginResponse getDefault() {
    return LoginResponse();
  }

  bool status;
  UserResponse? data;
  String? token;
  String? refreshToken;

  LoginResponse copyWith(
      {bool? status, UserResponse? data, String? token, String? refreshToken}) {
    return LoginResponse(
      data: data ?? this.data,
      token: token ?? this.token,
      status: status ?? this.status,
      refreshToken: refreshToken ?? this.refreshToken,
    );
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'data': data?.toJson(),
        'token': token,
        'refresh_token': refreshToken
      };
}
