class AuthProviderResponse {
  AuthProviderResponse({this.id, this.thirdPartyAuth, this.thirdPartyId});

  factory AuthProviderResponse.fromJson(Map<String, dynamic> json) =>
      AuthProviderResponse(
          id: json['id'],
          thirdPartyAuth: json['third_party_auth'],
          thirdPartyId: json['third_party_id']);
  AuthProviderResponse copyWith(
      {int? id, String? thirdPartyAuth, String? thirdPartyId}) {
    return AuthProviderResponse(
        id: id ?? this.id,
        thirdPartyAuth: thirdPartyAuth ?? this.thirdPartyAuth,
        thirdPartyId: thirdPartyId ?? this.thirdPartyId);
  }

  int? id;
  String? thirdPartyAuth;
  String? thirdPartyId;

  Map<String, dynamic> toJson() => {
        'id': id,
        'third_party_auth': thirdPartyAuth,
        'third_party_id': thirdPartyId
      };
}
