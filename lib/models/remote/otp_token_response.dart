import 'dart:convert';

OtpTokenResponse optTokenResponseFromJson(String str) =>
    OtpTokenResponse.fromJson(json.decode(str));

String optTokenResponseToJson(OtpTokenResponse data) =>
    json.encode(data.toJson());

class OtpTokenResponse {
  OtpTokenResponse({
    this.token,
  });

  factory OtpTokenResponse.fromJson(Map<String, dynamic> json) =>
      OtpTokenResponse(
        token: json['data']['token'],
      );

  OtpTokenResponse copyWith({
    String? token,
  }) {
    return OtpTokenResponse(
      token: token ?? this.token,
    );
  }

  String? token;

  Map<String, dynamic> toJson() => {
        'token': token,
      };
}