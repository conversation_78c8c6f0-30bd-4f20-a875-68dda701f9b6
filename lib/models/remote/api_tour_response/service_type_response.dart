import 'dart:convert';

ServiceTypesSlugResponse serviceTypeResponseFromJson(String str) =>
    ServiceTypesSlugResponse.fromJson(json.decode(str));

String serviceTypeResponseToJson(ServiceTypesSlugResponse data) =>
    json.encode(data.toJson());

class ServiceTypesSlugResponse {
  final bool status;
  final ServiceType? data;

  ServiceTypesSlugResponse({
    required this.status,
    this.data,
  });

  factory ServiceTypesSlugResponse.fromJson(Map<String, dynamic> json) =>
      ServiceTypesSlugResponse(
        status: json["status"] ?? false,
        data: json["data"] == null
            ? null
            : ServiceType.fromJson(json["data"] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data?.toJson(),
  };
}


ServiceTypesResponse serviceTypesResponseFromJson(String str) =>
    ServiceTypesResponse.fromJson(json.decode(str));

String listCategoryResponseToJson(ServiceTypesResponse data) =>
    json.encode(data.toJson());

class ServiceTypesResponse {
  bool status;
  List<ServiceType> data;
  int? total;

  ServiceTypesResponse({
    required this.status,
    this.data = const [],
    this.total,
  });

  factory ServiceTypesResponse.fromJson(Map<String, dynamic> json) =>
      ServiceTypesResponse(
        status: json["status"] ?? false,
        data: json["data"] == null
            ? []
            : List<ServiceType>.from(
            json["data"].map((x) => ServiceType.fromJson(x))),
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": List<dynamic>.from(data.map((x) => x.toJson())),
    "total": total,
  };
}

class ServiceType {
  final int? id;
  final String name;
  final String? slug;
  final int? parentId;
  final List<ServiceType>? children;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const ServiceType({
    this.id,
    required this.name,
    this.slug,
    this.parentId,
    this.children = const [],
    this.createdAt,
    this.updatedAt,
  });

  factory ServiceType.fromJson(Map<String, dynamic> json) =>
      ServiceType(
        id: json["id"],
        name: json["name"],
        parentId: json["parent_id"],
        slug: json["slug"],
        children: json["children"] == null
            ? []
            : List<ServiceType>.from(
            json["children"].map((x) => ServiceType.fromJson(x))),
        createdAt: json["created_at"] !=null ? DateTime.parse(json["created_at"]) : null,
        updatedAt: json["updated_at"] !=null ? DateTime.parse(json["updated_at"]) : null,
      );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "parent_id": parentId,
    "slug": slug,
    "children": children != null ? List<dynamic>.from(children!.map((x) => x.toJson())) : [],
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
  };
}
