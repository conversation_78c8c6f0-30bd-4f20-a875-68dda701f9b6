import 'dart:convert';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';

ListTourResponse listTourResponseFromJson(String str) =>
    ListTourResponse.fromJson(json.decode(str));

String listTourResponseToJson(ListTourResponse data) =>
    json.encode(data.toJson());

class ListTourResponse {
  bool status;
  List<TourResponse> data;
  int? total;

  ListTourResponse({
    required this.status,
    this.data = const [],
    this.total,
  });

  factory ListTourResponse.fromJson(Map<String, dynamic> json) =>
      ListTourResponse(
        status: json["status"] ?? false,
        data: json["data"] == null
            ? []
            : List<TourResponse>.from(
                json["data"].map((x) => TourResponse.fromJson(x))),
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "total": total,
      };
}

DetailedTourResponse detailedTourResponseFromJson(String str) =>
    DetailedTourResponse.fromJson(json.decode(str));

String detailedTourResponseToJson(DetailedTourResponse data) =>
    json.encode(data.toJson());

class DetailedTourResponse {
  bool status;
  TourResponse? data;

  DetailedTourResponse({
    required this.status,
    this.data,
  });

  factory DetailedTourResponse.fromJson(Map<String, dynamic> json) =>
      DetailedTourResponse(
        status: json["status"] ?? false,
        data: json["data"] == null ? null : TourResponse.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {"status": status, "data": data?.toJson()};
}

SavedTourResponse savedTourResponseFromJson(String str) =>
    SavedTourResponse.fromJson(json.decode(str));

String savedTourResponseToJson(SavedTourResponse data) =>
    json.encode(data.toJson());

class SavedTourResponse {
  bool status;
  SavedResponse? data;

  SavedTourResponse({
    required this.status,
    this.data,
  });

  factory SavedTourResponse.fromJson(Map<String, dynamic> json) =>
      SavedTourResponse(
        status: json["status"] ?? false,
        data:
            json["data"] == null ? null : SavedResponse.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {"status": status, "data": data?.toJson()};
}

class SavedResponse {
  bool? isSave;
  SavedResponse({
    this.isSave,
  });
  factory SavedResponse.fromJson(Map<String, dynamic> json) =>
      SavedResponse(isSave: json["is_save"]);

  Map<String, dynamic> toJson() => {"is_save": isSave};
}
