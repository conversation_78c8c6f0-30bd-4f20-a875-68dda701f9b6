import 'dart:convert';

import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/api_tour_response/applicable_time_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/image_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/sub_product_response.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../booking_response/supplier_responses/supplier_response.dart';

TourResponse tourResponseFromJson(String str) =>
    TourResponse.fromJson(json.decode(str));

String tourResponseToJson(TourResponse data) => json.encode(data.toJson());

class TourResponse {
  int? id;
  String? name;
  double? rating;
  int? reviews;
  int? totalPrice;
  String? thumbnail;
  Supplier? supplier;
  int? sale;
  int? returnTcent;
  String? tickerCategory;
  // Location? location;
  String? address;
  bool? like;
  String? videoUrl;
  List<ImageResponse> images;
  String? description;
  String? createdAt;
  String? updatedAt;
  String? validity;
  String? generalTerms;
  String? usageInstructions;
  String? bookingInformation;
  ApplicableTimeResponse? applicableTime;
  int? type;
  int? price;
  String? departureLocation;
  String? hotel;
  String? restaurant;
  String? guide;
  List<SubProductResponse>? subProducts;
  List<Trip>? trip;
  bool? isLike;
  PaymentOptions? paymentOptions;
  String? lastSeenAt;
  bool? isExportInvoice;
  bool? isAllowedRefund;
  int? serviceId;
  String? ticketCategory;
  CategoryResponse? category;
  List<DiscountResponse>? discounts;
  List<AttributeResponse>? attributes;
  String? duration;

  TourResponse(
      {this.id,
      this.name,
      this.rating,
      this.reviews,
      this.totalPrice,
      this.price,
      this.thumbnail,
      this.supplier,
      this.sale,
      this.returnTcent,
      this.tickerCategory,
      // this.location,
      this.address,
      this.like,
      this.videoUrl,
      this.description,
      this.type,
      this.applicableTime,
      this.images = const [],
      this.departureLocation,
      this.hotel,
      this.restaurant,
      this.generalTerms,
      this.bookingInformation,
      this.usageInstructions,
      this.validity,
      this.subProducts,
      this.guide,
      this.trip,
      this.isLike,
      this.paymentOptions,
      this.lastSeenAt,
      this.isExportInvoice,
      this.isAllowedRefund,
      this.createdAt,
      this.updatedAt,
      this.serviceId,
      this.ticketCategory,
      this.category,
      this.discounts,
      this.attributes,
      this.duration
      });

  TourResponse copyWith({
    int? id,
    String? name,
    double? rating,
    int? reviews,
    int? totalPrice,
    String? thumbnail,
    Supplier? supplier,
    int? sale,
    int? returnTcent,
    String? tickerCategory,
    // Location? location,
    String? address,
    bool? like,
    String? videoUrl,
    List<ImageResponse>? images,
    String? description,
    String? createdAt,
    String? updatedAt,
    String? validity,
    String? generalTerms,
    String? bookingInformation,
    String? usageInstructions,
    ApplicableTimeResponse? applicableTime,
    int? type,
    int? price,
    String? departureLocation,
    String? hotel,
    String? restaurant,
    String? guide,
    List<SubProductResponse>? subProducts,
    List<Trip>? trip,
    bool? isLike,
    PaymentOptions? paymentOptions,
    String? lastSeenAt,
    bool? isExportInvoice,
    bool? isAllowedRefund,
    int? serviceId,
    String? ticketCategory,
    CategoryResponse? category,
    List<DiscountResponse>? discounts,
    List<AttributeResponse>? attributes,
    String? duration
  }) {
    return TourResponse(
      id: id ?? this.id,
      name: name ?? this.name,
      rating: rating ?? this.rating,
      reviews: reviews ?? this.reviews,
      totalPrice: totalPrice ?? this.totalPrice,
      thumbnail: thumbnail ?? this.thumbnail,
      supplier: supplier ?? this.supplier,
      sale: sale ?? this.sale,
      returnTcent: returnTcent ?? this.returnTcent,
      tickerCategory: tickerCategory ?? this.tickerCategory,
      // location: location ?? this.location,
      address: address ?? this.address,
      like: like ?? this.like,
      videoUrl: videoUrl ?? this.videoUrl,
      images: images ?? this.images,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      validity: validity ?? this.validity,
      generalTerms: generalTerms ?? this.generalTerms,
      bookingInformation: bookingInformation ?? this.bookingInformation,
      usageInstructions: usageInstructions ?? this.usageInstructions,
      applicableTime: applicableTime ?? this.applicableTime,
      type: type ?? this.type,
      price: price ?? this.price,
      departureLocation: departureLocation ?? this.departureLocation,
      hotel: hotel ?? this.hotel,
      restaurant: restaurant ?? this.restaurant,
      guide: guide ?? this.guide,
      subProducts: subProducts ?? this.subProducts,
      trip: trip ?? this.trip,
      isLike: isLike ?? this.isLike,
      paymentOptions: paymentOptions ?? this.paymentOptions,
      lastSeenAt: lastSeenAt ?? this.lastSeenAt,
      isExportInvoice: isExportInvoice ?? this.isExportInvoice,
      isAllowedRefund: isAllowedRefund ?? this.isAllowedRefund,
      serviceId: serviceId ?? this.serviceId,
      ticketCategory: tickerCategory ?? this.ticketCategory,
      category: category ?? this.category,
      discounts: discounts ?? this.discounts,
      attributes: attributes ?? this.attributes,
      duration: duration ?? this.duration
    );
  }

  factory TourResponse.fromJson(Map<String, dynamic> json) => TourResponse(
        id: json["id"],
        name: json["name"],
        rating: json["rating"]?.toDouble(),
        reviews: json["reviews"],
        totalPrice: json["total_price"],
        price: json["price"],
        thumbnail: json["thumbnail"],
        supplier: json["supplier"] == null
            ? null
            : Supplier.fromJson(json["supplier"]),
        sale: json["sale"],
        returnTcent: json["return_tcent"],
        tickerCategory: json["ticker_category"],
        // location: json["location"] == null
        //     ? null
        //     : Location.fromJson(json["location"]),
        address: json["address"],
        like: json["like"],
        videoUrl: json["video_url"],
        description: json["description"],
        applicableTime: json["applicable_time"] == null
            ? null
            : ApplicableTimeResponse.fromJson(json["applicable_time"]),
        type: json["type"],
        departureLocation: json["departure_location"],
        hotel: json["hotel"],
        restaurant: json["restaurant"],
        guide: json["guide"],
        generalTerms: json["general_terms"],
        usageInstructions: json["usage_instructions"],
        bookingInformation: json["booking_information"],
        validity: json["validity"],
        subProducts: json["sub_products"] == null
            ? []
            : List<SubProductResponse>.from(json["sub_products"]
                .map((x) => SubProductResponse.fromJson(x))),
        images: json["images"] == null
            ? []
            : List<ImageResponse>.from(
                json["images"].map((x) => ImageResponse.fromJson(x))),
        trip: json["trip"] == null
            ? []
            : List<Trip>.from(json["trip"].map((x) => Trip.fromJson(x))),
        isLike: json["is_like"],
        paymentOptions: json["payment_options"] == null
            ? null
            : PaymentOptions.fromJson(json["payment_options"]),
        lastSeenAt: json["last_seen_at"],
        isExportInvoice: json["is_export_invoice"],
        isAllowedRefund: json["is_allowed_refund"],
        serviceId: json["service_id"],
        ticketCategory: json["ticket_category"],
        duration: json["duration"],
        category: json["category"] == null
            ? null
            : CategoryResponse.fromJson(json["category"]),
        discounts: json["discounts"] == null
            ? []
            : List<DiscountResponse>.from(
                json["discounts"].map((x) => DiscountResponse.fromJson(x))),
        attributes: json["attributes"] == null
            ? []
            : List<AttributeResponse>.from(
                json["attributes"].map((x) => AttributeResponse.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "rating": rating,
        "reviews": reviews,
        "total_price": totalPrice,
        "price": price,
        "thumbnail": thumbnail,
        "supplier": supplier?.toJson(),
        "sale": sale,
        "return_tcent": returnTcent,
        "ticker_category": tickerCategory,
        // "location": location?.toJson(),
        "address": address,
        "like": like,
        "video_url": videoUrl,
        "description": description,
        "images": images,
        "departure_location": departureLocation,
        "hotel": hotel,
        "restaurant": restaurant,
        "guide": guide,
        "general_terms": generalTerms,
        "usage_instructions": usageInstructions,
        "booking_information": bookingInformation,
        "validity": validity,
        "sub_products": subProducts == null
            ? []
            : List<dynamic>.from(subProducts!.map((x) => x.toJson())),
        'applicable_time': applicableTime?.toJson(),
        'is_like': isLike,
        'payment_options': paymentOptions?.toJson(),
        "is_export_invoice": isExportInvoice,
        "ticket_category": ticketCategory,
        "category": category?.toJson(),
        "duration": duration
      };

  int get salePrice {
    return ((totalPrice ?? price ?? 0) * (100 - (sale ?? 0)) / 100).round();
  }

  String get tourTime {
    if (applicableTime == null) {
      return '-';
    }
    final time = applicableTime!.time ?? [];
    if ((time.isEmpty)) {
      return '-';
    }
    return '${timeOfDayFromTimeFrame(time.first)} - ${timeOfDayFromTimeFrame(time.last)}';
  }

  TripCServiceCategory get serviceType {
    return TripCServiceCategory.getByValue(type ?? 2);
  }
}

// class Supplier {
//   int? id;
//   String? name;

//   Supplier({
//     this.id,
//     this.name,
//   });

//   factory Supplier.fromJson(Map<String, dynamic> json) => Supplier(
//         id: json["id"],
//         name: json["name"],
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "name": name,
//       };
// }

class Trip {
  Trip({
    this.time,
    this.activity,
  });

  String? time;
  String? activity;

  factory Trip.fromJson(Map<String, dynamic> json) => Trip(
        time: json["time"],
        activity: json["activity"],
      );

  Map<String, dynamic> toJson() => {
        "time": time,
        "activity": activity,
      };
}

class PaymentOptions {
  PaymentOptions({
    required this.payLater,
    required this.tcent,
    required this.vnpay,
  });
  bool payLater;
  bool tcent;
  bool vnpay;

  factory PaymentOptions.fromJson(Map<String, dynamic> json) => PaymentOptions(
        payLater: json["pay_later"] == 1,
        tcent: json["tcent"] == 1,
        vnpay: json["vnpay"] == 1,
      );

  Map<String, dynamic> toJson() => {
        "pay_later": payLater ? 1 : 0,
        "tcent": tcent ? 1 : 0,
        "vnpay": vnpay ? 1 : 0,
      };
}

class CategoryResponse {
  final int? id;
  final String? categoryName;
  final String? groupName;

  CategoryResponse({this.id, this.categoryName, this.groupName});

  factory CategoryResponse.fromJson(Map<String, dynamic> json) {
    return CategoryResponse(
      id: json['id'] as int?,
      categoryName: json['categoryName'] as String?,
      groupName: json['groupName'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'categoryName': categoryName,
      'groupName': groupName,
    };
  }
}

class DiscountResponse {
  final int? id;
  final int? audienceType;
  final int? minQuantity;
  final int? maxQuantity;
  final String? discountValue;
  final String? unit;

  DiscountResponse({
    this.id,
    this.audienceType,
    this.minQuantity,
    this.maxQuantity,
    this.discountValue,
    this.unit,
  });

  factory DiscountResponse.fromJson(Map<String, dynamic> json) {
    return DiscountResponse(
      id: json['id'],
      audienceType: json['audience_type'],
      minQuantity: json['min_quantity'],
      maxQuantity: json['max_quantity'],
      discountValue: json['discount_value'],
      unit: json['unit'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'audience_type': audienceType,
      'min_quantity': minQuantity,
      'max_quantity': maxQuantity,
      'discount_value': discountValue,
      'unit': unit,
    };
  }
}

class AttributeResponse {
  final int? attributeId;
  final String? name;
  final List<AttributeValue>? values;
  final int? visibility;
  final TourResponseAttibuteValueType? type;

  AttributeResponse(
      {this.attributeId, this.name, this.values, this.visibility, this.type});

  factory AttributeResponse.fromJson(Map<String, dynamic> json) {
    final attributeValueType = getByValue(json['Type']);

    return AttributeResponse(
        attributeId: json['attribute_id'],
        name: json['name'],
        values: (json['values'] as List?)
            ?.map((e) => AttributeValue.fromJson(e, attributeValueType))
            .toList(),
        visibility: json['visibility'],
        type: attributeValueType);
  }

  Map<String, dynamic> toJson() {
    return {
      'attribute_id': attributeId,
      'name': name,
      'values': values?.map((e) => e.toJson()).toList(),
      'visibility': visibility,
      'type': type,
    };
  }
}

class AttributeValue {
  final String? seatType;
  final ApplicableTimeResponse? specialPriceTimeFrame;
  final String? price;
  final String? priceType;
  final int? id;

  AttributeValue({
    this.seatType,
    this.specialPriceTimeFrame,
    this.price,
    this.priceType,
    this.id,
  });

  factory AttributeValue.fromJson(
      Map<String, dynamic> json, TourResponseAttibuteValueType type) {
    if (type == TourResponseAttibuteValueType.time) {
      return AttributeValue(
        specialPriceTimeFrame: json['value'] != null
            ? ApplicableTimeResponse.fromJson(jsonDecode(json['value']))
            : null,
        price: json['price'],
        priceType: json['price_type'],
        id: json['id'],
      );
    }
    return AttributeValue(
      seatType: json['value'],
      price: json['price'],
      priceType: json['price_type'],
      id: json['id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'value': specialPriceTimeFrame != null
          ? specialPriceTimeFrame?.toJson()
          : seatType,
      'price': price,
      'price_type': priceType,
      'id': id,
    };
  }
}

enum TourResponseAttibuteValueType { time, text }

TourResponseAttibuteValueType getByValue(String value) {
  if (value == 'time') {
    return TourResponseAttibuteValueType.time;
  }
  return TourResponseAttibuteValueType.text;
}
