import 'dart:convert';

import 'package:tripc_app/utils/app_extension.dart';

ApplicableTimeResponse applicableTimeResponseFromJson(String str) =>
    ApplicableTimeResponse.fromJson(json.decode(str));

String applicableTimeResponseToJson(ApplicableTimeResponse data) =>
    json.encode(data.toJson());

class ApplicableTimeResponse {
  String? startDate;
  String? endDate;
  List<String>? time;

  ApplicableTimeResponse({
    this.startDate,
    this.endDate,
    this.time,
  });

  factory ApplicableTimeResponse.fromJson(Map<String, dynamic> json) =>
      ApplicableTimeResponse(
          startDate: json["start_date"],
          endDate: json["end_date"],
          time: json['time_slots'] == null ? [] : List<String>.from(json['time_slots']));

  Map<String, dynamic> toJson() => {"start_date": startDate, "end_date": endDate, "time_slots": time};

  DateTime get startDateTime {
    if (startDate == null) {
      return DateTime.now();
    }
    return startDate!.convertToDateTime;
  }

  DateTime get currentDate {
    final now = DateTime.now();
    if (startDate == null) return now;
    return startDateTime.isBefore(now) ? now : startDateTime;
  }

  DateTime get endDateTime {
    if (endDate == null) {
      return DateTime.now();
    }
    return endDate!.convertToDateTime;
  }

  List<DateTime> get listApplicableTime {
    if (startDate == null || endDate == null) {
      return [];
    }
    List<DateTime> dates = [];
    final now = DateTime.now();
    DateTime currentDate = startDateTime.isBefore(now) ? now : startDateTime;
    while (currentDate.isBefore(endDateTime)) {
      dates.add(currentDate);
      currentDate = currentDate.add(const Duration(days: 1));
    }
    if (currentDate.day == endDateTime.day) {
      dates.add(currentDate);
    }
    return dates;
  }
}
