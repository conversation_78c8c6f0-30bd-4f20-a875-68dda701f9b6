import 'dart:convert';
import '../booking_response/supplier_responses/supplier_response.dart';

SubProductResponse subProductResponseFromJson(String str) =>
    SubProductResponse.fromJson(json.decode(str));

String subProductResponseToJson(SubProductResponse data) =>
    json.encode(data.toJson());

class SubProductResponse {
  final int? id;
  final AudienceResponse? audience;
  final int? price;
  final int? quantity;
  final int stock; //use for add passenger quantity

  SubProductResponse({
    this.id,
    this.audience,
    this.price,
    this.quantity,
    this.stock = 0,
  });

  int get ticketPrice {
    return stock * (price ?? 0);
  }

  SubProductResponse copyWith({
    int? id,
    AudienceResponse? audience,
    int? price,
    int? quantity,
    int? stock,
  }) {
    return SubProductResponse(
      id: id ?? this.id,
      audience: audience ?? this.audience,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      stock: stock ?? this.stock,
    );
  }

  factory SubProductResponse.fromJson(Map<String, dynamic> json) {
    return SubProductResponse(
      id: json["id"],
      audience: json["audience"] == null
          ? null
          : AudienceResponse.fromJson(json["audience"]),
      price: json["price"],
      quantity: json["quantity"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "audience": audience?.toJson(),
      "price": price,
      "quantity": quantity,
    };
  }
}

class SubService {
  int? id;
  String? name;
  int? price;
  int? comission;
  Supplier? supplier;
  int? status;
  int? quantity;

  SubService({
    this.id,
    this.name,
    this.price,
    this.comission,
    this.supplier,
    this.status,
    this.quantity,
  });

  factory SubService.fromJson(Map<String, dynamic> json) => SubService(
        id: json["id"],
        name: json["name"],
        price: json["price"],
        comission: json["comission"],
        supplier: json["supplier"] == null
            ? null
            : Supplier.fromJson(json["supplier"]),
        status: json["status"],
        quantity: json["quantity"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "price": price,
        "comission": comission,
        "supplier": supplier?.toJson(),
        "status": status,
        "quantity": quantity,
      };
}

SeatResponse seatResponseFromJson(String str) =>
    SeatResponse.fromJson(json.decode(str));

String seatResponseToJson(SeatResponse data) => json.encode(data.toJson());

class SeatResponse {
  int? id;
  String? name;

  SeatResponse({
    this.id,
    this.name,
  });

  factory SeatResponse.fromJson(Map<String, dynamic> json) => SeatResponse(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {"id": id, "name": name};
}

AudienceResponse audienceResponseFromJson(String str) =>
    AudienceResponse.fromJson(json.decode(str));

String audienceResponseToJson(AudienceResponse data) =>
    json.encode(data.toJson());

class AudienceResponse {
  int? id;
  String? name;
  int? isDefault;

  AudienceResponse({
    this.id,
    this.name,
    this.isDefault,
  });

  factory AudienceResponse.fromJson(Map<String, dynamic> json) =>
      AudienceResponse(
        id: json["id"],
        name: json["name"],
        isDefault: json["is_default"]
      );

  Map<String, dynamic> toJson() => {"id": id, "name": name, "is_default": isDefault};
}
