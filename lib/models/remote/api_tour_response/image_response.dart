import 'dart:convert';

ImageResponse imageResponseFromJson(String str) =>
    ImageResponse.fromJson(json.decode(str));

String imageResponseToJson(ImageResponse data) => json.encode(data.toJson());

class ImageResponse {
  int? id;
  String? url;
  int? productId;
  String? createdAt;

  ImageResponse({
    this.id,
    this.url,
    this.productId,
    this.createdAt,
  });

  factory ImageResponse.fromJson(Map<String, dynamic> json) => ImageResponse(
      id: json["id"],
      url: json["url"],
      productId: json["product_id"],
      createdAt: json["created_at"]);

  Map<String, dynamic> toJson() =>
      {"id": id, "url": url, "product_id": productId, "created_at": createdAt};
}
