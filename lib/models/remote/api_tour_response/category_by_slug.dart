import 'dart:convert';

CategoryBySlugResponse categoryBySlugResponseFromJson(String str) => CategoryBySlugResponse.fromJson(json.decode(str));

String categoryBySlugResponseToJson(CategoryBySlugResponse data) => json.encode(data.toJson());

class CategoryBySlugResponse {
    bool? status;
    CategoryBySlug? data;

    CategoryBySlugResponse({
        this.status,
        this.data,
    });

    CategoryBySlugResponse copyWith({
        bool? status,
        CategoryBySlug? data,
    }) => 
        CategoryBySlugResponse(
            status: status ?? this.status,
            data: data ?? this.data,
        );

    factory CategoryBySlugResponse.fromJson(Map<String, dynamic> json) => CategoryBySlugResponse(
        status: json["status"],
        data: json["data"] == null ? null : CategoryBySlug.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
    };
}

class CategoryBySlug {
    int? id;
    String? name;
    String? slug;
    int? parentId;
    List<CategoryBySlug>? children;
    DateTime? createdAt;
    DateTime? updatedAt;

    CategoryBySlug({
        this.id,
        this.name,
        this.slug,
        this.parentId,
        this.children,
        this.createdAt,
        this.updatedAt,
    });

    CategoryBySlug copyWith({
        int? id,
        String? name,
        String? slug,
        int? parentId,
        List<CategoryBySlug>? children,
        DateTime? createdAt,
        DateTime? updatedAt,
    }) => 
        CategoryBySlug(
            id: id ?? this.id,
            name: name ?? this.name,
            slug: slug ?? this.slug,
            parentId: parentId ?? this.parentId,
            children: children ?? this.children,
            createdAt: createdAt ?? this.createdAt,
            updatedAt: updatedAt ?? this.updatedAt,
        );

    factory CategoryBySlug.fromJson(Map<String, dynamic> json) => CategoryBySlug(
        id: json["id"],
        name: json["name"],
        slug: json["slug"],
        parentId: json["parent_id"],
        children: json["children"] == null ? [] : List<CategoryBySlug>.from(json["children"]!.map((x) => CategoryBySlug.fromJson(x))),
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "slug": slug,
        "parent_id": parentId,
        "children": children == null ? [] : List<dynamic>.from(children!.map((x) => x.toJson())),
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
    };
}
