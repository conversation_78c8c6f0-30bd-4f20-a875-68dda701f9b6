import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/membership_default_status_enum.dart';
import 'package:tripc_app/models/app/tripc_membership_rank.dart';
import 'package:tripc_app/models/app/tripc_tier.dart';
import 'package:tripc_app/utils/app_extension.dart';

class MembershipModel {
  MembershipModel(
      {this.id,
      this.ownerUserId,
      this.userId,
      this.isSelected,
      this.isDefault,
      this.tcent,
      this.passcode,
      this.number,
      this.rank,
      this.createdAt,
      this.status,
      this.price,
      this.expiredDate,
      this.isExistPasscode});

  factory MembershipModel.fromJson(Map<String, dynamic> json) =>
      MembershipModel(
        createdAt: json['created_at'],
        id: json['id'],
        ownerUserId: json['owner_user_id'],
        userId: json['user_id'],
        isSelected: json['is_selected'],
        isDefault: json['is_default'],
        tcent: json['tcent'],
        passcode: json['passcode'],
        number: json['number'],
        rank: json['rank'],
        status: json['status'],
        price: json['price'],
        expiredDate: json['expired_date'],
        isExistPasscode: json['is_exist_passcode'],
      );

  get accessToken => null;
  MembershipModel copyWith(
      {String? createdAt,
      int? id,
      int? ownerUserId,
      int? userId,
      int? isSelected,
      int? isDefault,
      int? tcent,
      String? passcode,
      int? number,
      int? rank,
      String? status,
      int? price,
      String? expiredDate,
      int? isExistPasscode}) {
    return MembershipModel(
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        ownerUserId: ownerUserId ?? this.ownerUserId,
        userId: userId ?? this.userId,
        isSelected: isSelected ?? this.isSelected,
        isDefault: isDefault ?? this.isDefault,
        tcent: tcent ?? this.tcent,
        passcode: passcode ?? this.passcode,
        number: number ?? this.number,
        rank: rank ?? this.rank,
        status: status ?? this.status,
        price: price ?? this.price,
        expiredDate: expiredDate ?? this.expiredDate);
  }

  int? id;
  int? ownerUserId;
  int? userId;
  int? isSelected;
  int? isDefault;
  int? tcent;
  String? passcode;
  int? number;
  int? rank;
  String? createdAt;
  String? status;
  int? price;
  String? expiredDate;
  int? isExistPasscode;

  Map<String, dynamic> toJson() => {
        'created_at': createdAt,
        'id': id,
        'owner_user_id': ownerUserId,
        'user_id': userId,
        'is_selected': isSelected,
        'is_default': isDefault,
        'tcent': tcent,
        'passcode': passcode,
        'number': number,
        'rank': rank,
        'status': status,
        'price': price,
        'expired_date': expiredDate,
        'is_exist_passcode': isExistPasscode
      };

  String receiveTcentText(BuildContext context) {
    return '${context.strings.text_receive_now} ${tcent?.tcent}';
  }

  TripcMembershipRank get rankType {
    return TripcMembershipRank.getByValue(1);
  }

  int get vat {
    return ((price ?? 0) * 0.1).toInt();
  }

  int get priceWithoutTax {
    return (price ?? 0) - vat;
  }

  bool get hasPasscode {
    return isExistPasscode == 1;
  }

  bool get isSelectedMbs {
    return isSelected == 1;
  }

  bool get isDefaultMbs {
    return isDefault == 1;
  }

  MembershipDefaultStatus get defaultStatus {
    return MembershipDefaultStatus.getByValue(isDefault ?? 0);
  }

  TripcTierType get tierType {
    return TripcTierType.getByValue(rank ?? 1);
  }
}
