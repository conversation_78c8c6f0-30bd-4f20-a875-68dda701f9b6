import 'dart:convert';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_model.dart';

DetailedMembershipResponse detailedMembershipFromJson(String str) =>
    DetailedMembershipResponse.fromJson(json.decode(str));

String detailedMembershipToJson(DetailedMembershipResponse data) =>
    json.encode(data.toJson());

class DetailedMembershipResponse {
  bool status;
  MembershipModel? data;

  DetailedMembershipResponse({required this.status, this.data});

  factory DetailedMembershipResponse.fromJson(Map<String, dynamic> json) =>
      DetailedMembershipResponse(
          status: json["status"] ?? false,
          data: json['data'] == null
              ? null
              : MembershipModel.fromJson(json['data']));

  Map<String, dynamic> toJson() => {"status": status, "data": data?.toJson()};
}
