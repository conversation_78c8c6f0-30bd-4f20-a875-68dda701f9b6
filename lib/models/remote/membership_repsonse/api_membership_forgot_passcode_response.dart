import 'dart:convert';

MembershipForgotPasscodeResponse membershipForgotPasscodeFromJson(String str) =>
    MembershipForgotPasscodeResponse.fromJson(json.decode(str));

String membershipForgotPasscodeToJson(MembershipForgotPasscodeResponse data) =>
    json.encode(data.toJson());

class MembershipForgotPasscodeResponse {
  bool status;
  String? error;
  String? token;

  MembershipForgotPasscodeResponse({required this.status, this.error, this.token});

  factory MembershipForgotPasscodeResponse.fromJson(Map<String, dynamic> json) =>
      MembershipForgotPasscodeResponse(
          status: json["status"] ?? false,
          error: json["error"] ?? '',
          token: json['data']['token'],
      );

  Map<String, dynamic> toJson() => {"status": status, "error": error, "token": token};
}
