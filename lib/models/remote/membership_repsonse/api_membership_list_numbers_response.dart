import 'dart:convert';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_model.dart';

MembershipListNumbersResponse membershipListNumbersFromJson(String str) =>
    MembershipListNumbersResponse.fromJson(json.decode(str));

String membershipListNumbersToJson(MembershipListNumbersResponse data) =>
    json.encode(data.toJson());

class MembershipListNumbersResponse {
  bool status;
  List<MembershipModel> data;
  int? total;

  MembershipListNumbersResponse({
    required this.status,
    this.data = const [],
    this.total,
  });

  factory MembershipListNumbersResponse.fromJson(Map<String, dynamic> json) =>
      MembershipListNumbersResponse(
        status: json["status"] ?? false,
        data: json["data"] == null
            ? []
            : List<MembershipModel>.from(
                json["data"].map((x) => MembershipModel.fromJson(x))),
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "total": total,
      };
}
