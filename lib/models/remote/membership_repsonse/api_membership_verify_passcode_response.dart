import 'dart:convert';

VerifyPasscodeResponse verifyPasscodeFromJson(String str) =>
    VerifyPasscodeResponse.fromJson(json.decode(str));

class VerifyPasscodeResponse {
  bool status;
  int remainingAttempts;

  VerifyPasscodeResponse(
      {required this.status, required this.remainingAttempts});

  factory VerifyPasscodeResponse.fromJson(Map<String, dynamic> json) =>
      VerifyPasscodeResponse(
          status: json['data']['valid'] ?? false,
          remainingAttempts: json['data']['remaining_attempts'] ?? 0);
}
