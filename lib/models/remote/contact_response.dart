import 'dart:convert';

ContactResponse contactResponseFromJson(String str) =>
    ContactResponse.fromJson(json.decode(str));

String contactResponseToJson(ContactResponse data) =>
    json.encode(data.toJson());

class ContactResponse {
  ContactResponse({
    this.id,
    this.fullname,
    this.email,
    this.phone,
    this.isDefault,
  });

  factory ContactResponse.fromJson(Map<String, dynamic> json) =>
      ContactResponse(
        id: json['id'],
        fullname: json['fullname'],
        email: json['email'],
        phone: json['phone'],
        isDefault: json['is_default'],
      );

  ContactResponse copyWith({
    String? fullname,
    String? email,
    String? phone,
    int? isDefault,
  }) {
    return ContactResponse(
      fullname: fullname ?? this.fullname,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  int? id;
  String? email;
  String? phone;
  String? fullname;
  int? isDefault;

  Map<String, dynamic> toJson() => {
        'id': id,
        'email': email,
        'phone': phone,
        'full_name': fullname,
        'is_default': isDefault,
      };
}


ListContactResponse listContactResponseFromJson(String str) =>
    ListContactResponse.fromJson(json.decode(str));

String listContactResponseToJson(ListContactResponse data) =>
    json.encode(data.toJson());

class ListContactResponse {
  bool status;
  List<ContactResponse> data;
  int? total;

  ListContactResponse({
    required this.status,
    this.data = const [],
    this.total,
  });

  factory ListContactResponse.fromJson(Map<String, dynamic> json) =>
      ListContactResponse(
        status: json["status"] ?? false,
        data: json["data"] == null
            ? []
            : List<ContactResponse>.from(
                json["data"].map((x) => ContactResponse.fromJson(x))),
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "total": total,
      };
}

