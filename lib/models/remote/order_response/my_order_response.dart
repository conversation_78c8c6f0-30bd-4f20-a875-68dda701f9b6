import 'dart:convert';

import 'package:tripc_app/models/app/order_type_enum.dart';
import 'package:tripc_app/models/app/payment_status.dart';
import 'package:tripc_app/models/app/refund_status.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/tour-payment/views/electronic_invoice.dart';

import '../api_tour_response/sub_product_response.dart';

MyOrderResponse myOrderResponseFromJson(String str) =>
    MyOrderResponse.fromJson(json.decode(str));

String myOrderResponseToJson(MyOrderResponse data) =>
    json.encode(data.toJson());

class MyOrderResponse {
  bool status;
  List<OrderResponse> data;
  int? total;

  MyOrderResponse({
    required this.status,
    this.data = const [],
    this.total,
  });

  factory MyOrderResponse.fromJson(Map<String, dynamic> json) =>
      MyOrderResponse(
          status: json["status"] ?? false,
        data: json["data"] == null
            ? []
            : List<OrderResponse>.from(
            json["data"].map((x) => OrderResponse.fromJson(x))),
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data,
    "total": total,
  };
}

class OrderResponse {
  final int? id;
  final String? orderCode;
  final String? title;
  final dynamic customer;
  final OrderTypeEnum? orderType;
  final String? description;
  final String? departureTime;
  final int? totalPrice;
  final String? sku;
  final PaymentStatus? paymentStatus;
  final String? createdAt;
  final int? expiredTime;
  final OrderStatus? orderStatus;
  final int? tcentBack;

  OrderResponse({
    this.id,
    this.orderCode,
    this.title,
    this.customer,
    this.orderType,
    this.description,
    this.departureTime,
    this.totalPrice,
    this.sku,
    this.paymentStatus,
    this.createdAt,
    this.expiredTime,
    this.orderStatus,
    this.tcentBack,
  });

  int get pendingTime {
    if (createdAt == null) return 0;
    DateTime time = DateTime.parse(createdAt!).toLocal();
    DateTime now = DateTime.now();
    DateTime endTime = time.add(const Duration(minutes: 10));
    int remainingSeconds = endTime.difference(now).inSeconds;
    return remainingSeconds > 0 ? remainingSeconds : 0;
  }

  factory OrderResponse.fromJson(Map<String, dynamic> json) {
    return OrderResponse(
      id: json['id'],
      orderCode: json['order_code'],
      title: json['title'],
      customer: json['customer'],
      orderType: OrderTypeEnum.getByValue(json['order_type']),
      description: json['description'],
      departureTime: json['departure_time'],
      totalPrice: json['total_price'],
      sku: json['sku'],
      paymentStatus: PaymentStatus.getByValue(json['payment_status']),
      createdAt: json['created_at'],
      expiredTime: json['expired_time'],
      orderStatus: OrderStatus.byName(json['order_status']),
      tcentBack: json['tcent_back']
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'customer': customer,
      'order_type': orderType,
      'description': description,
      'departure_time': departureTime,
      'total_price': totalPrice,
      'sku': sku,
      'payment_status': paymentStatus,
      'created_at': createdAt,
      'expired_time': expiredTime,
      'order_status': orderStatus,
      'tcent_back': tcentBack,
      'order_code': orderCode
    };
  }
}

OrderDetailResponse orderDetailResponseFromJson(String str) =>
    OrderDetailResponse.fromJson(json.decode(str));

String orderDetailResponseToJson(OrderDetailResponse data) =>
    json.encode(data.toJson());

class OrderDetailResponse {
  bool status;
  OrderDetail? data;

  OrderDetailResponse({
    required this.status,
    this.data,
  });

  factory OrderDetailResponse.fromJson(Map<String, dynamic> json) =>
      OrderDetailResponse(
        status: json["status"] ?? false,
        data: OrderDetail.fromJson(json['data']),
      );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data,
  };
}

class OrderDetail {
  final int? id;
  final String? orderCode;
  final OrderTypeEnum? orderType;
  final String? description;
  final String? departureTime;
  final int? totalPrice;
  final String? sku;
  final PaymentStatus? paymentStatus;
  final String? createdAt;
  final int? expiredTime;
  final List<OrderItemResponse>? order;
  final InvoiceResponse? invoice;
  final RefundResponse? refund;
  final bool? isExportInvoice;
  final bool? isAllowRefund;
  final String? validity;
  final String? bookingInformation;
  final String? generalTerms;
  final String? usageInstructions;
  final String? condition;
  final OrderStatus? orderStatus;
  final String? transactionId;
  final ContactResponse? contact;

  OrderDetail({
    this.id,
    this.orderCode,
    this.orderType,
    this.description,
    this.departureTime,
    this.totalPrice,
    this.sku,
    this.paymentStatus,
    this.createdAt,
    this.expiredTime,
    this.order,
    this.invoice,
    this.refund,
    this.isExportInvoice,
    this.isAllowRefund,
    this.validity,
    this.bookingInformation,
    this.generalTerms,
    this.usageInstructions,
    this.condition,
    this.orderStatus,
    this.transactionId,
    this.contact
  });

  factory OrderDetail.fromJson(Map<String, dynamic> json) {
    return OrderDetail(
      id: json['id'],
      orderCode: json['order_code'],
      orderType: OrderTypeEnum.getByValue(json['order_type']),
      description: json['description'],
      departureTime: json['departure_time'],
      totalPrice: json['total_price'],
      sku: json['sku'],
      paymentStatus: PaymentStatus.getByValue(json['payment_status']),
      createdAt: json['created_at'],
      expiredTime: json['expired_time'],
      order: (json['order'] as List?)?.map((e) => OrderItemResponse.fromJson(e)).toList(),
      invoice: json['invoice'] == null ? null : InvoiceResponse.fromJson(json['invoice']),
      refund: json['refund'] == null ? null : RefundResponse.fromJson(json['refund']),
      isExportInvoice: json["is_export_invoice"],
      isAllowRefund: json["is_allow_refund"],
      validity: json["validity"],
      bookingInformation: json["booking_information"],
      generalTerms: json["general_terms"],
      usageInstructions: json["usage_instructions"],
      condition: json["condition"],
      orderStatus: OrderStatus.byName(json["order_status"]),
      transactionId: json['transaction_id'],
      contact: json['contact'] == null ? null : ContactResponse.fromJson(json['contact'])
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_type': orderType,
      'description': description,
      'departure_time': departureTime,
      'total_price': totalPrice,
      'sku': sku,
      'payment_status': paymentStatus,
      'created_at': createdAt,
      'expired_time': expiredTime,
      'order': order?.map((e) => e.toJson()).toList(),
      'invoice': invoice?.toJson(),
      'refund': refund?.toJson(),
      'is_export_invoice': isExportInvoice,
      'is_allow_refund': isAllowRefund,
      'validity': validity,
      'general_terms': generalTerms,
      'usage_instructions': usageInstructions,
      'order_status': orderStatus?.name,
      'transaction_id': transactionId
    };
  }
}

class OrderItemResponse {
  final int? quantity;
  final TourResponse? product;
  final SubProductResponse? subProduct;
  final List<CustomerTicket>? customerTickets;

  OrderItemResponse({
    this.quantity,
    this.product,
    this.subProduct,
    this.customerTickets,
  });

  factory OrderItemResponse.fromJson(Map<String, dynamic> json) {
    return OrderItemResponse(
      quantity: json['quantity'],
      product: json['product'] != null ? TourResponse.fromJson(json['product']) : null,
      subProduct: json['sub_product'] != null ? SubProductResponse.fromJson(json['sub_product']) : null,
      customerTickets: json["customer_tickets"] == null
          ? []
          : List<CustomerTicket>.from(
          json["customer_tickets"].map((x) => CustomerTicket.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'quantity': quantity,
      'product': product?.toJson(),
      'sub_product': subProduct?.toJson(),
      'customer_tickets': customerTickets,
    };
  }
}

class CustomerTicket {
  final int? id;
  final String? fullname;
  final String? phoneNumber;
  final String? email;
  final String? departDate;
  final String? reservationCode;
  final String? ticketQrCode;
  final String? ticketId;

  CustomerTicket({
    this.id,
    this.fullname,
    this.phoneNumber,
    this.email,
    this.departDate,
    this.reservationCode,
    this.ticketQrCode,
    this.ticketId,
  });

  factory CustomerTicket.fromJson(Map<String, dynamic> json) {
    return CustomerTicket(
      id: json['id'],
      fullname: json['fullname'],
      phoneNumber: json['phone_number'],
      email: json['email'],
      departDate: json['depart_date'],
      reservationCode: json['reservation_code'],
      ticketQrCode: json['ticker_qr_code'],
      ticketId: json['ticket_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullname': fullname,
      'phone_number': phoneNumber,
      'email': email,
      'depart_date': departDate,
      'reservation_code': reservationCode,
      'ticker_qr_code': ticketQrCode,
      'ticket_id': ticketId,
    };
  }
}

class InvoiceResponse {
  final int? id;
  final String? type;
  final String? name;
  final String? receiverEmail;
  final String? address;
  final String? taxCode;
  final DateTime? createdAt;
  final InvoiceStatus? status;

  InvoiceResponse({
    this.id,
    this.type,
    this.name,
    this.receiverEmail,
    this.address,
    this.taxCode,
    this.createdAt,
    this.status,
  });

  factory InvoiceResponse.fromJson(Map<String, dynamic> json) {
    return InvoiceResponse(
      id: json['id'] as int?,
      type: json['type'] as String?,
      name: json['name'] as String?,
      receiverEmail: json['receiver_email'] as String?,
      address: json['address'] as String?,
      taxCode: json['tax_code'] as String?,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      status: InvoiceStatus.fromByString(json["status"]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'name': name,
      'receiver_email': receiverEmail,
      'address': address,
      'tax_code': taxCode,
      'created_at': createdAt?.toIso8601String(),
      'status': status,
    };
  }
}

class RefundResponse {
  int? id;
  RefundStatus? status;
  String? reason;
  List<RefundItemResponse>? refundItems;
  String? createdAt, updatedAt, expiredTime;

  RefundResponse({
    this.id,
    this.status,
    this.reason,
    this.refundItems,
    this.createdAt,
    this.updatedAt,
    this.expiredTime,
  });

  factory RefundResponse.fromJson(Map<String, dynamic> json) => RefundResponse(
    id: json['id'],
    status: RefundStatus.getByString(json['status']),
    reason: json['reason'],
    refundItems: (json['refund_items'] as List?)
        ?.map((e) => RefundItemResponse.fromJson(e))
        .toList(),
    createdAt: json['created_at'],
    updatedAt: json['updated_at'],
    expiredTime: json['expired_time'],
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'status': status,
    'reason': reason,
    'refund_items': refundItems?.map((e) => e.toJson()).toList(),
    'created_at': createdAt,
    'updated_at': updatedAt,
    'expired_time': expiredTime,
  };
}

class RefundItemResponse {
  int? subProductId, quantity;

  RefundItemResponse({this.subProductId, this.quantity});

  factory RefundItemResponse.fromJson(Map<String, dynamic> json) => RefundItemResponse(
    subProductId: json['sub_product_id'],
    quantity: json['quantity'],
  );

  Map<String, dynamic> toJson() => {
    'sub_product_id': subProductId,
    'quantity': quantity,
  };
}

class ContactResponse {
  final int? id;
  final String? fullname;
  final String? email;
  final String? phone;

  ContactResponse({
    this.id,
    this.fullname,
    this.email,
    this.phone,
  });

  factory ContactResponse.fromJson(Map<String, dynamic> json) {
    return ContactResponse(
      id: json['id'],
      fullname: json['fullname'],
      email: json['email'],
      phone: json['phone']
    );
  }
}