import 'dart:convert';

CreateOrderWithPayOSResponse createOrderPayOSResponseFromJson(String str) =>
    CreateOrderWithPayOSResponse.fromJson(json.decode(str));

class CreateOrderWithPayOSResponse {
  bool status;
  CreateOrderPayOSData? data;

  CreateOrderWithPayOSResponse({
    required this.status,
    this.data,
  });

  factory CreateOrderWithPayOSResponse.fromJson(Map<String, dynamic> json) =>
      CreateOrderWithPayOSResponse(
        status: json["status"] ?? false,
        data: CreateOrderPayOSData.fromJson(json["data"]),
      );
}

class CreateOrderPayOSData {
  final int orderId;
  final String paymentUrl;

  CreateOrderPayOSData({required this.orderId, required this.paymentUrl});

  factory CreateOrderPayOSData.fromJson(Map<String, dynamic> json) {
    return CreateOrderPayOSData(
        orderId: json['order_id'] ?? 0, paymentUrl: json['url'] ?? '');
  }
}
