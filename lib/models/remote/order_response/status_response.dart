import 'dart:convert';

StatusResponse statusResponseFromJson(String str) =>
    StatusResponse.fromJson(json.decode(str));

String statusResponseToJson(StatusResponse data) =>
    json.encode(data.toJson());

class StatusResponse {
  StatusResponse({required this.status, this.data});
  factory StatusResponse.fromJson(Map<String, dynamic> json) =>
      StatusResponse(
          status: json['status'] ?? false,
          data: json['data']);
  static StatusResponse getDefault() {
    return StatusResponse(status: false);
  }

  bool status;
  int? data;

  StatusResponse copyWith(
      {bool? status, int? data}) {
    return StatusResponse(
        status: status ?? this.status,
        data: data ?? this.data,);
  }

  Map<String, dynamic> toJson() => {
    'status': status,
    'data': data,
  };
}