import 'dart:convert';

CreateOrderWithVNPayResponse createOrderVNPayResponseFromJson(String str) =>
    CreateOrderWithVNPayResponse.fromJson(json.decode(str));

String createOrderVNPayResponseTo<PERSON>son(CreateOrderWithVNPayResponse data) =>
    json.encode(data.toJson());

class CreateOrderWithVNPayResponse {
  bool status;
  String? data;

  CreateOrderWithVNPayResponse({
    required this.status,
    this.data,
  });

  factory CreateOrderWithVNPayResponse.fromJson(Map<String, dynamic> json) =>
      CreateOrderWithVNPayResponse(
        status: json["status"] ?? false,
        data: json["data"]
      );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data,
  };
}