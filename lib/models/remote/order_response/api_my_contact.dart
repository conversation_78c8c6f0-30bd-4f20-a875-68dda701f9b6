import 'dart:convert';

GetIdByCodeResponse getIdByCodeResponseFromJson(String str) =>
    GetIdByCodeResponse.fromJson(json.decode(str));

String getIdByCodeResponseToJson(GetIdByCodeResponse data) =>
    json.encode(data.toJson());

class GetIdByCodeResponse {
  bool status;
  OrderData? data;

  GetIdByCodeResponse({
    required this.status,
    this.data,
  });

  factory GetIdByCodeResponse.fromJson(Map<String, dynamic> json) =>
      GetIdByCodeResponse(
        status: json["status"] ?? false,
        data: OrderData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
    "status": status,
    "data": data?.toJson(),
  };
}

class OrderData {
  final int orderId;

  OrderData({required this.orderId});

  factory OrderData.fromJson(Map<String, dynamic> json) {
    return OrderData(
      orderId: json['order_id'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
  "orderId": orderId,
};
}