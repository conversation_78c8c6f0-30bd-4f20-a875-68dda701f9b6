import 'dart:convert';

AvatarURLResponse userResponseFromJson(String str) =>
    AvatarURLResponse.fromJson(json.decode(str));

String userResponseToJson(AvatarURLResponse data) => json.encode(data.toJson());

class AvatarURLResponse {
  AvatarURLResponse({
    this.url,
  });

  factory AvatarURLResponse.fromJson(Map<String, dynamic> json) =>
      AvatarURLResponse(
        url: json['url'],
      );

  AvatarURLResponse copyWith({
    String? url,
  }) {
    return AvatarURLResponse(
      url: url ?? this.url,
    );
  }

  String? url;

  Map<String, dynamic> toJson() => {
        'email': url,
      };
}
