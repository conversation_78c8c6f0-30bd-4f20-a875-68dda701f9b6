import 'dart:convert';
import 'package:tripc_app/models/remote/api_s3_response/url_response.dart';

UploadS3Response uploadS3ResponseFromJson(String str) =>
    UploadS3Response.fromJson(json.decode(str));

String uploadS3ResponseToJson(UploadS3Response data) =>
    json.encode(data.toJson());

class UploadS3Response {
  bool status;
  AvatarURLResponse? data;

  UploadS3Response({
    required this.status,
    this.data,
  });

  factory UploadS3Response.fromJson(Map<String, dynamic> json) =>
      UploadS3Response(
        status: json["status"] ?? false,
        data: json["data"] == null
            ? null
            : AvatarURLResponse.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
      };
}
