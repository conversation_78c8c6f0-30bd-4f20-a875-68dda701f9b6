import 'dart:convert';

class KeywordResponse {
  KeywordResponse({
    this.id,
    this.keyword,
  });

  int? id;
  String? keyword;

  factory KeywordResponse.fromJson(Map<String, dynamic> json) =>
      KeywordResponse(
        id: json['id'],
        keyword: json['search_query'],
      );

  KeywordResponse copyWith({
    int? id,
    String? keyword,
  }) {
    return KeywordResponse(
      id: id ?? this.id,
      keyword: keyword ?? this.keyword,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'search_query': keyword,
      };
}

ListKeywordResponse listKeywordResponseFromJson(String str) =>
    ListKeywordResponse.fromJson(json.decode(str));

String listKeywordResponseToJson(ListKeywordResponse data) =>
    json.encode(data.toJson());

class ListKeywordResponse {
  bool status;
  List<KeywordResponse> data;
  int? total;

  ListKeywordResponse({
    required this.status,
    this.data = const [],
  });

  factory ListKeywordResponse.fromJson(Map<String, dynamic> json) =>
      ListKeywordResponse(
        status: json["status"] ?? false,
        data: json["data"] == null
            ? []
            : List<KeywordResponse>.from(
                json["data"].map((x) => KeywordResponse.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
      };
}
