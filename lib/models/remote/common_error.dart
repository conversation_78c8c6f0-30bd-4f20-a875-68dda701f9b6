// To parse this JSON data, do

import 'dart:convert';

CommonErrorResponse commonErrorResponseFromJson(String str) =>
    CommonErrorResponse.fromJson(json.decode(str));

String commonErrorResponseToJson(CommonErrorResponse data) =>
    json.encode(data.toJson());

class CommonErrorResponse {
  CommonErrorResponse({
    this.status,
    this.error,
  });

  factory CommonErrorResponse.fromJson(Map<String, dynamic> json) =>
      CommonErrorResponse(
        status: json['status'],
        error: json['json'] != null ? json['error'] : null,
      );
  bool? status;
  String? error;

  Map<String, dynamic> toJson() => {
        'status': status,
        'error': error,
      };
}
