import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent_bottom_nav_bar_v2.dart';
import 'package:tripc_app/pages/profile/components/tripc_not_yet_login_dialog.dart';
import 'package:tripc_app/pages/tabbar/app_convex_painter.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../status_display_notifier.dart';

class WavyBottomNavBar extends StatelessWidget {
  WavyBottomNavBar({
    super.key,
    required this.navBarConfig,
    this.navBarDecoration = const NavBarDecoration(),
    this.isLoading = false,
  });

  final NavBarConfig navBarConfig;
  final NavBarDecoration navBarDecoration;
  final bool isLoading;
  final int myTripTabIndex = globalReleaseStatusNotifier.isDisplayAll ? 2 : 1;

  Widget _buildItem(ItemConfig item, bool isSelected) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[
        SizedBox(height: 10.H),
        Container(
            height: 2.H,
            width: 20.W,
            color: isSelected
                ? AppAssets.init.tabbarEnableColor
                : Colors.transparent),
        SizedBox(height: 4.H),
        IconTheme(
          data: IconThemeData(
            size: item.iconSize,
            color: isSelected
                ? item.activeBackgroundColor
                : item.inactiveBackgroundColor,
          ),
          child: isSelected ? item.icon : item.inactiveIcon,
        ),
        SizedBox(height: 2.H),
        Text(
          item.title ?? '',
          style: item.textStyle.apply(
            color: isSelected
                ? item.activeForegroundColor
                : item.inactiveForegroundColor,
            fontWeightDelta: 2,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
            color: AppAssets.origin().whiteBackgroundColor,
            height: navBarConfig.navBarHeight,
            width: double.infinity,
            child: CustomPaint(
              painter: ConvexPainter(
                  top: -25.H,
                  width: 80.W,
                  height: 80.H,
                  cornerRadius: 10.H,
                  sigma: 0.3),
            ) // Adjusted height
            ),
        Container(
          color: AppAssets.origin().whiteBackgroundColor,
          padding: EdgeInsets.symmetric(horizontal: 16.W),
          child: SizedBox(
            height: navBarConfig.navBarHeight,
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: navBarConfig.items.map((item) {
                final int index = navBarConfig.items.indexOf(item);
                if (index == myTripTabIndex) {
                  return SizedBox(
                      width: 80.H); // Space for FloatingActionButton
                }
                return Expanded(
                  key: index.toPersistentTabType.globalKey,
                  child: InkWell(
                    onTap: () {
                      if (isLoading) return;
                      navBarConfig.onItemSelected(index);
                    },
                    child: _buildItem(
                      item,
                      navBarConfig.selectedIndex == index,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        Positioned(
          top: -15.H, // Move FAB into the wave cutout
          left: MediaQuery.of(context).size.width / 2 - 28.H,
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color:
                      AppAssets.init.tabbarEnableColor, // Set same as FAB color
                  boxShadow: [
                    BoxShadow(
                      color: AppAssets.init.tabbarEnableColor,
                      blurRadius: 8,
                      spreadRadius: 1,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: InkWell(
                  borderRadius: BorderRadius.circular(40.H),
                  splashColor: Colors.transparent, // Disable tap effect
                  highlightColor: Colors.transparent, // Remove highlight effect
                  onTap: () {
                    if (!globalCacheAuth.isLogged()) {
                      dialogHelpers.show(context,
                          child: const NotYetLoginDialog());
                      return;
                    }
                    navBarConfig.onItemSelected(myTripTabIndex);
                  },
                  child: Padding(
                    padding: EdgeInsets.all(
                        15.H), // Adjust padding to match FAB size
                    child: AppAssets.init.icMyTrip.widget(),
                  ),
                ),
              ),
              SizedBox(height: 8.H),
              Text(navBarConfig.items[myTripTabIndex].title ?? '',
                  style: TextStyle(
                    color: navBarConfig.selectedIndex == 2
                        ? AppAssets.init.tabbarEnableColor
                        : AppAssets.init.tabbarDisableColor,
                    fontWeight: FontWeight.w400,
                    fontSize: 11.SP,
                  ).apply(fontWeightDelta: 2))
            ],
          ),
        ),
      ],
    );
  }
}
