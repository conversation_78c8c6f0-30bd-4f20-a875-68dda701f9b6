// ignore_for_file: unused_result
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent_bottom_nav_bar_v2.dart';
import 'package:tripc_app/pages/tabbar/app_persistent_bottom_nav_bar.dart';
import 'package:tripc_app/pages/tabbar/app_tabbar_provider.dart';
import 'package:tripc_app/utils/app_arguments_screen.dart';
import 'package:tripc_app/utils/app_extension.dart';

final pAppBottomNavProvider =
    NotifierProvider<AppBottomNavProvider, AppBottomNavState>(
        AppBottomNavProvider.new);

class AppBottomNav extends ConsumerStatefulWidget {
  const AppBottomNav({
    super.key,
    this.argument,
  });

  final AppTabBarArgument? argument;

  @override
  ConsumerState<AppBottomNav> createState() => _AppTabbarState();
}

class _AppTabbarState extends ConsumerState<AppBottomNav>
    with WidgetsBindingObserver, RouteAware {
  final PersistentTabController _persistentTabController =
      PersistentTabController(
    initialIndex: 0,
  );
  void _resetProvider() {
    ref.refresh(pAppBottomNavProvider);
  }

  @override
  void initState() {
    // WidgetsBinding.instance.addPostFrameCallback((_) async {
    //   _persistentTabController.jumpToTab(1);
    // });
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _resetProvider();
    });
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // _appTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(pAppBottomNavProvider.select((state) => state.currentTab),
        (_, state) {
      _persistentTabController.jumpToTab(state.index);
    });

    return Stack(
      children: [
        AppPersistentBottomNavBar(
          isLoading: ref.watch(pAppBottomNavProvider).isLoading,
          needPreservesStateTab: false,
          // navBarDecoration: NavBarDecoration(
          //   color: Colors.white,
          //   padding: EdgeInsets.only(
          //       top: 15.H, bottom: Platform.isAndroid ? 10.H : 0),
          // ),
          persistentTabController: _persistentTabController,
          onTabChanged: (index) async {
            ref
                .read(pAppBottomNavProvider.notifier)
                .setTab(index.toPersistentTabType);
          },
        ),
      ],
    );
  }
}
