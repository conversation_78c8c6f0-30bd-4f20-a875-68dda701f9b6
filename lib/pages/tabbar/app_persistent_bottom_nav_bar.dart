import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent_bottom_nav_bar_v2.dart';
import 'package:tripc_app/models/app/tripc_persistent_tab_type.dart';
import 'package:tripc_app/pages/tabbar/app_persistent_bottom_nav_bar_config.dart';
import 'package:tripc_app/pages/tabbar/style_custom_navbar.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../status_display_notifier.dart';

class AppPersistentBottomNavBar extends StatefulWidget {
  const AppPersistentBottomNavBar({
    super.key,
    this.navBarDecoration = const NavBarDecoration(),
    this.persistentTabController,
    required this.onTabChanged,
    this.needPreservesStateTab = true,
    this.isLoading = false,
  });

  final NavBarDecoration navBarDecoration;
  final PersistentTabController? persistentTabController;
  final ValueChanged<int> onTabChanged;
  final bool needPreservesStateTab;
  final bool isLoading;

  @override
  State<AppPersistentBottomNavBar> createState() =>
      _AppPersistentBottomNavBarState();
}

class _AppPersistentBottomNavBarState extends State<AppPersistentBottomNavBar> {
  List<PersistentTabConfig> _tabs() => globalReleaseStatusNotifier.isDisplayAll
      ? [
          TripCPersistentTab(
              type: TripCPersistentTabType.home,
              activeForegroundColor: AppAssets.init.tabbarEnableColor,
              inactiveForegroundColor: AppAssets.init.tabbarDisableColor),
          TripCPersistentTab(
              type: TripCPersistentTabType.explore,
              activeForegroundColor: AppAssets.init.tabbarEnableColor,
              inactiveForegroundColor: AppAssets.init.tabbarDisableColor),
          TripCPersistentTab(type: TripCPersistentTabType.mytrip),
          TripCPersistentTab(
              type: TripCPersistentTabType.loyalty,
              activeForegroundColor: AppAssets.init.tabbarEnableColor,
              inactiveForegroundColor: AppAssets.init.tabbarDisableColor),
          TripCPersistentTab(
              type: TripCPersistentTabType.profile,
              activeForegroundColor: AppAssets.init.tabbarEnableColor,
              inactiveForegroundColor: AppAssets.init.tabbarDisableColor),
        ]
      : [
          TripCPersistentTab(
              type: TripCPersistentTabType.home,
              activeForegroundColor: AppAssets.init.tabbarEnableColor,
              inactiveForegroundColor: AppAssets.init.tabbarDisableColor),
          TripCPersistentTab(type: TripCPersistentTabType.mytrip),
          TripCPersistentTab(
              type: TripCPersistentTabType.profile,
              activeForegroundColor: AppAssets.init.tabbarEnableColor,
              inactiveForegroundColor: AppAssets.init.tabbarDisableColor),
        ];

  @override
  void dispose() {
    // widget.persistentTabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PersistentTabView(
        controller: widget.persistentTabController,
        tabs: _tabs(),
        backgroundColor: Colors.transparent,
        onTabChanged: widget.onTabChanged,
        navBarHeight: 82.H,
        stateManagement: widget.needPreservesStateTab,
        screenTransitionAnimation: const ScreenTransitionAnimation(
          duration: Duration(milliseconds: 100),
          curve: Curves.decelerate,
        ),
        navBarBuilder: (navBarConfig) => WavyBottomNavBar(
              navBarConfig: navBarConfig,
              navBarDecoration: widget.navBarDecoration,
              isLoading: widget.isLoading,
            ));
  }
}
