import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/tripc_persistent_tab_type.dart';

class AppBottomNavState {
  AppBottomNavState({
    required this.isLoading,
    this.currentTab = TripCPersistentTabType.home,
  });

  AppBottomNavState copyWith({
    bool? isLoading,
    TripCPersistentTabType? currentTab,
  }) {
    return AppBottomNavState(
      isLoading: isLoading ?? this.isLoading,
      currentTab: currentTab ?? this.currentTab,
    );
  }

  final bool isLoading;
  final TripCPersistentTabType currentTab;
}

class AppBottomNavProvider extends Notifier<AppBottomNavState> {
  Future<void> setTab(TripCPersistentTabType tabType) async {
    state = state.copyWith(
      currentTab: tabType,
    );
  }

  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  void resetBottomNav() {
    state = state.copyWith(
      currentTab: TripCPersistentTabType.home,
      isLoading: false,
    );
  }

  @override
  AppBottomNavState build() => AppBottomNavState(
        isLoading: false,
      );
}
