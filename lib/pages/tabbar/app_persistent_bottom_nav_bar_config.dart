import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent_bottom_nav_bar_v2.dart';
import 'package:tripc_app/models/app/tripc_persistent_tab_type.dart';
import 'package:tripc_app/my_app.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

class TripCPersistentTab extends PersistentTabConfig {
  TripCPersistentTab({
    this.title,
    required this.type,
    this.inactiveIcon,
    this.icon,
    this.activeForegroundColor,
    this.inactiveForegroundColor,
  }) : super(
          screen: type.getScreen(),
          item: TripCItemConfig(
            icon: icon ?? type.getIcon(),
            inactiveIcon: inactiveIcon ?? type.getInactiveIcon(),
            activeForegroundColor:
                activeForegroundColor ?? MyApp.customSelectedColor.title!,
            inactiveForegroundColor: inactiveForegroundColor ??
                AppAssets.init.backGroundCardBoderColor,
            title: title ?? type.alias(),
            textStyle: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 10.SP,
            ),
          ),
        );

  final TripCPersistentTabType type;
  final String? title;
  final Widget? icon;
  final Widget? inactiveIcon;
  final Color? activeForegroundColor;
  final Color? inactiveForegroundColor;
}

class TripCItemConfig extends ItemConfig {
  TripCItemConfig({
    required super.icon,
    super.activeColorSecondary,
    super.iconSize,
    super.inactiveBackgroundColor,
    super.inactiveForegroundColor,
    super.activeForegroundColor,
    super.inactiveIcon,
    super.title,
    super.opacity,
    super.filter,
    super.textStyle,
  });

  TripCItemConfig copyWith({
    Widget? icon,
    Widget? inactiveIcon,
    Color? inactiveBackgroundColor,
    Color? inactiveForegroundColor,
    Color? activeForegroundColor,
    String? title,
    TripCPersistentTabType? type,
  }) {
    return TripCItemConfig(
      icon: icon ?? this.icon,
      inactiveIcon: inactiveIcon ?? this.inactiveIcon,
      activeForegroundColor:
          activeForegroundColor ?? this.activeForegroundColor,
      inactiveBackgroundColor:
          inactiveBackgroundColor ?? this.inactiveBackgroundColor,
      inactiveForegroundColor:
          inactiveForegroundColor ?? this.activeForegroundColor,
      title: title ?? this.title,
    );
  }
}
