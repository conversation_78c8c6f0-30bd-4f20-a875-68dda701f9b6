import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewDataModel {
  WebViewDataModel({
    this.linkUrl,
    this.title,
  });
  final String? linkUrl;
  final String? title;
}

class TripcWebview extends StatefulWidget {
  const TripcWebview({super.key, this.linkUrl, this.title});
  final String? linkUrl;
  final String? title;

  @override
  State<TripcWebview> createState() => _TripcWebviewState();
}

class _TripcWebviewState extends State<TripcWebview> {
  late WebViewController _controller;
  bool _isLoading = true;
  @override
  void initState() {
    super.initState();
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onHttpError: (HttpResponseError error) {},
          onWebResourceError: (WebResourceError error) {},
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.linkUrl ?? ''));
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        TripcScaffold(
          hasBackButton: true,
          needUnFocus: true,
          backgroundColor: AppAssets.origin().whiteBackgroundColor,
          titleAppBar: TripcText(
            widget.title ?? '',
            fontSize: 16,
            fontWeight: FontWeight.w400,
            textColor: AppAssets.origin().blackColor,
          ),
          body: WebViewWidget(controller: _controller),
        ),
        AppLoading(isRequesting: _isLoading)
      ],
    );
  }
}
