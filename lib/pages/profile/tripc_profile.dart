import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/app/tripc_tier.dart';
import 'package:tripc_app/pages/profile/components/tripc_avatar.dart';
import 'package:tripc_app/pages/profile/components/tripc_balance.dart';
import 'package:tripc_app/pages/profile/components/tripc_profile_service.dart';
import 'package:tripc_app/pages/profile/components/tripc_tier.dart';
import 'package:tripc_app/pages/profile/components/tripc_tier_suggest_login.dart';
import 'package:tripc_app/pages/profile/providers/tripc_profile_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_card_info/tripc_card_info.dart';
import '../../status_display_notifier.dart';

class TripcProfilePage extends ConsumerStatefulWidget {
  const TripcProfilePage({super.key});

  @override
  ConsumerState<TripcProfilePage> createState() => _TripcProfilePageState();
}

class _TripcProfilePageState extends ConsumerState<TripcProfilePage> {
  final TextEditingController _controller = TextEditingController();
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(pAccountProvider.select((value) => value.user));
    final profileTourServiceCategories = ref.watch(pProfilePageProvider
        .select((value) => value.profileTourServiceCategories));
    final profileHelpServiceCategories = ref.watch(pProfilePageProvider
        .select((value) => value.profileHelpServiceCategories));
    final profilePolicyServiceCategories = ref.watch(pProfilePageProvider
        .select((value) => value.profilePolicyServiceCategories));
    final defaultTripcId = ref.watch(
        pAccountProvider.select((value) => value.user?.defaultMemberShip));
    final bool isLogin = globalCacheAuth.isLogged();
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        visibleAppBar: false,
        backgroundColor: AppAssets.origin().whiteSmokeColor,
        body: SafeArea(
            child: SingleChildScrollView(
          child: Column(
            children: [
              _header(
                userName: user?.fullname ?? '',
                isLogin: isLogin,
                avatarUrl: user?.avatarUrl,
                onTap: () => AppRoute.pushNamed(context,
                    routeName: AppRoute.routeManageProfile),
                onTapLogin: () => AppRoute.pushNamed(context,
                    routeName: AppRoute.routeSignIn),
              ),
              SizedBox(height: 12.H),
              Visibility(
                  visible: isLogin,
                  replacement: const TripcTierSuggestLogin(),
                  child: TripcTier(
                      onTapTier: () {},
                      tier: user?.getTier() ?? TripcTierType.bronze)),
              Visibility(
                visible: isLogin,
                replacement: SizedBox(height: 30.H),
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 24.W, vertical: 12.H)
                          .copyWith(bottom: 20.H),
                  child: TripcCardInfo(
                      isCardSelected: true,
                      tripcIDMembership: defaultTripcId,
                      width: context.mediaQuery.size.width - 48.W),
                ),
              ),
              Visibility(
                visible: globalReleaseStatusNotifier.isDisplayAll,
                child: Padding(
                  padding: EdgeInsets.only(bottom: 30.H),
                  child: TripcBalanceAndPromoTicket(
                    isLogin: isLogin,
                  ),
                ),
              ),
              ProfileServiceCategoriesArea(
                profileTourServiceCategories: profileTourServiceCategories,
                profileHelpServiceCategories: profileHelpServiceCategories,
                profilePolicyServiceCategories: profilePolicyServiceCategories,
              ),
              SizedBox(height: 30.H),
              // TripcText(
              //   'GO TO CUISINE',
              //   onTap: () => AppRoute.pushNamed(context,
              //       routeName: AppRoute.routeBookingAddPassengerQuantity,
              //       arguments: 164),
              // )
            ],
          ),
        )));
  }

  Widget _header(
      {VoidCallback? onTap,
      required String userName,
      required bool isLogin,
      required String? avatarUrl,
      VoidCallback? onTapLogin}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              TripcAvatar(avatarUrl: avatarUrl ?? ''),
              SizedBox(width: 12.W),
              Visibility(
                visible: isLogin,
                replacement: TripcText(
                  context.strings.text_sign_in_or_sign_up,
                  fontSize: 14,
                  textColor: AppAssets.origin().blackColor,
                  showSuggestLoginDialog: false,
                  isLogin: isLogin,
                  onTap: onTapLogin,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TripcText(
                      userName,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      textCase: TextCaseType.none,
                      textAlign: TextAlign.left,
                      textColor: AppAssets.origin().blackColor,
                    ),
                    SizedBox(height: 8.H),
                    TripcIconButton(
                      onPressed: onTap,
                      showSuggestLoginDialog: true,
                      isLogin: isLogin,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          TripcText(
                            onTap: () => AppRoute.pushNamed(context,
                                routeName: AppRoute.routeManageProfile),
                            context.strings.text_my_account,
                            fontSize: 12,
                            fontWeight: FontWeight.w300,
                            textCase: TextCaseType.none,
                            textAlign: TextAlign.center,
                            textColor:
                                AppAssets.origin().secondDarkGreyTextColor,
                          ),
                          SizedBox(width: 10.W),
                          AppAssets.origin().iconArrowRight.widget(
                              height: 9.H,
                              width: 12.W,
                              color: AppAssets.origin().secondDarkGreyTextColor)
                        ],
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 6.H),
            child: Row(
              children: [
                TripcIconButton(
                  onPressed: () {
                    AppRoute.pushNamed(context,
                        routeName: AppRoute.routeProfileSetting);
                  },
                  showSuggestLoginDialog: true,
                  isLogin: isLogin,
                  child: AppAssets.origin().icSetting.widget(
                      height: 16.H,
                      width: 16.W,
                      color: AppAssets.origin().blackColor),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
