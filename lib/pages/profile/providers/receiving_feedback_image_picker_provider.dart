import 'package:flutter_riverpod/flutter_riverpod.dart';

class ImagePickerState {
  final List<String> imagePaths;

  ImagePickerState({required this.imagePaths});

  static ImagePickerState getDefault() {
    return ImagePickerState(imagePaths: []);
  }

  ImagePickerState copyWith({List<String>? imagePaths}) {
    return ImagePickerState(imagePaths: imagePaths ?? this.imagePaths);
  }

  bool get canAddMore => imagePaths.length < 3;

  bool get isNotPickingImage => imagePaths.isEmpty;
}

class ImagePickerNotifier extends StateNotifier<ImagePickerState> {
  ImagePickerNotifier() : super(ImagePickerState(imagePaths: []));

  void addImage(String path) {
    if (state.imagePaths.length >= 3) return;
    state = state.copyWith(imagePaths: [...state.imagePaths, path]);
  }

  void removeImageAt(int index) {
    final updated = [...state.imagePaths]..removeAt(index);
    state = state.copyWith(imagePaths: updated);
  }

  void resetState() {
    state = ImagePickerState.getDefault();
  }
}

final imagePickerProvider =
    StateNotifierProvider<ImagePickerNotifier, ImagePickerState>(
  (ref) => ImagePickerNotifier(),
);
