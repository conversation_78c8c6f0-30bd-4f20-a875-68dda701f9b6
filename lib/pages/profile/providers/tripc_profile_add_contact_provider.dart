import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/contact_request.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/utils/app_validation.dart';

import '../../../services/apis/auth/api_user_login.dart';

class AddContactPageModel {
  final String email;
  final String phone;
  final String fullname;
  final bool isDefault;
  String errorEmail;
  String errorPhone;
  String errorFullName;
  bool isLoading;
  String errorMessage;

  static AddContactPageModel getDefault() {
    return AddContactPageModel(
      email: '',
      phone: '',
      fullname: '',
      isLoading: false,
    );
  }

  AddContactPageModel(
      {required this.email,
      required this.phone,
      required this.fullname,
      this.isDefault = false,
      this.errorPhone = '',
      this.errorEmail = '',
      this.errorFullName = '',
      this.errorMessage = '',
      this.isLoading = false});

  AddContactPageModel copyWith({
    String? email,
    String? phone,
    String? fullname,
    bool? isDefault,
    String? errorPhone,
    String? errorEmail,
    String? errorFullName,
    String? errorMessage,
    bool? isLoading,
  }) {
    return AddContactPageModel(
      email: email ?? this.email,
      phone: phone ?? this.phone,
      fullname: fullname ?? this.fullname,
      isDefault: isDefault ?? this.isDefault,
      errorPhone: errorPhone ?? this.errorPhone,
      errorEmail: errorEmail ?? this.errorEmail,
      errorFullName: errorFullName ?? this.errorFullName,
      errorMessage: errorMessage ?? this.errorMessage,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  bool get isAddContactEnable {
    return (errorPhone.isEmpty &&
        errorEmail.isEmpty &&
        errorFullName.isEmpty &&
        phone.isNotEmpty &&
        fullname.isNotEmpty &&
        email.isNotEmpty);
  }
}

class AddContactPageProvider extends StateNotifier<AddContactPageModel> {
  AddContactPageProvider(super.state);
  final ApiUser _api = ApiUser();

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void setNameErrorMessage(String? error) {
    state = state.copyWith(
      errorFullName: error,
    );
  }

  void setEmailErrorMessage(String? error) {
    state = state.copyWith(
      errorEmail: error,
    );
  }

  void setPhoneErrorMessage(String? error) {
    state = state.copyWith(
      errorPhone: error,
    );
  }

  void forceLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setPhone(BuildContext context, String phone) {
    setPhoneErrorMessage('');
    state = state.copyWith(
      phone: phone,
      errorPhone: ValidationAccount.phoneNumberValidation(context, phone),
    );
  }

  void setEmail(BuildContext context, String value) {
    setEmailErrorMessage('');
    state = state.copyWith(email: value);
    setEmailErrorMessage(ValidationAccount.emailValidation(context, value));
  }

  void setFullname(BuildContext context, String value) {
    setNameErrorMessage('');

    final result = ValidationAccount.fullNameValidation(context, value);
    state = state.copyWith(
      fullname: value,
      errorFullName: result.isNotEmpty ? result : null,
    );
  }

  void setDefault(bool value) {
    state = state.copyWith(isDefault: value);
  }

  Future<bool> addContact() async {
    try {
      forceLoading(true);
      final result = await _api
          .addContact(
              request: ContactRequest(
                  fullname: state.fullname,
                  email: state.email,
                  phone: state.phone.addLeadingZero(),
                  isDefault: state.isDefault ? 1 : 0))
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );

      forceLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  void resetState() {
    state = AddContactPageModel.getDefault();
  }
}

final pAddContactProvider =
    StateNotifierProvider<AddContactPageProvider, AddContactPageModel>(
  (ref) => AddContactPageProvider(AddContactPageModel.getDefault()),
);
