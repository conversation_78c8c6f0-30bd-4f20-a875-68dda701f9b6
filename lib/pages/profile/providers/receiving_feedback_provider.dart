import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/common_error.dart';

import '../../../models/app/feedback_request.dart';
import '../../../services/apis/feedback/api_feedback.dart';

class ReceivingFeedbackState {
  String name;
  String number;
  String content;

  String errorName;
  String errorNumber;
  String errorContent;

  bool isLoading;
  String? errorMessage;

  static ReceivingFeedbackState getDefault() {
    return ReceivingFeedbackState(
        name: '',
        number: '',
        content: '',
        errorName: '',
        errorNumber: '',
        errorContent: '',
        isLoading: false,
        errorMessage: null);
  }

  ReceivingFeedbackState(
      {this.name = '',
      this.number = '',
      this.content = '',
      this.errorName = '',
      this.errorContent = '',
      this.errorNumber = '',
      this.errorMessage,
      this.isLoading = false});

  ReceivingFeedbackState copyWith({
    String? name,
    String? number,
    String? content,
    String? errorName,
    String? errorContent,
    String? errorNumber,
    String? errorMessage,
    bool? isLoading,
  }) {
    return ReceivingFeedbackState(
      name: name ?? this.name,
      number: number ?? this.number,
      content: content ?? this.content,
      errorName: errorName ?? this.errorName,
      errorContent: errorContent ?? this.errorContent,
      errorNumber: errorNumber ?? this.errorNumber,
      errorMessage: errorMessage ?? this.errorMessage,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  bool get isButtonEnable {
    return (errorName.isEmpty &&
        errorContent.isEmpty &&
        errorNumber.isEmpty &&
        name.isNotEmpty &&
        number.isNotEmpty &&
        content.isNotEmpty);
  }
}

class AddContactPageProvider extends StateNotifier<ReceivingFeedbackState> {
  AddContactPageProvider(super.state);
  final ApiFeedback _api = ApiFeedback();

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void setName(BuildContext context, String value) {
    state = state.copyWith(
      name: value,
    );
  }

  void setNumber(BuildContext context, String number) {
    state = state.copyWith(
      number: number,
    );
  }

  void setContent(BuildContext context, String value) {
    state = state.copyWith(content: value);
  }

  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  Future<bool> createFeedback() async {
    try {
      setLoading(true);
      final result = await _api
          .createFeedback(
            FeedbackRequest(
                name: state.name,
                number: state.number,
                content: state.content,
                image: ''),
          )
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );

      setLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  void resetState() {
    state = ReceivingFeedbackState.getDefault();
  }
}

final pReceivingFeedbackProvider =
    StateNotifierProvider<AddContactPageProvider, ReceivingFeedbackState>(
  (ref) => AddContactPageProvider(ReceivingFeedbackState.getDefault()),
);
