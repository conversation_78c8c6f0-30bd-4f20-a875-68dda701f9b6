import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../models/remote/contact_response.dart';
import '../../../services/apis/auth/api_user_login.dart';

class ContactPageModel {
  ContactPageModel({this.contactList = const [], this.isLoading = false});
  final List<ContactResponse> contactList;
  final bool isLoading;

  static ContactPageModel getDefault() {
    return ContactPageModel();
  }

  ContactPageModel copyWith(
      {List<ContactResponse>? contactList, bool? isLoading}) {
    return ContactPageModel(
        contactList: contactList ?? this.contactList,
        isLoading: isLoading ?? this.isLoading);
  }
}

class ContactPageProvider extends StateNotifier<ContactPageModel> {
  ContactPageProvider(super._state);
  final ApiUser _api = ApiUser();

  Future<void> getContactList() async {
    setLoading(true);
    final result = await _api.getContactList().timeout(
          const Duration(
            seconds: 30,
          ),
        );
    state = state.copyWith(contactList: result.data);
    setLoading(false);
  }

  void setLoading(bool value) {
    state = state.copyWith(
      isLoading: value,
    );
  }
}

final pContactProvider =
    StateNotifierProvider<ContactPageProvider, ContactPageModel>(
  (ref) => ContactPageProvider(ContactPageModel.getDefault()),
);
