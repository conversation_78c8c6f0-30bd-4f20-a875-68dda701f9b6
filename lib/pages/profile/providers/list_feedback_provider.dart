import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/common_error.dart';

import '../../../models/remote/feedback_response/feedback_response.dart';
import '../../../services/apis/feedback/api_feedback.dart';
import '../../../utils/app_log.dart';

class ListFeedbackState {
  List<FeedbackResponse> feedbacks;
  bool isLoading;

  static ListFeedbackState getDefault() {
    return ListFeedbackState(
      feedbacks: [],
      isLoading: false,
    );
  }

  ListFeedbackState({this.feedbacks = const [], this.isLoading = false});

  ListFeedbackState copyWith({
    List<FeedbackResponse>? feedbacks,
    bool? isLoading,
  }) {
    return ListFeedbackState(
      feedbacks: feedbacks ?? this.feedbacks,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class ListFeedbackProvider extends StateNotifier<ListFeedbackState> {
  ListFeedbackProvider(super.state);
  final ApiFeedback _api = ApiFeedback();

  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setFeedbacks(List<FeedbackResponse>? value) {
    state = state.copyWith(feedbacks: value);
  }

  Future<void> feedbacks() async {
    try {
      setLoading(true);
      final result = await _api.feedbacks().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setFeedbacks(result.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        logger.e(exceptionMessage.error);
      }
    }
    setLoading(false);
  }

  void resetState() {
    state = ListFeedbackState.getDefault();
  }
}

final pListFeedbackProvider =
    StateNotifierProvider<ListFeedbackProvider, ListFeedbackState>(
  (ref) => ListFeedbackProvider(ListFeedbackState.getDefault()),
);
