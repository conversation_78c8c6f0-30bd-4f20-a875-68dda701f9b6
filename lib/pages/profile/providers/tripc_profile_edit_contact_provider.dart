import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/contact_request.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/models/remote/contact_response.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/utils/app_validation.dart';

import '../../../services/apis/auth/api_user_login.dart';

class EditContactPageModel {
  final int id;
  final String email;
  final String phone;
  final String fullname;
  final bool isDefault;
  String? errorEmail;
  bool errorPhone;
  String? errorFullName;
  bool isLoading;
  String? errorMessage;

  static EditContactPageModel getDefault() {
    return EditContactPageModel(
        id: 0, email: '', phone: '', fullname: '', isDefault: false);
  }

  EditContactPageModel(
      {required this.id,
      required this.email,
      required this.phone,
      required this.fullname,
      required this.isDefault,
      this.errorPhone = false,
      this.errorEmail,
      this.errorFullName,
      this.errorMessage,
      this.isLoading = false});

  EditContactPageModel copyWith({
    int? id,
    String? email,
    String? phone,
    String? fullname,
    bool? isDefault,
    bool? errorPhone,
    String? errorEmail,
    String? errorFullName,
    String? errorMessage,
    bool? isLoading,
  }) {
    return EditContactPageModel(
      id: id ?? this.id,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      fullname: fullname ?? this.fullname,
      isDefault: isDefault ?? this.isDefault,
      errorPhone: errorPhone ?? this.errorPhone,
      errorEmail: errorEmail,
      errorFullName: errorFullName,
      errorMessage: errorMessage,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  bool get isAddContactEnable {
    return (!errorPhone &&
        errorEmail.isNull &&
        errorFullName.isNull &&
        phone.isNotEmpty &&
        fullname.isNotEmpty &&
        email.isNotEmpty);
  }
}

class EditContactPageProvider extends StateNotifier<EditContactPageModel> {
  EditContactPageProvider(super.state);
  final ApiUser _api = ApiUser();

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void forceLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setPhone(BuildContext context, String phone) {
    state = state.copyWith(
      phone: phone,
      errorPhone: !ValidationAccount.isMyPhoneNumberPassed(phone),
    );
  }

  void setEmail(BuildContext context, String value) {
    final result = ValidationAccount.emailValidation(context, value);
    state = state.copyWith(
        email: value, errorEmail: result.isNotEmpty ? result : null);
  }

  void setFullname(BuildContext context, String value) {
    final result = ValidationAccount.fullNameValidation(context, value);
    state = state.copyWith(
      fullname: value,
      errorFullName: result.isNotEmpty ? result : null,
    );
  }

  void setDefault(bool value) {
    state = state.copyWith(isDefault: value);
  }

  void setContact(ContactResponse contact) {
    state = state.copyWith(
        id: contact.id,
        email: contact.email,
        phone: contact.phone,
        fullname: contact.fullname,
        isDefault: contact.isDefault == 1);
  }

  Future<bool> editContact() async {
    try {
      forceLoading(true);
      final result = await _api
          .updateContact(
              contactId: state.id,
              request: ContactRequest(
                fullname: state.fullname,
                email: state.email,
                phone: state.phone.addLeadingZero(),
                isDefault: state.isDefault ? 1 : 0,
              ))
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );

      forceLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  Future<bool> deleteContact() async {
    try {
      forceLoading(true);
      final result = await _api.deleteContact(contactId: state.id).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      forceLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }
}

final pEditContactProvider =
    StateNotifierProvider<EditContactPageProvider, EditContactPageModel>(
  (ref) => EditContactPageProvider(EditContactPageModel.getDefault()),
);
