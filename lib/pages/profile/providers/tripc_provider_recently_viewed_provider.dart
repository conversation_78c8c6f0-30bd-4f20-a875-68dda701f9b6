import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../models/app/list_data_request.dart';
import '../../../models/remote/common_error.dart';
import '../../../services/apis/tours/api_tours.dart';

enum SectionStatus {
  today(0),
  yesterday(1),
  previous(2);

  const SectionStatus(this.value);
  final int value;
  static SectionStatus getByValue(int i) {
    return SectionStatus.values.firstWhere(
      (x) => x.value == i,
      orElse: () => SectionStatus.previous,
    );
  }

  String statusText(BuildContext context, int days) {
    switch (this) {
      case SectionStatus.today:
        return context.strings.text_today;
      case SectionStatus.yesterday:
        return context.strings.text_yesterday;
      case SectionStatus.previous:
        return context.strings.text_previous_day(days);
    }
  }
}

class TourInSection {
  TourInSection({required this.section, required this.tourList});

  int section;
  List<TourResponse> tourList;
}

List<TourInSection> classifyTours(List<TourResponse> tours) {
  List<TourInSection> tourSections = [];
  Map<int, List<TourResponse>> groupedTours = {};

  for (var tour in tours) {
    DateTime? lastSeenDate = (tour.lastSeenAt ?? '').toDateTimeISO();
    if (lastSeenDate != null) {
      Duration difference = DateTime.now().difference(lastSeenDate);
      int daysAgo = difference.inDays;

      if (daysAgo < 1) {
        groupedTours.putIfAbsent(0, () => []).add(tour);
      } else if (daysAgo == 1) {
        groupedTours.putIfAbsent(1, () => []).add(tour);
      } else {
        groupedTours.putIfAbsent(daysAgo, () => []).add(tour);
      }
    }
  }

  groupedTours.forEach((section, tourList) {
    if (tourList.isNotEmpty) {
      tourSections.add(TourInSection(section: section, tourList: tourList));
    }
  });
  return tourSections;
}

class RecentlyViewedPageModel {
  RecentlyViewedPageModel(
      {required this.sectionList,
      this.page = 1,
      this.pageSize = 20,
      this.isLoading = false,
      this.errorMessage,
      this.enableLoadMore = true,
      this.isLoadingMore = false});
  List<TourInSection> sectionList;
  int page;
  int pageSize;
  bool isLoading;
  String? errorMessage;
  bool enableLoadMore;
  bool isLoadingMore;

  static RecentlyViewedPageModel getDefault() {
    return RecentlyViewedPageModel(sectionList: []);
  }

  RecentlyViewedPageModel copyWith(
      {List<TourInSection>? sectionList,
      int? page,
      int? pageSize,
      bool? isLoading,
      String? errorMessage,
      bool? enableLoadMore,
      bool? isLoadingMore}) {
    return RecentlyViewedPageModel(
        sectionList: sectionList ?? this.sectionList,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize,
        isLoading: isLoading ?? this.isLoading,
        errorMessage: errorMessage ?? this.errorMessage,
        enableLoadMore: enableLoadMore ?? this.enableLoadMore,
        isLoadingMore: isLoadingMore ?? this.isLoadingMore);
  }
}

class RecentlyViewedPageProvider
    extends StateNotifier<RecentlyViewedPageModel> {
  RecentlyViewedPageProvider(super._state);
  final ApiTours _api = ApiTours();

  void setLoading(bool value) {
    state = state.copyWith(
      isLoading: value,
    );
  }

  void setLoadingMore(bool value) {
    state = state.copyWith(
      isLoadingMore: value,
    );
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void setEnableLoadMore(bool status) {
    state = state.copyWith(enableLoadMore: status);
  }

  Future<void> getRecentlyViewed() async {
    final request = ListDataRequest(page: 1, pageSize: 20);
    setLoading(true);
    try {
      final result = await _api.getRecentlyViewed(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(sectionList: classifyTours(result.data));
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> loadmore() async {
    if (!state.enableLoadMore) return;

    final request = ListDataRequest(page: state.page + 1, pageSize: 20);
    setLoadingMore(true);
    try {
      final result = await _api.getRecentlyViewed(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );

      if (result.data.isEmpty) {
        setEnableLoadMore(false);
      } else {
        state.sectionList.addAll(classifyTours(result.data));
        state = state.copyWith(
            sectionList: state.sectionList, page: state.page + 1);
      }
      setLoadingMore(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<bool> delete() async {
    setLoadingMore(true);
    try {
      final result = await _api.deleteRecentlyViewed().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoadingMore(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoadingMore(false);
      return false;
    }
  }
}

final pRecentlyViewedProvider =
    StateNotifierProvider<RecentlyViewedPageProvider, RecentlyViewedPageModel>(
  (ref) => RecentlyViewedPageProvider(RecentlyViewedPageModel.getDefault()),
);
