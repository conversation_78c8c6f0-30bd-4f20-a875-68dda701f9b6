import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';

import '../../../models/app/list_data_request.dart';
import '../../../models/remote/common_error.dart';
import '../../../services/apis/tours/api_tours.dart';

class SavedTourPageModel {
  SavedTourPageModel(
      {required this.tours,
      this.isLoading = false,
      this.enableLoadMore = true,
      this.errorMessage,
      this.page = 1,
      this.pageSize = 20});
  List<TourResponse> tours;
  bool isLoading;
  bool enableLoadMore;
  String? errorMessage;
  int page;
  int pageSize;

  static SavedTourPageModel getDefault() {
    return SavedTourPageModel(tours: []);
  }

  SavedTourPageModel copyWith(
      {List<TourResponse>? tours,
      bool? isLoading,
      bool? enableLoadMore,
      String? errorMessage,
      int? page,
      int? pageSize}) {
    return SavedTourPageModel(
        tours: tours ?? this.tours,
        isLoading: isLoading ?? this.isLoading,
        enableLoadMore: enableLoadMore ?? this.enableLoadMore,
        errorMessage: errorMessage ?? this.errorMessage,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize);
  }
}

class SavedTourPageProvider extends StateNotifier<SavedTourPageModel> {
  SavedTourPageProvider(super._state);
  final ApiTours _api = ApiTours();

  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setErrorMessage(String? txt) {
    state = state.copyWith(errorMessage: txt);
  }

  Future<void> getSavedTours() async {
    final request = ListDataRequest(page: 1, pageSize: state.pageSize);
    setLoading(true);
    try {
      final result = await _api.getSavedTours(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(tours: result.data);
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> loadMore() async {
    if (!state.enableLoadMore) return;

    final request =
        ListDataRequest(page: state.page + 1, pageSize: state.pageSize);
    setLoading(true);
    try {
      final result = await _api.getSavedTours(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.data.isNotEmpty) {
        state.tours.addAll(result.data);
        state = state.copyWith(tours: state.tours, page: state.page + 1);
      } else {
        state = state.copyWith(enableLoadMore: false);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }
}

final pSavedTourProvider =
    StateNotifierProvider<SavedTourPageProvider, SavedTourPageModel>(
  (ref) => SavedTourPageProvider(SavedTourPageModel.getDefault()),
);
