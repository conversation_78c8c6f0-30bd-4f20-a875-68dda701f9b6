import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/app/tripc_id_model.dart';
import 'package:tripc_app/models/app/tripc_profile_service_category.dart';
import 'package:tripc_app/models/app/update_passcode_request.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_list_numbers_response.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_model.dart';
import 'package:tripc_app/services/apis/auth/api_auth.dart';
import 'package:tripc_app/services/apis/membership/api_membership.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../models/app/forgot_passcode_request.dart';
import '../../../models/app/verify_otp_forgot_passcode_request.dart';
import '../../../services/providers/providers.dart';

class ProfilePageModel {
  ProfilePageModel(
      {required this.profileTourServiceCategories,
      required this.profileHelpServiceCategories,
      required this.profilePolicyServiceCategories,
      required this.inputOldPasscode,
      required this.tryTimes,
      required this.newPasscode,
      required this.confirmNewPasscode,
      required this.myTripcIDs,
      this.page = 1,
      this.pageSize = 10,
      this.currentTripcID,
      this.isLoading = false,
      this.canLoadmore = true,
      this.isLoadingLoadMore = false,
      this.isLoadingDetailTripcId = false,
      this.errorMessage,
      this.oldPasscode,
      this.detailTripcID,
      this.forgotPasscodeOtp,
      this.errorReNewPasscode = '',
      this.tokenFromForgotPasscode = ''});
  final int page;
  final int pageSize;
  final List<TripcProfileServiceCategory> profileTourServiceCategories;
  final List<TripcProfileServiceCategory> profileHelpServiceCategories;
  final List<TripcProfileServiceCategory> profilePolicyServiceCategories;
  final List<MembershipModel> myTripcIDs;
  final String inputOldPasscode;
  final String? oldPasscode;
  final MembershipModel? currentTripcID;
  final TripcIdModel? detailTripcID;
  final bool canLoadmore;
  final bool isLoadingDetailTripcId;
  final bool isLoading;
  final bool isLoadingLoadMore;
  final String? errorMessage;

  final int tryTimes;
  final String? forgotPasscodeOtp;
  final String? tokenFromForgotPasscode;

  final String newPasscode;
  final String confirmNewPasscode;
  final String errorReNewPasscode;

  static ProfilePageModel getDefault() {
    return ProfilePageModel(
        tryTimes: 4,
        inputOldPasscode: '',
        newPasscode: '',
        confirmNewPasscode: '',
        myTripcIDs: [],
        profileTourServiceCategories: [
          TripcProfileServiceCategory.tourBooked,
          TripcProfileServiceCategory.tourSaved,
          TripcProfileServiceCategory.wallet
        ],
        profileHelpServiceCategories: [
          TripcProfileServiceCategory.moment,
          TripcProfileServiceCategory.recent,
          TripcProfileServiceCategory.contact,
          TripcProfileServiceCategory.gift,
          TripcProfileServiceCategory.support,
        ],
        profilePolicyServiceCategories: [
          TripcProfileServiceCategory.info,
          // TripcProfileServiceCategory.review,
          TripcProfileServiceCategory.pravicy
        ]);
  }

  ProfilePageModel copyWith(
      {MembershipModel? currentTripcID,
      List<TripcProfileServiceCategory>? profileTourServiceCategories,
      List<TripcProfileServiceCategory>? profileHelpServiceCategories,
      List<TripcProfileServiceCategory>? profilePolicyServiceCategories,
      List<MembershipModel>? myTripcIDs,
      bool? isLoadingDetailTripcId,
      TripcIdModel? detailTripcID,
      String? newPasscode,
      String? confirmNewPasscode,
      String? inputPasscode,
      bool? isLoading,
      int? tryTimes,
      int? page,
      String? oldPasscode,
      bool? canLoadmore,
      bool? isLoadingLoadMore,
      String? errorMessage,
      int? pageSize,
      String? tokenFromForgotPasscode,
      String? forgotPasscodeOtp,
      String? errorReNewPasscode}) {
    return ProfilePageModel(
        inputOldPasscode: inputPasscode ?? inputOldPasscode,
        currentTripcID: currentTripcID ?? this.currentTripcID,
        profileTourServiceCategories:
            profileTourServiceCategories ?? this.profileTourServiceCategories,
        profileHelpServiceCategories:
            profileHelpServiceCategories ?? this.profileHelpServiceCategories,
        tryTimes: tryTimes ?? this.tryTimes,
        confirmNewPasscode: confirmNewPasscode ?? this.confirmNewPasscode,
        newPasscode: newPasscode ?? this.newPasscode,
        detailTripcID: detailTripcID ?? this.detailTripcID,
        myTripcIDs: myTripcIDs ?? this.myTripcIDs,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize,
        canLoadmore: canLoadmore ?? this.canLoadmore,
        isLoading: isLoading ?? this.isLoading,
        isLoadingLoadMore: isLoadingLoadMore ?? this.isLoadingLoadMore,
        oldPasscode: oldPasscode ?? this.oldPasscode,
        errorMessage: errorMessage ?? this.errorMessage,
        isLoadingDetailTripcId:
            isLoadingDetailTripcId ?? this.isLoadingDetailTripcId,
        profilePolicyServiceCategories: profilePolicyServiceCategories ??
            this.profilePolicyServiceCategories,
        tokenFromForgotPasscode:
            tokenFromForgotPasscode ?? this.tokenFromForgotPasscode,
        forgotPasscodeOtp: forgotPasscodeOtp ?? this.forgotPasscodeOtp,
        errorReNewPasscode: errorReNewPasscode ?? this.errorReNewPasscode);
  }

  ///Enter Right Old Passcode in Bottomsheet
  bool get isRightOldPasscode {
    return (inputOldPasscode.length == 6 &&
            inputOldPasscode == oldPasscode &&
            !isLoading) ||
        (inputOldPasscode.length < 6);
  }

  // bool get resetPasscodeMatch {
  //   return newPasscode == confirmNewPasscode &&
  //       newPasscode.length == 6 &&
  //       confirmNewPasscode.length == 6;
  // }

  ///Valid New Passcode & Confirm Passcode in ResetPasscodePage
  bool get isEnableResetPass {
    return (newPasscode.length == 6 &&
        confirmNewPasscode.length == 6 &&
        newPasscode == confirmNewPasscode);
  }
}

class ProfilePageProvider extends StateNotifier<ProfilePageModel> {
  ProfilePageProvider(super._state);

  final ApiMembership _apiMembership = ApiMembership();
  final ApiAuth _apiAuth = ApiAuth();

  void resetState() {
    state = ProfilePageModel.getDefault();
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void forceLoading(bool value, {bool loadingDetailedTripcID = false}) {
    if (loadingDetailedTripcID) {
      state = state.copyWith(isLoadingDetailTripcId: value);
      return;
    }
    state = state.copyWith(isLoading: value);
  }

  void forceLoadingLoadMore(bool value) {
    state = state.copyWith(isLoadingLoadMore: value);
  }

  void loadMore() {
    if (!state.canLoadmore) return;
    forceLoadingLoadMore(true);
    setPageGetData(page: state.page + 1, pageSize: 10);
    getMyMembership();
  }

  void setPageGetData({required int page, required int pageSize}) {
    state = state.copyWith(page: page, pageSize: pageSize);
  }

  // void selectTripcID(MembershipModel value) {
  //   state = state.copyWith(currentTripcID: value);
  // }

  // void selectDetailTripcID(TripcIdModel value) {
  //   state = state.copyWith(detailTripcID: value);
  // }

  // void typingNewPasscode(String value) {
  //   // state = state.copyWith(newPasscode: value);
  // }

  // void typingConfirmPasscode(String value) {
  //   state = state.copyWith(confirmNewPasscode: value);
  // }

  void appendMyTripcIds(MembershipListNumbersResponse result) {
    if (state.myTripcIDs.isEmpty) {
      state = state.copyWith(myTripcIDs: result.data);
      state = state.copyWith(
          canLoadmore: state.myTripcIDs.length < (result.total ?? 0));
      return;
    }
    state = state.copyWith(myTripcIDs: [...state.myTripcIDs, ...result.data]);
    state = state.copyWith(
        canLoadmore: state.myTripcIDs.length < (result.total ?? 0));
    forceLoadingLoadMore(false);
  }

  Future<bool> getMyMembership() async {
    setErrorMessage('');
    final request = ListDataRequest(page: state.page, pageSize: state.pageSize);
    forceLoading(true);
    try {
      final result =
          await _apiMembership.getMyMembership(request: request).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      forceLoading(false);
      appendMyTripcIds(result);
      return true;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  Future<void> getDetailedMemberShip({required int membershipID}) async {
    forceLoading(true, loadingDetailedTripcID: true);
    try {
      final result =
          await _apiMembership.getMembershipDetail(id: membershipID).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      forceLoading(false, loadingDetailedTripcID: true);
      state = state.copyWith(currentTripcID: result.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false, loadingDetailedTripcID: true);
    }
  }

  Future<bool> refreshData() async {
    state = state.copyWith(
        myTripcIDs: [],
        canLoadmore: true,
        isLoadingLoadMore: false,
        isLoading: false);
    setPageGetData(page: 1, pageSize: 10);
    return await getMyMembership();
  }

  Future<bool> switchMembership() async {
    setErrorMessage('');
    forceLoading(true);
    try {
      final result = await _apiMembership
          .switchMemberShip(id: state.currentTripcID?.id ?? 0)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      forceLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  Future<bool> deactiveMembership() async {
    setErrorMessage('');
    forceLoading(true);
    try {
      final result = await _apiMembership
          .deactiveMemberShip(id: state.currentTripcID?.id ?? 0)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      forceLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  //*******************Verify Passcode BottemSheet***//
  void resetVerifyOldPasscodePage() {
    state = state.copyWith(
      inputPasscode: '',
      errorMessage: '',
      tryTimes: -1,
    );
  }

  Future<bool?> typingOldPasscode(String value) async {
    state = state.copyWith(inputPasscode: value);
    if (value.length == 6) {
      final result = await verifyOldPassCode();
      if (result) {
        state = state.copyWith(oldPasscode: value);
        return true;
      } else {
        return false;
      }
    }
    return null;
  }

  Future<bool> verifyOldPassCode() async {
    setErrorMessage('');
    try {
      final result = await _apiAuth
          .verifyOldPassCode(
              memberShipId: state.currentTripcID?.id,
              passcode: state.inputOldPasscode)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(tryTimes: result.remainingAttempts);
      return result.status;
    } catch (exceptionMessage) {
      state = state.copyWith(tryTimes: 0);
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      return false;
    }
  }
  //***********Reset Passcode Page****************//

  void clearResetPasscodePage() {
    state = state.copyWith(
        newPasscode: '',
        confirmNewPasscode: '',
        errorReNewPasscode: '',
        errorMessage: '');
  }

  void typingNewPassCode(String value, BuildContext context) {
    state = state.copyWith(newPasscode: value);

    if (value.length == 6 && state.confirmNewPasscode.length == 6) {
      state = state.copyWith(
          errorReNewPasscode: value != state.confirmNewPasscode
              ? context.strings.text_newly_entered_password_is_incorrect
              : '');
    } else {
      state = state.copyWith(errorReNewPasscode: '');
    }
  }

  void typingReNewPasscode(String value, BuildContext context) {
    if (value.length == 6 && state.newPasscode.length == 6) {
      state = state.copyWith(
          confirmNewPasscode: value,
          errorReNewPasscode: value.isNotEmpty && value != state.newPasscode
              ? context.strings.text_newly_entered_password_is_incorrect
              : '');
    } else {
      state = state.copyWith(errorReNewPasscode: '');
    }
  }

  Future<bool> updatePasscode() async {
    final request = UpdatePasscodeRequest(
        membershipId: state.currentTripcID?.id ?? 0,
        passcode: state.confirmNewPasscode);
    forceLoading(true);
    try {
      final result =
          await _apiMembership.updatePasscode(request: request).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      forceLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  //********Verify OTP Forgot Passcode Page************//

  void clearVerifyPasscodePage() {
    state = state.copyWith(forgotPasscodeOtp: '', errorMessage: '');
  }

  void typingForgotPasscodeOtp(String value) {
    state = state.copyWith(
      forgotPasscodeOtp: value,
    );
  }

  Future<bool> sendOtpForgotPasscode() async {
    final request = ForgotPasscodeRequest(
      membershipId: state.currentTripcID?.id ?? 0,
      email: globalCacheAuth.user?.email ?? '',
    );
    try {
      final result =
          await _apiMembership.sendOtpForgotPasscode(request: request).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      state = state.copyWith(tokenFromForgotPasscode: result.token);
      return result.status;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  Future<bool> verifyOtpForgotPasscode() async {
    setErrorMessage('');
    final request = VerifyOtpForgotPasscodeRequest(
      token: state.tokenFromForgotPasscode ?? '',
      otp: state.forgotPasscodeOtp ?? '',
      email: globalCacheAuth.user?.email ?? '',
    );
    try {
      forceLoading(true);
      final result = await _apiMembership
          .verifyOtpForgotPasscode(request: request)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      forceLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }
}

final pProfilePageProvider =
    StateNotifierProvider<ProfilePageProvider, ProfilePageModel>(
        (ref) => ProfilePageProvider(ProfilePageModel.getDefault()));
