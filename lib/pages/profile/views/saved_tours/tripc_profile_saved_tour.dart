import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/ticket_tour/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../../services/app/app_assets.dart';
import '../../../../services/providers/providers.dart';
import '../../../../widgets/app_loading.dart';
import '../../../../widgets/commons/tripc_scafford/tripc_scafford.dart';
import '../../../../widgets/commons/tripc_text/tripc_text.dart';
import '../../../homepage/components/list_tours_grid_view.dart';
import '../../components/tripc_not_yet_login_dialog.dart';
import '../../providers/tripc_profile_saved_tour_provider.dart';

class TripcProfileSavedTour extends ConsumerStatefulWidget {
  const TripcProfileSavedTour({super.key});

  @override
  ConsumerState<TripcProfileSavedTour> createState() =>
      _TripcProfileSavedTourState();
}

class _TripcProfileSavedTourState extends ConsumerState<TripcProfileSavedTour> {
  final _gridViewScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _gridViewScrollController.addListener(() {
      _loadMore();
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(pSavedTourProvider.notifier).getSavedTours();
    });
  }

  void _loadMore() {
    final ScrollController controller = _gridViewScrollController;
    if (controller.position.pixels >=
        controller.position.maxScrollExtent - 100) {
      ref.read(pSavedTourProvider.notifier).loadMore();
    }
  }

  @override
  void dispose() {
    _gridViewScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isLoading =
        ref.watch(pSavedTourProvider.select((value) => value.isLoading));
    final tours = ref.watch(pSavedTourProvider.select((value) => value.tours));
    return Stack(children: [
      TripcScaffold(
          hasBackButton: true,
          backgroundColor: AppAssets.origin().whiteBackgroundColor,
          toolbarHeight: 46.H,
          leadingPadding: EdgeInsets.only(top: 10.H),
          leadingWidth: 37,
          titleAppBar: TripcText(
            context.strings.text_saved_tour,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            textColor: AppAssets.origin().black,
            padding: EdgeInsets.only(top: 12.H),
          ),
          bottom: PreferredSize(
              preferredSize: const Size(double.infinity, 1),
              child: Divider(
                height: 1,
                color: AppAssets.init.lightGray,
              )),
          body: RefreshIndicator(
            onRefresh: () async {
              ref.read(pSavedTourProvider.notifier).getSavedTours();
            },
            child: Visibility(
              visible: tours.isNotEmpty,
              replacement: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppAssets.init.imEmpty.widget(fit: BoxFit.cover),
                    SizedBox(
                      height: 8.H,
                    ),
                    TripcText(
                      context.strings.text_no_saved_tour,
                      fontWeight: FontWeight.w400,
                      fontSize: 16,
                      textColor: AppAssets.origin().black,
                      height: 1.2,
                    ),
                  ],
                ),
              ),
              child: ListToursGridView(
                listScrollController: null,
                gridViewScrollController: _gridViewScrollController,
                services: tours,
                isGridView: true,
                isLoading: isLoading,
                onSaved: (id) {
                  if (globalCacheAuth.isLogged()) {
                    ref.read(pTicketTourProvider.notifier).listSavedTour(id);
                  } else {
                    dialogHelpers.show(context, child: NotYetLoginDialog(
                      resultHandler: () async {
                        ref.read(pSavedTourProvider.notifier).getSavedTours();
                      },
                    ));
                  }
                },
              ),
            ),
          )),
      AppLoading(isRequesting: isLoading),
    ]);
  }
}
