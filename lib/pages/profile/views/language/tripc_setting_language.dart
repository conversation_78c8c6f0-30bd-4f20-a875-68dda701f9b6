import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/provider_locale.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcSettingLanguage extends ConsumerWidget {
  const TripcSettingLanguage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedLanguage =
        ref.watch(pLocaleProvider.select((value) => value.applocale));
    return TripcScaffold(
      toolbarHeight: 44.H,
      appBarColor: AppAssets.origin().whiteBackgroundColor,
      titleAppBar: TripcText(
        context.strings.text_language,
        fontSize: 16.SP,
        fontWeight: FontWeight.w600,
        textCase: TextCaseType.none,
      ),
      hasBackButton: true,
      needUnFocus: true,
      resizeToAvoidBottomInset: true,
      backgroundColor: AppAssets.origin().whiteSmokeColor,
      body: Container(
        decoration: BoxDecoration(
            border: Border(
              top: BorderSide(
                color: AppAssets.origin().whiteSmokeColor,
                width: 1,
              ),
            ),
            color: AppAssets.origin().whiteBackgroundColor,
            boxShadow: [AppAssets.origin().profileServiceAreaShadow]),
        child: ListView.builder(
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: AppLocaLe.values.length,
          itemBuilder: (context, index) {
            bool isSelected = AppLocaLe.values[index] == selectedLanguage;

            return SizedBox(
              height: 54.H,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.W),
                child: TripcIconButton(
                  onPressed: () => ref
                      .read(pLocaleProvider)
                      .changeLocale(AppLocaLe.values[index]),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(height: 1.H),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TripcText(
                            AppLocaLe.values[index].name(context),
                            ignorePointer: true,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            textColor: isSelected
                                ? AppAssets.origin().darkBlueColor
                                : AppAssets.origin().blackColor,
                          ),
                          if (isSelected)
                            AppAssets.origin()
                                .icCheckmark
                                .widget(width: 16.W, height: 16.H),
                        ],
                      ),
                      if (index < AppLocaLe.values.length - 1)
                        Divider(
                            height: 1,
                            color: AppAssets.origin().settingDividerColor)
                      else
                        SizedBox(height: 1.H),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
