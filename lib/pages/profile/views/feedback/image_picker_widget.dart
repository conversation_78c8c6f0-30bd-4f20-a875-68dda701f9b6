import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../../services/app/app_assets.dart';
import '../../providers/receiving_feedback_image_picker_provider.dart';

class ImagePickerWidget extends ConsumerWidget {
  const ImagePickerWidget({super.key});

  Future<void> _pickImage(BuildContext context, WidgetRef ref) async {
    final picker = ImagePicker();
    final result = await picker.pickImage(source: ImageSource.gallery);
    if (result != null) {
      ref.read(imagePickerProvider.notifier).addImage(result.path);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final imageState = ref.watch(imagePickerProvider);

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: [
        for (int i = 0; i < imageState.imagePaths.length; i++)
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12.SP),
                child: Image.file(
                  File(imageState.imagePaths[i]),
                  width: 98.W,
                  height: 98.H,
                  fit: BoxFit.cover,
                ),
              ),
              Positioned(
                right: 0,
                top: 0,
                child: GestureDetector(
                  onTap: () =>
                      ref.read(imagePickerProvider.notifier).removeImageAt(i),
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: const BoxDecoration(
                      color: Colors.black54,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.white,
                    ),
                  ),
                ),
              )
            ],
          ),
        if (imageState.canAddMore)
          GestureDetector(
            onTap: () => _pickImage(context, ref),
            child: Container(
              width: 98.W,
              height: 98.H,
              decoration: BoxDecoration(
                color: AppAssets.origin().grayE5,
                borderRadius: BorderRadius.circular(12.SP),
              ),
              child: Center(
                child: AppAssets.origin().iconAddGallery.widget(
                      height: 24.H,
                      width: 24.W,
                    ),
              ),
            ),
          ),
      ],
    );
  }
}
