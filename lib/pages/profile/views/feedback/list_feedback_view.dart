import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/information_row.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';

import '../../../../models/app/text_case_type.dart';
import '../../../../services/app/app_assets.dart';
import '../../../../widgets/app_loading.dart';
import '../../../../widgets/commons/information_row.dart';
import '../../../../widgets/commons/tripc_text/tripc_text.dart';
import '../../providers/list_feedback_provider.dart';

class TripcListFeedback extends ConsumerStatefulWidget {
  const TripcListFeedback({super.key});

  @override
  ConsumerState<TripcListFeedback> createState() => _TripcListFeedbackState();
}

class _TripcListFeedbackState extends ConsumerState<TripcListFeedback> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await ref.read(pListFeedbackProvider.notifier).feedbacks();
    });
  }

  @override
  Widget build(BuildContext context) {
    final loading =
        ref.watch(pListFeedbackProvider.select((value) => value.isLoading));

    final feedbacks =
        ref.watch(pListFeedbackProvider.select((value) => value.feedbacks));

    return Stack(
      children: [
        TripcScaffold(
            toolbarHeight: 30.H,
            appBarColor: AppAssets.origin().whiteBackgroundColor,
            titleAppBar: TripcText(
              context.strings.list_feedback_social_organization,
              fontSize: 18.SP,
              fontWeight: FontWeight.w500,
              textCase: TextCaseType.none,
            ),
            hasBackButton: true,
            backgroundColor: AppAssets.origin().whiteSmokeColor,
            body: Stack(
              children: [
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: SingleChildScrollView(
                      physics: const ClampingScrollPhysics(),
                      child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.W, vertical: 16.H),
                          child: Column(
                            spacing: 16.H,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: feedbacks
                                .map((item) => Padding(
                                      padding: EdgeInsets.only(bottom: 16.H),
                                      child: _cardWidget(
                                        name: item.name,
                                        number: item.number,
                                        content: item.content,
                                        time: item.time.toRelativeTime(),
                                      ),
                                    ))
                                .toList(),
                          ))),
                ),
              ],
            )),
        AppLoading(isRequesting: loading)
      ],
    );
  }

  Widget _cardWidget(
      {required String? name,
      required String? number,
      required String? content,
      required String? time}) {
    final double titleWidth = 110.W;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.W, vertical: 8.H),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 16.H,
        children: [
          InformationRow(
            title: '${context.strings.name_of_social_organization}:',
            content: name ?? '',
            contentFontWeight: FontWeight.w400,
            contentFontSize: 14,
            titleWidth: titleWidth,
          ),
          InformationRow(
            title: '${context.strings.establishment_decision_number}:',
            content: number ?? '',
            contentFontWeight: FontWeight.w400,
            contentFontSize: 14,
            titleWidth: titleWidth,
          ),
          InformationRow(
            title: '${context.strings.content}:',
            content: content ?? '',
            contentFontWeight: FontWeight.w400,
            contentFontSize: 14,
            titleWidth: titleWidth,
          ),
          Divider(
            height: 0.5,
            color: AppAssets.origin().grayE5,
          ),
          TripcText(
            time,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            textColor: AppAssets.origin().grey53,
          ),
        ],
      ),
    );
  }
}
