import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';

import '../../../../models/app/text_case_type.dart';
import '../../../../services/app/app_assets.dart';
import '../../../../services/providers/providers.dart';
import '../../../../widgets/app_loading.dart';
import '../../../../widgets/commons/app_dialog/tripc_dialog.dart';
import '../../../../widgets/commons/app_dialog/tripc_error_dialog.dart';
import '../../../../widgets/commons/tripc_button/tripc_button.dart';
import '../../../../widgets/commons/tripc_text/tripc_text.dart';
import '../../../../widgets/tripc_text_filed/tripc_text_filed.dart';
import '../../providers/receiving_feedback_image_picker_provider.dart';
import '../../providers/receiving_feedback_provider.dart';
import 'image_picker_widget.dart';

class TripcReceivingFeedback extends ConsumerStatefulWidget {
  const TripcReceivingFeedback({super.key});

  @override
  ConsumerState<TripcReceivingFeedback> createState() =>
      _TripcReceivingFeedbackState();
}

class _TripcReceivingFeedbackState
    extends ConsumerState<TripcReceivingFeedback> {
  late final TextEditingController _nameController;
  late final TextEditingController _numberController;
  late final TextEditingController _contentController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _numberController = TextEditingController();
    _contentController = TextEditingController();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _numberController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void resetState() {
    ref.read(pReceivingFeedbackProvider.notifier).resetState();
    ref.read(imagePickerProvider.notifier).resetState();
    _nameController.text = '';
    _numberController.text = '';
    _contentController.text = '';
  }

  Future<void> onSendFeedback() async {
    final result =
        await ref.read(pReceivingFeedbackProvider.notifier).createFeedback();
    if (result) {
      dialogHelpers.show(context,
          barrierDismissible: false,
          child: TripcDialog(
              title: context.strings.feedback_successfully,
              onTap: () {
                resetState();
                Navigator.pop(context);
              },
              icon: AppAssets.init.icSuccess.widget(
                height: 40.H,
                width: 40.H,
              ),
              titleButton: context.strings.text_close,
              contentPadding: EdgeInsets.symmetric(horizontal: 24.W)
                  .copyWith(top: 24.H, bottom: 27.H),
              child: TripcText(
                context.strings.feedback_success_content,
                fontWeight: FontWeight.w400,
                padding: EdgeInsets.symmetric(vertical: 16.H),
              )));
    } else {
      dialogHelpers.show(context,
          child: TripcErrorDialog(
            errorText: context.strings.text_error_something_wrong,
          ));
    }
  }

  @override
  Widget build(BuildContext context) {
    final loading = ref
        .watch(pReceivingFeedbackProvider.select((value) => value.isLoading));
    return Stack(
      children: [
        TripcScaffold(
            toolbarHeight: 30.H,
            appBarColor: AppAssets.origin().whiteBackgroundColor,
            titleAppBar: TripcText(
              context.strings.receive_feedback_social_organization,
              fontSize: 18.SP,
              fontWeight: FontWeight.w500,
              textCase: TextCaseType.none,
            ),
            hasBackButton: true,
            backgroundColor: AppAssets.origin().whiteSmokeColor,
            body: Stack(
              children: [
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 151.H,
                  child: SingleChildScrollView(
                      physics: const ClampingScrollPhysics(),
                      child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.W, vertical: 16.H),
                          child: Column(
                            spacing: 24.H,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Column(
                                children: [
                                  _buildLabel(context
                                      .strings.name_of_social_organization),
                                  TripcTextField(
                                    hintText: context.strings
                                        .enter_name_of_social_organization,
                                    controller: _nameController,
                                    onChanged: (value) => ref
                                        .read(
                                            pReceivingFeedbackProvider.notifier)
                                        .setName(context, value),
                                    hintTextColor: AppAssets.origin().gray8D,
                                    fontSize: 16,
                                    fillColor: Colors.white,
                                    contentPadding: EdgeInsets.symmetric(
                                        vertical: 10.5.H, horizontal: 16.W),
                                  ),
                                ],
                              ),
                              Column(
                                children: [
                                  _buildLabel(context
                                      .strings.establishment_decision_number),
                                  TripcTextField(
                                    hintText: context.strings
                                        .enter_establishment_decision_number,
                                    controller: _numberController,
                                    onChanged: (value) => ref
                                        .read(
                                            pReceivingFeedbackProvider.notifier)
                                        .setNumber(context, value),
                                    hintTextColor: AppAssets.origin().gray8D,
                                    fontSize: 16,
                                    fillColor: Colors.white,
                                    contentPadding: EdgeInsets.symmetric(
                                        vertical: 10.5.H, horizontal: 16.W),
                                  ),
                                ],
                              ),
                              Column(
                                children: [
                                  _buildLabel(context.strings.content),
                                  TripcTextField(
                                    hintText: context.strings
                                        .enter_content_no_more_than_250_characters,
                                    controller: _contentController,
                                    onChanged: (value) => ref
                                        .read(
                                            pReceivingFeedbackProvider.notifier)
                                        .setContent(context, value),
                                    hintTextColor: AppAssets.origin().gray8D,
                                    maxLines: 5,
                                    maxLength: 250,
                                    fontSize: 16,
                                    fillColor: Colors.white,
                                    contentPadding: EdgeInsets.symmetric(
                                        vertical: 10.5.H, horizontal: 16.W),
                                  ),
                                ],
                              ),
                              const ImagePickerWidget()
                            ],
                          ))),
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        top: BorderSide(
                          color: Colors.grey.shade300,
                          width: 1.0,
                        ),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          offset: const Offset(0, -2),
                          blurRadius: 8.0,
                          spreadRadius: 1.0,
                        ),
                      ],
                    ),
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.W, vertical: 8.H)
                            .copyWith(bottom: 14.H),
                    child: TripcButton(
                      onPressed: () => onSendFeedback(),
                      showSuggestLoginDialog: true,
                      style: AppButtonStyle(
                          backgroundColor: AppAssets.origin().primaryColorV2),
                      textCase: TextCaseType.none,
                      isButtonDisabled: !ref.watch(pReceivingFeedbackProvider
                          .select((value) => value.isButtonEnable)),
                      height: 48,
                      title: context.strings.text_send,
                    ),
                  ),
                ),
              ],
            )),
        AppLoading(isRequesting: loading)
      ],
    );
  }

  Widget _buildLabel(String title) {
    return Row(
      children: [
        TripcText(
          title,
          fontSize: 14,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w500,
          padding: EdgeInsets.only(bottom: 4.H),
          textColor: AppAssets.origin().black,
        ),
        TripcText(
          '*',
          fontSize: 14,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w500,
          padding: EdgeInsets.only(left: 4.H),
          textColor: AppAssets.origin().redDotColor,
        ),
      ],
    );
  }
}
