import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/app_show_api_error_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../../models/app/text_case_type.dart';
import '../../../../widgets/commons/tripc_otp_text_field/tripc_otp_text_field.dart';
import '../../components/tripc_reset_passcode_success_dialog.dart';
import '../../providers/tripc_profile_provider.dart';

class TripcProfileResetPasscode extends ConsumerStatefulWidget {
  const TripcProfileResetPasscode({super.key});

  @override
  ConsumerState<TripcProfileResetPasscode> createState() =>
      _TripcProfileResetPasscodeState();
}

class _TripcProfileResetPasscodeState
    extends ConsumerState<TripcProfileResetPasscode> {
  final _newPassController = TextEditingController();
  final _reEnterNewPassController = TextEditingController();
  final _confirmPassCodeFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _newPassController.addListener(() {
      if (_newPassController.text.length == 6) {
        _confirmPassCodeFocusNode.requestFocus();
      }
    });
    _reEnterNewPassController.addListener(() {
      if (_reEnterNewPassController.text.length == 6) {
        unfocusKeyboard();
      }
    });
  }

  @override
  void dispose() {
    _newPassController.dispose();
    _reEnterNewPassController.dispose();
    _confirmPassCodeFocusNode.dispose();
    super.dispose();
  }

  Future<void> _resetPass() async {
    unfocusKeyboard();
    final result =
        await ref.read(pProfilePageProvider.notifier).updatePasscode();
    if (result) {
      dialogHelpers.show(context,
          barrierDismissible: false,
          child: const TripcResetPasscodeSuccessDialog());
    } else {
      final error = ref.read(pProfilePageProvider).errorMessage ?? '';
      dialogHelpers.show(context, child: ErrorDialog(text: error));
    }
  }

  @override
  Widget build(BuildContext context) {
    final errorReEnterNewPass = ref.watch(
        pProfilePageProvider.select((value) => value.errorReNewPasscode));
    final isEnableResetPass = ref
        .watch(pProfilePageProvider.select((value) => value.isEnableResetPass));
    final isLoading =
        ref.watch(pProfilePageProvider.select((value) => value.isLoading));
    return Stack(
      children: [
        TripcScaffold(
            toolbarHeight: 54.H,
            titleAppBar: TripcText(
              context.strings.text_reset_passcode,
              fontSize: 16.SP,
              fontWeight: FontWeight.w600,
              textCase: TextCaseType.none,
            ),
            hasBackButton: true,
            needUnFocus: true,
            backgroundColor: AppAssets.origin().whiteSmokeColor,
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.W)
                  .copyWith(top: 20.H, bottom: context.spacingBottom),
              child: Column(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TripcText(
                          context.strings.text_reset_passcode_note,
                          fontSize: 14,
                          fontWeight: FontWeight.w300,
                          textAlign: TextAlign.start,
                          height: 1.3,
                          textColor: AppAssets.origin().darkGreyTextColor,
                        ),
                        SizedBox(height: 30.H),
                        TripcText(
                          context.strings.text_enter_new_passcode,
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          textColor: AppAssets.origin().darkGreyTextColor,
                          padding: EdgeInsets.only(bottom: 12.H),
                        ),
                        TripcOtpTextField(
                          controller: _newPassController,
                          onChanged: (value) => ref
                              .read(pProfilePageProvider.notifier)
                              .typingNewPassCode(value, context),
                        ),
                        SizedBox(height: 20.H),
                        TripcText(
                          context.strings.text_re_enter_new_passcode,
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          textColor: AppAssets.origin().darkGreyTextColor,
                          padding: EdgeInsets.only(bottom: 12.H),
                        ),
                        TripcOtpTextField(
                          controller: _reEnterNewPassController,
                          focusNode: _confirmPassCodeFocusNode,
                          isError: errorReEnterNewPass.isNotEmpty,
                          onChanged: (value) => ref
                              .read(pProfilePageProvider.notifier)
                              .typingReNewPasscode(value, context),
                        ),
                        Visibility(
                          visible: errorReEnterNewPass.isNotEmpty,
                          child: TripcText(
                            context.strings
                                .text_re_entered_new_passcode_incorrectly,
                            fontSize: 12,
                            fontStyle: FontStyle.italic,
                            fontWeight: FontWeight.w400,
                            textColor: AppAssets.origin().redDotColor,
                            padding: EdgeInsets.only(top: 20.H),
                          ),
                        ),
                      ],
                    ),
                  ),
                  TripcButton(
                    onPressed: _resetPass,
                    isButtonDisabled: !isEnableResetPass,
                    title: context.strings.text_send,
                    height: 56,
                  )
                ],
              ),
            )),
        AppLoading(isRequesting: isLoading)
      ],
    );
  }
}
