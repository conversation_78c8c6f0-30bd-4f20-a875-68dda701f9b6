// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:tripc_app/models/app/text_case_type.dart';
// import 'package:tripc_app/pages/profile/components/tripc_reset_passcode_success_dialog.dart';
// import 'package:tripc_app/pages/profile/providers/tripc_profile_provider.dart';
// import 'package:tripc_app/services/app/app_assets.dart';
// import 'package:tripc_app/services/providers/providers.dart';
// import 'package:tripc_app/utils/app_extension.dart';
// import 'package:tripc_app/widgets/app_loading.dart';
// import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
// import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
// import 'package:tripc_app/widgets/commons/tripc_otp_text_field/tripc_otp_text_field.dart';
// import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
// import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

// class TripcIDResetPassCode extends ConsumerStatefulWidget {
//   const TripcIDResetPassCode({super.key});

//   @override
//   ConsumerState<TripcIDResetPassCode> createState() =>
//       _TripcIDResetPassCodeState();
// }

// class _TripcIDResetPassCodeState extends ConsumerState<TripcIDResetPassCode> {
//   final _newPasscodeCrl = TextEditingController();
//   final _confirmNewPasscodeCrl = TextEditingController();
//   final _confirmPassCodeFocusNode = FocusNode();

//   @override
//   void initState() {
//     super.initState();
//     _newPasscodeCrl.addListener(() {
//       if (_newPasscodeCrl.text.length == 6) {
//         _confirmPassCodeFocusNode.requestFocus();
//       }
//     });
//     _confirmNewPasscodeCrl.addListener(() {
//       if (_confirmNewPasscodeCrl.text.length == 6) {
//         unfocusKeyboard();
//       }
//     });
//   }

//   @override
//   void dispose() {
//     _newPasscodeCrl.dispose();
//     _confirmNewPasscodeCrl.dispose();
//     _confirmPassCodeFocusNode.dispose();
//     super.dispose();
//   }

//   Future _changePasscode() async {
//     final result =
//         await ref.read(pProfilePageProvider.notifier).updatePasscode();
//     if (result) {
//       dialogHelpers.show(context,
//           child: const TripcResetPasscodeSuccessDialog());
//     } else {
//       dialogHelpers.show(context,
//           child: TripcErrorDialog(
//               errorText: ref.read(pProfilePageProvider).errorMessage));
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     final isLoading =
//         ref.watch(pProfilePageProvider.select((value) => value.isLoading));
//     final resetPasscodeMatch = ref.watch(
//         pProfilePageProvider.select((value) => value.resetPasscodeMatch));
//     final newPasscode =
//         ref.watch(pProfilePageProvider.select((value) => value.newPasscode));
//     final confirmNewPasscode = ref.watch(
//         pProfilePageProvider.select((value) => value.confirmNewPasscode));
//     final isError = !resetPasscodeMatch &&
//         (newPasscode.length == 6 && confirmNewPasscode.length == 6);
//     return Stack(
//       children: [
//         TripcScaffold(
//             toolbarHeight: 54.H,
//             titleAppBar: TripcText(
//               context.strings.text_reset_passcode,
//               fontSize: 16.SP,
//               fontWeight: FontWeight.w600,
//               textCase: TextCaseType.none,
//             ),
//             hasBackButton: true,
//             needUnFocus: true,
//             backgroundColor: AppAssets.origin().whiteSmokeColor,
//             body: Padding(
//               padding: EdgeInsets.symmetric(horizontal: 24.W)
//                   .copyWith(top: 20.H, bottom: context.spacingBottom),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   TripcText(
//                     context.strings.text_reset_passcode_note,
//                     fontSize: 12,
//                     fontWeight: FontWeight.w700,
//                     textAlign: TextAlign.start,
//                     height: 1.3,
//                     textColor: AppAssets.origin().darkGreyTextColor,
//                   ),
//                   SizedBox(height: 30.H),
//                   TripcText(
//                     context.strings.text_enter_new_passcode,
//                     fontSize: 12,
//                     textColor: AppAssets.origin().darkGreyTextColor,
//                     padding: EdgeInsets.only(bottom: 12.H),
//                   ),
//                   TripcOtpTextField(
//                     controller: _newPasscodeCrl,
//                     onChanged: (value) => ref
//                         .read(pProfilePageProvider.notifier)
//                         .typingNewPasscode(value),
//                   ),
//                   SizedBox(height: 20.H),
//                   TripcText(
//                     context.strings.text_re_enter_new_passcode,
//                     fontSize: 12,
//                     textColor: AppAssets.origin().darkGreyTextColor,
//                     padding: EdgeInsets.only(bottom: 12.H),
//                   ),
//                   TripcOtpTextField(
//                     controller: _confirmNewPasscodeCrl,
//                     focusNode: _confirmPassCodeFocusNode,
//                     isError: isError,
//                     onChanged: (value) => ref
//                         .read(pProfilePageProvider.notifier)
//                         .typingConfirmPasscode(value),
//                   ),
//                   Visibility(
//                     visible: isError,
//                     child: TripcText(
//                       context.strings.text_re_entered_new_passcode_incorrectly,
//                       fontSize: 8,
//                       fontWeight: FontWeight.w700,
//                       textColor: AppAssets.origin().redDotColor,
//                       padding: EdgeInsets.only(top: 20.H),
//                     ),
//                   ),
//                   const Spacer(),
//                   TripcButton(
//                     onPressed: _changePasscode,
//                     isButtonDisabled: !resetPasscodeMatch,
//                     title: context.strings.text_send,
//                     height: 56,
//                   )
//                 ],
//               ),
//             )),
//         AppLoading(isRequesting: isLoading)
//       ],
//     );
//   }
// }
