import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/membership/providers/membership_provider.dart';
import 'package:tripc_app/pages/profile/providers/tripc_profile_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/tripc_card_info_loading/tripc_card_info_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_card_info/tripc_card_info.dart';
import '../../components/setting_passcode_dialog.dart';

class TripcProfileListTripcID extends ConsumerStatefulWidget {
  const TripcProfileListTripcID({super.key});

  @override
  ConsumerState<TripcProfileListTripcID> createState() =>
      _TripcProfileListTripcIDState();
}

class _TripcProfileListTripcIDState
    extends ConsumerState<TripcProfileListTripcID> {
  final _scrollController = ScrollController();
  @override
  void initState() {
    super.initState();
    _scrollController.onBottomReach(sensitivity: 150, () {
      _loadMore();
    }, throttleDuration: const Duration(seconds: 1));
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ref.read(pProfilePageProvider.notifier).refreshData();
      // ref.listenManual(
      //     pProfilePageProvider.select(
      //       (value) => value.errorMessage,
      //     ), (_, state) {
      //   if (state?.isEmpty ?? true) {
      //     return;
      //   }
      //   dialogHelpers.show(context,
      //       child: TripcErrorDialog(
      //           errorText: ErrorParser.getErrorMessage(state ?? '', context)));
      // });
    });
  }

  void _loadMore() {
    ref.read(pProfilePageProvider.notifier).loadMore();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final myTripcIDs =
        ref.watch(pProfilePageProvider.select((value) => value.myTripcIDs));
    final isloading =
        ref.watch(pProfilePageProvider.select((value) => value.isLoading));
    final user = ref.watch(pAccountProvider.select((value) => value.user));
    return TripcScaffold(
        toolbarHeight: 54.H,
        appBarColor: AppAssets.origin().whiteBackgroundColor,
        titleAppBar: TripcText(
          context.strings.text_tripc_id_list,
          fontSize: 16.SP,
          fontWeight: FontWeight.w600,
          textCase: TextCaseType.none,
        ),
        hasBackButton: true,
        needUnFocus: true,
        resizeToAvoidBottomInset: true,
        backgroundColor: AppAssets.origin().whiteSmokeColor,
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.W)
              .copyWith(top: 15.H, bottom: context.spacingBottom),
          child: Column(
            children: [
              Expanded(
                child: Visibility(
                  visible: !isloading,
                  replacement: const ListTripcCardInfoLoading(),
                  child: RefreshIndicator(
                    onRefresh: () async =>
                        ref.read(pProfilePageProvider.notifier).refreshData(),
                    child: ListView.separated(
                        controller: _scrollController,
                        padding: EdgeInsets.zero,
                        itemCount: myTripcIDs.length,
                        separatorBuilder: (context, _) => SizedBox(
                              height: 20.H,
                            ),
                        itemBuilder: (context, index) => TripcCardInfo(
                              onPressed: () {
                                final selectedTripC =
                                    user?.memberships?.firstWhereOrNull(
                                  (element) =>
                                      element.id == myTripcIDs[index].id,
                                );
                                if (selectedTripC?.isExistPasscode == 0) {
                                  dialogHelpers.show(context,
                                      barrierDismissible: false,
                                      child: SettingPasscodeDialog(onTap: () {
                                    Navigator.pop(context);
                                    ref
                                        .read(
                                            pMembershipScreenProvider.notifier)
                                        .selectNumber(selectedTripC);
                                    AppRoute.pushNamed(context,
                                        routeName: AppRoute.routeTypePassCode,
                                        arguments: true);
                                  }));
                                } else {
                                  AppRoute.pushNamed(context,
                                      routeName: AppRoute.routeTripcIDSetting,
                                      arguments: myTripcIDs[index].id ?? 0);
                                }
                              },
                              isCardSelected: user?.selectedMembership?.id ==
                                  myTripcIDs[index].id,
                              hasIconMore: true,
                              tripcIDMembership: myTripcIDs[index],
                              width: context.mediaQuery.size.width - 48.W,
                            )),
                  ),
                ),
              ),
              SizedBox(
                height: 20.H,
              ),
              TripcButton(
                onPressed: () => AppRoute.pushNamed(context,
                    routeName: AppRoute.routeMembershipSignIn,
                    arguments: user?.defaultMemberShip != null),
                title: context.strings.text_add_new_tripc_id,
                textCase: TextCaseType.none,
                style: const AppButtonStyle(
                    fontSize: 16, fontWeight: FontWeight.w600),
              )
            ],
          ),
        ));
  }
}
