import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/manage_profile/components/components.dart';
import 'package:tripc_app/pages/profile/components/tripc_remove_tripcId_dialog.dart';
import 'package:tripc_app/pages/profile/components/tripc_type_passcode_bottom_sheet.dart';
import 'package:tripc_app/pages/profile/providers/tripc_profile_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/tripc_card_info_loading/tripc_card_info_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_card_info/tripc_card_info.dart';
import 'package:tripc_app/widgets/tripc_switch/tripc_switch.dart';

class TripcProfileTripcIDSetting extends ConsumerStatefulWidget {
  const TripcProfileTripcIDSetting({super.key, required this.membershipId});
  final int membershipId;

  @override
  ConsumerState<TripcProfileTripcIDSetting> createState() =>
      _TripcProfileTripcIDSettingState();
}

class _TripcProfileTripcIDSettingState
    extends ConsumerState<TripcProfileTripcIDSetting> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(pProfilePageProvider.notifier)
          .getDetailedMemberShip(membershipID: widget.membershipId);
    });
  }

  Future _switchMembership() async {
    final result =
        await ref.read(pProfilePageProvider.notifier).switchMembership();
    if (result) {
      await ref.read(pAccountProvider.notifier).getme(needLoading: true);
    } else {
      dialogHelpers.show(context,
          child: TripcErrorDialog(
              errorText: ref.read(pProfilePageProvider).errorMessage ?? ''));
    }
  }

  Future _deactiveMembership() async {
    final result =
        await ref.read(pProfilePageProvider.notifier).deactiveMembership();
    if (result) {
      await ref.read(pAccountProvider.notifier).getme(needLoading: true);
    } else {
      dialogHelpers.show(context,
          child: TripcErrorDialog(
              errorText: ref.read(pProfilePageProvider).errorMessage ?? ''));
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentTripcID =
        ref.watch(pProfilePageProvider.select((value) => value.currentTripcID));
    final user = ref.watch(pAccountProvider.select((value) => value.user));
    final isLoadingDetail = ref.watch(
        pProfilePageProvider.select((value) => value.isLoadingDetailTripcId));
    final isLoading =
        ref.watch(pProfilePageProvider.select((value) => value.isLoading));
    final isLoadingGetme =
        ref.watch(pAccountProvider.select((value) => value.isLoading));
    final listTripcId =
        ref.watch(pProfilePageProvider.select((value) => value.myTripcIDs));
    return Stack(
      children: [
        TripcScaffold(
            toolbarHeight: 54.H,
            appBarColor: AppAssets.origin().whiteBackgroundColor,
            onLeadingPressed: () {
              Navigator.popUntil(context,
                  (route) => route.settings.name == AppRoute.routeListTripcID);
            },
            titleAppBar: TripcText(
              context.strings.text_tripc_id_setting,
              fontSize: 16.SP,
              fontWeight: FontWeight.w600,
              textCase: TextCaseType.none,
            ),
            hasBackButton: true,
            needUnFocus: true,
            resizeToAvoidBottomInset: true,
            backgroundColor: AppAssets.origin().whiteSmokeColor,
            body: ListView(
              padding: EdgeInsets.symmetric(horizontal: 24.W).copyWith(
                  top: 15.H, bottom: context.mediaQuery.padding.bottom),
              children: [
                Visibility(
                  visible: !isLoadingDetail,
                  replacement: const TripcCardInfoLoading(),
                  child: TripcCardInfo(
                    isCardSelected:
                        user?.selectedMembership?.id == currentTripcID?.id,
                    tripcIDMembership: currentTripcID,
                    width: context.mediaQuery.size.width - 48.W,
                  ),
                ),
                SizedBox(
                  height: 30.H,
                ),
                InfoBoxProfileShadow(
                  padding: EdgeInsets.symmetric(horizontal: 12.W),
                  child: Column(
                    children: [
                      SingleLineInfo(
                          padding: EdgeInsets.symmetric(vertical: 20.H),
                          title: context.strings.text_set_as_default,
                          child: TripcSwitch(
                              onToogle: (value) {
                                if (value) {
                                  return;
                                }
                                _switchMembership();
                              },
                              value: currentTripcID?.id ==
                                  user?.selectedMembership?.id)),
                      SingleLineInfo(
                          onTap: () {
                            ref
                                .read(pProfilePageProvider.notifier)
                                .resetVerifyOldPasscodePage();
                            bottomSheetHelpers.show(context,
                                borderRadius: 12,
                                backgroundColor:
                                    AppAssets.origin().whiteBackgroundColor,
                                child: const TripcTypePassCodeBottomSheet());
                          },
                          padding: EdgeInsets.only(top: 22.5.H, bottom: 16.H),
                          title: context.strings.text_reset_passcode,
                          child: AppAssets.origin()
                              .icArrowRightGray
                              .widget(height: 16.H, width: 16.H)),
                      SingleLineInfo(
                          onTap: listTripcId.length > 1
                              ? () => dialogHelpers.show(context,
                                      callback: (value) {
                                    if (value is bool && value) {
                                      _deactiveMembership();
                                    }
                                  },
                                      child: RemoveTripcidDialog(
                                          onTap: () =>
                                              Navigator.pop(context, true)))
                              : () => dialogHelpers.show(context,
                                  child: RemoveDefaultTripcidDialog(
                                      onTap: () =>
                                          Navigator.pop(context, true))),
                          title: context.strings.text_remove_tripc_id,
                          hasStroke: false,
                          padding: EdgeInsets.symmetric(vertical: 20.H),
                          child: AppAssets.origin()
                              .icArrowRightGray
                              .widget(height: 16.H, width: 16.H)),
                    ],
                  ),
                )
              ],
            )),
        AppLoading(isRequesting: isLoading || isLoadingGetme)
      ],
    );
  }
}
