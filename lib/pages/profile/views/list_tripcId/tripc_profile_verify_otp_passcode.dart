import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/app_toast/tripc_black_toast.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_otp_text_field/tripc_otp_text_field.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../../utils/app_error_string.dart';
import '../../providers/tripc_profile_provider.dart';

class TripcProfileVerifyOtpPasscode extends ConsumerStatefulWidget {
  const TripcProfileVerifyOtpPasscode({super.key, this.time = 120});
  final int time;

  @override
  ConsumerState<TripcProfileVerifyOtpPasscode> createState() =>
      _TripcProfileVerifyOtpPasscodeState();
}

class _TripcProfileVerifyOtpPasscodeState
    extends ConsumerState<TripcProfileVerifyOtpPasscode> {
  final TextEditingController _passCodeController = TextEditingController();
  Timer? _timer;
  late int time;
  Color progressColor = AppAssets.origin().darkGreenColor;
  String error = '';

  @override
  void initState() {
    super.initState();

    _passCodeController.addListener(
      () {
        setState(() {
          error = '';
        });
      },
    );
    time = widget.time;
    _countDown();
  }

  @override
  void dispose() {
    _passCodeController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _countDown() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      setState(() {
        if (time > 0) {
          time--;
          if (time <= 10) {
            progressColor = AppAssets.origin().redDotColor;
          }
        } else {
          _timer?.cancel();
        }
      });
    });
  }

  void _showError() {
    final errorText = ref.read(pProfilePageProvider).errorMessage;
    dialogHelpers.show(context, child: TripcErrorDialog(errorText: errorText));
  }

  Future<void> _resendOtp() async {
    final result =
        await ref.read(pProfilePageProvider.notifier).sendOtpForgotPasscode();
    if (result) {
      setState(() {
        time = widget.time;
        progressColor = AppAssets.origin().darkGreenColor;
      });
      _countDown();
      toastHelpers.showCustom(context,
          child: TripcBlackToast(
              margin: EdgeInsets.only(top: 100.H),
              message: context.strings.text_otp_has_been_sent));
    } else {
      _showError();
    }
  }

  Future<void> _verifyOtp(BuildContext context) async {
    final result =
        await ref.read(pProfilePageProvider.notifier).verifyOtpForgotPasscode();
    if (result) {
      ref.read(pProfilePageProvider.notifier).clearResetPasscodePage();
      AppRoute.pushReplacement(context,
          routeName: AppRoute.routeTripcProfileResetPasscode);
    } else {
      setState(() {
        error = ErrorParser.getErrorMessage(
            ref.read(pProfilePageProvider).errorMessage ?? '', context);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLoading =
        ref.watch(pProfilePageProvider.select((value) => value.isLoading));
    final otp = ref
        .watch(pProfilePageProvider.select((value) => value.forgotPasscodeOtp));
    return Stack(
      children: [
        TripcScaffold(
            onPressed: () => unfocusKeyboard(),
            backgroundColor: AppAssets.origin().whiteBackgroundColor,
            resizeToAvoidBottomInset: false,
            toolbarHeight: 73.H,
            leadingWidth: 40,
            leading: Padding(
              padding: EdgeInsets.only(top: 32.H),
              child: AppAssets.init.iconArrowleft.widget(
                color: AppAssets.origin().black,
              ),
            ),
            titleAppBar: TripcText(
              context.strings.text_otp_code_verification,
              fontSize: 28,
              fontWeight: FontWeight.w600,
              textCase: TextCaseType.none,
              textColor: AppAssets.origin().blackColor,
              padding: EdgeInsets.only(top: 32.H),
            ),
            hasBackButton: true,
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.W),
              child: Column(
                children: [
                  TripcText(context.strings.text_sent_6_digit_code,
                      fontSize: 16,
                      fontWeight: FontWeight.w300,
                      height: 1.2,
                      textColor: AppAssets.origin().darkGreyTextColor,
                      padding: EdgeInsets.symmetric(vertical: 14.H)),
                  SizedBox(
                      height: 46.H,
                      width: 46.H,
                      child: Stack(
                        fit: StackFit.expand,
                        children: [
                          CircularProgressIndicator(
                            value: 1 - time / widget.time,
                            backgroundColor: time == 0
                                ? AppAssets.origin().buttonStrokeColor93
                                : progressColor,
                            color: AppAssets.origin().buttonStrokeColor93,
                            strokeWidth: 4.H,
                            strokeCap: StrokeCap.round,
                          ),
                          Center(
                            child: TripcText(time.covertResultCountDown2,
                                fontSize: 12,
                                fontWeight: FontWeight.w700,
                                textColor: progressColor),
                          ),
                        ],
                      )),
                  Padding(
                    padding: EdgeInsets.only(top: 40.H),
                    child: TripcOtpTextField(
                        controller: _passCodeController,
                        isError: error.isNotEmpty,
                        onChanged: (value) => ref
                            .read(pProfilePageProvider.notifier)
                            .typingForgotPasscodeOtp(value)),
                  ),
                  Container(
                    height: 43.H,
                    padding: EdgeInsets.only(top: 8.H),
                    child: Visibility(
                      visible: error.isNotEmpty,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TripcText(error,
                              textAlign: TextAlign.start,
                              fontSize: 10,
                              fontStyle: FontStyle.italic,
                              fontWeight: FontWeight.w700,
                              textColor: AppAssets.origin().redDotColor),
                        ],
                      ),
                    ),
                  ),
                  TripcButton(
                    isButtonDisabled: otp?.length != 6,
                    onPressed: () {
                      _verifyOtp(context);
                    },
                    title: context.strings.text_verify,
                    height: 56,
                  ),
                  Visibility(
                      visible: time == 0,
                      child: _resendPasswordArea(onTap: _resendOtp)),
                ],
              ),
            )),
        AppLoading(isRequesting: isLoading)
      ],
    );
  }

  Widget _resendPasswordArea({required VoidCallback onTap}) {
    return Padding(
      padding: EdgeInsets.only(top: 24.H),
      child: Column(
        children: [
          TripcText(
            context.strings.text_did_not_receive_otp,
            fontSize: 12,
            fontWeight: FontWeight.w400,
            textColor: AppAssets.origin().darkGreyTextColor,
            padding: EdgeInsets.only(bottom: 12.H),
          ),
          TripcText(
              onTap: onTap,
              context.strings.text_resend_otp,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              textColor: AppAssets.origin().blackColor,
              decoration: TextDecoration.underline),
        ],
      ),
    );
  }
}
