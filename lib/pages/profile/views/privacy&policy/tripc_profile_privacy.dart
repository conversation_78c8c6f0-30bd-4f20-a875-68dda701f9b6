import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../../widgets/commons/last_page_warning/last_page_warning.dart';
import '../../../../widgets/commons/tripc_rich_text/tripc_rich_text.dart';

class TripcProfilePrivacy extends StatelessWidget {
  const TripcProfilePrivacy({super.key});

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
        hasBackButton: true,
        titleAppBar: TripcText(
          context.strings.text_privacy,
          fontSize: 18,
          fontWeight: FontWeight.w500,
          textCase: TextCaseType.none,
        ),
        toolbarHeight: 41.H,
        needUnFocus: true,
        resizeToAvoidBottomInset: true,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        body: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.W),
            child: Column(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TripcText(
                      context.strings.text_privacy_policy_at_tripc,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      textCase: TextCaseType.upper,
                      textAlign: TextAlign.start,
                      textColor: AppAssets.origin().blackColor,
                      padding: EdgeInsets.only(top: 16.H),
                    ),
                    _content(
                        content:
                            context.strings.text_commitment_to_data_protection),
                    _title(
                        title: context.strings.text_personal_data_management),
                    _infoLine(
                        info: context.strings.text_edit_personal_information),
                    _infoLine(
                        info: context.strings.text_delete_account_and_data),
                    _infoLine(
                        info: context.strings.text_download_personal_data),
                    _title(title: context.strings.text_security_settings),
                    _content(
                        content: context.strings.text_inactive_timeout_logout),
                    _title(title: context.strings.text_data_sharing_rights),
                    _content(
                        content: context
                            .strings.text_enable_disable_promotion_emails),
                    _content(
                        content:
                            context.strings.text_upload_photos_and_reviews),
                    _content(
                        content:
                            context.strings.text_change_data_sharing_rights),
                    _title(
                        title: context.strings.text_security_activity_and_log),
                    _content(content: context.strings.text_login_history),
                    _infoLine(info: context.strings.text_view_recent_login),
                    _infoLine(info: context.strings.text_receive_login_alerts),
                    _content(
                        content: context.strings.text_privacy_changes_history),
                    _infoLine(info: context.strings.text_view_privacy_changes),
                    _content(
                        content: context.strings.text_application_activity),
                    _infoLine(
                        info: context.strings
                            .text_view_booking_payment_cancellation_history),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 20.H),
                      child: TripcRichText(
                        text: '',
                        textAlign: TextAlign.start,
                        children: [
                          TextSpan(
                              text: context.strings.text_contact_tripc_at,
                              style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: AppAssets.origin()
                                      .secondDarkGreyTextColor)),
                          TextSpan(
                              text: '[<EMAIL>]',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w300,
                                color: AppAssets.origin().lightBlueFD,
                              )),
                          TextSpan(
                              text: context.strings.text_or_2,
                              style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: AppAssets.origin()
                                      .secondDarkGreyTextColor)),
                          TextSpan(
                              text: '[093.999.999]',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w300,
                                color: AppAssets.origin().lightBlueFD,
                              )),
                          TextSpan(
                              text: context.strings.text_to_be_supported_quick,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w300,
                                color: Colors.black,
                              ))
                        ],
                      ),
                    ),
                  ],
                ),
                const LastPageWarning(),
                SizedBox(height: 30.H)
              ],
            ),
          ),
        ));
  }

  Widget _title({required String title}) {
    return TripcText(
      title,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      textCase: TextCaseType.none,
      textAlign: TextAlign.start,
      textColor: AppAssets.origin().blackColor,
      padding: EdgeInsets.only(top: 16.H),
    );
  }

  Widget _content({required String content}) {
    return TripcText(
      content,
      fontSize: 14,
      fontWeight: FontWeight.w300,
      textCase: TextCaseType.none,
      textAlign: TextAlign.start,
      textColor: AppAssets.origin().blackColor,
      padding: EdgeInsets.only(top: 8.H),
    );
  }

  Widget _infoLine({required String info}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText('\u2022',
            fontSize: 10,
            fontWeight: FontWeight.w400,
            textColor: AppAssets.origin().black,
            padding: EdgeInsets.fromLTRB(0, 8.H, 8.W, 0)),
        Expanded(
          child: TripcText(
            info,
            fontSize: 14,
            fontWeight: FontWeight.w300,
            textCase: TextCaseType.none,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(top: 8.H),
          ),
        )
      ],
    );
  }
}
