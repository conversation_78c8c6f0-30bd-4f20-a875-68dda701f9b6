import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';

import '../../../../widgets/commons/last_page_warning/last_page_warning.dart';
import '../../../../widgets/commons/tripc_rich_text/tripc_rich_text.dart';

class TripcProfileTermsAndConditions extends StatelessWidget {
  const TripcProfileTermsAndConditions({super.key});

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
      hasBackButton: true,
      titleAppBar: TripcText(
        context.strings.text_term_and_condition_title,
        fontSize: 18,
        fontWeight: FontWeight.w500,
        textCase: TextCaseType.none,
      ),
      toolbarHeight: 41.H,
      needUnFocus: true,
      resizeToAvoidBottomInset: true,
      backgroundColor: AppAssets.origin().whiteBackgroundColor,
      body: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.W),
          child: Column(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TripcText(
                    context.strings.text_term_and_condition,
                    height: 1.5,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    textAlign: TextAlign.start,
                    textCase: TextCaseType.upper,
                    textColor: AppAssets.origin().blackColor,
                    padding: EdgeInsets.only(top: 16.H),
                  ),
                  TripcText(
                    context.strings.text_welcome_message,
                    fontSize: 14,
                    fontWeight: FontWeight.w300,
                    height: 1.5,
                    textAlign: TextAlign.start,
                    textColor: AppAssets.origin().blackColor,
                    padding: EdgeInsets.only(top: 16.H),
                  ),
                  _title(title: context.strings.text_personal_data_management),
                  _content(content: context.strings.text_user_information),
                  _content(
                      content: context.strings.text_account_responsibility),
                  _content(content: context.strings.text_account_privacy),
                  _title(title: context.strings.text_booking_and_payment),
                  _content(content: context.strings.text_tour_price),
                  _content(content: context.strings.text_payment_method),
                  _content(content: context.strings.text_payment_confirmation),
                  _title(title: context.strings.text_tour_cancellation),
                  _content(content: context.strings.text_cancellation_policy),
                  _content(content: context.strings.text_non_refundable),
                  _content(content: context.strings.text_contact_support),
                  _title(title: context.strings.text_user_rights),
                  _content(content: context.strings.text_unauthorized_usage),
                  _content(content: context.strings.text_responsibility_notice),
                  _title(title: context.strings.text_tripc_responsibility),
                  _content(content: context.strings.text_service_provision),
                  _content(
                      content: context.strings.text_support_during_booking),
                  _title(title: context.strings.text_privacy_policy),
                  _content(content: context.strings.text_data_protection),
                  _content(content: context.strings.text_data_sharing),
                  _title(title: context.strings.text_terms_change),
                  _content(content: context.strings.text_terms_modification),
                  _content(content: context.strings.text_terms_update_notice),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 20.H),
                    child: TripcRichText(
                      text: '',
                      lineHeight: 1.5,
                      textAlign: TextAlign.start,
                      children: [
                        TextSpan(
                            text: context.strings.text_contact_tripc_at,
                            style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: AppAssets.origin()
                                    .secondDarkGreyTextColor)),
                        TextSpan(
                            text: '<EMAIL>',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: AppAssets.origin().black70,
                            )),
                        TextSpan(
                            text: context.strings.text_or_2,
                            style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: AppAssets.origin()
                                    .secondDarkGreyTextColor)),
                        TextSpan(
                            text: '+84935479122',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: AppAssets.origin().black70,
                            )),
                        TextSpan(
                            text: context.strings.text_to_be_supported_quick,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                                color: AppAssets.origin()
                                    .secondDarkGreyTextColor
                            ))
                      ],
                    ),
                  ),
                ],
              ),
              const LastPageWarning(),
              SizedBox(height: 30.H)
            ],
          ),
        ),
      ),
    );
  }

  Widget _title({required String title}) {
    return TripcText(
      title,
      fontSize: 16,
      height: 1.5,
      fontWeight: FontWeight.w500,
      textCase: TextCaseType.none,
      textAlign: TextAlign.start,
      textColor: AppAssets.origin().blackColor,
      padding: EdgeInsets.only(top: 16.H),
    );
  }

  Widget _content({required String content}) {
    return TripcText(
      content,
      fontSize: 14,
      height: 1.5,
      fontWeight: FontWeight.w300,
      textCase: TextCaseType.none,
      textAlign: TextAlign.start,
      textColor: AppAssets.origin().blackColor,
      padding: EdgeInsets.only(top: 8.H),
    );
  }
}
