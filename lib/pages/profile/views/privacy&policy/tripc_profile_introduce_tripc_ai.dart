import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';

class TripcProfileIntroduceTripcAI extends StatelessWidget {
  const TripcProfileIntroduceTripcAI({super.key});

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
      hasBackButton: true,
      titleAppBar: TripcText(
        context.strings.text_tripc_about,
        fontSize: 18,
        fontWeight: FontWeight.w500,
        textCase: TextCaseType.none,
      ),
      toolbarHeight: 41.H,
      needUnFocus: true,
      resizeToAvoidBottomInset: true,
      backgroundColor: AppAssets.origin().whiteBackgroundColor,
      body: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.W),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TripcText(
                context.strings.text_tripc_intro_title,
                fontSize: 14,
                height: 1.5,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
                padding: EdgeInsets.only(top: 16.H),
              ),
              TripcText(
                context.strings.text_tripc_intro_description,
                fontSize: 14,
                height: 1.5,
                fontWeight: FontWeight.w300,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
                padding: EdgeInsets.only(top: 16.H),
              ),
              TripcText(
                context.strings.text_why_choose_tripc,
                fontSize: 14,
                height: 1.5,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
                padding: EdgeInsets.only(top: 16.H),
              ),
              _infoLine(info: context.strings.text_choose_tripc_point_1),
              _infoLine(info: context.strings.text_choose_tripc_point_2),
              _infoLine(info: context.strings.text_choose_tripc_point_3),
              _infoLine(info: context.strings.text_choose_tripc_point_4),
              TripcText(
                context.strings.for_partners_of_TripC,
                fontSize: 14,
                height: 1.5,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
                padding: EdgeInsets.only(top: 16.H, bottom: 12.H),
              ),
              RichText(
                  textAlign: TextAlign.start,
                  text: TextSpan(children: [
                    TextSpan(
                      text: context.strings.for_partners_of_TripC_step_one,
                      style: TextStyle(
                        fontSize: 14.SP,
                        fontWeight: FontWeight.w300,
                        color: AppAssets.origin().blackColor,
                        height: 1.5,
                      ),
                    ),
                    TextSpan(
                      text: ' <EMAIL> ',
                      style: TextStyle(
                        fontSize: 14.SP,
                        fontWeight: FontWeight.w300,
                        color: AppAssets.origin().blue0365,
                        height: 1.5,
                      ),
                    ),
                    TextSpan(
                      text: context.strings.or_phone_number,
                      style: TextStyle(
                        fontSize: 14.SP,
                        fontWeight: FontWeight.w300,
                        color: AppAssets.origin().blackColor,
                        height: 1.5,
                      ),
                    ),
                  ])),
              _infoLine(info: context.strings.for_partners_of_TripC_step_two),
              _infoLine(info: context.strings.for_partners_of_TripC_step_three),
              TripcText(
                context.strings.business_information_of_company_own_tripc,
                fontSize: 14,
                height: 1.5,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
                padding: EdgeInsets.only(top: 16.H),
              ),
              _infoLine(
                  info:
                      '${context.strings.text_company.toSentenceCase()}: ${context.strings.company_name.toUpperCase()}'),
              _infoLine(info: context.strings.business_registration_number),
              _infoLine(info: context.strings.company_address),
              _infoLine(info: context.strings.company_phone),
              _inforRichText(
                  label: '${context.strings.text_email}: ',
                  value: '<EMAIL>'),
              _infoLine(info: context.strings.legal_representative),
              _infoLine(info: context.strings.partnership_contact),
              _infoLine(info: context.strings.company_phone_2),
              _inforRichText(
                  label: '${context.strings.text_email}: ',
                  value: '<EMAIL>'),
              SizedBox(
                height: 30.H,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _infoLine({required String info}) {
    return TripcText(
      info,
      fontSize: 14,
      height: 1.5,
      fontWeight: FontWeight.w300,
      textCase: TextCaseType.none,
      textAlign: TextAlign.start,
      textColor: AppAssets.origin().blackColor,
      padding: EdgeInsets.only(top: 12.H),
    );
  }

  Widget _inforRichText({required String label, required String value}) {
    return Padding(
      padding: EdgeInsets.only(top: 12.H),
      child: RichText(
          textAlign: TextAlign.start,
          text: TextSpan(children: [
            TextSpan(
              text: label,
              style: TextStyle(
                fontSize: 14.SP,
                fontWeight: FontWeight.w300,
                color: AppAssets.origin().blackColor,
                height: 1.5,
              ),
            ),
            TextSpan(
              text: value,
              style: TextStyle(
                fontSize: 14.SP,
                fontWeight: FontWeight.w300,
                color: AppAssets.origin().blue0365,
                height: 1.5,
              ),
            ),
          ])),
    );
  }
}
