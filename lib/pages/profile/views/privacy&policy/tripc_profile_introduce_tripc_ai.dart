import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';

class TripcProfileIntroduceTripcAI extends StatelessWidget {
  const TripcProfileIntroduceTripcAI({super.key});

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
      hasBackButton: true,
      titleAppBar: TripcText(
        context.strings.text_tripc_about,
        fontSize: 18,
        fontWeight: FontWeight.w500,
        textCase: TextCaseType.none,
      ),
      toolbarHeight: 41.H,
      needUnFocus: true,
      resizeToAvoidBottomInset: true,
      backgroundColor: AppAssets.origin().whiteBackgroundColor,
      body: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.W),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TripcText(
                context.strings.text_tripc_intro_title,
                fontSize: 14,
                height: 1.5,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
                padding: EdgeInsets.only(top: 16.H),
              ),
              TripcText(
                context.strings.text_tripc_intro_description,
                fontSize: 14,
                height: 1.5,
                fontWeight: FontWeight.w300,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
                padding: EdgeInsets.only(top: 16.H),
              ),
              TripcText(
                context.strings.text_why_choose_tripc,
                fontSize: 14,
                height: 1.5,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
                padding: EdgeInsets.only(top: 16.H),
              ),
              _infoLine(info: context.strings.text_choose_tripc_point_1),
              _infoLine(info: context.strings.text_choose_tripc_point_2),
              _infoLine(info: context.strings.text_choose_tripc_point_3),
              _infoLine(info: context.strings.text_choose_tripc_point_4),
            ],
          ),
        ),
      ),
    );
  }

  Widget _infoLine({required String info}) {
    return TripcText(
      info,
      fontSize: 14,
      height: 1.5,
      fontWeight: FontWeight.w300,
      textCase: TextCaseType.none,
      textAlign: TextAlign.start,
      textColor: AppAssets.origin().blackColor,
      padding: EdgeInsets.only(top: 12.H),
    );
  }
}
