import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcSettingCountry extends StatefulWidget {
  const TripcSettingCountry({super.key});

  @override
  _TripcSettingCountryState createState() => _TripcSettingCountryState();
}

class _TripcSettingCountryState extends State<TripcSettingCountry> {
  String selectedLanguage = "Việt Nam";

  final List<String> languages = ["Việt Nam"];

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
      toolbarHeight: 44.H,
      appBarColor: AppAssets.origin().whiteBackgroundColor,
      titleAppBar: TripcText(
        context.strings.text_country_or_region,
        fontSize: 16.SP,
        fontWeight: FontWeight.w600,
        textCase: TextCaseType.none,
      ),
      hasBackButton: true,
      needUnFocus: true,
      resizeToAvoidBottomInset: true,
      backgroundColor: AppAssets.origin().whiteSmokeColor,
      body: Container(
        decoration: BoxDecoration(
            border: Border(
              top: BorderSide(
                color: AppAssets.origin().whiteSmokeColor,
                width: 1,
              ),
            ),
            color: AppAssets.origin().whiteBackgroundColor,
            boxShadow: [AppAssets.origin().profileServiceAreaShadow]),
        child: ListView.builder(
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: languages.length,
          itemBuilder: (context, index) {
            String language = languages[index];
            bool isSelected = language == selectedLanguage;

            return SizedBox(
              height: 54.H,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.W),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      selectedLanguage = language;
                    });
                  },
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(height: 1.H),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TripcText(
                            language,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            textColor: isSelected
                                ? AppAssets.origin().darkBlueColor
                                : AppAssets.origin().blackColor,
                          ),
                          if (isSelected)
                            AppAssets.origin()
                                .icCheckmark
                                .widget(width: 16.W, height: 16.H),
                        ],
                      ),
                      if (index < languages.length - 1)
                        Divider(
                            height: 1,
                            color: AppAssets.origin().settingDividerColor)
                      else
                        SizedBox(height: 1.H),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
