import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';

import '../../../../services/app/app_assets.dart';
import '../../../../services/app/app_route.dart';
import '../../../../services/providers/providers.dart';
import '../../../../widgets/commons/app_dialog/tripc_error_dialog.dart';
import '../../../../widgets/commons/app_shimmer_loading/tour_shimmer_loading/tour_shimmer_loading.dart';
import '../../../../widgets/commons/tripc_scafford/tripc_scafford.dart';
import '../../../../widgets/commons/tripc_text/tripc_text.dart';
import '../../components/tripc_recently_viewed_card.dart';
import '../../providers/tripc_provider_recently_viewed_provider.dart';

class TripcProfileRecentlyViewed extends ConsumerStatefulWidget {
  const TripcProfileRecentlyViewed({super.key});

  @override
  ConsumerState<TripcProfileRecentlyViewed> createState() =>
      _TripcProfileRecentlyViewedState();
}

class _TripcProfileRecentlyViewedState
    extends ConsumerState<TripcProfileRecentlyViewed> {
  final _scrollController = ScrollController();

  void _scrollListener() {
    if (_scrollController.position.atEdge) {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 100) {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          await ref.read(pRecentlyViewedProvider.notifier).loadmore();
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await ref.read(pRecentlyViewedProvider.notifier).getRecentlyViewed();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _delete() async {
    final result = await ref.read(pRecentlyViewedProvider.notifier).delete();
    if (result) {
      ref.read(pRecentlyViewedProvider.notifier).getRecentlyViewed();
    } else {
      final errorText = ref.read(pRecentlyViewedProvider).errorMessage;
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorText));
    }
  }

  void _showDialog(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return CupertinoAlertDialog(
            title: TripcText(context.strings.text_delete_browsing_history,
                padding:
                    EdgeInsets.symmetric(horizontal: 16.W, vertical: 12.H)),
            content: TripcText(
              context.strings.text_delete_browsing_history_note,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
            actions: [
              CupertinoDialogAction(
                child: TripcText(
                  context.strings.text_cancel,
                  textColor: AppAssets.origin().secondaryColor,
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
              CupertinoDialogAction(
                child: TripcText(context.strings.text_confirm,
                    textColor: AppAssets.origin().secondaryColor),
                onPressed: () {
                  Navigator.pop(context);
                  _delete();
                },
              )
            ],
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    final sectionList = ref.watch(pRecentlyViewedProvider).sectionList;
    final isLoading = ref.watch(pRecentlyViewedProvider).isLoading;
    final isLoadingMore = ref.watch(pRecentlyViewedProvider).isLoadingMore;

    return Stack(
      children: [
        TripcScaffold(
          toolbarHeight: 54.H,
          appBarColor: AppAssets.origin().whiteBackgroundColor,
          titleAppBar: TripcText(
            context.strings.text_recently_viewed,
            fontSize: 16.SP,
            fontWeight: FontWeight.w600,
          ),
          actions: [
            TripcIconButton(
              child: Padding(
                padding: EdgeInsets.only(right: 16.W),
                child: AppAssets.origin().icBin.widget(),
              ),
              onPressed: () => _showDialog(context),
            ),
          ],
          hasBackButton: true,
          resizeToAvoidBottomInset: false,
          backgroundColor: AppAssets.origin().whiteBackgroundColor,
          body: RefreshIndicator(
            onRefresh: () async {
              await ref.read(pRecentlyViewedProvider.notifier).getRecentlyViewed();
            },
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 16.W,
              ),
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  Visibility(
                    visible: !isLoading,
                    replacement: const SliverToBoxAdapter(
                      child: ListCardTourShimmerLoading(),
                    ),
                    child: Visibility(
                      visible: sectionList.
                      isNotEmpty,
                      replacement: SliverToBoxAdapter(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppAssets.origin().imEmptyView.widget(),
                          TripcText(
                            context.strings.text_no_broswing_history,
                            fontWeight: FontWeight.w300,
                          ),
                        ],
                      ),
                    ),
                      child: SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final tourList = sectionList[index].tourList;
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TripcText(SectionStatus.getByValue(
                                        sectionList[index].section)
                                    .statusText(
                                        context, sectionList[index].section)),
                                ...tourList.map((tour) => GestureDetector(
                                    onTap: () => AppRoute.pushNamed(
                                          context,
                                          routeName: AppRoute.routeTourDetailView,
                                          arguments: tour.id,
                                        ),
                                    child: TripcRecentlyViewedCard(tour: tour))),
                              ],
                            );
                          },
                          childCount: sectionList.length,
                        ),
                      ),
                    ),
                  ),
                   if (!isLoading && sectionList.isNotEmpty)
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: EdgeInsets.only(bottom: 20.H, top: 10.H),
                        child: Column(
                          children: [
                            AppAssets.origin().icCube.widget(),
                            TripcText(
                              context
                                  .strings.text_showing_view_history_in_30_days,
                              fontSize: 14,
                              fontWeight: FontWeight.w300,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
        AppLoading(isRequesting: isLoadingMore),
      ],
    );
  }
}
