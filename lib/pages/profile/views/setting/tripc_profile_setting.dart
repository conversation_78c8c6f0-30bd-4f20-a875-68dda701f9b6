import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/setting_enum.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_switch/tripc_switch.dart';

class TripcProfileSetting extends ConsumerStatefulWidget {
  const TripcProfileSetting({super.key});

  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends ConsumerState<TripcProfileSetting> {
  bool isDarkMode = false;
  @override
  Widget build(BuildContext context) {
    final selectedLanguage =
        ref.watch(pLocaleProvider.select((value) => value.applocale));
    return TripcScaffold(
      toolbarHeight: 44.H,
      appBarColor: AppAssets.origin().whiteBackgroundColor,
      titleAppBar: TripcText(
        context.strings.text_setting,
        fontSize: 16.SP,
        fontWeight: FontWeight.w600,
        textCase: TextCaseType.none,
      ),
      hasBackButton: true,
      needUnFocus: true,
      resizeToAvoidBottomInset: true,
      backgroundColor: AppAssets.origin().whiteSmokeColor,
      body: ListView(
        children: [
          // Section 1
          _buildSection([
            _buildListTile(
                SettingEnum.language, selectedLanguage.name(context)),
            _buildListTile(SettingEnum.currencyUnit, "VND"),
            _buildListTile(SettingEnum.national, "Việt Nam", true, false),
          ]),

          // Section 2
          _buildSection([
            _buildListTile(SettingEnum.accountManagement),
            // _buildToggleTile(SettingEnum.darkMode.title(context), isDarkMode),
            _buildListTile(SettingEnum.receiveFeedback),
            _buildListTile(SettingEnum.listFeedback),
          ]),

          // Section 3
          _buildSection([
            _buildListTile(SettingEnum.termConditions),
            _buildListTile(SettingEnum.privacy),
            _buildListTile(SettingEnum.version, "1.0.0", false, false),
          ]),
        ],
      ),
    );
  }

  _handleItemOnTap(BuildContext context, SettingEnum setting) {
    switch (setting) {
      case SettingEnum.language:
        AppRoute.pushNamed(context, routeName: AppRoute.routeSettingLanguage);
        break;
      case SettingEnum.currencyUnit:
        AppRoute.pushNamed(context, routeName: AppRoute.routeSettingCurrency);
        break;
      case SettingEnum.national:
        AppRoute.pushNamed(context, routeName: AppRoute.routeSettingCountry);
      case SettingEnum.accountManagement:
        AppRoute.pushNamed(context, routeName: AppRoute.routeManageProfile);
      case SettingEnum.privacy:
        AppRoute.pushNamed(context, routeName: AppRoute.routeProfilePrivacy);
      case SettingEnum.termConditions:
        AppRoute.pushNamed(context,
            routeName: AppRoute.routeProfileTermAndCondition);
      case SettingEnum.receiveFeedback:
        AppRoute.pushNamed(context, routeName: AppRoute.routeReceivingFeedback);
      case SettingEnum.listFeedback:
        AppRoute.pushNamed(context, routeName: AppRoute.routeListFeedback);
      default:
        break;
    }
  }

  /// Builds a section with a list of items
  Widget _buildSection(List<Widget> children) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 12.H),
      padding: EdgeInsets.only(bottom: 8.H),
      decoration: BoxDecoration(
          color: AppAssets.origin().whiteBackgroundColor,
          boxShadow: [AppAssets.origin().profileServiceAreaShadow]),
      child: Column(
        children: children,
      ),
    );
  }

  /// Builds a single list tile item
  Widget _buildListTile(SettingEnum setting,
      [String? value, bool showArrow = true, bool showDivider = true]) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: TripcIconButton(
        onPressed: () {
          _handleItemOnTap(context, setting);
        },
        child: Column(
          children: [
            SizedBox(height: 20.H),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TripcText(setting.title(context),
                    fontSize: 12, fontWeight: FontWeight.w500),
                Row(
                  children: [
                    if (value != null)
                      TripcText(
                        ignorePointer: true,
                        value,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        textColor: AppAssets.init.settingValueColor,
                      ),
                    if (showArrow)
                      Padding(
                        padding: EdgeInsets.only(left: 12.W),
                        child: AppAssets.init.icArrowRightGray
                            .widget(width: 16.W, height: 16.W),
                      ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 12.H),
            if (showDivider)
              Divider(height: 1, color: AppAssets.origin().settingDividerColor),
          ],
        ),
      ),
    );
  }

  /// Builds a toggle switch tile
  Widget _buildToggleTile(String title, bool value) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W, vertical: 12.H),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TripcText(title, fontSize: 12, fontWeight: FontWeight.w500),
          const TripcSwitch()
        ],
      ),
    );
  }
}
