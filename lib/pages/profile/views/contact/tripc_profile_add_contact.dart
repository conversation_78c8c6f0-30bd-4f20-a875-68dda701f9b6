import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../../models/app/text_case_type.dart';
import '../../../../services/app/app_assets.dart';
import '../../../../services/providers/providers.dart';
import '../../../../widgets/app_loading.dart';
import '../../../../widgets/commons/app_dialog/tripc_error_dialog.dart';
import '../../../../widgets/commons/tripc_box_input/tripc_box_input.dart';
import '../../../../widgets/commons/tripc_scafford/tripc_scafford.dart';
import '../../../../widgets/commons/tripc_text/tripc_text.dart';
import '../../../../widgets/tripc_switch/tripc_switch.dart';
import '../../../../widgets/tripc_text_filed/tripc_text_filed.dart';
import '../../providers/tripc_profile_add_contact_provider.dart';
import '../../providers/tripc_profile_contact_provider.dart';

class TripcProfileAddContact extends ConsumerStatefulWidget {
  const TripcProfileAddContact({super.key});

  @override
  ConsumerState<TripcProfileAddContact> createState() =>
      _TripcProfileAddContactState();
}

class _TripcProfileAddContactState
    extends ConsumerState<TripcProfileAddContact> {
  Future<void> _addContact() async {
    final result = await ref.read(pAddContactProvider.notifier).addContact();
    if (result) {
      Navigator.pop(context);
      ref.read(pContactProvider.notifier).getContactList();
    } else {
      final errorText = ref.read(pAddContactProvider).errorMessage;
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorText));
    }
  }

  @override
  Widget build(BuildContext context) {
    final isAddContactEnable = ref.watch(pAddContactProvider).isAddContactEnable;
    final errorName = ref.watch(pAddContactProvider).errorFullName;
    final errorEmail = ref.watch(pAddContactProvider).errorEmail;
    final errorPhone = ref.watch(pAddContactProvider).errorPhone;

    bool isLoading = ref.watch(pAddContactProvider).isLoading;

    return Stack(
      children: [
        TripcScaffold(
            resizeToAvoidBottomInset: false,
            hasBackButton: true,
            backgroundColor: AppAssets.origin().whiteBackgroundColor,
            titleAppBar: TripcText(
              context.strings.text_add_new_contact,
              fontWeight: FontWeight.w600,
              fontSize: 16,
              textColor: AppAssets.origin().black,
            ),
            actions: [
              TripcText(
                onTap: isAddContactEnable ? () => _addContact() : null,
                context.strings.text_save,
                fontWeight: FontWeight.w600,
                fontSize: 14,
                textColor: isAddContactEnable
                    ? AppAssets.origin().darkBlue5FF
                    : AppAssets.origin().darkGreyTextColor,
                padding: EdgeInsets.only(right: 20.W),
              ),
            ],
            body: Padding(
              padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0),
                        color: AppAssets.origin().grayF7),
                    padding:
                        EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TripcText(
                          context.strings.text_set_default_for_order,
                          fontWeight: FontWeight.w400,
                        ),
                        TripcSwitch(
                          onToogle: (value) {
                            setState(() {
                              ref
                                  .read(pAddContactProvider.notifier)
                                  .setDefault(value);
                            });
                          },
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 16.H,
                  ),
                  TripcBoxInput(
                      headerTitle: context.strings.text_contact_information,
                      child: Column(
                        children: [
                          TripcTextField(
                              onChanged: (value) => ref
                                  .read(pAddContactProvider.notifier)
                                  .setFullname(context, value),
                              hintText: context.strings.text_contact_name,
                              fontSize: 14,
                              hintTextColor: AppAssets.origin().lightGrayDD4,
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 13.H, horizontal: 12.5.W),
                              isError: errorName.isNotEmpty,
                              errorText: errorName,
                              inputFormatters: [
                                TextInputFormatter.withFunction(
                                    (oldValue, newValue) {
                                  return newValue.copyWith(
                                    text: newValue.text.toUpperCase(),
                                  );
                                }),
                              ]),
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 20.H),
                            child: TripcTextField(
                              onChanged: (value) => ref
                                  .read(pAddContactProvider.notifier)
                                  .setEmail(context, value),
                              hintText: context.strings.text_email,
                              textCase: TextCaseType.none,
                              hintTextColor: AppAssets.origin().lightGrayDD4,
                              fontSize: 14,
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 13.H, horizontal: 12.5.W),
                              isError: errorEmail.isNotEmpty,
                              errorText: errorEmail,
                            ),
                          ),
                          TripcTextField(
                            onChanged: (value) => ref
                                .read(pAddContactProvider.notifier)
                                .setPhone(context, value),
                            hintText: context.strings.text_phone,
                            hintTextColor: AppAssets.origin().lightGrayDD4,
                            keyboardType: TextInputType.number,
                            fontSize: 14,
                            isError: errorPhone.isNotEmpty,
                            errorText: errorPhone,
                            prefixIcon: Padding(
                              padding: EdgeInsets.only(left: 13.W, right: 24.W),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  TripcText(
                                    '+84',
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    textColor: AppAssets.origin().black,
                                    padding: EdgeInsets.only(right: 12.W),
                                  ),
                                  AppAssets.origin()
                                      .icBlackRight
                                      .widget(height: 8.H)
                                ],
                              ),
                            ),
                            contentPadding: EdgeInsets.symmetric(
                                vertical: 13.H, horizontal: 12.5.W),
                          ),
                        ],
                      )),
                ],
              ),
            )),
        AppLoading(isRequesting: isLoading)
      ],
    );
  }
}
