import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/remote/contact_response.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../../services/app/app_assets.dart';
import '../../../../services/providers/providers.dart';
import '../../../../widgets/app_loading.dart';
import '../../../../widgets/commons/app_dialog/tripc_error_dialog.dart';
import '../../../../widgets/commons/tripc_box_input/tripc_box_input.dart';
import '../../../../widgets/commons/tripc_button/tripc_button.dart';
import '../../../../widgets/commons/tripc_scafford/tripc_scafford.dart';
import '../../../../widgets/commons/tripc_text/tripc_text.dart';
import '../../../../widgets/tripc_switch/tripc_switch.dart';
import '../../../../widgets/tripc_text_filed/tripc_text_filed.dart';
import '../../providers/tripc_profile_contact_provider.dart';
import '../../providers/tripc_profile_edit_contact_provider.dart';

class TripcProfileEditContact extends ConsumerStatefulWidget {
  const TripcProfileEditContact({super.key, required this.contact});
  final ContactResponse contact;

  @override
  ConsumerState<TripcProfileEditContact> createState() =>
      _TripcProfileEditContactState();
}

class _TripcProfileEditContactState
    extends ConsumerState<TripcProfileEditContact> {
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();

  Future<void> _editContact() async {
    final result = await ref.read(pEditContactProvider.notifier).editContact();
    if (result) {
      Navigator.pop(context);
      ref.read(pContactProvider.notifier).getContactList();
    } else {
      final errorText = ref.read(pEditContactProvider).errorMessage;
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorText));
    }
  }

  Future<void> _deleteContact() async {
    final result =
        await ref.read(pEditContactProvider.notifier).deleteContact();
    if (result) {
      Navigator.pop(context);
      ref.read(pContactProvider.notifier).getContactList();
    } else {
      final errorText = ref.read(pEditContactProvider).errorMessage;
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorText));
    }
  }

  void _showDialog(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return CupertinoAlertDialog(
            title: TripcText(context.strings.text_delete_contact_question,
                padding:
                    EdgeInsets.symmetric(horizontal: 16.W, vertical: 12.H)),
            actions: [
              CupertinoDialogAction(
                child: TripcText(
                  context.strings.text_later,
                  textColor: AppAssets.origin().secondaryColor,
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
              CupertinoDialogAction(
                child: TripcText(context.strings.text_yes,
                    textColor: AppAssets.origin().secondaryColor),
                onPressed: () {
                  Navigator.pop(context);
                  _deleteContact();
                },
              )
            ],
          );
        });
  }

  @override
  void initState() {
    super.initState();
    nameController.text = (widget.contact.fullname ?? '').toUpperCase();
    emailController.text = widget.contact.email ?? '';
    phoneController.text = (widget.contact.phone ?? '').removeLeadingZero();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ref.read(pEditContactProvider.notifier).setContact(widget.contact);
    });
  }

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    bool isAddContactEnable =
        ref.watch(pEditContactProvider).isAddContactEnable;
    String? errorName = ref.watch(pEditContactProvider).errorFullName;
    String? errorEmail = ref.watch(pEditContactProvider).errorEmail;
    bool errorPhone = ref.watch(pEditContactProvider).errorPhone;

    bool isDefault = ref.watch(pEditContactProvider).isDefault;
    bool isLoading = ref.watch(pEditContactProvider).isLoading;

    return Stack(
      children: [
        TripcScaffold(
            resizeToAvoidBottomInset: false,
            hasBackButton: true,
            backgroundColor: AppAssets.origin().whiteBackgroundColor,
            titleAppBar: TripcText(
              context.strings.text_edit_contact,
              fontWeight: FontWeight.w600,
              fontSize: 16,
              textColor: AppAssets.origin().black,
            ),
            actions: [
              TripcText(
                onTap: isAddContactEnable ? () => _editContact() : null,
                context.strings.text_save,
                fontWeight: FontWeight.w600,
                fontSize: 14,
                textColor: isAddContactEnable
                    ? AppAssets.origin().darkBlue5FF
                    : AppAssets.origin().darkGreyTextColor,
                padding: EdgeInsets.only(right: 20.W),
              ),
            ],
            body: Padding(
              padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0),
                        color: AppAssets.origin().grayF7),
                    padding:
                        EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TripcText(
                          context.strings.text_set_default_for_order,
                          fontWeight: FontWeight.w400,
                        ),
                        TripcSwitch(
                          onToogle: (value) {
                            setState(() {
                              ref
                                  .read(pEditContactProvider.notifier)
                                  .setDefault(!value);
                            });
                          },
                          value: isDefault,
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 16.H,
                  ),
                  TripcBoxInput(
                      headerTitle: context.strings.text_contact_information,
                      child: Column(
                        children: [
                          TripcTextField(
                              onChanged: (value) => ref
                                  .read(pEditContactProvider.notifier)
                                  .setFullname(context, value),
                              hintText: widget.contact.fullname,
                              controller: nameController,
                              fontSize: 14,
                              textCase: TextCaseType.upper,
                              textCapitalization: TextCapitalization.characters,
                              hintTextColor: AppAssets.origin().lightGrayDD4,
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 13.H, horizontal: 12.5.W),
                              isError: (errorName ?? '').isNotEmpty,
                              errorText: errorName,
                              inputFormatters: [
                                TextInputFormatter.withFunction(
                                    (oldValue, newValue) {
                                  return newValue.copyWith(
                                    text: newValue.text.toUpperCase(),
                                  );
                                }),
                              ]),
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 20.H),
                            child: TripcTextField(
                              onChanged: (value) => ref
                                  .read(pEditContactProvider.notifier)
                                  .setEmail(context, value),
                              hintText: widget.contact.email,
                              controller: emailController,
                              textCase: TextCaseType.none,
                              hintTextColor: AppAssets.origin().lightGrayDD4,
                              fontSize: 14,
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 13.H, horizontal: 12.5.W),
                              isError: (errorEmail ?? '').isNotEmpty,
                              errorText: errorEmail,
                            ),
                          ),
                          TripcTextField(
                            onChanged: (value) => ref
                                .read(pEditContactProvider.notifier)
                                .setPhone(context, value),
                            hintText: (widget.contact.phone ?? '')
                                .removeLeadingZero(),
                            controller: phoneController,
                            hintTextColor: AppAssets.origin().lightGrayDD4,
                            keyboardType: TextInputType.number,
                            fontSize: 14,
                            isError: errorPhone,
                            prefixIcon: Padding(
                              padding: EdgeInsets.only(left: 13.W, right: 24.W),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  TripcText(
                                    '+84',
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    textColor: AppAssets.origin().black,
                                    padding: EdgeInsets.only(right: 12.W),
                                  ),
                                  AppAssets.origin()
                                      .icBlackRight
                                      .widget(height: 8.H)
                                ],
                              ),
                            ),
                            contentPadding: EdgeInsets.symmetric(
                                vertical: 13.H, horizontal: 12.5.W),
                          ),
                        ],
                      )),
                  SizedBox(
                    height: 30.H,
                  ),
                  TripcButton(
                    onPressed: () => _showDialog(context),
                    height: 56,
                    title: context.strings.text_delete_contact,
                    style: AppButtonStyle(
                      textColor: AppAssets.origin().redDotColor,
                      backgroundColor: AppAssets.origin().whiteBackgroundColor,
                    ),
                    buttonType: ButtonType.outline,
                    border: Border.all(
                        color: AppAssets.origin().redDotColor, width: 1.H),
                  ),
                ],
              ),
            )),
        AppLoading(isRequesting: isLoading)
      ],
    );
  }
}
