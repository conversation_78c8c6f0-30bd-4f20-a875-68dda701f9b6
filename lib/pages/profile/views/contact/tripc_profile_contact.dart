import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/contact_response.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../../services/app/app_assets.dart';
import '../../../../services/app/app_route.dart';
import '../../../../widgets/commons/app_shimmer_loading/tour_shimmer_loading/tour_shimmer_loading.dart';
import '../../../../widgets/commons/tripc_scafford/tripc_scafford.dart';
import '../../../../widgets/commons/tripc_text/tripc_text.dart';
import '../../providers/tripc_profile_contact_provider.dart';

class TripcProfileListContact extends ConsumerStatefulWidget {
  const TripcProfileListContact({super.key});

  @override
  ConsumerState<TripcProfileListContact> createState() =>
      _TripcProfileListContactState();
}

class _TripcProfileListContactState
    extends ConsumerState<TripcProfileListContact> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await ref.read(pContactProvider.notifier).getContactList();
    });
  }

  @override
  Widget build(BuildContext context) {
    bool isLoading =
        ref.watch(pContactProvider.select((value) => value.isLoading));

    final contactList =
        ref.watch(pContactProvider.select((value) => value.contactList));

    return Stack(
      children: [
        TripcScaffold(
            resizeToAvoidBottomInset: false,
            hasBackButton: true,
            backgroundColor: AppAssets.origin().whiteBackgroundColor,
            titleAppBar: TripcText(
              context.strings.text_contact,
              fontWeight: FontWeight.w600,
              fontSize: 16,
              textColor: AppAssets.origin().black,
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(vertical: 12.W, horizontal: 16.W),
              child: Column(
                children: [
                  GestureDetector(
                    onTap: () => AppRoute.pushNamed(context,
                        routeName: AppRoute.routeAddContact),
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 16.H),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0),
                        color: AppAssets.origin().grayF7,
                      ),
                      margin: EdgeInsets.symmetric(vertical: 8.H),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppAssets.origin().icContact.widget(),
                          SizedBox(width: 12.W),
                          TripcText(
                            context.strings.text_add_new_contact,
                            textColor: AppAssets.origin().darkBlue5FF,
                            ignorePointer: true,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: Visibility(
                      visible: !isLoading,
                      replacement: const ListCardContactShimmerLoading(),
                      child: ListView.builder(
                          itemCount: contactList.length,
                          itemBuilder: (context, index) {
                            return _contactCard(context, contactList[index]);
                          }),
                    ),
                  )
                ],
              ),
            )),
        // AppLoading(isRequesting: isLoading),
      ],
    );
  }

  Widget _contactCard(BuildContext context, ContactResponse contact) {
    return Container(
        height: 160.H,
        margin: EdgeInsets.symmetric(vertical: 8.H),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          color: AppAssets.origin().grayF7,
        ),
        padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TripcText(
                  (contact.fullname ?? '').toUpperCase(),
                  textAlign: TextAlign.left,
                ),
                IconButton(
                    icon: Icon(
                      Icons.arrow_forward_ios_rounded,
                      size: 16.H,
                      color: AppAssets.origin().secondDarkGreyTextColor,
                    ),
                    onPressed: () {
                      AppRoute.pushNamed(context,
                          routeName: AppRoute.routeEditContact,
                          arguments: contact);
                    })
              ],
            ),
            SizedBox(
              height: 12.H,
            ),
            _rowInfor('${context.strings.text_email}:', contact.email),
            _rowInfor('${context.strings.text_phone}:', contact.phone),
          ],
        ));
  }

  Widget _rowInfor(String label, String? content) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.H),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.W,
            child: TripcText(
              label,
              fontWeight: FontWeight.w400,
              textAlign: TextAlign.left,
            ),
          ),
          Expanded(
              child: TripcText(
            content,
            fontWeight: FontWeight.w300,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
            textColor: AppAssets.origin().gray63Color,
            textAlign: TextAlign.left,
          ))
        ],
      ),
    );
  }
}
