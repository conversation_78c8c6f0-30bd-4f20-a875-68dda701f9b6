import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

import '../../../models/remote/api_tour_response/tour_response.dart';
import '../../../widgets/commons/base_cached_network_image.dart';
import '../../../widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';
import '../../../widgets/presentationals/app_shimmer_loading/image_shimmer_loading.dart';

class TripcRecentlyViewedCard extends StatelessWidget {
  const TripcRecentlyViewedCard({super.key, required this.tour});
  final TourResponse tour;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120.H,
      margin: EdgeInsets.symmetric(vertical: 10.H, horizontal: 2.W),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: AppAssets.origin().whiteBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: AppAssets.origin().darkGreyTextColor,
              spreadRadius: 0.1,
              blurRadius: 1,
              offset: const Offset(1, 1),
            )
          ]),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.SP),
            child: BaseCachedNetworkImage(
              imageUrl: tour.thumbnail ?? '',
              width: 130.W,
              height: 120.H,
              placeholder: (context, _) => ImageShimmerLoading(
                height: 120.H,
                fit: BoxFit.scaleDown,
                color: context.appCustomPallet.buttonBG,
              ),
              errorWidget: (context, error, stackTrace) =>
                  AppAssets.origin().icErrorImg.widget(
                        height: 120.H,
                        width: 130.W,
                        color: context.appCustomPallet.buttonBG,
                      ),
              fit: BoxFit.cover,
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.W, vertical: 12.H),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TripcText(
                        tour.name ?? '',
                        textAlign: TextAlign.left,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.H),
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            color: AppAssets.origin().darkYellow,
                            size: 16.H,
                          ),
                          SizedBox(width: 4.W),
                          TripcRichText(
                            text: '',
                            children: [
                              TextSpan(
                                text: '${tour.rating}/5',
                                style:
                                    AppAssets.origin().normalTextStyle.copyWith(
                                          fontWeight: FontWeight.w300,
                                          fontSize: 14,
                                          color: AppAssets.origin().darkYellow,
                                        ),
                              ),
                              TextSpan(
                                text:
                                    ' (${tour.reviews} ${context.strings.text_rating_2})',
                                style: AppAssets.origin()
                                    .normalTextStyle
                                    .copyWith(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 14,
                                        color: AppAssets.origin()
                                            .darkGreyTextColor),
                              ),
                            ],
                          ),
                        ],
                      ),
                      SizedBox(height: 4.H),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          AppAssets.origin().icLocationMarker.widget(),
                          SizedBox(
                            width: 2.W,
                          ),
                          Expanded(
                            child: TripcText(' ${tour.address ?? ''}',
                                fontWeight: FontWeight.w300,
                                textAlign: TextAlign.left,
                                fontSize: 14,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textColor:
                                    AppAssets.origin().darkGreyTextColor),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: 12.H),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TripcText(
                        tour.totalPrice?.vnd,
                        textAlign: TextAlign.right,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
