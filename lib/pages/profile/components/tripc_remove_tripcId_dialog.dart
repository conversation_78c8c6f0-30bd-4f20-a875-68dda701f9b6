import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class RemoveTripcidDialog extends StatelessWidget {
  const RemoveTripcidDialog({super.key, this.onTap});
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return TripcDialog(
        onTap: onTap,
        title: context.strings.text_as_u_know_remove_tripcid,
        titleButton: context.strings.text_agree,
        contentPadding:
            EdgeInsets.only(top: 22.H, bottom: 24.H, left: 12.W, right: 16.W),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 12.H, bottom: 10.H),
              child: _infoLine(
                info: context.strings.text_remove_tripcid_dialog_note_1,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(bottom: 63.H),
              child: _infoLine(
                  info: context.strings.text_remove_tripcid_dialog_note_2),
            ),
          ],
        ));
  }

  Widget _infoLine({required String info}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // TripcText(
        //   '\u2022',
        //   fontSize: 10,
        //   fontWeight: FontWeight.w400,
        //   textColor: AppAssets.origin().black,
        //   padding: EdgeInsets.symmetric(horizontal: 8.W),
        // ),
        Expanded(
          child: TripcText(
            info,
            fontSize: 13,
            fontWeight: FontWeight.w300,
            textColor: AppAssets.origin().black,
          ),
        )
      ],
    );
  }
}

class RemoveDefaultTripcidDialog extends StatelessWidget {
  const RemoveDefaultTripcidDialog({super.key, this.onTap});
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return TripcDialog(
      onTap: onTap,
      title: context.strings.text_note_label,
      titleButton: context.strings.text_agree,
      contentPadding:
          EdgeInsets.only(top: 22.H, bottom: 24.H, left: 12.W, right: 16.W),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 30.H),
        child: TripcText(
          context.strings.text_cannot_remove_default_tripcid,
          fontSize: 14,
          height: 1.5,
          fontWeight: FontWeight.w300,
          textColor: AppAssets.origin().black,
        ),
      ),
    );
  }
}
