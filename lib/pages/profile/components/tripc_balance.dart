import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcBalanceAndPromoTicket extends ConsumerWidget {
  const TripcBalanceAndPromoTicket({
    super.key,
    required this.isLogin,
  });

  final bool isLogin;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final membershipSelected = ref.watch(
        pAccountProvider.select((value) => value.user?.defaultMemberShip));
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Left Card: Balance
          Expanded(
            flex: 9,
            child: Container(
              height: 54.H,
              padding: EdgeInsets.symmetric(horizontal: 12.W, vertical: 10.H),
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppAssets.init.balanceBorderColor,
                  width: 1.W,
                ),
                gradient: AppAssets.origin()
                    .balanceGradient, // Light yellow background
                borderRadius: BorderRadius.circular(8.H),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      AppAssets.init.icTcent.widget(width: 16.W, height: 16.W),
                      SizedBox(width: 8.W),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              TripcText(
                                  !isLogin
                                      ? '_ TCent '
                                      : '~ ${(membershipSelected?.tcent ?? 0).tcent} ',
                                  fontWeight: FontWeight.w500,
                                  fontSize: 12.SP),
                              // TripcText(
                              //   !isLogin ? '( _ USD)' : '(1,500 USD)',
                              //   fontSize: 9.SP,
                              //   textColor:
                              //       AppAssets.init.secondDarkGreyTextColor,
                              // ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                  TripcText(context.strings.text_tcent_hint,
                      fontWeight: FontWeight.w300, fontSize: 10.SP),
                ],
              ),
            ),
          ),

          SizedBox(width: 10.W), // Space between cards

          // Right Card: Promo Code
          Expanded(
            flex: 4,
            child: Container(
              height: 54.H,
              padding: EdgeInsets.symmetric(horizontal: 12.W, vertical: 10.H),
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppAssets.init.promoTicketBorderColor,
                  width: 1.W,
                ),
                gradient: AppAssets.origin()
                    .promoTicketGradient, // Light red background
                borderRadius: BorderRadius.circular(8.H),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      AppAssets.init.icPromoTicket
                          .widget(width: 16.W, height: 16.W),
                      SizedBox(width: 8.W),
                      TripcText(!isLogin ? '_' : '_',
                          fontWeight: FontWeight.w500, fontSize: 12.SP),
                    ],
                  ),
                  TripcText(context.strings.text_profile_promotion,
                      fontWeight: FontWeight.w300, fontSize: 10.SP),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
