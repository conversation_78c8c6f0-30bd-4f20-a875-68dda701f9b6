import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/tripc_tier.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../status_display_notifier.dart';

class TripcTier extends ConsumerWidget {
  const TripcTier({
    super.key,
    required this.tier,
    this.onTapTier,
  });
  final TripcTierType tier;
  final VoidCallback? onTapTier;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: InkWell(
        onTap: onTapTier,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        child: Container(
          height: 44.H,
          width: double.infinity,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.H),
              color: tier.backgroudColor()),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.W),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    TripcText(
                      tier.title(context),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      maxLines: 1,
                      textColor: tier.textColor(),
                    ),
                    Visibility(
                      visible: globalReleaseStatusNotifier.isDisplayAll,
                      child: Row(
                        children: [
                          SizedBox(width: 20.W),
                          Container(
                            width: 1.W,
                            height: 20.H,
                            color: tier.textColor(),
                          ),
                          SizedBox(width: 12.W),
                          TripcText(
                              context.strings.text_hotel_membership_offers,
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              maxLines: 1,
                              textColor: tier.textColor()),
                        ],
                      ),
                    ),
                  ],
                ),
                Visibility(
                  visible: globalReleaseStatusNotifier.isDisplayAll,
                  child: AppAssets.origin().icArrowRightGray.widget(
                        color: tier.textColor(),
                        height: 16.H,
                        width: 16.W,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
