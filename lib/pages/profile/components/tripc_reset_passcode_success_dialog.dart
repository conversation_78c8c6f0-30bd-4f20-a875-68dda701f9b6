import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcResetPasscodeSuccessDialog extends StatelessWidget {
  const TripcResetPasscodeSuccessDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return TripcDialog(
        onTap: () => Navigator.popUntil(
            context, ModalRoute.withName(AppRoute.routeListTripcID)),
        type: TripcDialogType.success,
        title: context.strings.text_passcode_reset_successfully,
        contentPadding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
        titleButton: context.strings.text_agree,
        child: Padding(
          padding: EdgeInsets.only(top: 12.H, bottom: 30.H),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TripcText(
                context.strings.text_you_have_successfully_set_pass,
                fontSize: 14,
                fontWeight: FontWeight.w300,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().black,
              ),
            ],
          ),
        ));
  }
}
