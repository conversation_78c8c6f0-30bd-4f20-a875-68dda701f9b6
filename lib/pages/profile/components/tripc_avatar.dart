import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';
import 'package:tripc_app/widgets/presentationals/app_shimmer_loading/image_shimmer_loading.dart';

class TripcAvatar extends ConsumerWidget {
  const TripcAvatar(
      {super.key,
      required this.avatarUrl,
      this.onTapAvt,
      this.source,
      this.width = 40,
      this.height = 40,
      this.hasEditIcon = false});
  final String? avatarUrl;
  final VoidCallback? onTapAvt;
  final File? source;
  final double width;
  final double height;
  final bool hasEditIcon;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final String? avatar = avatarUrl;
    return TripcIconButton(
        onPressed: onTapAvt,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Container(
              height: height.H,
              width: width.W,
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(52.H),
              ),
              child: BaseCachedNetworkImage(
                  height: height.H,
                  imageUrl: avatar ?? '',
                  fit: BoxFit.cover,
                  placeholder: (context, _) => ImageShimmerLoading(
                        height: height.H,
                        fit: BoxFit.scaleDown,
                        color: context.appCustomPallet.subText?.withAlpha(20),
                      ),
                  errorWidget: (context, error, object) =>
                      AppAssets.origin().icAvatarDetault.widget(
                            height: height.H,
                            width: width.H,
                          )),
            ),
            Visibility(visible: hasEditIcon, child: _buildEditIcon())
          ],
        ));
  }

  Widget _buildEditIcon() {
    return Positioned(
      bottom: 4.H,
      right: -5.W,
      child: Container(
          padding: EdgeInsets.all(4.H),
          decoration: BoxDecoration(
              color: AppAssets.origin().darkBlueColor, shape: BoxShape.circle),
          child:
              AppAssets.origin().icWhiteEdit.widget(height: 12.H, width: 12.H)),
    );
  }
}
