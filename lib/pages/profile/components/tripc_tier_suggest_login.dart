import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcTierSuggestLogin extends ConsumerWidget {
  const TripcTierSuggestLogin({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TripcIconButton(
      showSuggestLoginDialog: true,
      isLogin: false,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 14.H, horizontal: 12.W),
        margin: EdgeInsets.symmetric(horizontal: 24.W),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.SP),
          color: AppAssets.init.whiteBackgroundColor,
          // border: Border.all(color: AppAssets.origin().appBarUnderline),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // TripcText(
            //   context.strings.text_membership_rank,
            //   ignorePointer: true,
            //   fontSize: 10.SP,
            //   fontWeight: FontWeight.w700,
            //   textColor: AppAssets.origin().secondDarkGreyTextColor,
            // ),
            AppAssets.origin().logo.widget(
                  height: 40.H,
                  width: 97.W,
                ),
            SizedBox(
              height: 10.H,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TripcText(
                  context.strings.text_login_to_see_membership_rank,
                  ignorePointer: true,
                  fontSize: 14.SP,
                  fontWeight: FontWeight.w300,
                  textColor: AppAssets.origin().secondDarkGreyTextColor,
                ),
                AppAssets.origin()
                    .icArrowRightGray
                    .widget(height: 16.H, width: 16.H)
              ],
            )
          ],
        ),
      ),
    );
  }
}
