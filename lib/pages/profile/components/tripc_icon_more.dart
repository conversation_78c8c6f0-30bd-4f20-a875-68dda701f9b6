import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';

class IconMoreGradientButton extends StatelessWidget {
  const IconMoreGradientButton({super.key, this.onTap});
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
      onPressed: onTap,
      child: Container(
          height: 20.H,
          width: 27.W,
          padding: EdgeInsets.symmetric(vertical: 2.H, horizontal: 5.W),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.SP),
              gradient: AppAssets.origin().buttonGradient),
          child: AppAssets.origin().icDotMore.widget()),
    );
  }
}
