import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class NotYetLoginDialog extends StatelessWidget {
  const NotYetLoginDialog({super.key, this.resultHandler});
  final VoidCallback? resultHandler;

  @override
  Widget build(BuildContext context) {
    return TripcDialog(
      onTap: () {
        if (AppRoute.I.currentRoute != AppRoute.routeSignIn ||
            AppRoute.I.currentRoute != AppRoute.routeSignUp) {
          globalCacheAuth.saveCurrentScreen(AppRoute.I.currentRoute);
        }
        Navigator.pop(context);
        AppRoute.navigateToRoute(
          context,
          routeName: AppRoute.routeSignIn,
          resultHandler: (value) {
            resultHandler?.call();
          },
        );
      },
      icon: AppAssets.origin().icBlueLock.widget(height: 40.H, width: 40.H),
      title: context.strings.text_opp_not_logged_in,
      titleButton: context.strings.text_sign_in_now,
      contentPadding:
          EdgeInsets.only(top: 20.H, bottom: 30.H, left: 16.W, right: 16.W),
      child: Padding(
        padding: EdgeInsets.only(top: 12.H, bottom: 50.H),
        child: _infoLine(
          info: context.strings.text_please_log_in_or_register,
        ),
      ),
    );
  }

  Widget _infoLine({required String info}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // TripcText(
        //   '\u2022',
        //   fontSize: 10,
        //   fontWeight: FontWeight.w400,
        //   textColor: AppAssets.origin().black,
        //   padding: EdgeInsets.symmetric(horizontal: 8.W),
        // ),
        TripcText(
          info,
          fontSize: 14,
          fontWeight: FontWeight.w300,
          textColor: AppAssets.origin().black,
        )
      ],
    );
  }
}
