import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/profile/providers/tripc_profile_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_otp_text_field/tripc_otp_text_field.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../services/providers/providers.dart';
import '../../../widgets/commons/app_dialog/tripc_error_dialog.dart';

class TripcTypePassCodeBottomSheet extends ConsumerStatefulWidget {
  const TripcTypePassCodeBottomSheet({
    super.key,
  });

  @override
  ConsumerState<TripcTypePassCodeBottomSheet> createState() =>
      _TripcTypePassCodeBottomSheetState();
}

class _TripcTypePassCodeBottomSheetState
    extends ConsumerState<TripcTypePassCodeBottomSheet> {
  final TextEditingController _passCodeController = TextEditingController();
  bool showWarning = false;
  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   ref.listenManual(pProfilePageProvider, (_, state) {
    //     if (state.isRightOldPasscode && _passCodeController.text.length == 6) {
    //       unfocusKeyboard();
    //       // Navigator.pop(context);
    //       ref.read(pProfilePageProvider.notifier).clearResetPasscodePage();
    //       AppRoute.pushNamed(context,
    //           routeName: AppRoute.routeTripcProfileResetPasscode);
    //     }
    //   });
    // });
  }

  Future<void> forgotPasscode(BuildContext context) async {
    final result =
        await ref.read(pProfilePageProvider.notifier).sendOtpForgotPasscode();
    if (result) {
      ref.read(pProfilePageProvider.notifier).clearVerifyPasscodePage();
      Navigator.pop(context);
      AppRoute.pushNamed(context,
          routeName: AppRoute.routeTripcProfileVerifyOtpPasscode);
    } else {
      final errorMessage =
          ref.watch(pProfilePageProvider.select((value) => value.errorMessage));
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorMessage));
    }
  }

  @override
  void dispose() {
    _passCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tryTimes =
        ref.watch(pProfilePageProvider.select((value) => value.tryTimes));
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        TripcText(
          context.strings.text_enter_passcode,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          textColor: AppAssets.origin().black,
          padding: EdgeInsets.only(top: 15.H),
        ),
        Padding(
          padding: EdgeInsets.only(top: 30.H, left: 24.W, right: 24.W),
          child: TripcOtpTextField(
              enable: tryTimes != 0,
              onChanged: (value) async {
                final result = await ref
                    .read(pProfilePageProvider.notifier)
                    .typingOldPasscode(value);
                if (result == null) {
                  return;
                }
                if (result) {
                  unfocusKeyboard();
                  Navigator.pop(context);
                  ref.read(pProfilePageProvider.notifier).clearResetPasscodePage();
                  AppRoute.pushNamed(context,
                      routeName: AppRoute.routeTripcProfileResetPasscode);
                } else {
                  setState(() {
                    showWarning = true;
                  });
                }
              },
              autoFocus: true,
              controller: _passCodeController),
        ),
        SizedBox(
          height: 100.H,
          child: Stack(
            children: [
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Visibility(
                    visible: showWarning,
                    child: _wrongPasscode(context, tryTimes)),
              ),
            ],
          ),
        ),
        TripcButton(
          title: context.strings.text_forgot_passcode,
          onPressed: () => forgotPasscode(context),
          height: 47.H,
          style: AppButtonStyle(
              radius: 0,
              backgroundColor: AppAssets.origin().grayEFColor,
              fontWeight: FontWeight.w600,
              textColor: AppAssets.origin().black),
        ),
        SizedBox(
            height: context.mediaQuery.padding.bottom +
                context.mediaQuery.viewInsets.bottom),
      ],
    );
  }

  Widget _wrongPasscode(BuildContext context, int tryTimes) {
    return Container(
      color: AppAssets.origin().pink5B5Color,
      padding: EdgeInsets.symmetric(horizontal: 24.W, vertical: 12.H),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TripcText(context.strings.text_warning_type_passcode,
              fontSize: 12,
              fontWeight: FontWeight.w400,
              height: 1.5,
              textAlign: TextAlign.start,
              maxLines: 3,
              textColor: AppAssets.origin().secondaryRedColor),
          TripcRichText(text: '', children: [
            TextSpan(
              text: context.strings.text_you_have,
              style: AppAssets.origin().normalTextStyle.copyWith(
                  fontSize: 12.SP, color: AppAssets.origin().secondaryRedColor),
            ),
            const TextSpan(
              text: ' ',
            ),
            TextSpan(
              text: '${tryTimes != -1 ? tryTimes : 0}',
              style: AppAssets.origin().superBoldTextStyle.copyWith(
                  fontSize: 16.SP, color: AppAssets.origin().secondaryRedColor),
            ),
            const TextSpan(
              text: ' ',
            ),
            TextSpan(
              text: context.strings.text_try_times,
              style: AppAssets.origin().normalTextStyle.copyWith(
                  fontSize: 12.SP, color: AppAssets.origin().secondaryRedColor),
            ),
          ])
        ],
      ),
    );
  }
}
