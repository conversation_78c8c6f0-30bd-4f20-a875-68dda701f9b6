import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class SettingPasscodeDialog extends StatelessWidget {
  const SettingPasscodeDialog({super.key, this.onTap});
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return TripcDialog(
        onTap: onTap ?? () => Navigator.pop(context),
        type: TripcDialogType.warning,
        titleButton: context.strings.text_create_passcode,
        title: context.strings.text_you_have_not_set_a_passcode,
        contentPadding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
        child: TripcText(
          context.strings.text_you_have_not_set_a_passcode_message,
          fontSize: 12,
          fontWeight: FontWeight.w400,
          textColor: AppAssets.origin().black,
          padding: EdgeInsets.only(top: 16.H, bottom: 26.H),
        ));
  }
}