import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/tripc_profile_service_category.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class ProfileServiceCategory extends ConsumerWidget {
  const ProfileServiceCategory(
      {super.key,
      required this.category,
      this.isEnableTapIgnoreUnlogin = false});
  final TripcProfileServiceCategory category;
  final bool isEnableTapIgnoreUnlogin;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TripcIconButton(
      onPressed: () => category.onTap(context, ref),
      showSuggestLoginDialog: category.value < 12,
      isLogin: globalCacheAuth.isLogged(),
      isEnableTapIgnoreUnLogin: isEnableTapIgnoreUnlogin,
      child: Column(
        children: [
          category.icon.widget(
            height: 24.H,
            width: 24.H,
          ),
          TripcText(
            category.name(context),
            ignorePointer: true,
            fontSize: 12,
            height: 1.2,
            fontWeight: FontWeight.w400,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(top: 6.H),
          )
        ],
      ),
    );
  }
}

class ProfileServiceCategoriesArea extends StatelessWidget {
  const ProfileServiceCategoriesArea(
      {super.key,
      required this.profileTourServiceCategories,
      required this.profileHelpServiceCategories,
      required this.profilePolicyServiceCategories});
  final List<TripcProfileServiceCategory> profileTourServiceCategories;
  final List<TripcProfileServiceCategory> profileHelpServiceCategories;
  final List<TripcProfileServiceCategory> profilePolicyServiceCategories;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: Column(
        children: [
          Container(
              padding: EdgeInsets.only(top: 12.H, left: 10.W, right: 10.W),
              decoration: BoxDecoration(
                  color: AppAssets.origin().whiteBackgroundColor,
                  borderRadius: BorderRadius.circular(12.SP),
                  boxShadow: [AppAssets.origin().profileServiceAreaShadow]),
              child: GridView.builder(
                itemCount: profileTourServiceCategories.length,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    childAspectRatio: 2, crossAxisCount: 3),
                itemBuilder: (context, index) {
                  return ProfileServiceCategory(
                      category:
                          TripcProfileServiceCategory.getByValue(index + 1));
                },
              )),
          SizedBox(height: 30.H),
          Container(
              padding: EdgeInsets.only(top: 12.H, left: 10.W, right: 10.W),
              decoration: BoxDecoration(
                  color: AppAssets.origin().whiteBackgroundColor,
                  borderRadius: BorderRadius.circular(12.SP),
                  boxShadow: [AppAssets.origin().profileServiceAreaShadow]),
              child: GridView.builder(
                itemCount: profileHelpServiceCategories.length,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    childAspectRatio: 1.5, crossAxisCount: 3),
                itemBuilder: (context, index) {
                  return ProfileServiceCategory(
                      category:
                          TripcProfileServiceCategory.getByValue(index + 4));
                },
              )),
          SizedBox(height: 30.H),
          Container(
              padding: EdgeInsets.only(top: 12.H, left: 10.W, right: 10.W),
              decoration: BoxDecoration(
                  color: AppAssets.origin().whiteBackgroundColor,
                  borderRadius: BorderRadius.circular(12.SP),
                  boxShadow: [AppAssets.origin().profileServiceAreaShadow]),
              child: GridView.builder(
                itemCount: profilePolicyServiceCategories.length,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    childAspectRatio: 1.5, crossAxisCount: 3),
                itemBuilder: (context, index) {
                  return ProfileServiceCategory(
                      isEnableTapIgnoreUnlogin: true,
                      category:
                          TripcProfileServiceCategory.getByValue(index + 9));
                },
              )),
        ],
      ),
    );
  }
}
