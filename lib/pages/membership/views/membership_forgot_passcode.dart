import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_field_with_label.dart';

class MemberShipForgotPass extends StatelessWidget {
  const MemberShipForgotPass({super.key});

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
        hasBackButton: true,
        titleAppBar: TripcText(
          context.strings.text_forgot_passcode,
          fontSize: 28,
          fontWeight: FontWeight.w600,
          textCase: TextCaseType.none,
        ),
        toolbarHeight: 41.H,
        needUnFocus: true,
        resizeToAvoidBottomInset: true,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        body: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.W),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TripcText(
                  context.strings.text_enter_mail_reset_passcode,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  textCase: TextCaseType.none,
                  textAlign: TextAlign.center,
                  textColor: AppAssets.origin().darkGreyTextColor,
                  padding: EdgeInsets.only(top: 8.H),
                ),
                TripcTextFieldWithLabel(
                  onChanged: (value) {},
                  label: context.strings.text_tripcID,
                  hintText: context.strings.text_enter_your_tripc_id,
                  padding: EdgeInsets.only(top: 55.H),
                ),
                TripcTextFieldWithLabel(
                  onChanged: (value) {},
                  label: context.strings.text_email,
                  hintText: context.strings.text_enter_email_to_get_passcode,
                  padding: EdgeInsets.only(top: 20.H, bottom: 63.H),
                ),
                TripcButton(
                  onPressed: () {},
                  title: context.strings.text_send,
                ),
              ],
            ),
          ),
        ));
  }
}
