import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/membership/providers/membership_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class MembershipTripCIDResult extends ConsumerWidget {
  const MembershipTripCIDResult({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(pAccountProvider.select((value) => value.user));
    return TripcScaffold(
      backgroundColor: AppAssets.origin().whiteBackgroundColor,
      hasBackButton: true,
      onLeadingPressed: () => Navigator.pop(context),
      body: SafeArea(
        bottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 24.W),
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                  height: 14.H,
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: AppAssets.origin().logo.widget(
                        height: 40.H,
                        width: 97.W,
                      ),
                ),
                TripcText(
                  context.strings.text_give_a_tripc_id,
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  textCase: TextCaseType.none,
                  textAlign: TextAlign.center,
                  textColor: AppAssets.origin().black,
                  padding: EdgeInsets.only(top: 12.H),
                ),
                TripcText(
                  user?.defaultMemberShip?.number?.formatTripcId,
                  fontSize: 36,
                  fontWeight: FontWeight.w600,
                  textCase: TextCaseType.none,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().black,
                  padding: EdgeInsets.symmetric(vertical: 50.H),
                ),
                TripcText(
                  context.strings.text_lets_create_passcode,
                  fontSize: 14,
                  height: 1.2,
                  fontWeight: FontWeight.w400,
                  textCase: TextCaseType.none,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().black,
                ),
                TripcButton(
                  onPressed: () {
                    ref
                        .read(pMembershipScreenProvider.notifier)
                        .selectNumber(user?.firstMembership);
                    AppRoute.pushNamed(context,
                        routeName: AppRoute.routeTypePassCode, arguments: false);
                  },
                  title: context.strings.text_create_passcode,
                  margin: EdgeInsets.only(top: 50.H),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
