import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/membership/components/membership_sign_up_option.dart';
import 'package:tripc_app/pages/membership/providers/membership_provider.dart';
import 'package:tripc_app/pages/profile/providers/tripc_profile_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_filed.dart';

class MembershipTripCSignIn extends ConsumerStatefulWidget {
  const MembershipTripCSignIn({super.key, this.isExistTripcIdDefault = false});
  final bool isExistTripcIdDefault;

  @override
  ConsumerState<MembershipTripCSignIn> createState() =>
      _MembershipTripCSignInState();
}

class _MembershipTripCSignInState extends ConsumerState<MembershipTripCSignIn> {
  final TextEditingController _tripcIdController = TextEditingController();
  final TextEditingController _tripcPassController = TextEditingController();
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        ref
            .read(pMembershipScreenProvider.notifier)
            .setIsExistTripcIdDefault(widget.isExistTripcIdDefault);
      },
    );
    _tripcIdController.addListener(
      () {
        setState(() {});
      },
    );
    _tripcPassController.addListener(
      () {
        setState(() {});
      },
    );
  }

  bool get isConfirmEnable {
    return _tripcIdController.text.length == 10 &&
        _tripcPassController.text.length == 6;
  }

  Future<void> _loginMemberShip() async {
    _forceLoading(true);
    final result = await ref.read(pAccountProvider.notifier).loginMemberShip(
          context,
          number: _tripcIdController.text,
          passcode: _tripcPassController.text,
        );

    if (result) {
      await _handleNavigate();
    } else {
      _forceLoading(false);
    }
  }

  Future<void> _handleNavigate() async {
    if (AppRoute.I.routeObserver.isContainRoute(AppRoute.routeListTripcID)) {
      final result =
          await ref.read(pProfilePageProvider.notifier).refreshData();
      _forceLoading(false);
      result ? Navigator.pop(context) : _showError();
    } else {
      _forceLoading(false);
      AppRoute.pushReplacement(context, routeName: AppRoute.routeHome);
    }
  }

  void _forceLoading(bool value) {
    setState(() {
      _loading = value;
    });
  }

  void _showError() {
    final error = ref.read(pAccountProvider).errorMessage ?? '';
    dialogHelpers.show(context,
        child: TripcErrorDialog(
          errorText: error,
        ));
  }

  @override
  void dispose() {
    super.dispose();
    _tripcIdController.dispose();
    _tripcPassController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final hasBackButton = AppRoute.I.routeObserver.isContainRoute(
      AppRoute.routeListTripcID,
    );
    final error =
        ref.watch(pAccountProvider.select((value) => value.errorSignTripc));
    return Stack(
      children: [
        TripcScaffold(
          onPressed: () => unfocusKeyboard(),
          backgroundColor: AppAssets.origin().whiteBackgroundColor,
          hasBackButton: hasBackButton,
          visibleAppBar: hasBackButton,
          body: SafeArea(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24.W),
              child: SingleChildScrollView(
                clipBehavior: Clip.none,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    AppAssets.origin().logo.widget(
                          height: 40.H,
                          width: 97.W,
                        ),
                    SizedBox(height: 8.H),
                    _buildSignInArea(context, error: error),
                    Visibility(
                      visible: !widget.isExistTripcIdDefault,
                      child: Padding(
                          padding: EdgeInsets.only(
                              top: 12.H +
                                  (widget.isExistTripcIdDefault ? 25.H : 0),
                              bottom: 10.H),
                          child: Stack(fit: StackFit.loose, children: [
                            Positioned.fill(
                              child: Divider(
                                thickness: 1.0.H,
                                color: AppAssets.init.disableButtonColor,
                              ),
                            ),
                            Align(
                              child: Container(
                                  color: AppAssets.init.whiteBackgroundColor,
                                  padding: EdgeInsets.symmetric(horizontal: 21.W),
                                  child: TripcText(
                                    context.strings.text_or,
                                    textColor:
                                        AppAssets.init.secondDarkGreyTextColor,
                                    fontWeight: FontWeight.w500,
                                  )),
                            ),
                          ])),
                    ),
                    _buildSignUpArea(context),
                  ],
                ),
              ),
            ),
          ),
        ),
        AppLoading(isRequesting: _loading)
      ],
    );
  }

  Widget _buildSignInArea(BuildContext context, {String error = ''}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_lets_enter_your_tripc_id,
          fontSize: 14,
          textCase: TextCaseType.none,
          textAlign: TextAlign.center,
          textColor: AppAssets.origin().black,
        ),
        Padding(
          padding: EdgeInsets.only(top: 18.H, bottom: 20.H),
          child: TripcTextField(
            controller: _tripcIdController,
            onChanged: (value) =>
                ref.read(pAccountProvider.notifier).setErrorTripcIdSignIn(''),
            hintText: context.strings.text_enter_your_tripc_id,
            textCase: TextCaseType.none,
            isError: error.isNotEmpty,
            keyboardType: TextInputType.number,
            contentPadding:
                EdgeInsets.symmetric(horizontal: 24.W, vertical: 17.H),
          ),
        ),
        TripcTextField(
          controller: _tripcPassController,
          onChanged: (value) =>
              ref.read(pAccountProvider.notifier).setErrorTripcIdSignIn(''),
          hintText: context.strings.text_enter_your_tripc_passcode,
          isError: error.isNotEmpty,
          keyboardType: TextInputType.number,
          textCase: TextCaseType.none,
          contentPadding:
              EdgeInsets.symmetric(horizontal: 24.W, vertical: 17.H),
        ),
        SizedBox(
          height: 37.H,
          child: TripcText(
            error,
            fontSize: 12,
            fontWeight: FontWeight.w300,
            textColor: AppAssets.origin().redDotColor,
            enableAutoResize: true,
            fontStyle: FontStyle.italic,
            maxLines: 1,
            padding: EdgeInsets.only(top: 8.H, left: 8.W, right: 8.W),
          ),
        ),
        TripcButton(
          onPressed: _loginMemberShip,
          title: context.strings.text_confirm,
          isButtonDisabled: !isConfirmEnable,
        )
      ],
    );
  }

  Widget _buildSignUpArea(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: !widget.isExistTripcIdDefault,
          // replacement: TripcText(
          //   context.strings.text_select_a_nice_account_number,
          //   fontSize: 16,
          //   fontWeight: FontWeight.w600,
          //   textCase: TextCaseType.none,
          //   textAlign: TextAlign.start,
          //   textColor: AppAssets.origin().black,
          //   padding: EdgeInsets.only(top: 2.H, bottom: 12.H),
          // ),
          child: Padding(
            padding: EdgeInsets.only(bottom: 20.H),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TripcText(
                  context.strings.text_register_new_id,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  textCase: TextCaseType.none,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().black,
                ),
                TripcText(
                  context.strings.text_pls_choose_your_own_tripc_id,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  textCase: TextCaseType.none,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().black,
                  padding: EdgeInsets.only(top: 6.H, bottom: 20.H),
                ),
                MembershipSignUpOption(
                  onTapRegister: () => AppRoute.pushNamed(context,
                      routeName: AppRoute.routeMembershipTripcIdResult),
                  gradient: AppAssets.origin().signUpOptionGradient1,
                  title: context.strings.text_tripc_id_free,
                  content: context.strings.text_tripc_id_free_content,
                  icon: AppAssets.origin()
                      .iconBook
                      .widget(height: 68.H, width: 68.H),
                ),
              ],
            ),
          ),
        ),
        // MembershipSignUpOption(
        //   onTapRegister: () => AppRoute.pushNamed(context,
        //       routeName: AppRoute.routeSelectGoodNumber),
        //   gradient: AppAssets.origin().signUpOptionGradient2,
        //   title: context.strings.text_tripc_id_nice_number,
        //   content: context.strings.text_tripc_id_nice_number_content,
        //   icon: AppAssets.origin().iconCat.widget(height: 67.H, width: 84.H),
        // ),
      ],
    );
  }
}
