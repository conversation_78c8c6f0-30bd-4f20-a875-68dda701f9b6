import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/membership/components/membership_good_number_detail.dart';
import 'package:tripc_app/pages/membership/components/membership_payment_method.dart';
import 'package:tripc_app/pages/membership/providers/membership_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../services/app/app_route.dart';

class MembershipPaymentView extends ConsumerStatefulWidget {
  const MembershipPaymentView({super.key});

  @override
  ConsumerState<MembershipPaymentView> createState() =>
      _MembershipPaymentViewState();
}

class _MembershipPaymentViewState extends ConsumerState<MembershipPaymentView> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        ref.listenManual(
            pMembershipScreenProvider.select((state) => state.isPaymentSuccess),
            (_, state) {
          if (!state) {
            return;
          }
          AppRoute.pushNamed(context,
              routeName: AppRoute.routePaymentSuccess, arguments: true);
        });
        ref.listenManual(
            pMembershipScreenProvider.select((state) => state.isPaymentError),
            (_, state) {
          if (!state) {
            return;
          }
          AppRoute.pushNamed(context,
              routeName: AppRoute.routePaymentSuccess, arguments: false);
        });
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final selectedNumber = ref.watch(pMembershipScreenProvider.select(
      (value) => value.selectedNumber,
    ));
    final selectedPaymentMethod = ref.watch(pMembershipScreenProvider.select(
      (value) => value.isSelectedPaymentMethod,
    ));

    return TripcScaffold(
      backgroundColor: AppAssets.origin().whiteBackgroundColor,
      hasBackButton: true,
      body: Container(
        padding: EdgeInsets.only(
            top: 12.H, left: 16.W, right: 16.W, bottom: context.spacingBottom),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppAssets.origin().logo.widget(
                  height: 40.H,
                  width: 97.W,
                ),
            TripcText(
              context.strings.text_you_are_choosing,
              fontSize: 24,
              fontWeight: FontWeight.w600,
              textCase: TextCaseType.none,
              textAlign: TextAlign.center,
              textColor: AppAssets.origin().black,
              padding: EdgeInsets.only(top: 18.H, bottom: 30.H),
            ),
            GoodNumberDetailItem(
              goodNumber: selectedNumber,
            ),
            MbsPaymentMethod(
                selectedPaymentMethod: selectedPaymentMethod,
                onTapCheck: () => ref
                    .read(pMembershipScreenProvider.notifier)
                    .setIsSelectedPaymentMethod()),
            const Spacer(),
            TripcButton(
              onPressed: () {
                ref
                    .read(pMembershipScreenProvider.notifier)
                    .createOrderTourWithVNPay(tripcId: selectedNumber?.id);
              },
              isButtonDisabled: !selectedPaymentMethod,
              title: context.strings.text_pay,
              margin: EdgeInsets.only(top: 40.H),
            )
          ],
        ),
      ),
    );
  }
}
