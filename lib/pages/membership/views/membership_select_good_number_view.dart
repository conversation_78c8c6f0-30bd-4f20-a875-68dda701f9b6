import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/good_number.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_model.dart';
import 'package:tripc_app/pages/membership/components/membership_list_good_number_option.dart';
import 'package:tripc_app/pages/membership/providers/membership_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/last_page_warning/last_page_warning.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcSelectGoodNumber extends ConsumerStatefulWidget {
  const TripcSelectGoodNumber({super.key});

  @override
  ConsumerState<TripcSelectGoodNumber> createState() =>
      _TripcSelectGoodNumberState();
}

class _TripcSelectGoodNumberState extends ConsumerState<TripcSelectGoodNumber> {
  bool loadingData = false;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      forceLoading(true);
      await _getData();
      forceLoading(false);
    });
  }

  Future<void> _getData() async {
    ref
        .read(pMembershipScreenProvider.notifier)
        .setPageGetData(page: 1, pageSize: 4);
    await ref.read(pMembershipScreenProvider.notifier).getLuckyWeathNumbers(
          getDetail: false,
        );
    await ref.read(pMembershipScreenProvider.notifier).getGreatPeace(
          getDetail: false,
        );
    await ref.read(pMembershipScreenProvider.notifier).getQuadrupleNumbers(
          getDetail: false,
        );
    await ref.read(pMembershipScreenProvider.notifier).getRichNobleNumbers(
          getDetail: false,
        );
  }

  List<MembershipModel> getNumberOptionsByType(TripcGoodNumberType type) {
    switch (type) {
      case TripcGoodNumberType.prosper:
        return ref.watch(pMembershipScreenProvider
            .select((value) => value.luckyWeatherMemberships));
      case TripcGoodNumberType.tuquy:
        return ref.watch(pMembershipScreenProvider
            .select((value) => value.quadrupleMemberships));
      case TripcGoodNumberType.wealthy:
        return ref.watch(pMembershipScreenProvider
            .select((value) => value.richNobleMemberships));
      case TripcGoodNumberType.greatpeace:
        return ref.watch(pMembershipScreenProvider
            .select((value) => value.greatPeaceMemberships));
    }
  }

  void forceLoading(bool value) {
    if (mounted) {
      setState(() {
        loadingData = value;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
        onPopScope: () =>
            ref.read(pMembershipScreenProvider.notifier).resetState(),
        onLeadingPressed: () {
          ref.read(pMembershipScreenProvider.notifier).resetState();
          Navigator.pop(context);
        },
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        hasBackButton: true,
        toolbarHeight: 46.H,
        leadingWidth: 40,
        padding: EdgeInsets.only(top: 12.H),
        titleAppBar: TripcText(
          context.strings.text_choose_beautiful_number,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          textCase: TextCaseType.none,
          textAlign: TextAlign.start,
          textColor: AppAssets.origin().black,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            await _getData();
          },
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 16.H)
                .copyWith(bottom: context.mediaQuery.padding.bottom),
            children: [
              ListView.separated(
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.zero,
                  itemCount: TripcGoodNumberType.values.length,
                  shrinkWrap: true,
                  separatorBuilder: (context, _) => SizedBox(
                        height: 24.H,
                      ),
                  itemBuilder: (context, index) {
                    final numberOptions = getNumberOptionsByType(
                      TripcGoodNumberType.values[index],
                    );
                    return MembershipListGoodNumberOption(
                        onSelectNumber: (value) {
                          ref
                              .read(pMembershipScreenProvider.notifier)
                              .selectNumber(value);
                          AppRoute.pushNamed(context,
                              routeName: AppRoute.routePaymentNumber);
                        },
                        type: TripcGoodNumberType.values[index],
                        isLoading: loadingData,
                        numberOptions: numberOptions);
                  }),
              Padding(
                padding: EdgeInsets.only(top: 20.H),
                child: const LastPageWarning(),
              )
            ],
          ),
        ));
  }
}
