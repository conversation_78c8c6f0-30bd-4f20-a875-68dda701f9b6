import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/good_number.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/membership/components/membership_good_number_option.dart';
import 'package:tripc_app/pages/membership/providers/membership_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/number_option_loading/number_option_shimmer_loading.dart';
import 'package:tripc_app/widgets/commons/last_page_warning/last_page_warning.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcGoodNumberSeeMore extends ConsumerStatefulWidget {
  const TripcGoodNumberSeeMore({super.key, required this.type});
  final TripcGoodNumberType type;

  @override
  ConsumerState<TripcGoodNumberSeeMore> createState() =>
      _TripcGoodNumberSeeMoreState();
}

class _TripcGoodNumberSeeMoreState
    extends ConsumerState<TripcGoodNumberSeeMore> {
  final _scrollController = ScrollController();
  @override
  void initState() {
    super.initState();
    _scrollController.onBottomReach(sensitivity: 150, () {
      _loadMore();
    }, throttleDuration: const Duration(seconds: 1));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getNumberOptions();
    });
  }

  void getNumberOptions() {
    ref.read(pMembershipScreenProvider.notifier).refreshData(type: widget.type);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadMore() {
    ref.read(pMembershipScreenProvider.notifier).loadMore(type: widget.type);
  }

  @override
  Widget build(BuildContext context) {
    final currentMemberships = ref.watch(pMembershipScreenProvider.select(
      (value) => value.currentMemberships,
    ));
    final isLoading = ref.watch(pMembershipScreenProvider.select(
      (value) => value.isLoading,
    ));
    final isLoadingLoadMore = ref.watch(
        pMembershipScreenProvider.select((value) => value.isLoadingLoadMore));
    final canloadMore = ref.watch(pMembershipScreenProvider.select(
      (value) => value.canLoadmore,
    ));
    return Stack(
      children: [
        TripcScaffold(
            onPopScope: () =>
                ref.read(pMembershipScreenProvider.notifier).resetSeeMorePage(),
            onPressed: () {
              ref.read(pMembershipScreenProvider.notifier).resetSeeMorePage();
              Navigator.pop(context);
            },
            backgroundColor: AppAssets.origin().whiteBackgroundColor,
            hasBackButton: true,
            titleAppBar: TripcText(
              '${context.strings.text_select} ${widget.type.title(context)}',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              textCase: TextCaseType.none,
              textAlign: TextAlign.start,
              textColor: AppAssets.origin().black,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            body: Visibility(
              visible: !isLoading,
              replacement: ListNumberOptionShimmerLoading(
                length: 12,
                padding: EdgeInsets.symmetric(horizontal: 24.W, vertical: 20.H)
                    .copyWith(bottom: context.mediaQuery.padding.bottom),
              ),
              child: RefreshIndicator(
                onRefresh: () async {
                  ref
                      .read(pMembershipScreenProvider.notifier)
                      .refreshData(type: widget.type);
                },
                child: ListView(
                  padding:
                      EdgeInsets.symmetric(horizontal: 24.W, vertical: 20.H)
                          .copyWith(bottom: context.mediaQuery.padding.bottom),
                  children: [
                    ListView.separated(
                        physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.zero,
                        controller: _scrollController,
                        itemCount: currentMemberships.length,
                        shrinkWrap: true,
                        separatorBuilder: (context, _) => SizedBox(
                              height: 8.H,
                            ),
                        itemBuilder: (context, index) => GoodNumberOption(
                            onPressed: () {
                              ref
                                  .read(pMembershipScreenProvider.notifier)
                                  .selectNumber(currentMemberships[index]);
                              AppRoute.pushNamed(context,
                                  routeName: AppRoute.routePaymentNumber);
                            },
                            number: currentMemberships[index])),
                    Visibility(
                      visible: !canloadMore,
                      child: Padding(
                        padding: EdgeInsets.only(top: 20.H),
                        child: const LastPageWarning(),
                      ),
                    )
                  ],
                ),
              ),
            )),
        AppLoading(isRequesting: isLoadingLoadMore)
      ],
    );
  }
}
