import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/membership/providers/membership_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/qr_code/qr_code.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class MembershipScanQrView extends ConsumerWidget {
  const MembershipScanQrView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedNumber = ref.watch(pMembershipScreenProvider.select(
      (value) => value.selectedNumber,
    ));
    final accountPayment = ref.watch(pMembershipScreenProvider.select(
      (value) => value.accountPayment,
    ));
    return TripcScaffold(
      backgroundColor: AppAssets.origin().whiteBackgroundColor,
      hasBackButton: true,
      onLeadingPressed: () => Navigator.pop(context),
      body: Container(
        padding: EdgeInsets.only(left: 24.W, right: 24.W),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            TripcQrCode(
                data: accountPayment.qrData,
                margin: EdgeInsets.symmetric(vertical: 30.H)),
            TripcText(
              accountPayment.name,
              fontSize: 14,
              fontWeight: FontWeight.w600,
              textCase: TextCaseType.none,
              textAlign: TextAlign.center,
              textColor: AppAssets.origin().black,
            ),
            TripcText(
              selectedNumber?.price?.vnd,
              fontSize: 20,
              fontWeight: FontWeight.w600,
              textCase: TextCaseType.none,
              textAlign: TextAlign.center,
              textColor: AppAssets.origin().black,
              padding: EdgeInsets.only(top: 8.H, bottom: 10.H),
            ),
            TripcText(
              context.strings.text_payment_content,
              fontSize: 12,
              fontWeight: FontWeight.w600,
              textCase: TextCaseType.none,
              textAlign: TextAlign.center,
              textColor: AppAssets.origin().black,
            ),
            TripcButton(
              onPressed: () => AppRoute.pushNamed(context,
                  routeName: AppRoute.routePaymentSuccess),
              title: context.strings.text_scan_qr,
              margin: EdgeInsets.only(top: 20.H),
            )
          ],
        ),
      ),
    );
  }
}
