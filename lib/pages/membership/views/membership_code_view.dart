import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/membership/providers/membership_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_filed.dart';

import '../../../services/providers/providers.dart';
import '../components/membership_referral_code_dialog.dart';

class MembershipCodeView extends ConsumerStatefulWidget {
  const MembershipCodeView({super.key});

  @override
  ConsumerState<MembershipCodeView> createState() => _MembershipCodeViewState();
}

class _MembershipCodeViewState extends ConsumerState<MembershipCodeView> {
  final TextEditingController textEditingController = TextEditingController();
  Future<void> _inviteCode() async {
    final referralCode = ref
        .watch(pMembershipScreenProvider.select((value) => value.referralCode));
    final result = await ref
        .read(pMembershipScreenProvider.notifier)
        .inviteCode(referralCode: referralCode);
    ref.read(pMembershipScreenProvider.notifier).setReferralCode('');
    textEditingController.text = '';
    if (result) {
      ref.read(pAccountProvider.notifier).markSkipInviteCode();
      dialogHelpers.show(context,
          barrierDismissible: false,
          child: const MemberShipReferralCodeDialog());
    }
  }

  @override
  void dispose() {
    textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final error = ref.watch(
            pMembershipScreenProvider.select((value) => value.errorMessage)) ??
        '';
    return TripcScaffold(
      onPressed: () => unfocusKeyboard(),
      backgroundColor: AppAssets.origin().whiteBackgroundColor,
      resizeToAvoidBottomInset: false,
      visibleAppBar: false,
      body: SafeArea(
        bottom: false,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.W)
              .copyWith(bottom: context.spacingBottom),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 12.H, left: 9.W, right: 12.W),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      AppAssets.origin().logo.widget(
                            height: 40.H,
                            width: 97.W,
                          ),
                      TripcText(
                        onTap: () {
                          unfocusKeyboard();
                          ref
                              .read(pAccountProvider.notifier)
                              .markSkipInviteCode();
                          AppRoute.pushNamed(context,
                              routeName: AppRoute.routeMembershipSignIn,
                              arguments: false);
                        },
                        context.strings.text_skip,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        textCase: TextCaseType.none,
                        textAlign: TextAlign.center,
                        textColor: AppAssets.origin().black,
                      ),
                    ]),
              ),
              Padding(
                padding: EdgeInsets.only(top: 12.H),
                child: AppAssets.origin()
                    .membershipBg
                    .widget(height: 207.H, width: 293.W),
              ),
              TripcText(
                context.strings.text_referral_code,
                fontSize: 16,
                fontWeight: FontWeight.w600,
                textCase: TextCaseType.none,
                textAlign: TextAlign.center,
                textColor: AppAssets.origin().black,
                padding: EdgeInsets.symmetric(vertical: 12.H),
              ),
              Container(
                constraints: BoxConstraints(maxWidth: 296.W),
                child: TripcText(
                  context.strings.text_enter_your_referral_code_from_friend,
                  fontSize: 12,
                  height: 1.2,
                  fontWeight: FontWeight.w300,
                  textCase: TextCaseType.none,
                  textAlign: TextAlign.center,
                  textColor: AppAssets.origin().black,
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 24.H, bottom: 12.H),
                child: TripcTextField(
                  onChanged: ref
                      .read(pMembershipScreenProvider.notifier)
                      .setReferralCode,
                  keyboardType: TextInputType.number,
                  controller: textEditingController,
                  hintText: context.strings.text_enter_your_referral_code,
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 24.W, vertical: 17.H),
                  errorText: error.isNotEmpty
                      ? context.strings.text_invalid_referral_code
                      : null,
                ),
              ),
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppAssets.origin().iconMoreDetail.widget(
                          height: 16.H,
                          width: 16.H,
                        ),
                    SizedBox(
                      width: 6.W,
                    ),
                    Expanded(
                      child: TripcText(
                        context.strings.text_referral_code_define,
                        fontSize: 12,
                        height: 1.5,
                        fontWeight: FontWeight.w400,
                        textCase: TextCaseType.none,
                        textAlign: TextAlign.start,
                        textColor: AppAssets.origin().secondDarkGreyTextColor,
                      ),
                    ),
                  ]),
              const Spacer(),
              TripcButton(
                onPressed: () {
                  unfocusKeyboard();
                  _inviteCode();
                },
                height: 56,
                title: context.strings.text_confirm,
                isButtonDisabled: ref
                        .watch(pMembershipScreenProvider
                            .select((value) => value.referralCode))
                        .length !=
                    10,
              )
            ],
          ),
        ),
      ),
    );
  }
}
