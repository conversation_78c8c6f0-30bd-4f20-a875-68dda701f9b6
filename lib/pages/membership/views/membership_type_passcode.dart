import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/membership/components/membership_creare_passcode_successfully_dialog.dart';
import 'package:tripc_app/pages/membership/providers/membership_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_otp_text_field/tripc_otp_text_field.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../services/providers/tripc_account_provider.dart';

class MembershipTypePassCode extends ConsumerStatefulWidget {
  const MembershipTypePassCode({super.key, this.isFromProfile = false});
  final bool isFromProfile;

  @override
  ConsumerState<MembershipTypePassCode> createState() =>
      _MembershipTypePassCodeState();
}

class _MembershipTypePassCodeState
    extends ConsumerState<MembershipTypePassCode> {
  final TextEditingController _passCodeController = TextEditingController();

  @override
  void initState() {
    _passCodeController.addListener(() {
      setState(() {});
      if (_passCodeController.text.length == 6) {
        unfocusKeyboard();
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    _passCodeController.dispose();
    super.dispose();
  }

  Future<void> _createPasscode(int? id) async {
    final result = await ref
        .read(pMembershipScreenProvider.notifier)
        .createPasscode(passcode: _passCodeController.text);
    if (result) {
      dialogHelpers.show(context, barrierDismissible: false,
          child: CreatePassCodeSuccessDialog(onTap: () {
        Navigator.pop(context);
        ref.read(pAccountProvider.notifier).getme();
        if (ref.watch(pMembershipScreenProvider.select((value) => value.isExistTripcIdDefault)) || widget.isFromProfile) {
          AppRoute.pushReplacement(context, routeName: AppRoute.routeTripcIDSetting, arguments: id ?? 0);
        } else {
          final currentScreen = globalCacheAuth.getCurrentScreen();
          if (currentScreen != AppRoute.routeTourDetailView) {
            Navigator.popUntil(context, (route) => route.settings.name == AppRoute.routeHome);
            return;
          }
          Navigator.popUntil(context, (route) => route.settings.name == currentScreen);
        }
      }));
    } else {
      final error = ref.read(pMembershipScreenProvider).errorMessage ?? '';
      dialogHelpers.show(context, child: TripcErrorDialog(errorText: error));
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedNumber = ref.watch(pMembershipScreenProvider.select(
      (value) => value.selectedNumber,
    ));

    final isLoading = ref.watch(pMembershipScreenProvider.select(
      (value) => value.isLoading,
    ));
    return Stack(
      children: [
        TripcScaffold(
          onPressed: () => unfocusKeyboard(),
          visibleAppBar: true,
          backgroundColor: AppAssets.origin().whiteBackgroundColor,
          resizeToAvoidBottomInset: true,
          hasBackButton: true,
          body: SafeArea(
            child: Container(
              padding: EdgeInsets.only(top: 48.H, left: 24.W, right: 24.W),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppAssets.origin().logo.widget(
                                height: 40.H,
                                width: 97.W,
                              ),
                          _tripcIDInfo(
                              number: selectedNumber?.number.toString() ?? ''),
                          _inputPassCode(),
                        ],
                      ),
                    ),
                  ),
                  TripcButton(
                    onPressed: () {
                      _createPasscode(selectedNumber?.id);
                    },
                    isButtonDisabled: _passCodeController.text.length != 6,
                    title: context.strings.text_confirm,
                    margin: EdgeInsets.only(top: 71.H),
                  )
                ],
              ),
            ),
          ),
        ),
        AppLoading(isRequesting: isLoading)
      ],
    );
  }

  Widget _tripcIDInfo({required String number}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_finish_membership_card,
          fontSize: 14,
          fontWeight: FontWeight.w400,
          textCase: TextCaseType.none,
          textAlign: TextAlign.start,
          textColor: AppAssets.origin().black,
          padding: EdgeInsets.only(bottom: 4.H, top: 20.H),
        ),
        Padding(
          padding: EdgeInsets.only(top: 20.H, bottom: 40.H),
          child: Row(
            children: [
              AppAssets.origin().iconBook.widget(height: 30.H, width: 30.H),
              SizedBox(
                width: 24.W,
              ),
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                TripcText(
                  context.strings.text_tripc_id_nice_number,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  textCase: TextCaseType.none,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().secondDarkGreyTextColor,
                  padding: EdgeInsets.only(bottom: 4.H),
                ),
                TripcText(
                  number,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  textCase: TextCaseType.none,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().black,
                ),
              ])
            ],
          ),
        ),
      ],
    );
  }

  Widget _inputPassCode() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_pls_enter_6_digit,
          fontSize: 14,
          fontWeight: FontWeight.w400,
          textCase: TextCaseType.none,
          textAlign: TextAlign.start,
          textColor: AppAssets.origin().black,
          padding: EdgeInsets.only(bottom: 38.H),
        ),
        TripcOtpTextField(controller: _passCodeController)
      ],
    );
  }
}
