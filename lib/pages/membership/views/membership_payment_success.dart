import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class MembershipPaymentSuccess extends ConsumerWidget {
  const MembershipPaymentSuccess({super.key, this.isPaymentSuccess = false});
  final bool isPaymentSuccess;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TripcScaffold(
      backgroundColor: AppAssets.origin().whiteBackgroundColor,
      hasBackButton: true,
      leading: const SizedBox.shrink(),
      onLeadingPressed: () => Navigator.pop(context),
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.only(top: 27.H, left: 24.W, right: 24.W),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: isPaymentSuccess ? AppAssets.origin().icSuccess.widget(
                      height: 120.H,
                      width: 120.H,
                    ) : AppAssets.origin().icWarning.widget(
                  height: 120.H,
                  width: 120.H,
                )
              ),
              TripcText(
                isPaymentSuccess ? context.strings.text_success_payment : context.strings.text_payment_fail,
                fontSize: 24,
                fontWeight: FontWeight.w600,
                textCase: TextCaseType.none,
                textAlign: TextAlign.center,
                textColor: AppAssets.origin().black,
                padding: EdgeInsets.only(top: 27.H, bottom: 20.H),
              ),
              TripcText(
                isPaymentSuccess ? context.strings.text_congratulation_and_make_passcode : context.strings.text_please_try_again,
                fontSize: 14,
                height: 1.5,
                fontWeight: FontWeight.w400,
                textCase: TextCaseType.none,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().black,
              ),
              const Spacer(),
              TripcButton(
                onPressed: () {
                  if (isPaymentSuccess) {
                    AppRoute.pushReplacement(context,
                        routeName: AppRoute.routeTypePassCode, arguments: false);
                  } else {
                    Navigator.pop(context);
                  }
                },
                title:isPaymentSuccess ? context.strings.text_create_passcode : context.strings.text_try_again,
                margin: EdgeInsets.only(top: 141.H),
              )
            ],
          ),
        ),
      ),
    );
  }
}
