import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/account_payment.dart';
import 'package:tripc_app/models/app/good_number.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/app/payment_method_enum.dart';
import 'package:tripc_app/models/app/update_passcode_request.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_list_numbers_response.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_model.dart';
import 'package:tripc_app/services/apis/auth/api_auth.dart';
import 'package:tripc_app/services/apis/membership/api_membership.dart';
import 'package:tripc_app/services/apis/order/api_order.dart';
import 'package:vnpay_flutter/vnpay_flutter.dart';

class MembershipScreenModel {
  MembershipScreenModel(
      {this.selectedNumber,
      required this.accountPayment,
      this.currentMethod = PaymentMethod.vnpay,
      this.currentMemberships = const [],
      this.page = 1,
      this.pageSize = 20,
      this.inputNumber = '',
      this.isLoading = false,
      this.canLoadmore = true,
      this.errorMessage = '',
      this.isLoadingLoadMore = false,
      this.luckyWeatherMemberships = const [],
      this.quadrupleMemberships = const [],
      this.richNobleMemberships = const [],
      this.greatPeaceMemberships = const [],
      this.isSelectedPaymentMethod = true,
      this.isPaymentSuccess = false,
      this.isPaymentError = false,
      this.referralCode = '',
      this.isExistTripcIdDefault = false});
  final List<MembershipModel> luckyWeatherMemberships;
  final List<MembershipModel> quadrupleMemberships;
  final List<MembershipModel> richNobleMemberships;
  final List<MembershipModel> greatPeaceMemberships;
  final List<MembershipModel> currentMemberships;
  final String inputNumber;
  final MembershipModel? selectedNumber;
  final AccountPayment accountPayment;
  final PaymentMethod? currentMethod;
  final int page;
  final int pageSize;
  final bool isLoading;
  final String? errorMessage;
  final bool canLoadmore;
  final bool isLoadingLoadMore;
  final bool isSelectedPaymentMethod;
  final bool isPaymentSuccess;
  final bool isPaymentError;
  final String referralCode;
  final bool isExistTripcIdDefault;

  static MembershipScreenModel getDefault() {
    return MembershipScreenModel(
      accountPayment:
          AccountPayment(name: 'NGUYEN NAM HAI', qrData: '*********'),
    );
  }

  MembershipScreenModel copyWith(
      {MembershipModel? selectedNumber,
      String? inputNumber,
      AccountPayment? accountPayment,
      PaymentMethod? currentMethod,
      List<MembershipModel>? currentMemberships,
      int? page,
      int? pageSize,
      bool? isLoading,
      String? errorMessage,
      bool? canLoadmore,
      bool? isLoadingLoadMore,
      List<MembershipModel>? luckyWeatherMemberships,
      List<MembershipModel>? quadrupleMemberships,
      List<MembershipModel>? richNobleMemberships,
      List<MembershipModel>? greatPeaceMemberships,
      bool? isSelectedPaymentMethod,
      bool? isPaymentSuccess,
      bool? isPaymentError,
      String? referralCode,
      bool? isExistTripcIdDefault}) {
    return MembershipScreenModel(
        selectedNumber: selectedNumber ?? this.selectedNumber,
        inputNumber: inputNumber ?? this.inputNumber,
        accountPayment: accountPayment ?? this.accountPayment,
        currentMemberships: currentMemberships ?? this.currentMemberships,
        page: page ?? this.page,
        canLoadmore: canLoadmore ?? this.canLoadmore,
        isLoading: isLoading ?? this.isLoading,
        pageSize: pageSize ?? this.pageSize,
        currentMethod: currentMethod ?? this.currentMethod,
        isLoadingLoadMore: isLoadingLoadMore ?? this.isLoadingLoadMore,
        luckyWeatherMemberships:
            luckyWeatherMemberships ?? this.luckyWeatherMemberships,
        quadrupleMemberships: quadrupleMemberships ?? this.quadrupleMemberships,
        richNobleMemberships: richNobleMemberships ?? this.richNobleMemberships,
        errorMessage: errorMessage ?? this.errorMessage,
        greatPeaceMemberships:
            greatPeaceMemberships ?? this.greatPeaceMemberships,
        isSelectedPaymentMethod: isSelectedPaymentMethod ?? this.isSelectedPaymentMethod,
        isPaymentSuccess: isPaymentSuccess ?? this.isPaymentSuccess,
        isPaymentError: isPaymentError ?? this.isPaymentError,
        referralCode: referralCode ?? this.referralCode,
        isExistTripcIdDefault: isExistTripcIdDefault ?? this.isExistTripcIdDefault);
  }
}

class MemberShipScreenProvider extends StateNotifier<MembershipScreenModel> {
  MemberShipScreenProvider(super._state);
  final ApiMembership _api = ApiMembership();
  final ApiOrder _apiOrder = ApiOrder();
  final ApiAuth _apiAuth = ApiAuth();

  void refreshData({required TripcGoodNumberType type}) {
    state = state.copyWith(
        currentMemberships: [],
        canLoadmore: true,
        isLoadingLoadMore: false,
        isLoading: false);
    setPageGetData(page: 1, pageSize: 20);
    switch (type) {
      case TripcGoodNumberType.prosper:
        getLuckyWeathNumbers();
        break;
      case TripcGoodNumberType.tuquy:
        getQuadrupleNumbers();
        break;
      case TripcGoodNumberType.wealthy:
        getRichNobleNumbers();
        break;
      case TripcGoodNumberType.greatpeace:
        getGreatPeace();
        break;
    }
  }

  void setIsExistTripcIdDefault(bool value) {
    state = state.copyWith(isExistTripcIdDefault: value);
  }

  void setReferralCode(String value) {
    state = state.copyWith(referralCode: value);
  }

  void setPageGetData({required int page, required int pageSize}) {
    state = state.copyWith(page: page, pageSize: pageSize);
  }

  void searchNumber(String number) {
    state = state.copyWith(inputNumber: number);
  }

  void selectNumber(MembershipModel? number) {
    if (number == null) return;
    state = state.copyWith(selectedNumber: number);
  }

  void selectPaymentMethod(PaymentMethod method) {
    state = state.copyWith(currentMethod: method);
  }

  void setIsSelectedPaymentMethod() {
    state = state.copyWith(isSelectedPaymentMethod: !state.isSelectedPaymentMethod);
  }

  void forceLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void forceLoadingLoadMore(bool value) {
    state = state.copyWith(isLoadingLoadMore: value);
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void loadMore({required TripcGoodNumberType type}) {
    if (!state.canLoadmore) return;
    forceLoadingLoadMore(true);
    setPageGetData(page: state.page + 1, pageSize: 20);
    switch (type) {
      case TripcGoodNumberType.prosper:
        getLuckyWeathNumbers();
        break;
      case TripcGoodNumberType.tuquy:
        getQuadrupleNumbers();
        break;
      case TripcGoodNumberType.wealthy:
        getRichNobleNumbers();
        break;
      case TripcGoodNumberType.greatpeace:
        getGreatPeace();
        break;
    }
  }

  void appendListMembership(MembershipListNumbersResponse result) {
    if (state.currentMemberships.isEmpty) {
      state = state.copyWith(currentMemberships: result.data);
      state = state.copyWith(
          canLoadmore: state.currentMemberships.length < (result.total ?? 0));
      return;
    }
    state = state.copyWith(
        currentMemberships: [...state.currentMemberships, ...result.data]);
    state = state.copyWith(
        canLoadmore: state.currentMemberships.length < (result.total ?? 0));
    forceLoadingLoadMore(false);
  }

  Future<void> getLuckyWeathNumbers({bool getDetail = true}) async {
    final request = ListDataRequest(page: state.page, pageSize: state.pageSize);
    if (getDetail) {
      forceLoading(true);
    }
    try {
      final result = await _api.getLuckyWeathNumbers(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (getDetail) {
        appendListMembership(result);
        forceLoading(false);
      } else {
        state = state.copyWith(luckyWeatherMemberships: result.data);
      }
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      if (getDetail) {
        forceLoading(false);
        forceLoadingLoadMore(false);
      }
    }
  }

  Future<void> getQuadrupleNumbers({bool getDetail = true}) async {
    final request = ListDataRequest(page: state.page, pageSize: state.pageSize);
    if (getDetail) {
      forceLoading(true);
    }
    try {
      final result = await _api.getQuadrupleNumbers(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (getDetail) {
        appendListMembership(result);
        forceLoading(false);
      } else {
        state = state.copyWith(quadrupleMemberships: result.data);
      }
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      if (getDetail) {
        forceLoading(false);
        forceLoadingLoadMore(false);
      }
    }
  }

  Future<void> getRichNobleNumbers({bool getDetail = true}) async {
    final request = ListDataRequest(page: state.page, pageSize: state.pageSize);
    if (getDetail) {
      forceLoading(true);
    }
    try {
      final result = await _api.getRichNobleNumbers(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (getDetail) {
        appendListMembership(result);
        forceLoading(false);
      } else {
        state = state.copyWith(richNobleMemberships: result.data);
      }
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      if (getDetail) {
        forceLoadingLoadMore(false);
        forceLoading(false);
      }
    }
  }

  Future<void> getGreatPeace({bool getDetail = true}) async {
    final request = ListDataRequest(page: state.page, pageSize: state.pageSize);
    if (getDetail) {
      forceLoading(true);
    }
    try {
      final result = await _api.getGreatPeace(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (getDetail) {
        appendListMembership(result);
        forceLoading(false);
      } else {
        state = state.copyWith(greatPeaceMemberships: result.data);
      }
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      if (getDetail) {
        forceLoadingLoadMore(false);
        forceLoading(false);
      }
    }
  }

  Future<bool> createPasscode({required String passcode}) async {
    final request = UpdatePasscodeRequest(
        membershipId: state.selectedNumber?.id ?? 0, passcode: passcode);
    forceLoading(true);
    try {
      final result = await _api.updatePasscode(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      forceLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  Future<bool> inviteCode({required String referralCode}) async {
    forceLoading(true);
    try {
      final result = await _apiAuth.inviteCode(inviteCode: referralCode).timeout(
        const Duration(
          seconds: 30,
        ),
      );
      forceLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  Future<void> paymentFailure(Map<String, dynamic> request) async {
    setErrorMessage('');
    forceLoading(true);
    try {
      await _apiOrder.paymentFailure(request: request).timeout(
        const Duration(
          seconds: 30,
        ),
      );
      forceLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);

    }
  }

  void resetSeeMorePage() {
    state = state.copyWith(
        currentMemberships: [],
        page: 1,
        pageSize: 20,
        canLoadmore: true,
        isLoadingLoadMore: false,
        isLoading: false);
  }

  Future<void> createOrderTourWithVNPay({int? tripcId}) async {
    forceLoading(true);
    try {
      final result = await _apiOrder.createOrderTripCWithVNPay(tripcId: tripcId ?? 0).timeout(
        const Duration(
          seconds: 30,
        ),
      );
      forceLoading(false);
      onPayment(result.data ?? '');
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
    }
  }

  Future<void> onPayment(String url) async {
    state = state.copyWith(isPaymentSuccess: false, isPaymentError: false);
    await VNPAYFlutter.instance.show(
      paymentUrl: url,
      onPaymentSuccess: (params) {
        state = state.copyWith(isPaymentSuccess: true);
      },
      onPaymentError: (params) {
        state = state.copyWith(isPaymentError: true);
        if (!state.isLoading) {
          paymentFailure(params);
        }
      },
    );
  }

  void resetState() {
    state = MembershipScreenModel.getDefault();
  }
}

final pMembershipScreenProvider =
    StateNotifierProvider<MemberShipScreenProvider, MembershipScreenModel>(
        (ref) => MemberShipScreenProvider(MembershipScreenModel.getDefault()));
