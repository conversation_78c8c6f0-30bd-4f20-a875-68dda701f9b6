import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/good_number.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_model.dart';
import 'package:tripc_app/pages/membership/components/membership_good_number_option.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/number_option_loading/number_option_shimmer_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/commons/view_all_row/view_all_row.dart';

class MembershipListGoodNumberOption extends StatelessWidget {
  const MembershipListGoodNumberOption(
      {super.key,
      required this.type,
      required this.numberOptions,
      this.isLoading = false,
      this.onSelectNumber});
  final TripcGoodNumberType type;
  final List<MembershipModel> numberOptions;
  final Function(MembershipModel)? onSelectNumber;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ViewAllRow(
          title: type.title(context),
          buttonText: context.strings.text_see_more,
          titleTextCase: TextCaseType.title,
          padding: EdgeInsets.zero,
          onTapViewAll: () => AppRoute.pushNamed(context,
              routeName: AppRoute.routeGoodNumberSeemore, arguments: type),
        ),
        TripcText(type.meaning(context),
            fontSize: 14,
            height: 1.2,
            fontWeight: FontWeight.w300,
            textColor: AppAssets.origin().black,
            textAlign: TextAlign.start,
            padding: EdgeInsets.symmetric(vertical: 12.H)),
        Visibility(
          visible: !isLoading,
          replacement: const ListNumberOptionShimmerLoading(),
          child: ListView.separated(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              itemCount: numberOptions.length,
              separatorBuilder: (context, _) => SizedBox(
                    height: 12.H,
                  ),
              itemBuilder: (context, index) => GoodNumberOption(
                  onPressed: () => onSelectNumber?.call(numberOptions[index]),
                  number: numberOptions[index])),
        )
      ],
    );
  }
}
