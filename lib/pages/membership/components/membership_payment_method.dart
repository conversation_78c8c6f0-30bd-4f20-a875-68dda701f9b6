import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/payment_method_enum.dart';
import 'package:tripc_app/pages/tour-payment/components/payment_method_selection.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class MbsPaymentMethod extends StatelessWidget {
  const MbsPaymentMethod({
    super.key,
    this.onTapCheck,
    this.selectedPaymentMethod,
  });

  final bool? selectedPaymentMethod;
  final VoidCallback? onTapCheck;

  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      TripcText(
        context.strings.text_payment_method,
        fontSize: 14,
        textAlign: TextAlign.start,
        fontWeight: FontWeight.w400,
        textColor: AppAssets.origin().blackColor,
        padding: EdgeInsets.only(top: 40.H, bottom: 16.H),
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: PaymentMethod.values.map((method) {
          if (method != PaymentMethod.vnpay) {
            return const SizedBox.shrink();
          }
          return PaymentMethodSelection(
              onTapCheck: () => onTapCheck?.call(),
              method: method,
              value: selectedPaymentMethod,
              padding: EdgeInsets.symmetric(vertical: 9.H));
        }).toList(),
      ),
    ]);
  }
}
