import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_model.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class GoodNumberDetailItem extends StatelessWidget {
  const GoodNumberDetailItem({
    super.key,
    required this.goodNumber,
  });
  final MembershipModel? goodNumber;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(color: AppAssets.origin().disableButtonColor),
          boxShadow: [
            AppAssets.origin()
                .itemShadow
                .copyWith(color: AppAssets.origin().black12),
          ]),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(12.H),
            decoration: BoxDecoration(
                color: AppAssets.origin().whiteBackgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.SP),
                  topRight: Radius.circular(12.SP),
                )),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                AppAssets.origin().icBill.widget(width: 40.H, height: 40.H),
                SizedBox(width: 14.W),
                Expanded(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        TripcText(
                          context.strings.text_tripc_id_nice_number,
                          fontSize: 16,
                          fontWeight: FontWeight.w300,
                          textCase: TextCaseType.none,
                          textAlign: TextAlign.center,
                          textColor: AppAssets.origin().secondDarkGreyTextColor,
                          padding: EdgeInsets.only(bottom: 8.H),
                        ),
                        TripcText(
                          goodNumber?.number.toString() ?? '',
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          textCase: TextCaseType.none,
                          textAlign: TextAlign.center,
                          textColor: AppAssets.origin().blackTextColor,
                        ),
                      ]),
                ),
                Container(
                  height: 20.H,
                  padding: EdgeInsets.symmetric(horizontal: 10.W),
                  color: AppAssets.origin().secondLightYellow,
                  child: Center(
                    child: TripcText(
                      goodNumber?.tcent?.plusTcent ?? '',
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      textCase: TextCaseType.none,
                      textAlign: TextAlign.center,
                      textColor: AppAssets.origin().darkYellow,
                    ),
                  ),
                )
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(12.H),
            decoration: BoxDecoration(
              color: AppAssets.origin().whiteBackgroundColor,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(12.SP),
                bottomRight: Radius.circular(12.SP),
              ),
            ),
            child: Column(children: [
              _singleRowOfBill(context.strings.text_tripc_id_free,
                  goodNumber?.priceWithoutTax.separator ?? '',
                  padding: EdgeInsets.only(bottom: 9.H)),
              _singleRowOfBill(
                  context.strings.text_vat, goodNumber?.vat.separator ?? '',
                  padding: EdgeInsets.only(bottom: 16.H)),
              Divider(
                  thickness: 1.H,
                  height: 0,
                  color: AppAssets.origin().disableButtonColor),
              _singleRowOfBill(context.strings.text_total_payment,
                  goodNumber?.price?.separator ?? '',
                  contentFontSize: 16.SP,
                  titleFontSize: 12.SP,
                  padding: EdgeInsets.only(top: 15.H, bottom: 18.H)),
              Divider(
                  thickness: 1.H,
                  height: 0,
                  color: AppAssets.origin().disableButtonColor),
              _singleRowOfBill(context.strings.text_in_words, '',
                  hasUnit: false,
                  titleFontSize: 12.SP,
                  contentFontSize: 12.SP,
                  content: (goodNumber?.price ?? 0).convertNumberToVietnamese,
                  padding: EdgeInsets.only(top: 12.H, bottom: 8.H)),
            ]),
          )
        ],
      ),
    );
  }

  Widget _singleRowOfBill(String title, String price,
      {bool hasUnit = true,
      String content = '',
      EdgeInsetsGeometry? padding,
      double? titleFontSize,
      double? contentFontSize}) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TripcText(
            title,
            fontSize: titleFontSize ?? 14.SP,
            fontWeight: FontWeight.w400,
            textCase: TextCaseType.none,
            textAlign: TextAlign.center,
            textColor: AppAssets.origin().secondDarkGreyTextColor,
          ),
          Visibility(
            visible: hasUnit,
            replacement: SizedBox(
              width: 174.W,
              child: TripcText(
                content,
                fontSize: contentFontSize ?? 14.SP,
                fontWeight: FontWeight.w600,
                textCase: TextCaseType.none,
                textAlign: TextAlign.end,
                textColor: AppAssets.origin().blackColor,
              ),
            ),
            child: Expanded(
              child: RichText(
                  textAlign: TextAlign.end,
                  text: TextSpan(children: [
                    TextSpan(
                      text: price,
                      style: TextStyle(
                        fontSize: contentFontSize ?? 14.SP,
                        fontWeight: FontWeight.w600,
                        color: AppAssets.origin().blackColor,
                      ),
                    ),
                    TextSpan(
                      text: ' VNĐ',
                      style: TextStyle(
                        fontSize: 14.SP,
                        fontWeight: FontWeight.w600,
                        color: AppAssets.origin().blackColor,
                      ),
                    ),
                  ])),
            ),
          )
        ],
      ),
    );
  }
}
