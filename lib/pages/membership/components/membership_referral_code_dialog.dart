import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class MemberShipReferralCodeDialog extends StatelessWidget {
  const MemberShipReferralCodeDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return TripcDialog(
        onTap: () => AppRoute.pushNamed(context,
            routeName: AppRoute.routeMembershipSignIn, arguments: false),
        type: TripcDialogType.success,
        title: context.strings.text_congratulations_on_receiving_the_offer,
        contentPadding: EdgeInsets.symmetric(vertical: 24.H, horizontal: 24.W),
        titleButton: context.strings.text_agree,
        child: Padding(
            padding: EdgeInsets.only(top: 18.H, bottom: 20.H),
            child: Tripc<PERSON>ex<PERSON>(
              context.strings.text_valid_referral_code,
              fontSize: 14,
              fontWeight: FontWeight.w300,
              height: 1.4,
            )));
  }
}
