import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_model.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class GoodNumberOption extends StatelessWidget {
  const GoodNumberOption({super.key, required this.number, this.onPressed});
  final VoidCallback? onPressed;
  final MembershipModel number;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(boxShadow: [AppAssets.origin().itemShadow]),
      child: TripcIconButton(
          onPressed: onPressed,
          child: Stack(
            children: [
              Container(
                  padding:
                      EdgeInsets.symmetric(vertical: 8.H, horizontal: 12.W),
                  decoration: BoxDecoration(
                    color: AppAssets.origin().whiteBackgroundColor,
                    borderRadius: BorderRadius.circular(12.SP),
                  ),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TripcText(
                          number.number.toString(),
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          textColor: AppAssets.origin().black,
                          padding: EdgeInsets.only(bottom: 10.H),
                        ),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TripcText(
                                number.price?.vndong,
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                                textColor: AppAssets.origin().darkBlueColor,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  AppAssets.origin()
                                      .icTcent
                                      .widget(height: 16.H, width: 16.H),
                                  TripcText(
                                    number.receiveTcentText(context),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    textCase: TextCaseType.none,
                                    textAlign: TextAlign.end,
                                    textColor: AppAssets.origin().darkYellow,
                                    padding: EdgeInsets.only(left: 8.W),
                                  ),
                                ],
                              )
                            ])
                      ])),
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  height: 24.H,
                  padding: EdgeInsets.symmetric(horizontal: 9.W),
                  decoration: BoxDecoration(
                      color: AppAssets.origin().redColor,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(12.SP),
                          topRight: Radius.circular(12.SP))),
                  child: Center(
                    child: TripcText(context.strings.text_new_deal,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        textColor: AppAssets.init.whiteBackgroundColor),
                  ),
                ),
              )
            ],
          )),
    );
  }
}
