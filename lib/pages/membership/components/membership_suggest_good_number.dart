import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/suggest_number.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class MembershipSuggestGoodNumber extends StatelessWidget {
  const MembershipSuggestGoodNumber(
      {super.key, required this.suggestGoodNumber});
  final SuggestGoodNumber suggestGoodNumber;

  String suggestExanple(BuildContext context) {
    return '${context.strings.text_example} ${suggestGoodNumber.numbers.map((e) => e.toString()).join(', ')}';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 60.H,
        width: 254.W,
        padding: EdgeInsets.symmetric(horizontal: 0.W, vertical: 5.H)
            .copyWith(right: 27.W),
        decoration: BoxDecoration(
          color: AppAssets.origin().light<PERSON><PERSON>w,
          borderRadius: BorderRadius.circular(12.SP),
        ),
        child: Row(
          children: [
            AppAssets.origin().icCoin.widget(width: 51.H),
            SizedBox(width: 9.W),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  TripcText(
                    suggestGoodNumber.type.title(context),
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    textCase: TextCaseType.none,
                    textAlign: TextAlign.start,
                    textColor: AppAssets.origin().black,
                  ),
                  TripcText(
                    suggestExanple(context),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    textCase: TextCaseType.none,
                    textAlign: TextAlign.start,
                    textColor: AppAssets.origin().darkGreyTextColor,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    padding: EdgeInsets.only(top: 2.H),
                  ),
                ],
              ),
            )
          ],
        ));
  }
}
