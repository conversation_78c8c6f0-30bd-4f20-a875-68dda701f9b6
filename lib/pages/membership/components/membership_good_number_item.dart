import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/good_number.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_highlight_keyword/tripc_highlight_keyword.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class GoodNumberItem extends StatelessWidget {
  const GoodNumberItem({
    super.key,
    required this.goodNumber,
    required this.keyword,
    this.onTap,
  });
  final TripcGoodNumber goodNumber;
  final VoidCallback? onTap;
  final String keyword;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
          height: 60.H,
          padding: EdgeInsets.only(left: 20.W, right: 12.W),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.SP),
              color: AppAssets.origin().whiteBackgroundColor,
              boxShadow: [AppAssets.origin().itemShadow]),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcHighlightKeyword(
                  description: goodNumber.number.toString(), keyword: keyword),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  TripcText(
                    goodNumber.price.vnd,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    fontStyle: FontStyle.italic,
                    textCase: TextCaseType.none,
                    textAlign: TextAlign.center,
                    textColor: AppAssets.origin().black,
                  ),
                  Container(
                    height: 15.H,
                    padding: EdgeInsets.symmetric(horizontal: 4.5.W),
                    decoration: BoxDecoration(
                      color: AppAssets.origin().secondLightYellow,
                      borderRadius: BorderRadius.circular(2.SP),
                    ),
                    child: Center(
                      child: TripcText(
                        goodNumber.receiveTcentText(context),
                        fontSize: 8,
                        fontWeight: FontWeight.w600,
                        textCase: TextCaseType.none,
                        textAlign: TextAlign.center,
                        textColor: AppAssets.origin().darkYellow,
                      ),
                    ),
                  )
                ],
              )
            ],
          )),
    );
  }
}
