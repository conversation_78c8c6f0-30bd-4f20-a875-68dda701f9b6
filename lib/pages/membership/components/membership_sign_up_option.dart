import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class MembershipSignUpOption extends StatelessWidget {
  const MembershipSignUpOption(
      {super.key,
      required this.title,
      required this.content,
      required this.gradient,
      this.onTapRegister,
      required this.icon});
  final String title;
  final String content;
  final Widget icon;
  final Gradient gradient;
  final VoidCallback? onTapRegister;

  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
      onPressed: onTapRegister,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            decoration: BoxDecoration(
                color: AppAssets.origin().whiteBackgroundColor,
                borderRadius: BorderRadius.circular(12.SP),
                boxShadow: [AppAssets.origin().membershipSignUpOptionShadow],
                gradient: gradient),
            height: 175.H,
            width: 345.W,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 19.H, bottom: 13.H, left: 28.W),
                  child: icon,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.W),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TripcText(
                        title,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        textCase: TextCaseType.none,
                        textColor: AppAssets.origin().black,
                      ),
                      TripcText(
                        content,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        textCase: TextCaseType.none,
                        textAlign: TextAlign.start,
                        textColor: AppAssets.origin().black,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                        topRight: Radius.circular(12.SP),
                        bottomLeft: Radius.circular(12.SP)),
                    color: AppAssets.origin().lightPink),
                height: 34.H,
                width: 101.W,
                child: Center(
                    child: TripcText(
                  context.strings.text_register_now,
                  fontSize: 12,
                  fontWeight: FontWeight.w700,
                ))),
          )
        ],
      ),
    );
  }
}
