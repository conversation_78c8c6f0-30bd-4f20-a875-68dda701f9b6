import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import '../../../models/remote/api_tour_response/tour_response.dart';
import '../../../models/remote/common_error.dart';
import '../../../models/remote/search_response/keyword_response.dart';
import '../../../services/apis/tours/api_tours.dart';
import '../../../services/cache/cache_search.dart';

class SearchScreenModel {
  SearchScreenModel(
      {this.keyWord = '',
      this.currentIndexPage = 0,
      this.recentKeywords = const [],
      this.popularKeywords = const [],
      this.topSearchTours = const [],
      this.popularSearchTours = const [],
      this.suggestTypingSearch = const [],
      this.resultSearchTours = const []});
  final String keyWord;
  final int currentIndexPage;

  final List<KeywordResponse> recentKeywords;
  final List<KeywordResponse> popularKeywords;

  final List<TourResponse> topSearchTours;
  final List<TourResponse> popularSearchTours;

  final List<KeywordResponse> suggestTypingSearch;

  final List<TourResponse> resultSearchTours;

  static SearchScreenModel getDefault() {
    return SearchScreenModel();
  }

  SearchScreenModel copyWith({
    String? keyWord,
    int? currentIndexPage,
    List<KeywordResponse>? recentKeywords,
    List<KeywordResponse>? popularKeywords,
    List<TourResponse>? topSearchTours,
    List<TourResponse>? popularSearchTours,
    List<KeywordResponse>? suggestTypingSearch,
    List<TourResponse>? resultSearchTours,
  }) {
    return SearchScreenModel(
        keyWord: keyWord ?? this.keyWord,
        currentIndexPage: currentIndexPage ?? this.currentIndexPage,
        recentKeywords: recentKeywords ?? this.recentKeywords,
        popularKeywords: popularKeywords ?? this.popularKeywords,
        topSearchTours: topSearchTours ?? this.topSearchTours,
        popularSearchTours: popularSearchTours ?? this.popularSearchTours,
        suggestTypingSearch: suggestTypingSearch ?? this.suggestTypingSearch,
        resultSearchTours: resultSearchTours ?? this.resultSearchTours);
  }

  List<TourResponse> get tourTickets {
    return resultSearchTours
        .where((element) =>
            element.serviceType == TripCServiceCategory.tourSightSeeing)
        .toList();
  }

  List<TourResponse> get combos {
    return resultSearchTours
        .where((element) => element.serviceType == TripCServiceCategory.combo)
        .toList();
  }

  // List<TourResponse> get hotels {
  //   return resultSearchTours
  //       .where((element) => element.serviceType == TripcServiceCategory.hotel)
  //       .toList();
  // }
}

class SearchScreenProvider extends StateNotifier<SearchScreenModel> {
  final ApiTours _api = ApiTours();
  SearchScreenProvider(super._state);

  void resetState() {
    state =
        state.copyWith(keyWord: '', currentIndexPage: 0, resultSearchTours: []);
  }

  void setKeyword(String txt) {
    state = state.copyWith(keyWord: txt);
  }

  void getRecentKeyword() {
    final recentSearches = globalSearchHistoryCache.searchHistory;
    List<KeywordResponse> recentKeywords = [];
    for (int i = 0; i < recentSearches.length; i++) {
    recentKeywords.add(
      KeywordResponse(
        id: i, 
        keyword: recentSearches[i]
      )
    );
  }
  
  state = state.copyWith(recentKeywords: recentKeywords);

  }
  Future<void> deleteRecentKeyword() async {
    await globalSearchHistoryCache.clearSearchHistory();
     state = state.copyWith(recentKeywords: []);
  }

  Future<void> getPopularKeyword() async {
    try {
      final response = await _api.getPopularKeyword().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(popularKeywords: response.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        // setErrorMessage(exceptionMessage.error);
      }
    }
  }

  Future<void> getTopSearchTours() async {
    try {
      final response = await _api.getTopSearchTours().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(topSearchTours: response.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        // setErrorMessage(exceptionMessage.error);
      }
    }
  }

  Future<void> getPopularSearchTours() async {
    try {
      final response = await _api.getPopularSearchTours().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(popularSearchTours: response.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        // setErrorMessage(exceptionMessage.error);
      }
    }
  }

  Future<void> getSuggestSearch() async {
    try {
      final response = await _api.getSearchSuggest(txt: state.keyWord).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(suggestTypingSearch: response.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        // setErrorMessage(exceptionMessage.error);
      }
    }
  }

  Future<void> search() async {
    if (state.keyWord.isEmpty) {
      state = state.copyWith(resultSearchTours: []);
      return;
    }
    try {
      final response = await _api.searchTours(txt: state.keyWord).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(resultSearchTours: response.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        // setErrorMessage(exceptionMessage.error);
      }
    }
  }

  void changeIndexPage({required int index}) {
    state = state.copyWith(currentIndexPage: index);
  }

}

final pSearchProvider =
    StateNotifierProvider<SearchScreenProvider, SearchScreenModel>(
        (ref) => SearchScreenProvider(SearchScreenModel.getDefault()));
