import 'package:flutter/material.dart';
import '../components/components.dart';

class TripcActionSearchView extends StatelessWidget {
  const TripcActionSearchView({super.key, required this.keyWord});
  final String keyWord;

  @override
  Widget build(BuildContext context) {
    return Visibility(
        visible: keyWord.isNotEmpty,
        replacement: const SuggestSearchView(),
        child: const TypingSearchView());
  }
}
