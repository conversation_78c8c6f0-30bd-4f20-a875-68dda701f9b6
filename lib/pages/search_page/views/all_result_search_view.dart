import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/view_all_row/view_all_row.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';
import '../components/components.dart';
import '../providers/providers.dart';

class AllResultSearchView extends ConsumerWidget {
  const AllResultSearchView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final services =
        ref.watch(pSearchProvider.select((value) => value.resultSearchTours));

    final tourTicket =
        ref.watch(pSearchProvider.select((value) => value.tourTickets));

    final combos = ref.watch(pSearchProvider.select((value) => value.combos));
    // final hotels = ref.watch(pSearchProvider.select((value) => value.hotels));

    return Visibility(
      visible: services.isNotEmpty,
      replacement: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AppAssets.origin().imEmptyView.widget(),
          TripcText(context.strings.text_search_not_found)
        ],
      ),
      child: ListView(
        padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
        children: [
          _serviceList(context,
              title: context.strings.text_all, services: services),
          SizedBox(
            height: 20.H,
          ),
          _serviceList(context,
              title: context.strings.text_tour_ticket, services: tourTicket),
          SizedBox(
            height: 20.H,
          ),
          _serviceList(context,
              title: context.strings.text_promotion_combo, services: combos),
          SizedBox(
            height: 20.H,
          ),
          // _serviceList(context,
          //     title: context.strings.text_hotel, services: hotels),
          SizedBox(height: context.mediaQuery.padding.bottom),
        ],
      ),
    );
  }

  Widget _serviceList(BuildContext context,
      {required String title,
      required List<TourResponse> services,
      VoidCallback? onTapViewAll}) {
    return Visibility(
      visible: services.isNotEmpty,
      child: Column(children: [
        ViewAllRow(
          title: title,
          onTapViewAll: onTapViewAll,
          padding: EdgeInsets.zero,
          seeAllColor: AppAssets.origin().darkBlueColor,
          // Link view all action
          buttonText: '',
        ),
        CategorySearchView(
            services: services, padding: EdgeInsets.symmetric(vertical: 15.H))
      ]),
    );
  }
}
