import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/search_page/providers/tripc_search_page_provider.dart';
import 'package:tripc_app/pages/search_page/views/all_result_search_view.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_search_scafford.dart';
import '../../../services/cache/cache_search.dart';
import 'tripc_action_search_view.dart';

class TripcSearchPage extends ConsumerStatefulWidget {
  const TripcSearchPage({super.key});

  @override
  ConsumerState<TripcSearchPage> createState() => _TripcSearchTicketViewState();
}

class _TripcSearchTicketViewState extends ConsumerState<TripcSearchPage> {
  final TextEditingController _controller = TextEditingController();
  final PageController _pageController = PageController(initialPage: 0);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ref.listenManual(
          pSearchProvider.select((state) => state.currentIndexPage),
          (_, state) {
        _pageController.jumpToPage(state);
      });
      ref.read(pSearchProvider.notifier).getRecentKeyword();
      await ref.read(pSearchProvider.notifier).getPopularKeyword();
      await ref.read(pSearchProvider.notifier).getTopSearchTours();
      await ref.read(pSearchProvider.notifier).getPopularSearchTours();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final keyWord = ref.watch(pSearchProvider.select((value) => value.keyWord));
    final currentIndex =
        ref.watch(pSearchProvider.select((value) => value.currentIndexPage));

    return TripcSearchScafford(
        onChanged: (value) async {
          if (currentIndex != 0) {
            ref.read(pSearchProvider.notifier).changeIndexPage(index: 0);
          }
          ref.read(pSearchProvider.notifier).setKeyword(value);
          await ref.read(pSearchProvider.notifier).getSuggestSearch();
        },
        onPopScope: () => ref.read(pSearchProvider.notifier).resetState(),
        onLeadingPressed: () async {
          if (currentIndex == 1) {
            ref.read(pSearchProvider.notifier).changeIndexPage(index: 0);
            await ref.read(pSearchProvider.notifier).getSuggestSearch();
            return;
          }
          ref.read(pSearchProvider.notifier).resetState();
          Navigator.pop(context);
        },
        onTapSearch: () async {
          unfocusKeyboard();
          await ref.read(pSearchProvider.notifier).search();
          if (currentIndex != 1) {
            ref.read(pSearchProvider.notifier).changeIndexPage(index: 1);
          }
          await globalSearchHistoryCache.addSearchKeyword(keyWord);
        },
        controller: _controller,
        hintText: context.strings.text_danang_2d_1n,
        keyword: keyWord,
        body: PageView(
            physics: const NeverScrollableScrollPhysics(),
            controller: _pageController,
            children: [
              TripcActionSearchView(
                keyWord: keyWord,
              ),
              const AllResultSearchView()
            ]));
  }
}
