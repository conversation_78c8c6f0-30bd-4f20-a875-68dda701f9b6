import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';

class SuggestSearchRow extends StatelessWidget {
  const SuggestSearchRow(
      {super.key, required this.suggestText, required this.onTap});
  final String suggestText;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: suggestText.isNotEmpty,
      child: Column(
        children: [
          GestureDetector(
            onTap: onTap,
            behavior: HitTestBehavior.opaque,
            child: SizedBox(
              height: 32.H,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: TripcText(suggestText,
                        fontSize: 14,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        fontWeight: FontWeight.w400,
                        textAlign: TextAlign.left,
                        ignorePointer: true),
                  ),
                  AppAssets.origin().icBlackRight.widget(
                      height: 8.H,
                      width: 4.H,
                      color: AppAssets.origin().secondDarkGreyTextColor),
                ],
              ),
            ),
          ),
          Divider(
            height: 0,
            thickness: 0.5.H,
            color: AppAssets.origin().secondDarkGreyTextColor,
          )
        ],
      ),
    );
  }
}
