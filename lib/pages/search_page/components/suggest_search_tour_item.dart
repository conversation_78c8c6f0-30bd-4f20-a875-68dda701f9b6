import 'package:flutter/material.dart';
import 'package:tripc_app/pages/search_page/components/tag_custom_clipper.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

import '../../../models/remote/api_tour_response/tour_response.dart';
import '../../../widgets/commons/base_cached_network_image.dart';
import '../../../widgets/presentationals/app_shimmer_loading/image_shimmer_loading.dart';

class SuggestSearchTourItem extends StatelessWidget {
  const SuggestSearchTourItem({
    super.key,
    required this.service,
    required this.index,
    required this.onTap,
  });
  final TourResponse service;
  final int index;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.H),
        child: SizedBox(
          height: 60.H,
          width: double.infinity,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.W),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                    width: 70.W,
                    height: 60.H,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.SP),
                    ),
                    child: Stack(
                      children: [
                        Positioned.fill(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8.SP),
                            child: BaseCachedNetworkImage(
                              imageUrl: service.thumbnail ?? '',
                              width: 70.W,
                              height: 60.H,
                              placeholder: (context, _) => ImageShimmerLoading(
                                height: 60.H,
                                fit: BoxFit.scaleDown,
                                color: context.appCustomPallet.buttonBG,
                              ),
                              errorWidget: (context, error, stackTrace) =>
                                  AppAssets.origin().icErrorImg.widget(
                                        height: 60.H,
                                        width: 70.W,
                                        color: context.appCustomPallet.buttonBG,
                                      ),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Positioned(
                            top: 0,
                            left: 0,
                            child: SizedBox(
                              width: 25.W,
                              height: 22.H,
                              child: ClipPath(
                                clipper: TagCustomClipper(),
                                child: Container(
                                  color: AppAssets.origin().darkBlue5FF,
                                  child: TripcText(
                                    '$index',
                                    textColor:
                                        AppAssets.origin().whiteBackgroundColor,
                                  ),
                                ),
                              ),
                            ))
                      ],
                    )),
                SizedBox(
                  width: 12.W,
                ),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TripcText(service.name,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          textAlign: TextAlign.start,
                          ignorePointer: true),
                      Row(
                        children: [
                          Expanded(
                            child: TripcText(service.address,
                                fontSize: 14,
                                textAlign: TextAlign.start,
                                overflow: TextOverflow.ellipsis,
                                fontWeight: FontWeight.w400,
                                textColor:
                                    AppAssets.origin().secondDarkGreyTextColor,
                                padding: EdgeInsets.only(right: 8.W),
                                ignorePointer: true),
                          ),
                          TripcText(
                              '${context.strings.text_from} ${service.totalPrice?.vnd}',
                              fontSize: 14,
                              textAlign: TextAlign.end,
                              ignorePointer: true),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
