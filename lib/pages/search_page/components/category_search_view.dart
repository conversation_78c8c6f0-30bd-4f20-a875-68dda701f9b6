import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';
import '../../../models/remote/api_tour_response/tour_response.dart';
import 'components.dart';

class CategorySearchView extends StatelessWidget {
  const CategorySearchView(
      {super.key, required this.services, this.padding, this.canScroll = true});
  final List<TourResponse> services;
  final EdgeInsetsGeometry? padding;
  final bool canScroll;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding:
          padding ?? EdgeInsets.symmetric(vertical: 15.H, horizontal: 24.W),
      physics: canScroll
          ? const BouncingScrollPhysics()
          : const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: services.length,
      separatorBuilder: (context, _) => SizedBox(
        height: 20.H,
      ),
      itemBuilder: (context, index) {
        return SearchServiceItem(service: services[index]);
      },
    );
  }
}
