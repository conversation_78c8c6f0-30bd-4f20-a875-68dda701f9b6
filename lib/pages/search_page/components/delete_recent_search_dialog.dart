import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class DeleteRecentSearchDialog extends StatelessWidget {
  const DeleteRecentSearchDialog({super.key, required this.onTap});
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Dialog(
        insetPadding: EdgeInsets.symmetric(horizontal: 50.W),
        elevation: 0,
        backgroundColor: AppAssets.init.whiteBackgroundColor,
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.SP)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 31.H, horizontal: 14.W),
              width: double.infinity,
              decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(
                          width: 1.H,
                          color: AppAssets.origin().disableButtonColor))),
              child: TripcText(
                context.strings.text_delete_recent_search,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                textColor: AppAssets.origin().black,
              ),
            ),
            Row(
              children: [
                Expanded(
                  child: TripcIconButton(
                    onPressed: () => Navigator.pop(context),
                    child: Container(
                      decoration: BoxDecoration(
                          border: Border(
                              right: BorderSide(
                                  width: 1.H,
                                  color:
                                      AppAssets.origin().disableButtonColor))),
                      child: TripcText(context.strings.text_later,
                          fontSize: 17,
                          fontWeight: FontWeight.w500,
                          ignorePointer: true,
                          textColor: AppAssets.origin().blueAFF,
                          padding: EdgeInsets.symmetric(vertical: 12.H)),
                    ),
                  ),
                ),
                Expanded(
                  child: TripcIconButton(
                    onPressed: () {
                      onTap();
                      Navigator.pop(context);
                    },
                    child: SizedBox(
                      width: double.infinity,
                      child: TripcText(
                        context.strings.text_yes,
                        fontSize: 17,
                        fontWeight: FontWeight.w500,
                        ignorePointer: true,
                        textColor: AppAssets.origin().blueAFF,
                        padding: EdgeInsets.symmetric(vertical: 12.H),
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ));
  }
}
