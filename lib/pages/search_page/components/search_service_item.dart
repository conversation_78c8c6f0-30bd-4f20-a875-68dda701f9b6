import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

import '../../../models/remote/api_tour_response/tour_response.dart';
import '../../../services/app/app_route.dart';
import '../../../widgets/commons/base_cached_network_image.dart';
import '../../../widgets/presentationals/app_shimmer_loading/image_shimmer_loading.dart';

class SearchServiceItem extends StatelessWidget {
  const SearchServiceItem({super.key, required this.service});
  final TourResponse service;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        AppRoute.pushNamed(
          context,
          routeName: AppRoute.routeTourDetailView,
          arguments: service.id,
        );
      },
      child: Container(
          height: 110.H,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.SP),
              color: AppAssets.origin().whiteBackgroundColor,
              boxShadow: [AppAssets.origin().itemShadow]),
          child: Row(
            children: [
              ClipRRect(
                  borderRadius: BorderRadius.circular(8.SP),
                  child: BaseCachedNetworkImage(
                    imageUrl: service.thumbnail ?? '',
                    height: 104.H,
                    width: 120.W,
                    placeholder: (context, _) => ImageShimmerLoading(
                      height: 104.H,
                      fit: BoxFit.scaleDown,
                      color: context.appCustomPallet.buttonBG,
                    ),
                    errorWidget: (context, error, stackTrace) =>
                        AppAssets.origin().icErrorImg.widget(
                              height: 104.H,
                              width: 120.W,
                              color: context.appCustomPallet.buttonBG,
                            ),
                    fit: BoxFit.cover,
                  )),
              Expanded(
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.W, vertical: 12.H)
                          .copyWith(top: 8.H),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TripcText(
                        service.name,
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        textAlign: TextAlign.start,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textColor: AppAssets.origin().blackColor,
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: 4.H),
                        child: Row(
                          children: [
                            AppAssets.origin()
                                .icStar
                                .widget(height: 12.H, width: 12.H),
                            SizedBox(width: 6.W),
                            TripcRichText(
                              text: '',
                              children: [
                                TextSpan(
                                  text: service.rating != 0
                                      ? '${service.rating}/5 '
                                      : '',
                                  style: AppAssets.origin()
                                      .boldTextStyle
                                      .copyWith(
                                          fontSize: 12,
                                          color: AppAssets.origin().yellow444),
                                ),
                                TextSpan(
                                  text: (service.reviews ?? 0) == 0
                                      ? context.strings.text_no_review
                                      : '(${service.reviews} ${context.strings.text_rating})',
                                  style: AppAssets.origin()
                                      .normalTextStyle
                                      .copyWith(
                                          fontSize: 12,
                                          color: AppAssets.origin()
                                              .secondDarkGreyTextColor),
                                ),
                              ],
                            )
                          ],
                        ),
                      ),
                      Visibility(
                        visible: service.address?.isNotEmpty ?? false,
                        child: Row(
                          children: [
                            AppAssets.origin()
                                .icLocation
                                .widget(height: 12.H, width: 12.H),
                            SizedBox(width: 4.W),
                            Expanded(
                              child: TripcText(
                                service.address,
                                fontSize: 10,
                                maxLines: 1,
                                fontWeight: FontWeight.w500,
                                textAlign: TextAlign.start,
                                textColor: AppAssets.origin().darkGreyTextColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      Row(
                        children: [
                          const Spacer(),
                          TripcText(
                            service.salePrice.vndong,
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                            textAlign: TextAlign.end,
                            textColor: AppAssets.origin().blackColor,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              )
            ],
          )),
    );
  }
}
