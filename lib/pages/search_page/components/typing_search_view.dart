import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/search_page/providers/tripc_search_page_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import '../../../services/cache/cache_search.dart';
import 'suggest_search_row.dart';

class TypingSearchView extends ConsumerStatefulWidget {
  const TypingSearchView({super.key});

  @override
  ConsumerState<TypingSearchView> createState() => _TypingSearchViewState();
}

class _TypingSearchViewState extends ConsumerState<TypingSearchView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final services =
        ref.watch(pSearchProvider.select((value) => value.suggestTypingSearch));

    return ListView.separated(
      itemCount: services.length,
      shrinkWrap: true,
      padding: EdgeInsets.symmetric(vertical: 20.H, horizontal: 24.W),
      separatorBuilder: (context, _) => SizedBox(
        height: 20.H,
      ),
      itemBuilder: (context, index) {
        return SuggestSearchRow(
          onTap: () async {
            unfocusKeyboard();
            ref
                .read(pSearchProvider.notifier)
                .setKeyword(services[index].keyword ?? '');
            ref.read(pSearchProvider.notifier).search();
            ref.read(pSearchProvider.notifier).changeIndexPage(index: 1);
            await globalSearchHistoryCache
                .addSearchKeyword(services[index].keyword ?? '');
          },
          suggestText: services[index].keyword ?? '',
        );
      },
    );
  }
}
