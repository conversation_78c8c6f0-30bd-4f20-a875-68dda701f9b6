import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/search_page/providers/tripc_search_page_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../../../models/remote/api_tour_response/tour_response.dart';
import '../../../services/app/app_route.dart';
import 'components.dart';

class TopSearchTourList extends ConsumerStatefulWidget {
  const TopSearchTourList({super.key});

  @override
  ConsumerState<TopSearchTourList> createState() => _TopSearchTourListState();
}

class _TopSearchTourListState extends ConsumerState<TopSearchTourList> {
  int currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listenManual(pSearchProvider, (_, state) {
        _updateTabIndex();
      });
    });
  }

  void _updateTabIndex() {
    final topSearchTours =
        ref.watch(pSearchProvider.select((value) => value.topSearchTours));
    final popularSearchTours =
        ref.watch(pSearchProvider.select((value) => value.popularSearchTours));

    int newIndex;
    if (topSearchTours.isEmpty && popularSearchTours.isEmpty) {
      newIndex = -1;
    } else if (topSearchTours.isEmpty && popularSearchTours.isNotEmpty) {
      newIndex = 1;
    } else if (topSearchTours.isNotEmpty && popularSearchTours.isEmpty) {
      newIndex = 0;
    } else {
      newIndex = 0;
    }

    if (newIndex != currentTabIndex) {
      setState(() {
        currentTabIndex = newIndex;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final topSearchTours =
        ref.watch(pSearchProvider.select((value) => value.topSearchTours));
    final popularSearchTours =
        ref.watch(pSearchProvider.select((value) => value.popularSearchTours));

    if (topSearchTours.isEmpty && popularSearchTours.isEmpty) {
      return const SizedBox.shrink();
    }

    int tabCount = 0;
    if (topSearchTours.isNotEmpty) tabCount++;
    if (popularSearchTours.isNotEmpty) tabCount++;

    List<Widget> tabWidgets = [];
    List<Widget> stackChildren = [];

    if (topSearchTours.isNotEmpty) {
      tabWidgets.add(
        TripcText(
          onTap: () => setState(() {
            currentTabIndex = 0;
          }),
          fontSize: 16,
          fontWeight: FontWeight.w500,
          padding: EdgeInsets.only(right: 24.W),
          context.strings.text_top_search,
          textColor:
              currentTabIndex == 0 ? AppAssets.origin().secondaryColor : null,
        ),
      );

      stackChildren.add(
        Offstage(
          offstage: currentTabIndex != 0,
          child: _listTours(topSearchTours),
        ),
      );
    }

    if (popularSearchTours.isNotEmpty) {
      tabWidgets.add(
        TripcText(
          onTap: () => setState(() {
            currentTabIndex = 1;
          }),
          fontSize: 16,
          fontWeight: FontWeight.w500,
          context.strings.text_popular_tour,
          textColor: (topSearchTours.isNotEmpty && currentTabIndex == 1)
              ? AppAssets.origin().secondaryColor
              : null,
        ),
      );

      stackChildren.add(
        Offstage(
          offstage: topSearchTours.isEmpty ? false : currentTabIndex != 1,
          child: _listTours(popularSearchTours),
        ),
      );
    }

    return DefaultTabController(
      length: tabCount,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: tabWidgets,
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 12.H),
            child: Stack(
              children: stackChildren,
            ),
          ),
        ],
      ),
    );
  }

  Widget _listTours(List<TourResponse> tours) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [AppAssets.origin().itemShadow],
        borderRadius: BorderRadius.circular(8.SP),
        color: AppAssets.origin().whiteBackgroundColor,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 48.H,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(8.SP),
                topLeft: Radius.circular(8.SP),
              ),
              gradient: AppAssets.origin().keywordsBoxHeader,
            ),
          ),
          Transform.translate(
            offset: Offset(0, -30.H),
            child: ListView.separated(
              itemCount: tours.length <= 10 ? tours.length : 10,
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              separatorBuilder: (context, _) => Divider(
                thickness: 0.5,
                height: 1.H,
              ),
              itemBuilder: (context, index) {
                return SuggestSearchTourItem(
                  onTap: () => AppRoute.pushNamed(
                    context,
                    routeName: AppRoute.routeTourDetailView,
                    arguments: tours[index].id,
                  ),
                  service: tours[index],
                  index: index + 1,
                );
              },
            ),
          ),
          Transform.translate(
            offset: Offset(0, -40.H),
            child: Container(
              height: 35.H,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(8.SP),
                    bottomRight: Radius.circular(8.SP),
                  ),
                  gradient: AppAssets.origin().keywordsBox),
            ),
          ),
        ],
      ),
    );
  }
}
