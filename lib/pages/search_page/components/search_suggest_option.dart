import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class SearchSuggestOption extends StatelessWidget {
  const SearchSuggestOption(
      {super.key, required this.value, this.onTap, this.child});
  final String value;
  final VoidCallback? onTap;
  final Widget? child;
  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
      onPressed: () {
        unfocusKeyboard();
        onTap?.call();
      },
      child: Container(
        height: 34.H,
        padding: EdgeInsets.symmetric(horizontal: 20.W, vertical: 10.H),
        decoration: BoxDecoration(
            color: AppAssets.origin().lightDarkE1,
            borderRadius: BorderRadius.circular(8.SP)),
        child: child ??
            TripcText(
              value,
              ignorePointer: true,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
      ),
    );
  }
}
