import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/search_page/components/delete_recent_search_dialog.dart';
import 'package:tripc_app/pages/search_page/providers/tripc_search_page_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../../../models/remote/search_response/keyword_response.dart';
import '../../../services/providers/providers.dart';
import 'components.dart';

class SuggestSearchView extends ConsumerStatefulWidget {
  const SuggestSearchView({super.key});

  @override
  ConsumerState<SuggestSearchView> createState() => _SuggestSearchViewState();
}

class _SuggestSearchViewState extends ConsumerState<SuggestSearchView> {
  @override
  void initState() {
    super.initState();
  }

  Future<void> onTapSuggestItem(String value) async {
    ref.read(pSearchProvider.notifier).setKeyword(value);
    await ref.read(pSearchProvider.notifier).search();
    ref.read(pSearchProvider.notifier).changeIndexPage(index: 1);
  }

  @override
  Widget build(BuildContext context) {
    final recents =
        ref.watch(pSearchProvider.select((value) => value.recentKeywords));
    final places =
        ref.watch(pSearchProvider.select((value) => value.popularKeywords));

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 24.W, vertical: 20.H)
          .copyWith(bottom: context.mediaQuery.padding.bottom),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _searchRecent(context, recents: recents),
          _suggestPlaces(context, places: places),
          const TopSearchTourList()
        ],
      ),
    );
  }

  Widget _searchRecent(BuildContext context,
      {required List<KeywordResponse> recents}) {
    return Visibility(
      visible: recents.isNotEmpty,
      child: Padding(
        padding: EdgeInsets.only(bottom: 18.H),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TripcText(
                  context.strings.text_recent_searches,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  textColor: AppAssets.origin().blackColor,
                  padding: EdgeInsets.only(bottom: 12.H),
                ),
                TripcIconButton(
                  onPressed: () => dialogHelpers.show(context,
                      child: DeleteRecentSearchDialog(
                        onTap: () async => await ref
                            .read(pSearchProvider.notifier)
                            .deleteRecentKeyword(),
                      )),
                  child: AppAssets.origin().icBin.widget(width: 16.H),
                )
              ],
            ),
            Wrap(
                spacing: 12.H,
                runSpacing: 12.H,
                children: List.generate(recents.length, (index) {
                  return SearchSuggestOption(
                    onTap: () => onTapSuggestItem(recents[index].keyword ?? ''),
                    value: recents[index].keyword ?? '',
                  );
                }))
          ],
        ),
      ),
    );
  }

  Widget _suggestPlaces(BuildContext context,
      {required List<KeywordResponse> places}) {
    return Visibility(
      visible: places.isNotEmpty,
      child: Padding(
        padding: EdgeInsets.only(bottom: 18.H),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TripcText(
              context.strings.text_people_are_searching,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              textColor: AppAssets.origin().blackColor,
              padding: EdgeInsets.only(bottom: 12.H),
            ),
            Wrap(
                spacing: 12.H,
                runSpacing: 12.H,
                children: List.generate(
                    places.length,
                    (index) => SearchSuggestOption(
                        onTap: () =>
                            onTapSuggestItem(places[index].keyword ?? ''),
                        value: places[index].keyword ?? '')))
          ],
        ),
      ),
    );
  }
}
