import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class IncludeBottomSheetV2 extends StatelessWidget {
  const IncludeBottomSheetV2({super.key, this.content, this.title = ''});

  final String? content;
  final String title;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.H, horizontal: 12.W),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: double.infinity,
            child: TripcText(
              title,
              fontSize: 16,
              fontWeight: FontWeight.w600,
              textAlign: TextAlign.center,
              textColor: AppAssets.origin().blue1E40,
            ),
          ),
          SizedBox(height: 18.H),
          TripcText(
            title,
            fontSize: 14,
            fontWeight: FontWeight.w700,
            height: 2,
          ),
          Html(
            data: content,
            style: {
              "p": Style(
                fontSize: FontSize(14),
                fontWeight: FontWeight.w300,
                lineHeight: const LineHeight(1.5),
              ),
              "ul": Style(
                margin: Margins(top: Margin(6.H), bottom: Margin(6.H)),
              ),
              "li": Style(
                fontSize: FontSize(14),
                fontWeight: FontWeight.w300,
                lineHeight: const LineHeight(1.5),
              ),
              "span": Style(
                color: Colors.black,
              ),
            },
          ),
          SizedBox(
            height: context.spacingBottom,
          )
        ],
      ),
    );
  }
}
