import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:html2md/html2md.dart' as html2md;
import 'package:markdown/markdown.dart' as md;
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class OrderDetailBottomSheetV2 extends StatelessWidget {
  const OrderDetailBottomSheetV2({super.key, this.content, this.title = ''});

  final String? content;
  final String title;

  // List parseTextToList(String text) => text
  //     .split('\n')
  //     .map((line) => line.trim())
  //     .where((line) => line.isNotEmpty)
  //     .toList();

  @override
  Widget build(BuildContext context) {
    final convertedContent = html2md.convert(content ?? '');
    // final listContent = parseTextToList(convertedContent);
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.H, horizontal: 12.W),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: double.infinity,
              child: TripcText(
                title,
                fontSize: 16,
                fontWeight: FontWeight.w600,
                textAlign: TextAlign.center,
                textColor: AppAssets.origin().blue1E40,
              ),
            ),
            SizedBox(height: 18.H),
            Markdown(
              padding: EdgeInsets.symmetric(horizontal: 5.W, vertical: 7.H)
                  .copyWith(top: 0),
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              data: convertedContent,
              extensionSet: md.ExtensionSet(
                md.ExtensionSet.gitHubFlavored.blockSyntaxes,
                <md.InlineSyntax>[
                  md.EmojiSyntax(),
                  ...md.ExtensionSet.gitHubFlavored.inlineSyntaxes,
                ],
              ),
            ),
            // Column(
            //   crossAxisAlignment: CrossAxisAlignment.start,
            //   children: listContent.map(
            //     (e) {
            //       return Column(
            //         crossAxisAlignment: CrossAxisAlignment.start,
            //         children: [
            //           Row(
            //             crossAxisAlignment: CrossAxisAlignment.start,
            //             children: [
            //               SizedBox(
            //                 width: 24.W,
            //                 height: 24.H,
            //                 child: const Center(
            //                   child: TripcText('•',
            //                       fontSize: 14,
            //                       fontWeight: FontWeight.w300,
            //                       textAlign: TextAlign.left),
            //                 ),
            //               ),
            //               Expanded(
            //                 child: TripcText(
            //                   e,
            //                   fontSize: 14,
            //                   height: 1.5,
            //                   fontWeight: FontWeight.w300,
            //                   textAlign: TextAlign.left,
            //                 ),
            //               ),
            //             ],
            //           ),
            //         ],
            //       );
            //     },
            //   ).toList(),
            // ),
            SizedBox(
              height: context.spacingBottom,
            )
          ],
        ),
      ),
    );
  }
}
