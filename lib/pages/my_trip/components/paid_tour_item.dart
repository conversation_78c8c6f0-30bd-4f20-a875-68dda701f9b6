import 'dart:async';
import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/payment_status.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
// import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../models/app/order_type_enum.dart';

class TripCPaidTour extends StatefulWidget {
  const TripCPaidTour({super.key, required this.payment, this.onTap});
  final OrderResponse payment;
  final VoidCallback? onTap;

  @override
  State<TripCPaidTour> createState() => _TripCPaidTourState();
}

class _TripCPaidTourState extends State<TripCPaidTour> {
  late int countDownTime;
  Timer? _countDownTimer;

  @override
  void initState() {
    countDownTime = widget.payment.pendingTime;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _countDownTimer?.cancel();
      _countDownTimer = Timer.periodic(const Duration(seconds: 1), (_) {
        setState(() {
          if (countDownTime > 0) {
            countDownTime--;
          } else {
            _countDownTimer?.cancel();
          }
        });
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    _countDownTimer?.cancel();
    super.dispose();
  }

  String statusText(BuildContext context){
     switch(widget.payment.orderType){
      case OrderTypeEnum.reservation:
        return statusBookingText(context);
      case OrderTypeEnum.tour:
        return statusTourText(context);
      default:
        return context.strings.text_processing;
    }
  }

  String statusTourText(BuildContext context) {
    switch (widget.payment.paymentStatus) {
      case PaymentStatus.paid:
        return context.strings
            .text_received_tcent('${widget.payment.tcentBack ?? 0}');
      case PaymentStatus.pending:
        return context.strings.text_processing;
      // return '${context.strings.text_waiting_receive} ${widget.payment.tcentBack ?? 0}';
      case PaymentStatus.failed:
        return context.strings.text_payment_fail;
      case PaymentStatus.payLater:
        return context.strings.text_waiting_payment;
      case PaymentStatus.cancelled:
        return context.strings.text_canceled;
      default:
        return context.strings.text_waiting_payment;
    }
  }

  String statusBookingText(BuildContext context) {
    switch (widget.payment.orderStatus) {
      case OrderStatus.pending:
        return context.strings.text_processing;
      case OrderStatus.cancelled:
        return context.strings.text_canceled;
      case OrderStatus.confirmed:
        return context.strings.confirmed;
      default:
        return context.strings.text_processing;
    }
  }

  Color get statusTextColor {
    switch(widget.payment.orderType){
      case OrderTypeEnum.reservation:
        return statusBookingTextColor;
      case OrderTypeEnum.tour:
        return statusTourTextColor;
      default:
        return AppAssets.origin().black;
    }
  }
  Color get statusBookingTextColor {
    switch (widget.payment.orderStatus) {
      case OrderStatus.pending:
        return AppAssets.origin().darkBlueColor;
      case OrderStatus.cancelled:
        return AppAssets.origin().redFF0;
      case OrderStatus.confirmed:
        return AppAssets.origin().darkGreenColor;
      default:
        return AppAssets.origin().darkBlueColor;
    }
  }
  Color get statusTourTextColor {
    switch (widget.payment.paymentStatus) {
      case PaymentStatus.pending:
        return AppAssets.origin().darkBlueColor;
      case PaymentStatus.paid:
        return AppAssets.origin().darkGreenColor;
      case PaymentStatus.failed:
        return AppAssets.origin().redFF0;
      case PaymentStatus.payLater:
        return AppAssets.origin().darkRedColor;
      default:
        return AppAssets.origin().darkRedColor;
    }
  }

  Color get statusBgColor{
    switch(widget.payment.orderType){
      case OrderTypeEnum.reservation:
        return statusBookingBgColor;
      case OrderTypeEnum.tour:
        return statusTourBgColor;
      default:
        return AppAssets.origin().gray63Color;
    }
  }

  Color get statusTourBgColor {
    switch (widget.payment.paymentStatus) {
      case PaymentStatus.pending:
        return AppAssets.origin().blueCAE;
      case PaymentStatus.paid:
        return AppAssets.origin().lightGreen;
      case PaymentStatus.failed:
        return AppAssets.origin().lightPink;
      case PaymentStatus.payLater:
        return AppAssets.origin().lightPink;
      default:
        return AppAssets.origin().lightPink;
    }
  }

  Color get statusBookingBgColor {
    switch (widget.payment.orderStatus) {
      case OrderStatus.pending:
        return AppAssets.origin().blueCAE;
      case OrderStatus.cancelled:
        return AppAssets.origin().lightPink;
      case OrderStatus.confirmed:
        return AppAssets.origin().lightGreen;
      default:
        return AppAssets.origin().blueCAE;
    }
  }

  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
      onPressed: widget.onTap,
      child: Container(
          padding: EdgeInsets.symmetric(horizontal: 12.W, vertical: 12.H),
          decoration: BoxDecoration(
              color: AppAssets.origin().whiteBackgroundColor,
              borderRadius: BorderRadius.circular(12.SP),
              boxShadow: [
                AppAssets.origin()
                    .itemShadow
                    .copyWith(color: AppAssets.origin().black10)
              ]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      TripcText(
                        context.strings.text_booking_code(''),
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        textColor: AppAssets.origin().blackColor,
                      ),
                      SizedBox(
                        width: 2.W,
                      ),
                      TripcText(
                        widget.payment.orderCode ?? '',
                        fontSize: 14,
                        fontWeight: FontWeight.w300,
                        textColor: AppAssets.origin().blackColor,
                      ),
                    ],
                  ),
                  TripcText(
                    widget.payment.totalPrice?.vnd ?? '',
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    textColor: AppAssets.origin().blackColor,
                  )
                ],
              ),
              TripcText(
                widget.payment.title ?? '',
                fontSize: 14,
                fontWeight: FontWeight.w300,
                textColor: AppAssets.origin().blackColor,
                maxLines: 2,
                textAlign: TextAlign.start,
                padding: EdgeInsets.only(top: 12.H, bottom: 20.H),
              ),
              Row(
                children: [
                  Visibility(
                    visible: !(widget.payment.paymentStatus ==
                            PaymentStatus.paid &&
                        (widget.payment.orderStatus != OrderStatus.confirmed &&
                            widget.payment.orderStatus !=
                                OrderStatus.completed)),
                    replacement: Visibility(
                      visible: widget.payment.orderStatus ==
                          OrderStatus.waitingForRefund,
                      replacement: _buildStatus(
                          AppAssets.origin().lightPink,
                          context.strings.text_canceled,
                          AppAssets.origin().redFF0),
                      child: _buildStatus(
                          AppAssets.origin().yellowCB,
                          context.strings.text_waiting_for_completion,
                          AppAssets.origin().darkYellow),
                    ),
                    child: _buildStatus(
                        statusBgColor,
                        statusText(context),
                        statusTextColor),
                  ),
                  const Spacer(),
                  Visibility(
                    visible: false,
                    child: Visibility(
                      visible: countDownTime != 0,
                      child: TripcText(
                        countDownTime.covertResultCountDown,
                        fontSize: 12,
                        fontWeight: FontWeight.w700,
                        textColor: AppAssets.origin().black,
                        padding: EdgeInsets.only(right: 28.W),
                      ),
                    ),
                  )
                ],
              )
            ],
          )),
    );
  }

  Widget _buildStatus(Color statusBgColor, String title, Color textColor) {
    return Container(
      padding: EdgeInsets.only(top: 6.H, left: 7.W, right: 13.W, bottom: 6.H),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.SP), color: statusBgColor),
      child: TripcText(title,
          fontSize: 12, fontWeight: FontWeight.w400, textColor: textColor),
    );
  }

  // Widget _useNowButton() {
  //   return TripcButton(
  //     title: context.strings.text_use_now,
  //     height: 24,
  //     width: 98.W,
  //     style: AppButtonStyle(
  //         textColor: AppAssets.origin().darkBlueText,
  //         backgroundColor: AppAssets.origin().lightBlueBg,
  //         fontSize: 12,
  //         fontWeight: FontWeight.w700,
  //         radius: 4),
  //   );
  // }
}
