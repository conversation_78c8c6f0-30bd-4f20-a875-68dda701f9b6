import 'package:flutter/material.dart';
import 'dart:math';

import 'package:tripc_app/utils/app_extension.dart';

class TicketPainterWidget extends CustomPainter {
  TicketPainterWidget({required this.backgroundColor, required this.color});
  final Color backgroundColor;
  final Color color;

  
@override
void paint(Canvas canvas, Size size) {
  Paint paint = Paint()
    ..color = color
    ..style = PaintingStyle.fill;

  Rect rect = Rect.fromLTWH(0, 0, size.width, size.height); 
  RRect rrect = RRect.fromRectAndRadius(rect, Radius.circular(16.SP));
  canvas.drawRRect(rrect, paint); 

  paint.color = backgroundColor; 
  canvas.drawArc(
    Rect.fromCircle(center: Offset(0, size.height / 2), radius: 8),
    1.5 * pi, 
    pi, 
    true, 
    paint,
  );

  canvas.drawArc(
    Rect.fromCircle(center: Offset(size.width, size.height / 2), radius: 8), 
    0.5 * pi, 
    pi,
    true,
    paint,
  );
}

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
