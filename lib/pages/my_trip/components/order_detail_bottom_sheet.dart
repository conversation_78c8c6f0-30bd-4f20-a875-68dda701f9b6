import 'package:flutter/material.dart';
import 'package:html2md/html2md.dart' as html2md;
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class OrderDetailBottomSheet extends StatelessWidget {
  const OrderDetailBottomSheet({super.key, this.content, this.title = ''});

  final String? content;
  final String title;

  List parseTextToList(String text) => text
      .split('\n')
      .map((line) => line.trim())
      .where((line) => line.isNotEmpty)
      .toList();

  @override
  Widget build(BuildContext context) {
    final convertedContent = html2md.convert(content ?? '');
    final listContent = parseTextToList(convertedContent);
    return Container(
      height: 458.H,
      padding: EdgeInsets.symmetric(vertical: 16.H, horizontal: 16.W)
          .copyWith(bottom: 0),
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: double.infinity,
                child: TripcText(
                  title,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 18.H),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: listContent.map(
                  (e) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: 24.W,
                              height: 24.H,
                              child: const Center(
                                child: TripcText('•',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w300,
                                    textAlign: TextAlign.left),
                              ),
                            ),
                            Expanded(
                              child: TripcText(
                                e,
                                fontSize: 14,
                                height: 1.5,
                                fontWeight: FontWeight.w300,
                                textAlign: TextAlign.left,
                              ),
                            ),
                          ],
                        ),
                      ],
                    );
                  },
                ).toList(),
              ),
              SizedBox(
                height: context.spacingBottom,
              )
            ],
          ),
          Positioned(
            top: 0,
            right: 0,
            child: Padding(
              padding: EdgeInsets.only(right: 9.W),
              child: TripcIconButton(
                onPressed: () => Navigator.pop(context),
                child: AppAssets.origin().icClose.widget(
                    width: 13.H,
                    height: 13.H,
                    color: AppAssets.origin().blackColor),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
