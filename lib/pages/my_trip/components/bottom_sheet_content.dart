import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../models/app/refund_status.dart';
import '../../../widgets/commons/tripc_triangle_painter/tripc_triangle_painter.dart';
import '../providers/my_trip_provider.dart';

class BottomSheetContent extends ConsumerWidget {
  const BottomSheetContent({
    super.key,
    this.orderDetail,
  });

  final OrderDetail? orderDetail;

  bool hasBeenRefunded(RefundStatus status) {
    return status == RefundStatus.success;
  }

  String statusText(BuildContext context, RefundStatus status) {
    switch (status) {
      case RefundStatus.pending:
        return context.strings.text_waiting_for_refund;
      case RefundStatus.success:
        return context.strings.text_refund_successful;
    }
  }

  Color statusColor(RefundStatus status) {
    switch (status) {
      case RefundStatus.pending:
        return AppAssets.origin().darkYellow;
      case RefundStatus.success:
        return AppAssets.origin().darkGreenColor;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final orderItem = orderDetail?.order?.firstOrNull;
    final aldQty = ref.watch(pPaidTourProvider.select((value) => value.aldQty));
    final childQty = ref.watch(pPaidTourProvider.select((value) => value.childQty));
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              hasBeenRefunded(orderDetail?.refund?.status ?? RefundStatus.success)
                  ? AppAssets.origin().icSuccess.widget(
                        height: 50.H,
                        width: 50.H,
                      )
                  : AppAssets.origin().imClock.widget(
                        height: 50.H,
                        width: 50.H,
                      ),
              SizedBox(
                width: 10.W,
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TripcText(statusText(context, orderDetail?.refund?.status ?? RefundStatus.success),
                        fontWeight: FontWeight.w500,
                        textAlign: TextAlign.left,
                        textColor: statusColor(orderDetail?.refund?.status ?? RefundStatus.success)),
                    SizedBox(height: 4.H),
                    TripcText(
                      orderDetail?.refund?.status?.getContent(context),
                      fontWeight: FontWeight.w300,
                      maxLines: 5,
                      textAlign: TextAlign.left,
                      overflow: TextOverflow.ellipsis,
                      textColor: AppAssets.origin().darkGreyTextColor,
                    )
                  ],
                ),
              )
            ],
          ),
          SizedBox(
            height: 18.H,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                context.strings.text_actual_refund_amount_2,
                textAlign: TextAlign.left,
                fontWeight: FontWeight.w500,
              ),
              TripcText(
                orderDetail?.totalPrice?.vnd ?? '',
                fontWeight: FontWeight.w500,
              ),
            ],
          ),
          _paidPriceContainer(context, orderDetail?.totalPrice ?? 0),
          _tourContainer(context, orderItem?.product?.name ?? '',
              aldQty, childQty),
          SizedBox(height: 8.H),
          _rowInfor(
              '${context.strings.text_refund_reason}:', orderDetail?.refund?.reason),
          _rowInfor(context.strings.text_request_time,
              orderDetail?.refund?.createdAt?.convertHHMMSSddMMYYYY),
          if (orderDetail?.refund?.status == RefundStatus.success)
            _rowInfor(context.strings.text_refund_time,
                orderDetail?.refund?.updatedAt?.convertHHMMSSddMMYYYY),
        ],
      ),
    );
  }

  Widget _paidPriceContainer(BuildContext context, int price) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.H),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              color: AppAssets.origin().grayF2,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TripcText(
                  context.strings.text_amount_paid,
                  textAlign: TextAlign.left,
                  fontSize: 12,
                  fontWeight: FontWeight.w300,
                ),
                TripcText(price.vnd,
                fontWeight: FontWeight.w400,
                textColor: AppAssets.init.blackColor,
                fontSize: 12),
              ],
            ),
          ),
          Positioned(
            top: -9,
            right: 22,
            child: CustomPaint(
              size: Size(16.H, 16.H),
              painter: TrianglePainter(
                color: AppAssets.origin().grayF2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _tourContainer(BuildContext context, String tourName,
      int? aldultQuantity, int? childQuantity) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppAssets.origin().grayF2,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TripcText(
                tourName,
                fontWeight: FontWeight.w500,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 8.H),
              if (aldultQuantity != 0)
                TripcText(
                  '$aldultQuantity ${context.strings.text_adult_above_10}',
                  fontWeight: FontWeight.w300,
                ),
              Visibility(visible: childQuantity != 0,child: SizedBox(height: 4.H)),
              if (childQuantity != 0)
                TripcText(
                  '$childQuantity ${context.strings.text_children_5_to_9}',
                  fontWeight: FontWeight.w300,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _rowInfor(String label, String? value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.H),
      child: Row(
        children: [
          SizedBox(
            width: 150.W,
            child: TripcText(
              label,
              textAlign: TextAlign.left,
              fontWeight: FontWeight.w300,
            ),
          ),
          Expanded(
            child: TripcText(
              value,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.left,
              fontWeight: FontWeight.w300,
            ),
          ),
        ],
      ),
    );
  }
}
