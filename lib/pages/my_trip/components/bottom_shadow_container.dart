import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../widgets/commons/tripc_text/tripc_text.dart';

class BottomShadowContainer extends StatelessWidget {
  const BottomShadowContainer(
      {super.key,
      this.title,
      required this.child,
      this.rightPadding,
      this.disablePadding});
  final String? title;
  final Widget child;
  final double? rightPadding;
  final bool? disablePadding;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: AppAssets.origin().whiteBackgroundColor,
          boxShadow: [AppAssets.origin().infoBoxShadow]),
      child: Padding(
        padding: disablePadding == null
            ? EdgeInsets.fromLTRB(24.W, 8.H, 24.W - (rightPadding ?? 0), 8.H)
            : const EdgeInsets.all(0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TripcText(
              title ?? '',
              fontWeight: FontWeight.w300,
              textColor: AppAssets.origin().darkGreyTextColor,
            ),
            child,
          ],
        ),
      ),
    );
  }
}
