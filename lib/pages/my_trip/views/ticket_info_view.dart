import 'package:collection/collection.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../services/app/app_route.dart';
import '../../../widgets/commons/tripc_button/tripc_button.dart';
import '../providers/my_trip_provider.dart';

class TicketInfoView extends ConsumerStatefulWidget {
  const TicketInfoView({super.key, this.customerTicket});

  final CustomerTicket? customerTicket;

  @override
  ConsumerState<TicketInfoView> createState() => _TicketInfoViewState();
}

class _TicketInfoViewState extends ConsumerState<TicketInfoView> {
  @override
  Widget build(BuildContext context) {
    final orderDetail =
        ref.watch(pPaidTourProvider.select((value) => value.orderDetail));
    return TripcScaffold(
        needUnFocus: true,
        hasBackButton: true,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        titleAppBar: TripcText(
          context.strings.text_ticket_info,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          textColor: Colors.black,
        ),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTextInfo(context,
                        title: context.strings.text_passenger_name,
                        value: widget.customerTicket?.fullname),
                    SizedBox(height: 12.H),
                    Row(
                      children: [
                        Expanded(
                            child: _buildTextInfo(context,
                                title: context.strings.text_phone,
                                value:
                                    widget.customerTicket?.phoneNumber ?? '')),
                        Expanded(
                            child: _buildTextInfo(context,
                                title: context.strings.text_email,
                                value: widget.customerTicket?.email ?? '')),
                      ],
                    ),
                    SizedBox(height: 12.H),
                    _buildTextInfo(context,
                        title: context.strings.text_departure_date,
                        value: widget
                                .customerTicket?.departDate?.toFormattedDate ??
                            ''),
                    SizedBox(height: 12.H),
                    _buildTextInfo(context,
                        title: context.strings.text_booking_code_title,
                        value: widget.customerTicket?.ticketId ?? ''),
                    SizedBox(height: 15.H),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 48.W),
                      child: DottedLine(
                        direction: Axis.horizontal,
                        alignment: WrapAlignment.center,
                        lineThickness: 2.H,
                        dashLength: 6,
                        dashColor: AppAssets.origin().appBarUnderline,
                        dashGapLength: 3,
                        dashGapColor: Colors.transparent,
                      ),
                    ),
                    SizedBox(height: 20.H),
                    _buildQRTicket(context,
                        customerTickets: widget.customerTicket),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.W),
                child: TripcButton(
                  onPressed: () {
                    final orderProduct = orderDetail?.order!
                        .firstWhereOrNull((element) => element.product != null);
                    AppRoute.pushNamed(context,
                        routeName: AppRoute.routeTourDetailView,
                        arguments: orderProduct?.product?.id);
                  },
                  height: 56,
                  title: context.strings.text_reset,
                  style: AppButtonStyle(
                    backgroundColor: AppAssets.origin().lightBlueFD,
                    textColor: Colors.white,
                  ),
                  buttonType: ButtonType.outline,
                  border: Border.all(
                      color: AppAssets.origin().secondaryColor, width: 1.H),
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildTextInfo(BuildContext context, {String? title, String? value}) {
    return Padding(
      padding: EdgeInsets.only(right: 32.W, left: 32.W, top: 13.H),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TripcText(
            title,
            fontSize: 12,
            fontWeight: FontWeight.w300,
            textColor: AppAssets.init.darkGreyTextColor,
          ),
          SizedBox(height: 12.H),
          TripcText(
            value,
            fontSize: 16,
            fontWeight: FontWeight.w700,
            textColor: AppAssets.init.black,
          ),
        ],
      ),
    );
  }

  Widget _buildQRTicket(BuildContext context,
      {CustomerTicket? customerTickets}) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TripcText(
            context.strings.text_your_qr,
            fontSize: 14,
            fontWeight: FontWeight.w300,
            textColor: AppAssets.init.blackColor,
            textAlign: TextAlign.center,
          ),
          BaseCachedNetworkImage(
            imageUrl: customerTickets?.ticketQrCode ?? '',
            height: 99.H,
            width: 99.H,
            placeholder: (context, _) => Container(
              color: AppAssets.origin().lightGrayDD4,
            ),
            errorWidget: (context, error, stackTrace) =>
                AppAssets.origin().icErrorImg.widget(
                      height: 99.H,
                      width: 99.W,
                      color: context.appCustomPallet.buttonBG,
                    ),
            fit: BoxFit.cover,
          ),
          SizedBox(height: 8.H),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TripcRichText(text: '', children: [
                TextSpan(
                  text: context.strings.text_ticket_id_no,
                  style: AppAssets.origin().mediumTextStyle.copyWith(
                      fontSize: 14.SP,
                      fontWeight: FontWeight.w300,
                      color: AppAssets.origin().blackColor),
                ),
                TextSpan(
                  text: customerTickets?.ticketId,
                  style: AppAssets.origin().mediumTextStyle.copyWith(
                      fontSize: 14.SP, color: AppAssets.origin().blackColor),
                ),
              ]),
              SizedBox(width: 10.W),
              GestureDetector(
                onTap: () {
                  Clipboard.setData(
                      ClipboardData(text: customerTickets?.ticketId ?? ''));
                  Fluttertoast.showToast(
                    msg: context.strings.text_copied,
                    toastLength: Toast.LENGTH_SHORT,
                    gravity: ToastGravity.BOTTOM,
                    backgroundColor: Colors.black54,
                    textColor: Colors.white,
                    fontSize: 14.0,
                  );
                },
                child: AppAssets.init.icCopy.widget(width: 16.H),
              )
            ],
          )
        ],
      ),
    );
  }
}
