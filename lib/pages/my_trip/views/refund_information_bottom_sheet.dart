import 'package:flutter/material.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/tripc_button/tripc_icon_button.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';
import '../components/bottom_sheet_content.dart';

class TripcRefundInformationBottomSheet extends StatelessWidget {
  const TripcRefundInformationBottomSheet({
    super.key,
    this.orderDetail,
  });
  final OrderDetail? orderDetail;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 493.H,
      padding: EdgeInsets.symmetric(vertical: 18.H, horizontal: 22.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 6.W),
            child: TripcIconButton(
              onPressed: () => Navigator.pop(context),
              child: Stack(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: TripcText(
                      context.strings.text_refund_information_2,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      textColor: AppAssets.origin().blackColor,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Positioned(
                    top: 0,
                    right: 0,
                    child: SizedBox(
                        height: 24.H,
                        width: 24.H,
                        child: Center(
                            child:
                            AppAssets.origin().icClose.widget(height: 13.H, color: AppAssets.init.blackColor))),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 18.H,
          ),
          Expanded(child: BottomSheetContent(
            orderDetail: orderDetail,
          ))
        ],
      ),
    );
  }
}
