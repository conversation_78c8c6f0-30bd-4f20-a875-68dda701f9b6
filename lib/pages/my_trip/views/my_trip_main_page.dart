import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/booking_detail.dart';
// import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';
import 'package:tripc_app/pages/homepage/homepage_export.dart';
// import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_empty.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_search/tripc_search.dart';
// import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/commons/view_all_row/view_all_row.dart';
import '../../../models/app/order_type_enum.dart';
import '../../../widgets/app_loading.dart';
import '../components/components.dart';
import '../providers/my_trip_provider.dart';

class TripcMyTripPage extends ConsumerStatefulWidget {
  const TripcMyTripPage({super.key});

  @override
  ConsumerState<TripcMyTripPage> createState() => _TripcMyTripPageState();
}

class _TripcMyTripPageState extends ConsumerState<TripcMyTripPage> {
  final TextEditingController _controller = TextEditingController();
  final _scrollController = ScrollController();

  @override
  void initState() {
    _scrollController.addListener(() {
      _loadMore();
    });
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        ref.read(pPaidTourProvider.notifier).refresh();
      },
    );
    super.initState();
  }

  void _loadMore() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 100) {
      ref.read(pPaidTourProvider.notifier).loadMore();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final pendingOrderList =
        ref.watch(pPaidTourProvider.select((value) => value.payLaterOrderList));
    final paidOrderList =
        ref.watch(pPaidTourProvider.select((value) => value.paidOrderList));
    // final recentView = ref.watch(
    //     pHomepageScreenProvider.select((value) => value.dealsAroundHere));
    final loadingLoadMore =
        ref.watch(pPaidTourProvider.select((value) => value.isLoadingLoadMore));
    return Stack(
      children: [
        TripcScaffold(
            onPressed: () => unfocusKeyboard(),
            hasBackButton: false,
            resizeToAvoidBottomInset: false,
            toolbarHeight: 38.H,
            backgroundColor: AppAssets.origin().whiteBackgroundColor,
            actions: [
              // NotificationIcon(
              //   icon: AppAssets.origin().icChatNoti,
              //   backgroundColor: AppAssets.origin().whiteSmokeColor,
              //   hasDot: true,
              //   onTap: () => AppRoute.pushNamed(context,
              //       routeName: AppRoute.routeChatCommingSoon),
              // ),
              // SizedBox(width: 4.W),
              NotificationIcon(
                onTap: () => AppRoute.pushNamed(context,
                    routeName: AppRoute.routeSavedTour),
                icon: AppAssets.origin().icTag,
                backgroundColor: AppAssets.origin().whiteSmokeColor,
                color: AppAssets.origin().black,
                hasDot: true,
              ),
              SizedBox(width: 32.W),
            ],
            body: Padding(
              padding: EdgeInsets.symmetric(vertical: 5.H),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24.W),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12.SP),
                      child: TripcSearch(
                          controller: _controller,
                          onChanged: (txt) {
                            if (txt.isNotEmpty) {
                              ref
                                  .read(pPaidTourProvider.notifier)
                                  .searchMyOrder(txt);
                            } else {
                              ref
                                  .read(pPaidTourProvider.notifier)
                                  .getMyOrders();
                            }
                          },
                          keyword: _controller.text,
                          borderColor: AppAssets.origin().greyTextColorC8,
                          keyboardType: TextInputType.text,
                          contentPadding: EdgeInsets.symmetric(vertical: 12.H),
                          prefixPadding:
                              EdgeInsets.only(left: 12.W, right: 9.W),
                          radius: 12,
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          hintText: context.strings.text_sale_refund_10),
                    ),
                  ),
                  SizedBox(
                    height: 15.H,
                  ),
                  Expanded(
                    child: Visibility(
                      visible: paidOrderList.isNotEmpty ||
                          pendingOrderList.isNotEmpty,
                      replacement: Padding(
                        padding: EdgeInsets.only(top: 29.H),
                        child: AppEmpty(
                            message:
                                context.strings.text_my_tripc_empty_message),
                      ),
                      child: RefreshIndicator(
                        onRefresh: () async {
                          ref.read(pPaidTourProvider.notifier).getMyOrders();
                        },
                        child: ListView(
                          padding: EdgeInsets.zero,
                          children: [
                            Visibility(
                              visible: pendingOrderList.isNotEmpty,
                              child: _listPending(pendingOrderList),
                            ),
                            SizedBox(
                              height: 20.H,
                            ),
                            Visibility(
                              visible: paidOrderList.isNotEmpty,
                              child: _listPaid(paidOrderList),
                            ),
                            // SizedBox(
                            //   height: 23.H,
                            // ),
                            // _recentList(recentView),
                            SizedBox(
                              height: 100.H,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            )),
        AppLoading(isRequesting: loadingLoadMore),
      ],
    );
  }

  Widget _listPending(List<OrderResponse> pendingList) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ViewAllRow(
              onTapViewAll: () {},
              title: context.strings.text_not_yet_paid,
              fontStyle: FontStyle.normal,
              titleFontWeight: FontWeight.w500,
              buttonText: '',
              titleColor: AppAssets.origin().blackColor,
              seeAllColor: AppAssets.origin().blackColor,
              padding: EdgeInsets.only(bottom: 6.H, right: 4.W)),
          ListView.separated(
            itemCount: pendingList.length,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            separatorBuilder: (context, _) => SizedBox(height: 20.H),
            itemBuilder: (context, index) {
              return TripCPaidTour(
                payment: pendingList[index],
                onTap: () => pendingList[index].orderType ==
                        OrderTypeEnum.reservation
                    ? AppRoute.pushNamed(context,
                        routeName: AppRoute.routeBookingDetailOrder,
                        arguments: pendingList[index].id)
                    : AppRoute.pushNamed(context,
                        routeName: AppRoute.routeMyTripDetailedTourV2,
                        arguments: BookingDetail(
                          isElectronicInvoice: false,
                          orderId: pendingList[index].id,
                        )),
              );
            },
          )
        ],
      ),
    );
  }

  Widget _listPaid(List<OrderResponse> paidList) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ViewAllRow(
              onTapViewAll: () {},
              title: context.strings.text_paid,
              fontStyle: FontStyle.normal,
              titleFontWeight: FontWeight.w500,
              buttonText: '',
              titleColor: AppAssets.origin().blackColor,
              seeAllColor: AppAssets.origin().blackColor,
              padding: EdgeInsets.only(bottom: 6.H, right: 4.W)),
          ListView.separated(
            itemCount: paidList.length,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            separatorBuilder: (context, _) => SizedBox(height: 20.H),
            itemBuilder: (context, index) {
              return TripCPaidTour(
                onTap: () => AppRoute.pushNamed(context,
                    routeName: AppRoute.routeMyTripDetailedTourV2,
                    arguments: BookingDetail(
                        isElectronicInvoice: false,
                        orderId: paidList[index].id)),
                payment: paidList[index],
              );
            },
          )
        ],
      ),
    );
  }

  // Widget _recentList(List<TourResponse> recentView) {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Row(
  //         children: [
  //           TripcText(
  //             context.strings.text_recently_viewed,
  //             fontSize: 16,
  //             fontWeight: FontWeight.w500,
  //             textAlign: TextAlign.start,
  //             textColor: AppAssets.origin().black,
  //             padding: EdgeInsets.symmetric(horizontal: 24.W),
  //           ),
  //         ],
  //       ),
  //       Container(
  //         height: 259.H,
  //         margin: EdgeInsets.only(top: 12.H),
  //         child: ListView.separated(
  //           padding: EdgeInsets.symmetric(horizontal: 24.W),
  //           shrinkWrap: true,
  //           clipBehavior: Clip.none,
  //           scrollDirection: Axis.horizontal,
  //           itemCount: recentView.length,
  //           separatorBuilder: (context, _) => SizedBox(width: 18.W),
  //           itemBuilder: (context, index) {
  //             return ServiceItem(
  //                 onTap: () {
  //                   ref
  //                       .read(pTicketTourProvider.notifier)
  //                       .selectTour(recentView[index]);
  //                   AppRoute.pushNamed(context,
  //                       routeName: AppRoute.routeTourDetailView,
  //                       arguments: recentView[index].id);
  //                 },
  //                 service: recentView[index]);
  //           },
  //         ),
  //       )
  //     ],
  //   );
  // }
}
