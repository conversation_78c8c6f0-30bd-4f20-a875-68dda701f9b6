import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/my_trip/providers/my_trip_provider.dart';
import 'package:tripc_app/pages/my_trip/providers/refund_reason_enum.dart';

import '../../../services/app/app_assets.dart';
import '../../../services/app/app_route.dart';
import '../../../services/providers/providers.dart';
import '../../../utils/app_extension.dart';
import '../../../widgets/commons/app_dialog/tripc_error_dialog.dart';
import '../../../widgets/commons/tripc_button/tripc_button.dart';
import '../../../widgets/commons/tripc_scafford/tripc_scafford.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';
import '../components/bottom_shadow_container.dart';
import '../components/refund_success_dialog.dart';
import '../providers/my_trip_refund_provider.dart';

class TripcMyTripConfirmRefundPage extends ConsumerStatefulWidget {
  const TripcMyTripConfirmRefundPage({super.key, required this.total});

  final int total;

  @override
  ConsumerState<TripcMyTripConfirmRefundPage> createState() =>
      _TripcMyTripConfirmRefundPageState();
}

class _TripcMyTripConfirmRefundPageState
    extends ConsumerState<TripcMyTripConfirmRefundPage> {

  Future<void> _requestRefund(int orderId) async {
    final result = await ref
        .read(pRefundProvider.notifier).requestRefund(context);
    _handleResult(result, orderId);
  }
  void _handleResult(bool result, int orderId) {
    if (result) {
      dialogHelpers.show(context, barrierDismissible: false,
          child: RefundSuccessDialog(onTap: () {
            Navigator.pop(context);
            ref.read(pPaidTourProvider.notifier).getOrderDetail(orderId);
            Navigator.popUntil(context, (route) => route.settings.name == AppRoute.routeMyTripDetailedTour);
            ref.read(pRefundProvider.notifier).resetState();
          }));
    } else {
      final error = ref.watch(pRefundProvider.select((value) => value.errorMessage)) ?? '';
      dialogHelpers.show(context, child: TripcErrorDialog(errorText: error));
    }
  }
  @override
  Widget build(BuildContext context) {
    int adults = ref.watch(pRefundProvider).adultRefund;
    int children = ref.watch(pRefundProvider).childRefund;
    final orderDetail = ref.watch(pRefundProvider.select((value) => value.orderDetail));
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        resizeToAvoidBottomInset: false,
        hasBackButton: true,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        titleAppBar: TripcText(
          context.strings.text_refund,
          fontWeight: FontWeight.w600,
          fontSize: 16,
          textColor: AppAssets.origin().black,
        ),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      BottomShadowContainer(
                        disablePadding: true,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _containerColumn(
                              context.strings.text_refund_quantity,
                              fontSize: 14,
                              isFirst: true,
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (adults != 0)
                                  TripcText(
                                    '$adults ${context.strings.text_adult_above_10}',
                                    fontWeight: FontWeight.w300,
                                    fontSize: 12,
                                    textColor: AppAssets.origin().black,
                                  ),
                                  Visibility(
                                    visible: children != 0,
                                    child: SizedBox(height: 4.H),
                                  ),
                                  if (children != 0)
                                  TripcText(
                                      '$children ${context.strings.text_children_5_to_9}',
                                      fontWeight: FontWeight.w300,
                                      fontSize: 12,
                                      textColor: AppAssets.origin().black)
                                ],
                              ),
                            ),
                            Divider(
                                height: 1, color: AppAssets.origin().grayE9),
                            _containerColumn(
                                context.strings.text_refund_information,
                                fontSize: 14,
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(height: 12.H),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        TripcText(
                                          context.strings
                                              .text_total_refunded_amount,
                                          fontWeight: FontWeight.w300,
                                          fontSize: 14,
                                          textColor: AppAssets.origin().black,
                                        ),
                                        TripcText(
                                          widget.total.vnd,
                                          fontSize: 16,
                                          textColor: AppAssets.origin().black,
                                        )
                                      ],
                                    ),
                                    SizedBox(height: 18.H),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        TripcText(
                                            context.strings
                                                .text_actual_refund_amount,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w300,
                                            textColor:
                                                AppAssets.origin().black),
                                        TripcText(
                                            widget.total.vnd,
                                            fontSize: 16,
                                            textColor: AppAssets.origin().black)
                                      ],
                                    ),
                                  ],
                                )),
                            Divider(
                                height: 1, color: AppAssets.origin().grayE9),
                            _containerColumn(
                              context.strings.text_refund_reason,
                              fontSize: 14,
                              TripcText(
                                ref.watch(pRefundProvider
                                    .select((value) => value.reason))?.toLocalizedString(context),
                                fontWeight: FontWeight.w300,
                                fontSize: 14,
                                textColor: AppAssets.origin().black,
                                padding: EdgeInsets.only(top: 12.H),
                              ),
                            )
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.W,
                          vertical: 12.H,
                        ),
                        child: TripcText(
                          context.strings.text_refund_note,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          textAlign: TextAlign.start,
                          textColor: AppAssets.origin().darkGreyTextColor,
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 8.H),
                child: TripcButton(
                  onPressed: () {
                    _requestRefund(orderDetail?.id ?? 0);
                  },
                  title: context.strings.text_refund,
                  height: 60,
                ),
              )
            ],
          ),
        ));
  }

  Widget _containerColumn(String title, Widget child, {bool isFirst = false, double? fontSize}) {
    return Padding(
      padding: isFirst
          ? EdgeInsets.fromLTRB(16.W, 0, 16.W, 18.H)
          : EdgeInsets.symmetric(horizontal: 16.W, vertical: 18.H),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TripcText(
            title,
            fontSize: fontSize,
            fontWeight: FontWeight.w300,
            textColor: AppAssets.origin().darkGreyTextColor,
          ),
          child
        ],
      ),
    );
  }
}
