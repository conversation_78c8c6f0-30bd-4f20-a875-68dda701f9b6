import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';
import 'package:tripc_app/pages/my_trip/providers/refund_reason_enum.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../services/app/app_route.dart';
import '../components/bottom_shadow_container.dart';
import '../providers/my_trip_refund_provider.dart';

class TripcMyTripRefundPage extends ConsumerStatefulWidget {
  const TripcMyTripRefundPage({super.key, required this.payment});

  final OrderDetail payment;

  @override
  ConsumerState<TripcMyTripRefundPage> createState() =>
      _TripcMyTripRefundPageState();
}

class _TripcMyTripRefundPageState extends ConsumerState<TripcMyTripRefundPage> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        ref.read(pRefundProvider.notifier).initQuantity(widget.payment);
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    int adults =
        ref.watch(pRefundProvider.select((value) => value.adultRefund));
    int children =
        ref.watch(pRefundProvider.select((value) => value.childRefund));
    RefundReason? refundReason =
        ref.watch(pRefundProvider.select((value) => value.reason));
    int total = ref.watch(pRefundProvider.select(
      (value) => value.total,
    ));
    bool isDisable = !ref.watch(pRefundProvider.notifier).enableContinueButton;
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        resizeToAvoidBottomInset: false,
        hasBackButton: true,
        onPopScope: () {
          ref.read(pRefundProvider.notifier).resetState();
        },
        onLeadingPressed: () {
          Navigator.pop(context);
          ref.read(pRefundProvider.notifier).resetState();
        },
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        titleAppBar: TripcText(
          context.strings.text_refund,
          fontWeight: FontWeight.w600,
          fontSize: 16,
          textColor: AppAssets.origin().black,
        ),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(vertical: 12.H)
                      .copyWith(bottom: context.mediaQuery.padding.bottom),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      BottomShadowContainer(
                        title: context.strings.text_refund_reason,
                        child: GestureDetector(
                            onTap: () => AppRoute.pushNamed(
                                  context,
                                  routeName: AppRoute.routeMyTripRefundReason,
                                ),
                            child: _dropdown(context, refundReason)),
                      ),
                      SizedBox(height: 16.H),
                      BottomShadowContainer(
                        title: context.strings.text_refund_quantity,
                        rightPadding: 8.W,
                        child: Column(
                          children: [
                            _quantitySelector(
                                context.strings.text_adult_above_10,
                                adults,
                                ref.watch(pRefundProvider
                                    .select((value) => value.maxAdultQty)), () {
                              ref
                                  .read(pRefundProvider.notifier)
                                  .incrementAdultRefund(ref.watch(pRefundProvider
                                  .select((value) => value.maxAdultQty)));
                            }, () {
                              ref
                                  .read(pRefundProvider.notifier)
                                  .decrementAdultRefund();
                            }),
                            SizedBox(height: 14.H),
                            _quantitySelector(
                                context.strings.text_children_5_to_9,
                                children,
                                ref.watch(pRefundProvider
                                    .select((value) => value.maxChildQty)), () {
                              ref
                                  .read(pRefundProvider.notifier)
                                  .incrementChildRefund(ref.watch(pRefundProvider
                                  .select((value) => value.maxChildQty)));
                            }, () {
                              ref
                                  .read(pRefundProvider.notifier)
                                  .decrementChildRefund();
                            }),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 8.H),
                child: _totalPriceArea(context, total, isDisable, refundReason),
              )
            ],
          ),
        ));
  }

  Widget _dropdown(BuildContext context, RefundReason? reason) {
    final otherReason =
        ref.watch(pRefundProvider.select((value) => value.otherReason));
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 4.H),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: TripcText(
                reason != RefundReason.otherReasons
                    ? reason?.toLocalizedString(context) ??
                        context.strings.text_select_refund_reason
                    : '${context.strings.text_other_reasons}: $otherReason',
                textAlign: TextAlign.left,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                fontWeight: FontWeight.w300,
                textColor: AppAssets.origin().black,
              ),
            ),
            Icon(
              Icons.keyboard_arrow_down_outlined,
              color: AppAssets.origin().secondDarkGreyTextColor,
            )
          ],
        ),
      ],
    );
  }

  Widget _quantitySelector(String title, int count, int max,
      VoidCallback increase, VoidCallback decrease) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TripcText(title,
                fontWeight: FontWeight.w300,
                textColor: count != 0
                    ? AppAssets.origin().black
                    : AppAssets.origin().secondDarkGreyTextColor),
            Row(
              children: [
                IconButton(
                  icon: Icon(Icons.remove_circle,
                      color: count > 0
                          ? AppAssets.origin().darkBlueColor
                          : AppAssets.origin().greyTextColorC8),
                  onPressed: max != 0 ? decrease : null,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.W),
                  child: TripcText(
                    "$count",
                    fontWeight: FontWeight.w700,
                    fontSize: 16,
                    textColor: max == 0
                        ? AppAssets.init.greyTextColorC8
                        : AppAssets.origin().black,
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.add_circle,
                      color: count < max
                          ? AppAssets.origin().darkBlueColor
                          : AppAssets.origin().greyTextColorC8),
                  onPressed: max != 0 ? increase : null,
                ),
              ],
            ),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(right: 15.W),
          child: TripcText(
            '${context.strings.text_refund_unit} $max',
            fontSize: 10,
            fontWeight: FontWeight.w300,
            textColor: AppAssets.init.darkGreyTextColor,
          ),
        )
      ],
    );
  }

  Widget _totalPriceArea(BuildContext context, int total, bool isDisable, RefundReason? reason) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TripcText(
              context.strings.text_total_price,
              fontSize: 16,
              fontWeight: FontWeight.w700,
              textColor: AppAssets.origin().blackColor,
            ),
            TripcText(
              total.vnd,
              fontSize: 24,
              fontWeight: FontWeight.w700,
              textColor: AppAssets.origin().blackColor,
            ),
          ],
        ),
        SizedBox(
          height: 8.H,
        ),
        TripcButton(
          onPressed: () {
            AppRoute.pushNamed(context,
                routeName: AppRoute.routeMyTripConfirmRefund, arguments: total);
          },
          isButtonDisabled: total == 0 || reason == null,
          title: context.strings.text_continue,
          height: 60,
        )
      ],
    );
  }
}
