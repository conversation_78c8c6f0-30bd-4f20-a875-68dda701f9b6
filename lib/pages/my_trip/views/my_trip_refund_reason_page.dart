import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/tripc_scafford/tripc_scafford.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';
import '../components/bottom_shadow_container.dart';
import '../providers/my_trip_refund_provider.dart';
import '../providers/refund_reason_enum.dart';

class TripcMyTripRefundReasonPage extends ConsumerStatefulWidget {
  const TripcMyTripRefundReasonPage({super.key});

  @override
  ConsumerState<TripcMyTripRefundReasonPage> createState() =>
      _TripcMyTripRefundReasonPageState();
}

class _TripcMyTripRefundReasonPageState
    extends ConsumerState<TripcMyTripRefundReasonPage> {
  RefundReason? _selectedReason;
  final otherReasonController = TextEditingController();

  bool _outsideControlExpanded = false;
  bool _neededFixOrderExpanded = false;
  bool _badExperienceExpanded = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final otherReason = ref.watch(pRefundProvider.select((value) => value.otherReason));
      final selectReason = ref.watch(pRefundProvider.select((value) => value.reason));
      if (selectReason != null) {
       setState(() {
         _selectedReason = selectReason;
       });
      }
      if (otherReason.isNotEmpty) {
        otherReasonController.text = otherReason;
      }
    },);
  }

  @override
  void dispose() {
    otherReasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final List<RefundReason> outsideControlReasons = [
      RefundReason.badWeatherOrNaturalDisaster,
      RefundReason.transportationDelaysOrAccidents,
      RefundReason.illnessOrBurstingBubble,
      RefundReason.socialOrPublicDisturbances,
      RefundReason.otherExternalConditions,
    ];

    final List<RefundReason> neededFixOrderReasons = [
      RefundReason.needToCorrectInformation,
      RefundReason.needToCorrectPackage
    ];

    final List<RefundReason> badExperienceReasons = [
      RefundReason.dissatisfactionWithExperience,
      RefundReason.injuryOrSevereIncident
    ];

    return TripcScaffold(
      onPressed: () => unfocusKeyboard(),
      resizeToAvoidBottomInset: true,
      hasBackButton: true,
      backgroundColor: AppAssets.origin().whiteBackgroundColor,
      onPopScope: () {
        if (_selectedReason != RefundReason.otherReasons){
          ref.read(pRefundProvider.notifier).setOtherReason('');
        }
      },
      onLeadingPressed: () {
        if (_selectedReason != RefundReason.otherReasons){
          ref.read(pRefundProvider.notifier).setOtherReason('');
        }
        Navigator.pop(context);
      },
      titleAppBar: TripcText(
        context.strings.text_refund_reason,
        fontWeight: FontWeight.w600,
        fontSize: 16,
        textColor: AppAssets.origin().black,
      ),
      actions: [
        TripcText(
          onTap: () {
            if (_selectedReason == RefundReason.otherReasons) {
              ref.read(pRefundProvider.notifier).setErrorMessage('');
            }
            if (_selectedReason != null) {
              ref.read(pRefundProvider.notifier).updateRefundReason(_selectedReason!);
              Navigator.pop(context);
            }
          },
          context.strings.text_confirm,
          fontWeight: FontWeight.w600,
          fontSize: 14,
          textColor: AppAssets.origin().darkBlue5FF,
          padding: EdgeInsets.only(right: 20.W),
        ),
      ],
      body: SingleChildScrollView(
        child: BottomShadowContainer(
          disablePadding: true,
          child: Column(
            children: [
              _singleRadioListTitle(
                RefundReason.issueWithOrderOrVoucher,
              ),
              _listRadioListTitle(
                  context.strings.text_outside_the_scope_of_control,
                  _outsideControlExpanded,
                  (expanded) => _outsideControlExpanded = expanded,
                  outsideControlReasons,
                  1,
                  RefundReason.otherExternalConditions),
              _listRadioListTitle(
                  context.strings.text_need_to_correct_order,
                  _neededFixOrderExpanded,
                  (expanded) => _neededFixOrderExpanded = expanded,
                  neededFixOrderReasons,
                  neededFixOrderReasons.length + 1,
                  RefundReason.usePromoCodeIssue),
              Column(
                children: [
                  Divider(height: 1, color: AppAssets.origin().grayE9),
                  _singleRadioListTitle(RefundReason.usePromoCodeIssue),
                ],
              ),
              _listRadioListTitle(
                  context.strings.text_bad_service_or_product_experience,
                  _badExperienceExpanded,
                  (expanded) => _badExperienceExpanded = expanded,
                  badExperienceReasons,
                  outsideControlReasons.length +
                      neededFixOrderReasons.length +
                      1,
                  RefundReason.injuryOrSevereIncident),
              Column(
                children: [
                  Divider(height: 1, color: AppAssets.origin().grayE9),
                  _singleRadioListTitle(RefundReason.changePlan),
                ],
              ),
              Column(
                children: [
                  Divider(height: 1, color: AppAssets.origin().grayE9),
                  _singleRadioListTitle(RefundReason.otherReasons),
                ],
              ),
              if (_selectedReason == RefundReason.otherReasons ||
                  _outsideControlExpanded &&
                      _neededFixOrderExpanded &&
                      _badExperienceExpanded)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.W),
                  child: _otherReasonTextField(),
                ),
              SizedBox(
                height: 30.H,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _singleRadioListTitle(RefundReason value) {
    return RadioListTile<RefundReason>(
      title: Padding(
        padding: EdgeInsets.only(left: 10.W),
        child: TripcText(
          value.toLocalizedString(context),
          textAlign: TextAlign.left,
          fontWeight: FontWeight.w300,
        ),
      ),
      value: value,
      groupValue: _selectedReason,
      activeColor: AppAssets.origin().secondaryColor,
      controlAffinity: ListTileControlAffinity.trailing,
      onChanged: (value) {
        setState(() {
          _selectedReason = value;
        });
        unfocusKeyboard();
      },
    );
  }

  Widget _listRadioListTitle(
      String title,
      bool controlExpanded,
      Function(bool) onExpansionChanged,
      List<RefundReason> items,
      int previousIndex,
      RefundReason hideDividerIndex) {
    return ExpansionTile(
      title: Padding(
        padding: EdgeInsets.only(left: 10.W),
        child: TripcText(
          title,
          textAlign: TextAlign.left,
          fontWeight: FontWeight.w300,
        ),
      ),
      onExpansionChanged: (expanded) {
        setState(() {
          onExpansionChanged(expanded);
        });
        unfocusKeyboard();
      },
      trailing: Padding(
        padding: EdgeInsets.only(right: 8.W),
        child: Icon(
          controlExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
          color: AppAssets.origin().secondDarkGreyTextColor,
        ),
      ),
      collapsedShape: Border(
        top: BorderSide(color: AppAssets.origin().grayE9, width: 1),
        bottom: BorderSide.none,
      ),
      shape: Border(
        top: BorderSide(color: AppAssets.origin().grayE9, width: 1),
        bottom: BorderSide.none,
      ),
      children: [
        ...items.map((item) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 30.W),
                child: RadioListTile<RefundReason>(
                  title: TripcText(
                    item.toLocalizedString(context),
                    textAlign: TextAlign.left,
                    fontWeight: FontWeight.w300,
                  ),
                  value: item,
                  groupValue: _selectedReason,
                  activeColor: AppAssets.origin().secondaryColor,
                  controlAffinity: ListTileControlAffinity.trailing,
                  onChanged: (value) {
                    setState(() {
                      _selectedReason = value;
                    });
                    unfocusKeyboard();
                  },
                ),
              ),
              if (item != hideDividerIndex)
                Padding(
                  padding: EdgeInsets.only(left: 15.W),
                  child: Divider(height: 1, color: AppAssets.origin().grayE9),
                )
            ],
          );
        }),
      ],
    );
  }

  Widget _otherReasonTextField() {
    return TextField(
      maxLines: 4,
      controller: otherReasonController,
      onChanged: ref.watch(pRefundProvider.notifier).setOtherReason,
      decoration: InputDecoration(
        hintText: context.strings.text_state_your_reason,
        hintStyle: AppAssets.origin()
            .normalTextStyle
            .copyWith(fontSize: 12.sp, color: AppAssets.origin().lightGrayDD4),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: AppAssets.origin().lightGrayDD4,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(5.0),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: AppAssets.origin().lightGrayDD4,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4.0),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: 14.W,
          vertical: 13.H,
        ),
      ),
    );
  }
}
