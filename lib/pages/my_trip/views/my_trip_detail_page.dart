import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/payment_status.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';
import 'package:tripc_app/pages/my_trip/components/include_bottom_sheet.dart';
import 'package:tripc_app/pages/my_trip/components/order_detail_bottom_sheet.dart';
import 'package:tripc_app/pages/my_trip/providers/my_trip_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../services/providers/providers.dart';
import '../../tour-payment/components/Invoice_status_view.dart';
import '../../tour-payment/views/electronic_invoice.dart';
import '../../../widgets/commons/content_shadow_view.dart';
import '../../../widgets/commons/information_row.dart';

class TripcMyTripDetailedPage extends ConsumerStatefulWidget {
  const TripcMyTripDetailedPage(
      {super.key, required this.orderId, this.isElectronicInvoice = false});

  final int? orderId;
  final bool isElectronicInvoice;

  @override
  ConsumerState<TripcMyTripDetailedPage> createState() =>
      _TripcMyTripDetailedPageState();
}

class _TripcMyTripDetailedPageState
    extends ConsumerState<TripcMyTripDetailedPage> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        ref
            .read(pPaidTourProvider.notifier)
            .getOrderDetail(widget.orderId ?? 0);
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final orderDetail =
        ref.watch(pPaidTourProvider.select((value) => value.orderDetail));
    final hasBeenPaid = orderDetail?.paymentStatus == PaymentStatus.paid;
    final qty = ref.watch(pPaidTourProvider.select(
      (value) => value.qty,
    ));
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        resizeToAvoidBottomInset: false,
        canPop: false,
        onLeadingPressed: () {
          Navigator.popUntil(context, ModalRoute.withName(AppRoute.routeHome));
        },
        hasBackButton: true,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        titleAppBar: TripcText(
          context.strings.text_reservation_details,
          fontWeight: FontWeight.w600,
          fontSize: 16,
          textColor: AppAssets.origin().black,
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            ref
                .read(pPaidTourProvider.notifier)
                .getOrderDetail(widget.orderId ?? 0);
          },
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 12.H)
                .copyWith(bottom: context.mediaQuery.padding.bottom),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ContentShadowView(
                  child: _generalInfo(context, orderDetail: orderDetail),
                ),
                SizedBox(
                  height: 8.H,
                ),
                ContentShadowView(
                  child:
                      _serviceInfo(context, orderDetail: orderDetail, qty: qty),
                ),
                SizedBox(
                  height: 8.H,
                ),
                ContentShadowView(
                  child: _passengerInfo(context,
                      hasBeenPaid: hasBeenPaid, orderDetail: orderDetail),
                ),
                SizedBox(height: 6.H),
                _sectionInfo(context, orderDetail),
                _electronicInvoiceInfo(context, orderDetail: orderDetail),
              ],
            ),
          ),
        ));
  }

  Widget _electronicInvoiceInfo(BuildContext context,
      {OrderDetail? orderDetail}) {
    final invoice = orderDetail?.invoice;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //button electronic invoice
        Visibility(
          visible: orderDetail?.isExportInvoice ?? false,
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 36.H),
            child: InvoiceStatusView(
                status: invoice != null
                    ? invoice.status ?? InvoiceStatus.processing
                    : InvoiceStatus.request,
                onTap: invoice == null
                    ? () => AppRoute.pushNamed(
                          context,
                          routeName: AppRoute.routeElectronicInvoice,
                        )
                    : () => AppRoute.pushNamed(context,
                        routeName: AppRoute.routeElectronicInvoice,
                        arguments: [false, invoice])),
          ),
        ),
        //button reset
        Visibility(
          visible: orderDetail?.orderStatus == OrderStatus.completed ||
              orderDetail?.orderStatus == OrderStatus.cancelled,
          child: Padding(
            padding: EdgeInsets.only(bottom: 16.H),
            child: Column(
              children: [
                TripcButton(
                  height: 60.H,
                  title: context.strings.text_reset_2,
                  onPressed: () {
                    final orderProduct = orderDetail?.order!
                        .firstWhereOrNull((element) => element.product != null);
                    AppRoute.pushNamed(context,
                        routeName: AppRoute.routeTourDetailView,
                        arguments: orderProduct?.product?.id);
                  },
                  style: AppButtonStyle(
                      radius: 12.H,
                      backgroundColor: AppAssets.init.lightBlueFD,
                      textColor: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        ),
        //button review
        Visibility(
            //TODO: review tour
            visible: false,
            child: Padding(
              padding: EdgeInsets.only(bottom: 16.H),
              child: TripcButton(
                height: 60.H,
                title: context.strings.text_trip_review,
                onPressed: () {},
                border: Border.all(
                  color: AppAssets.init.lightBlueFD,
                  width: 1.H,
                ),
                style: AppButtonStyle(
                    radius: 12.H,
                    textStyle: TextStyle(
                      color: AppAssets.init.lightBlueFD,
                      fontWeight: FontWeight.w500,
                      fontSize: 16.SP,
                    ),
                    backgroundColor: Colors.white,
                    textColor: Colors.white,
                    fontWeight: FontWeight.w500),
              ),
            )),

        //button contact tripc
        Padding(
          padding: EdgeInsets.only(bottom: 16.H),
          child: TripcButton(
            onPressed: () {
              if (orderDetail?.refund == null) {
                dialogHelpers.show(context,
                    child: TripcDialog(
                      title: context.strings.text_contact_tripc,
                      onTap: () {
                        Navigator.pop(context);
                      },
                      icon: AppAssets.init.imageContactTripC.widget(
                        height: 40.H,
                        width: 40.H,
                      ),
                      titleButton: context.strings.text_close,
                      contentPadding: EdgeInsets.symmetric(horizontal: 24.W)
                          .copyWith(top: 24.H, bottom: 27.H),
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 16.H),
                        child: TripcRichText(
                          text: '',
                          children: [
                            TextSpan(
                                text: context.strings.text_contact_message_1,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w300,
                                  color: Colors.black,
                                )),
                            TextSpan(
                                text: context.strings.text_contact_tripc_email,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w300,
                                  color: AppAssets.init.darkBlue5FF,
                                )),
                            TextSpan(
                                text: context.strings.text_contact_message_2,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w300,
                                  color: Colors.black,
                                ))
                          ],
                        ),
                      ),
                    ));
                return;
              }
              final orderProduct = orderDetail?.order!
                  .firstWhereOrNull((element) => element.product != null);

              AppRoute.pushNamed(context,
                  routeName: AppRoute.routeTourDetailView,
                  arguments: orderProduct?.product?.id);
            },
            height: 56,
            title: orderDetail?.refund == null
                ? context.strings.text_contact_tripc
                : context.strings.text_reset,
          ),
        ),
        //button cancle
        Visibility(
          visible: orderDetail?.isAllowRefund == true ||
              orderDetail?.orderStatus == OrderStatus.pending ||
              orderDetail?.orderStatus == OrderStatus.confirmed &&
                  orderDetail?.paymentStatus == PaymentStatus.payLater,
          child: Padding(
            padding: EdgeInsets.only(bottom: 16.H),
            child: TripcButton(
                onPressed: () => AppRoute.pushNamed(context,
                    routeName: AppRoute.routeMyTripRefund,
                    arguments: orderDetail),
                height: 56,
                title: context.strings.text_cancle,
                style: AppButtonStyle(
                    textColor: Colors.white,
                    backgroundColor: AppAssets.origin().redDotColor),
                buttonType: ButtonType.outline,
                border: null),
          ),
        ),
      ],
    );
  }

  Widget _generalInfo(BuildContext context, {OrderDetail? orderDetail}) {
    final double titleWidth = 110.W;
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 15.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              TripcText(
                context.strings.text_booking_code_title,
                fontWeight: FontWeight.w300,
                fontSize: 14,
                textColor: AppAssets.origin().blackColor,
                padding: EdgeInsets.only(right: 64.W),
              ),
              TripcText(
                orderDetail?.orderCode ?? '',
                fontWeight: FontWeight.w400,
                fontSize: 16,
                textCase: TextCaseType.upper,
                textColor: AppAssets.origin().blackColor,
              ),
            ],
          ),
          SizedBox(height: 14.H),
          TripcText(
            context.strings.text_general_information,
            fontWeight: FontWeight.w400,
            fontSize: 16,
            textColor: AppAssets.origin().blackColor,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 4.H,
              ),
              InformationRow(
                title: context.strings.text_booking_date_time,
                content: orderDetail?.createdAt?.toFormattedDate ?? '',
                contentFontWeight: FontWeight.w300,
                contentFontSize: 14,
                titleWidth: titleWidth,
              ),
              Padding(
                padding: EdgeInsets.symmetric(vertical: 4.H),
                child: InformationRow(
                  title: context.strings.text_price,
                  content: orderDetail?.totalPrice?.vnd ?? '',
                  titleWidth: titleWidth,
                  contentFontWeight: FontWeight.w300,
                  contentFontSize: 14,
                ),
              ),
              InformationRow(
                title: context.strings.text_status,
                content: orderDetail?.paymentStatus?.statusText(context) ?? '',
                titleWidth: titleWidth,
                contentFontWeight: FontWeight.w300,
                contentFontSize: 14,
              ),
              InformationRow(
                title: context.strings.text_transaction_code,
                content: orderDetail?.sku ?? '',
                titleWidth: titleWidth,
                contentFontWeight: FontWeight.w300,
                contentFontSize: 14,
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _serviceInfo(BuildContext context,
      {OrderDetail? orderDetail, int? qty}) {
    final double titleWidth = 110.W;
    final tour = orderDetail?.order
        ?.where((element) => element.product != null)
        .firstOrNull;
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 15.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TripcText(
            context.strings.text_service_information,
            fontWeight: FontWeight.w400,
            fontSize: 16,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(bottom: 15.H),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InformationRow(
                title: context.strings.text_service_name,
                content: tour?.product?.name ?? '',
                titleWidth: titleWidth,
              ),
              InformationRow(
                title: context.strings.text_quantity,
                content: qty.toString().padLeft(2, '0'),
                titleWidth: titleWidth,
              ),
              InformationRow(
                title: context.strings.text_note,
                content: orderDetail?.description ?? '',
                titleWidth: titleWidth,
                isHtmlContent: true,
              ),
              InformationRow(
                title: context.strings.text_departure_date,
                content: orderDetail?.departureTime?.toFormattedDate ?? '',
                titleWidth: titleWidth,
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _passengerInfo(BuildContext context,
      {OrderDetail? orderDetail, required bool hasBeenPaid}) {
    final passengers = orderDetail?.order
            ?.where((element) => element.customerTickets != null)
            .map((e) => e.customerTickets)
            .expand((element) => element!)
            .toList() ??
        [];
    return SizedBox(
      width: double.infinity,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 15.W),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TripcText(
              context.strings.text_passenger_information,
              fontWeight: FontWeight.w400,
              fontSize: 16,
              textColor: AppAssets.origin().blackColor,
              padding: EdgeInsets.only(bottom: 12.H),
            ),
            Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: List.generate(passengers.length, (index) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: 20.H),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: TripcText(
                            '${context.strings.text_passenger} ${(index + 1).qttText}:',
                            fontSize: 14,
                            fontWeight: FontWeight.w300,
                            textAlign: TextAlign.left,
                            textColor: AppAssets.origin().blackColor,
                          ),
                        ),
                        // SizedBox(width: 26.W),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TripcText(
                                passengers[index].fullname,
                                fontSize: 14,
                                fontWeight: FontWeight.w300,
                                textColor: AppAssets.origin().blackColor,
                              ),
                              SizedBox(height: 12.H),
                              TripcText(
                                passengers[index].phoneNumber,
                                fontSize: 14,
                                fontWeight: FontWeight.w300,
                                textColor: AppAssets.origin().blackColor,
                                padding: EdgeInsets.only(bottom: 12.H),
                              ),
                              TripcText(
                                passengers[index].email,
                                fontSize: 14,
                                fontWeight: FontWeight.w300,
                                textColor: AppAssets.origin().blackColor,
                              ),
                              SizedBox(height: 12.H),
                              TripcText(
                                onTap: hasBeenPaid
                                    ? () {
                                        AppRoute.pushNamed(context,
                                            routeName: AppRoute.routeTicketInfo,
                                            arguments: passengers[index]);
                                      }
                                    : null,
                                context.strings.text_download_ticket,
                                fontSize: 12,
                                height: 1.5,
                                fontWeight: FontWeight.w500,
                                textColor: !hasBeenPaid
                                    ? AppAssets.origin().greyTextColorC8
                                    : AppAssets.origin().darkBlueColor,
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  );
                }))
          ],
        ),
      ),
    );
  }

  Widget _sectionItem(BuildContext context,
      {String? title, VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        height: 28.H,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TripcText(
              title,
              fontSize: 14,
              fontWeight: FontWeight.w300,
              textColor: AppAssets.init.blackColor,
            ),
            AppAssets.init.icArrowRightGray.widget(width: 20.H)
          ],
        ),
      ),
    );
  }

  Widget _sectionInfo(BuildContext context, OrderDetail? orderDetail) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.H),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            spreadRadius: 2,
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 15.W),
        child: Column(
          children: [
            _sectionItem(
              context,
              title: context.strings.text_includes,
              onTap: () {
                bottomSheetHelpers.show(context,
                    backgroundColor: Colors.white,
                    borderRadius: 12,
                    child: IncludeBottomSheet(
                      content: orderDetail?.generalTerms,
                      title: context.strings.text_includes,
                    ));
              },
            ),
            SizedBox(height: 12.H),
            _sectionItem(
              context,
              title: context.strings.text_how_to_use,
              onTap: () {
                bottomSheetHelpers.show(context,
                    backgroundColor: Colors.white,
                    borderRadius: 12,
                    child: OrderDetailBottomSheet(
                      content: orderDetail?.usageInstructions,
                      title: context.strings.text_how_to_use,
                    ));
              },
            ),
            SizedBox(height: 12.H),
            _sectionItem(
              context,
              title: context.strings.text_before_placing,
              onTap: () {
                bottomSheetHelpers.show(context,
                    backgroundColor: Colors.white,
                    borderRadius: 12,
                    child: OrderDetailBottomSheet(
                      content: orderDetail?.condition,
                      title: context.strings.text_before_placing,
                    ));
              },
            ),
          ],
        ),
      ),
    );
  }
}
