import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';
import 'package:tripc_app/widgets/commons/content_shadow_view.dart';
import 'package:tripc_app/widgets/commons/information_row.dart';
import 'package:tripc_app/pages/my_trip/components/ticket_painter_widget.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

import '../../../services/app/app_assets.dart';

class TicketInforViewV2 extends ConsumerStatefulWidget {
  const TicketInforViewV2({super.key, this.order});

  final OrderDetail? order;

  @override
  ConsumerState<TicketInforViewV2> createState() => _TicketInfoViewState();
}

class _TicketInfoViewState extends ConsumerState<TicketInforViewV2> {
  @override
  Widget build(BuildContext context) {
    final orderDetail = widget.order;
    final double titleWidth = 110.W;
    final customerTickets = orderDetail?.order
            ?.expand((orderItem) => orderItem.customerTickets ?? [])
            .toList() ??
        [];
    return TripcScaffold(
        needUnFocus: true,
        hasBackButton: true,
        backgroundColor: AppAssets.origin().grayF4F6,
        appBarColor: AppAssets.origin().grayF4F6,
        titleAppBar: TripcText(
          context.strings.text_ticket_info,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          textColor: Colors.black,
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
            horizontal: 16.W,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //contact information
              Padding(
                padding: EdgeInsets.symmetric(vertical: 16.H),
                child: ContentShadowView(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 16.H, horizontal: 16.W),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                            bottom: 8.H,
                          ),
                          child: TripcText(
                            context.strings.text_contact_information,
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                            textColor: AppAssets.origin().blue1E40,
                          ),
                        ),
                        InformationRow(
                          title: context.strings.text_fullname,
                          content: orderDetail?.contact?.fullname ?? '',
                          titleWidth: titleWidth,
                          contentFontWeight: FontWeight.w400,
                          contentFontSize: 14,
                        ),
                        InformationRow(
                          title: '${context.strings.text_linked_phone}:',
                          content: orderDetail?.contact?.phone ?? '',
                          titleWidth: titleWidth,
                          contentFontWeight: FontWeight.w400,
                          contentFontSize: 14,
                        ),
                        InformationRow(
                          title: '${context.strings.text_email}:',
                          content: orderDetail?.contact?.email ?? '',
                          titleWidth: titleWidth,
                          contentFontWeight: FontWeight.w400,
                          contentFontSize: 14,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              //list ticket
              ContentShadowView(
                child: Row(
                  children: [
                    TripcText(
                      context.strings.text_your_ticket_list,
                      fontWeight: FontWeight.w700,
                      fontSize: 16,
                      textColor: AppAssets.origin().blue1E40,
                      padding: EdgeInsets.symmetric(
                          vertical: 16.H, horizontal: 16.W),
                    ),
                  ],
                ),
              ),

              Column(
                  children: List.generate(customerTickets.length, (index) {
                return Padding(
                  padding: EdgeInsets.only(top: 5.H),
                  child: _buildQRTicket(context,
                      customerTicket: customerTickets[index],
                      index: (index + 1).toString()),
                );
              })),
            ],
          ),
        ));
  }

  Widget _buildQRTicket(BuildContext context,
      {CustomerTicket? customerTicket, String? index}) {
    String formattedIndex = (index != null) ? index.padLeft(2, '0') : '00';
    return Stack(
      children: [
        CustomPaint(
          size: Size(360.W, 183.H),
          painter: TicketPainterWidget(
              color: Colors.white,
              backgroundColor: AppAssets.origin().grayF4F6),
        ),
        Positioned.fill(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TripcText(
                context.strings.text_qr_code_ticket(formattedIndex),
                fontSize: 14,
                fontWeight: FontWeight.w500,
                textColor: AppAssets.init.blackColor,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.H),
              BaseCachedNetworkImage(
                imageUrl: customerTicket?.ticketQrCode ?? '',
                height: 99.H,
                width: 99.H,
                placeholder: (context, _) => Container(
                  color: AppAssets.origin().lightGrayDD4,
                ),
                errorWidget: (context, error, stackTrace) =>
                    AppAssets.origin().icErrorImg.widget(
                          height: 99.H,
                          width: 99.W,
                          color: context.appCustomPallet.buttonBG,
                        ),
                fit: BoxFit.cover,
              ),
              SizedBox(height: 8.H),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TripcRichText(text: '', children: [
                    TextSpan(
                      text: context.strings.text_ticket_id,
                      style: AppAssets.origin().mediumTextStyle.copyWith(
                          fontSize: 14.SP,
                          fontWeight: FontWeight.w500,
                          color: AppAssets.origin().blackColor),
                    ),
                    TextSpan(
                      text: customerTicket?.ticketId,
                      style: AppAssets.origin().mediumTextStyle.copyWith(
                          fontWeight: FontWeight.w500,
                          fontSize: 14.SP,
                          color: AppAssets.origin().blackColor),
                    ),
                  ]),
                  SizedBox(width: 10.W),
                  GestureDetector(
                    onTap: () {
                      Clipboard.setData(
                          ClipboardData(text: customerTicket?.ticketId ?? ''));
                      Fluttertoast.showToast(
                        msg: context.strings.text_copied,
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.BOTTOM,
                        backgroundColor: Colors.black54,
                        textColor: Colors.white,
                        fontSize: 14.0,
                      );
                    },
                    child: AppAssets.init.icCopy.widget(width: 16.H),
                  )
                ],
              )
            ],
          ),
        ),
      ],
    );
  }
}
