import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/payment_status.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';
import 'package:tripc_app/pages/my_trip/components/include_bottom_sheet_v2.dart';
import 'package:tripc_app/pages/my_trip/components/order_detail_bottom_sheet_v2.dart';
import 'package:tripc_app/pages/my_trip/providers/my_trip_provider.dart';
import 'package:tripc_app/pages/tour-payment/components/invoice_status_view_v2.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../services/providers/providers.dart';
import '../../tour-payment/views/electronic_invoice.dart';
import '../../../widgets/commons/content_shadow_view.dart';
import '../../../widgets/commons/information_row.dart';

class TripcMyTripDetailedPageV2 extends ConsumerStatefulWidget {
  const TripcMyTripDetailedPageV2({super.key, required this.orderId});

  final int? orderId;

  @override
  ConsumerState<TripcMyTripDetailedPageV2> createState() =>
      _TripcMyTripDetailedPageState();
}

class _TripcMyTripDetailedPageState
    extends ConsumerState<TripcMyTripDetailedPageV2> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        ref
            .read(pPaidTourProvider.notifier)
            .getOrderDetail(widget.orderId ?? 0);
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final orderDetail =
        ref.watch(pPaidTourProvider.select((value) => value.orderDetail));
    final hasBeenPaid = orderDetail?.paymentStatus == PaymentStatus.paid;
    final qty = ref.watch(pPaidTourProvider.select(
      (value) => value.qty,
    ));
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        resizeToAvoidBottomInset: false,
        canPop: false,
        onLeadingPressed: () {
          Navigator.popUntil(context, ModalRoute.withName(AppRoute.routeHome));
        },
        hasBackButton: true,
        hasAppbarBottomLine: false,
        backgroundColor: AppAssets.origin().grayF4F6,
        appBarColor: AppAssets.origin().grayF4F6,
        titleAppBar: TripcText(
          context.strings.text_reservation_details,
          fontWeight: FontWeight.w600,
          fontSize: 16,
          textColor: AppAssets.origin().black,
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            ref
                .read(pPaidTourProvider.notifier)
                .getOrderDetail(widget.orderId ?? 0);
          },
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 12.H)
                .copyWith(bottom: context.mediaQuery.padding.bottom),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ContentShadowView(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 16.H, horizontal: 16.W),
                    child: Column(
                      children: [
                        _generalInfo(
                          context,
                          orderDetail: orderDetail,
                        ),
                        _serviceInfo(context,
                            orderDetail: orderDetail, qty: qty),
                        _passengerInfo(context,
                            hasBeenPaid: hasBeenPaid, orderDetail: orderDetail),
                        SizedBox(height: 12.H),
                        TripcRichText(
                          text: '',
                          textAlign: TextAlign.start,
                          children: [
                            TextSpan(
                                text: '*',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  color: AppAssets.origin().redDotColor,
                                )),
                            TextSpan(
                                text: context.strings
                                    .text_contact_tripc_to_change_your_information,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  color: AppAssets.origin().blackColor,
                                )),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 12.H),
                _sectionInfo(context, orderDetail),
                SizedBox(height: 24.H),
                _electronicInvoiceInfo(context, orderDetail: orderDetail),
              ],
            ),
          ),
        ));
  }

  Widget _electronicInvoiceInfo(BuildContext context,
      {OrderDetail? orderDetail}) {
    final invoice = orderDetail?.invoice;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Button electronic invoice
        Visibility(
          visible: orderDetail?.invoice.isNotNull ?? false,
          child: Padding(
            padding: EdgeInsets.only(bottom: 24.H),
            child: InvoiceStatusViewV2(
                status: invoice?.status ?? InvoiceStatus.request,
                onTap: invoice == null
                    ? null
                    : () => AppRoute.pushNamed(context,
                        routeName: AppRoute.routeElectronicInvoice,
                        arguments: [false, invoice])),
          ),
        ),
        //button reset
        Visibility(
          visible: orderDetail?.orderStatus == OrderStatus.completed ||
              orderDetail?.orderStatus == OrderStatus.cancelled,
          child: Padding(
            padding: EdgeInsets.only(bottom: 16.H),
            child: Column(
              children: [
                TripcButton(
                  height: 60.H,
                  title: context.strings.text_reset_2,
                  onPressed: () {
                    final orderProduct = orderDetail?.order!
                        .firstWhereOrNull((element) => element.product != null);
                    AppRoute.pushNamed(context,
                        routeName: AppRoute.routeTourDetailView,
                        arguments: orderProduct?.product?.id);
                  },
                  style: AppButtonStyle(
                      radius: 12.H,
                      backgroundColor: AppAssets.init.lightBlueFD,
                      textColor: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        ),
        //button review
        Visibility(
            //TODO: review tour
            visible: false,
            child: Padding(
              padding: EdgeInsets.only(bottom: 16.H),
              child: TripcButton(
                height: 60.H,
                title: context.strings.text_trip_review,
                onPressed: () {},
                border: Border.all(
                  color: AppAssets.init.lightBlueFD,
                  width: 1.H,
                ),
                style: AppButtonStyle(
                    radius: 12.H,
                    textStyle: TextStyle(
                      color: AppAssets.init.lightBlueFD,
                      fontWeight: FontWeight.w500,
                      fontSize: 16.SP,
                    ),
                    backgroundColor: Colors.white,
                    textColor: Colors.white,
                    fontWeight: FontWeight.w500),
              ),
            )),

        //button contact tripc
        Padding(
          padding: EdgeInsets.only(bottom: 16.H),
          child: TripcButton(
            onPressed: () {
              if (orderDetail?.refund == null) {
                dialogHelpers.show(context,
                    child: TripcDialog(
                      title: context.strings.text_contact_tripc,
                      onTap: () {
                        Navigator.pop(context);
                      },
                      icon: AppAssets.init.imageContactTripC.widget(
                        height: 40.H,
                        width: 40.H,
                      ),
                      titleButton: context.strings.text_close,
                      contentPadding: EdgeInsets.symmetric(horizontal: 24.W)
                          .copyWith(top: 24.H, bottom: 27.H),
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 16.H),
                        child: TripcRichText(
                          text: '',
                          children: [
                            TextSpan(
                                text: context.strings.text_contact_message_1,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w300,
                                  color: Colors.black,
                                )),
                            TextSpan(
                                text: context.strings.text_contact_tripc_email,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w300,
                                  color: AppAssets.init.darkBlue5FF,
                                )),
                            TextSpan(
                                text: context.strings.text_contact_message_2,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w300,
                                  color: Colors.black,
                                ))
                          ],
                        ),
                      ),
                    ));
                return;
              }
              final orderProduct = orderDetail?.order!
                  .firstWhereOrNull((element) => element.product != null);

              AppRoute.pushNamed(context,
                  routeName: AppRoute.routeTourDetailView,
                  arguments: orderProduct?.product?.id);
            },
            textCase: TextCaseType.none,
            height: 56,
            title: orderDetail?.refund == null
                ? context.strings.text_contact_tripc
                : context.strings.text_reset,
          ),
        ),
        //button cancle
        Visibility(
          visible: orderDetail?.isAllowRefund == true ||
              orderDetail?.orderStatus == OrderStatus.pending ||
              orderDetail?.orderStatus == OrderStatus.confirmed &&
                  orderDetail?.paymentStatus == PaymentStatus.payLater,
          child: Padding(
            padding: EdgeInsets.only(bottom: 16.H),
            child: TripcButton(
                onPressed: () => AppRoute.pushNamed(context,
                    routeName: AppRoute.routeMyTripRefund,
                    arguments: orderDetail),
                height: 56,
                title: context.strings.text_cancle,
                style: AppButtonStyle(
                    textColor: Colors.white,
                    backgroundColor: AppAssets.origin().redDotColor),
                buttonType: ButtonType.outline,
                border: null),
          ),
        ),
      ],
    );
  }

  Widget _generalInfo(BuildContext context, {OrderDetail? orderDetail}) {
    final double titleWidth = 110.W;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_general_information,
          fontWeight: FontWeight.w700,
          fontSize: 16,
          textColor: AppAssets.origin().blue1E40,
          padding: EdgeInsets.only(bottom: 8.H),
        ),
        InformationRow(
          title: '${context.strings.order_code}:',
          content: orderDetail?.orderCode ?? '',
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
          titleWidth: titleWidth,
        ),
        InformationRow(
          title: context.strings.text_booking_date_time,
          content: orderDetail?.createdAt?.toFormattedDate ?? '',
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
          titleWidth: titleWidth,
        ),
        InformationRow(
          title: '${context.strings.text_price}:',
          content: orderDetail?.totalPrice?.vnd ?? '',
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
        InformationRow(
          title: '${context.strings.text_status}:',
          content: orderDetail?.paymentStatus?.statusText(context) ?? '',
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
      ],
    );
  }

  Widget _serviceInfo(BuildContext context,
      {OrderDetail? orderDetail, int? qty}) {
    final double titleWidth = 110.W;
    final tour = orderDetail?.order
        ?.where((element) => element.product != null)
        .firstOrNull;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_service_information,
          fontWeight: FontWeight.w700,
          fontSize: 16,
          textColor: AppAssets.origin().blue1E40,
          padding: EdgeInsets.only(bottom: 8.H, top: 24.H),
        ),
        InformationRow(
          title: context.strings.text_service_name,
          content: tour?.product?.name ?? '',
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
        InformationRow(
          title: context.strings.text_quantity,
          content: qty.toString().padLeft(2, '0'),
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
        InformationRow(
          title: context.strings.text_note,
          content: orderDetail?.description ?? '',
          titleWidth: titleWidth,
          isHtmlContent: true,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
        InformationRow(
          title: '${context.strings.time}:',
          content: orderDetail?.departureTime?.toFormattedDate ?? '',
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        )
      ],
    );
  }

  Widget _passengerInfo(BuildContext context,
      {OrderDetail? orderDetail, required bool hasBeenPaid}) {
    final double titleWidth = 110.W;
    final contactPerson = orderDetail?.contact;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 8.H, top: 24.H),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                context.strings.text_contact_information,
                fontWeight: FontWeight.w700,
                fontSize: 16,
                textColor: AppAssets.origin().blue1E40,
              ),
              TripcText(
                context.strings.text_view_booked_ticket,
                fontWeight: FontWeight.w400,
                fontSize: 12,
                textColor: AppAssets.origin().blue0365,
                underlineColor: AppAssets.origin().blue0365,
                decoration: TextDecoration.underline,
                onTap: () {
                  AppRoute.pushNamed(context,
                      routeName: AppRoute.routeTicketInfoV2,
                      arguments: orderDetail);
                },
              ),
            ],
          ),
        ),
        InformationRow(
          title: context.strings.text_fullname,
          content: contactPerson?.fullname ?? '',
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
        InformationRow(
          title: '${context.strings.text_linked_phone}:',
          content: contactPerson?.phone ?? '',
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
        InformationRow(
          title: '${context.strings.text_email}:',
          content: contactPerson?.email ?? '',
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
      ],
    );
  }

  Widget _sectionItem(BuildContext context,
      {String? title, VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        height: 28.H,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TripcText(
              title,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              textColor: AppAssets.init.blackColor,
            ),
            AppAssets.init.icArrowRightGray.widget(width: 20.H)
          ],
        ),
      ),
    );
  }

  Widget _sectionInfo(BuildContext context, OrderDetail? orderDetail) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.H),
        boxShadow: [
          BoxShadow(
            color: AppAssets.origin().blackColor.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 15.W),
        child: Column(
          children: [
            _sectionItem(
              context,
              title: context.strings.text_includes,
              onTap: () {
                showDialog(
                    context: context,
                    builder: (context) => Dialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.SP),
                          ),
                          backgroundColor: Colors.white,
                          insetPadding: EdgeInsets.symmetric(
                              horizontal: 16.W, vertical: 16.H),
                          child: IncludeBottomSheetV2(
                            content: orderDetail?.generalTerms,
                            title: context.strings.text_includes,
                          ),
                        ));
              },
            ),
            SizedBox(height: 12.H),
            _sectionItem(
              context,
              title: context.strings.text_how_to_use,
              onTap: () {
                showDialog(
                    context: context,
                    builder: (context) => Dialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.SP),
                          ),
                          backgroundColor: Colors.white,
                          insetPadding: EdgeInsets.symmetric(
                              horizontal: 16.W, vertical: 16.H),
                          child: IncludeBottomSheetV2(
                            content: orderDetail?.usageInstructions,
                            title: context.strings.text_how_to_use,
                          ),
                        ));
              },
            ),
            SizedBox(height: 12.H),
            _sectionItem(
              context,
              title: context.strings.text_before_placing,
              onTap: () {
                showDialog(
                    context: context,
                    builder: (context) => Dialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.SP),
                          ),
                          backgroundColor: Colors.white,
                          insetPadding: EdgeInsets.symmetric(
                              horizontal: 16.W, vertical: 16.H),
                          child: OrderDetailBottomSheetV2(
                            content: orderDetail?.condition,
                            title: context.strings.text_before_placing,
                          ),
                        ));
              },
            ),
          ],
        ),
      ),
    );
  }
}
