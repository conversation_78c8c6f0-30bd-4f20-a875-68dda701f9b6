import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../models/app/refund_information.dart';
import '../../../models/remote/api_tour_response/tour_response.dart';

class CanceledTourModel {
  CanceledTourModel({required this.refundInfor});

  final RefundInformation refundInfor;

  static CanceledTourModel getDefault() {
    return CanceledTourModel(
        refundInfor: RefundInformation(
            status: 2,
            refundPrice: 1500000,
            paidPrice: 1000000,
            tour: TourResponse(name: 'Tour tham quan Hà Nội - 3 ngày 2 đêm'),
            aldultQuantity: 1,
            childQuantity: 1,
            reason: '.......',
            requestDate: DateTime(2025, 1, 20),
            refundDate: DateTime(2025, 1, 26)));
  }
}

class CanceledTourProvider extends StateNotifier<CanceledTourModel> {
  CanceledTourProvider(super._state);
}

final pCanceledTourProvider =
    StateNotifierProvider<CanceledTourProvider, CanceledTourModel>(
        (ref) => CanceledTourProvider(CanceledTourModel.getDefault()));
