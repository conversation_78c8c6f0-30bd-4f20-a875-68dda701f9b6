import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/refund_request.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';
import 'package:tripc_app/pages/my_trip/providers/refund_reason_enum.dart';
import 'package:tripc_app/services/apis/order/api_order.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../models/remote/common_error.dart';

class RefundPageModel {
  RefundPageModel({
    this.adultRefund = 0,
    this.childRefund = 0,
    this.reason,
    this.isLoading = false,
    this.errorMessage = '',
    this.adultPrice = 0,
    this.childPrice = 0,
    this.maxAdultQty = 0,
    this.maxChildQty = 0,
    this.orderDetail,
    this.otherReason = ''
  });

  int adultRefund;
  int childRefund;
  RefundReason? reason;
  bool? isLoading;
  String? errorMessage;
  int adultPrice;
  int childPrice;
  int maxAdultQty;
  int maxChildQty;
  final OrderDetail? orderDetail;
  final String otherReason;

  static RefundPageModel initilize() {
    return RefundPageModel();
  }

  int get total => adultPrice * adultRefund + childPrice * childRefund;

  RefundPageModel copyWith({
    int? adultRefund,
    int? childRefund,
    RefundReason? reason,
    bool? isLoading,
    String? errorMessage,
    int? adultPrice,
    int? childPrice,
    int? maxAdultQty,
    int? maxChildQty,
    OrderDetail? orderDetail,
    String? otherReason,
  }) {
    return RefundPageModel(
      adultRefund: adultRefund ?? this.adultRefund,
      childRefund: childRefund ?? this.childRefund,
      reason: reason ?? this.reason,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      adultPrice: adultPrice ?? this.adultPrice,
      childPrice: childPrice ?? this.childPrice,
      maxAdultQty: maxAdultQty ?? this.maxAdultQty,
      maxChildQty: maxChildQty ?? this.maxChildQty,
      orderDetail: orderDetail ?? this.orderDetail,
      otherReason: otherReason ?? this.otherReason,
    );
  }
}

class RefundPageProvider extends StateNotifier<RefundPageModel> {
  RefundPageProvider(super._state);

  final ApiOrder _api = ApiOrder();

  void setErrorMessage(String? value) {
    state = state.copyWith(
      errorMessage: value,
    );
  }

  void setLoading(bool value) {
    state = state.copyWith(
      isLoading: value,
    );
  }

  Future<bool> requestRefund(BuildContext context) async {
    setLoading(true);
    try {
      final orders = state.orderDetail?.order
          ?.where((element) => element.subProduct.isNotNull)
          .toList() ??
          [];
      final refundItem = orders.map((e) {
        return RefundItem(
          quantity: e.quantity,
          subProductId: e.subProduct?.id,
        );
      },).toList();
      final request = RefundRequest(
        orderId: state.orderDetail?.id,
        reason: state.reason == RefundReason.otherReasons ? state.otherReason : state.reason?.toLocalizedString(context),
        refundItems: refundItem,
      );
      await _api.requestRefund(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      return true;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  void setOtherReason(String? value) {
    state = state.copyWith(
      otherReason: value,
    );
  }

  void initQuantity(OrderDetail detail) {
    state = state.copyWith(orderDetail: detail);
    final subProducts = detail.order
            ?.where((element) => element.subProduct != null)
            .toList() ??
        [];
    if (subProducts.length == 2) {
      state = state.copyWith(childPrice: subProducts.last.subProduct?.price, maxChildQty: subProducts.last.quantity);
    }
    state = state.copyWith(adultPrice: subProducts.first.subProduct?.price, maxAdultQty: subProducts.last.quantity);
  }

  void incrementAdultRefund(int max) {
    if (state.adultRefund == max) return;
    state = state.copyWith(adultRefund: state.adultRefund + 1);
  }

  void decrementAdultRefund() {
    if (state.adultRefund == 0) return;
    state = state.copyWith(adultRefund: state.adultRefund - 1);
  }

  void incrementChildRefund(int max) {
    if (state.childRefund == max) return;
    state = state.copyWith(childRefund: state.childRefund + 1);
  }

  void decrementChildRefund() {
    if (state.childRefund == 0) return;
    state = state.copyWith(childRefund: state.childRefund - 1);
  }

  void updateRefundReason(RefundReason reason) {
    state = state.copyWith(reason: reason);
  }

  bool get enableContinueButton {
    if (state.reason == null) return false;
    return state.maxChildQty != 0 ? state.adultRefund != 0 || state.childRefund != 0 : state.adultRefund != 0;
  }

  int get getActualRefund {
    return 50000;
  }

  void resetState() {
    state = RefundPageModel.initilize();
  }
}

final pRefundProvider =
    StateNotifierProvider<RefundPageProvider, RefundPageModel>(
  (ref) => RefundPageProvider(RefundPageModel.initilize()),
);
