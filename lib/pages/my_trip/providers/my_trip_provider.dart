import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/app/payment.dart';
import 'package:tripc_app/models/app/payment_status.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/models/remote/order_response/my_order_response.dart';
import 'package:tripc_app/services/apis/order/api_order.dart';

import '../../../models/remote/common_error.dart';

class PaidTourScreenModel {
  PaidTourScreenModel({
    this.pendingList = const [],
    this.paidList = const [],
    this.recentView = const [],
    this.isLoading = false,
    this.errorMessage = '',
    this.orderDetail,
    this.orders = const [],
    this.canLoadMore = false,
    this.isLoadingLoadMore = false,
    this.page = 1,
    this.pageSize = 20,
    this.keyWord = '',
  });

  final List<Payment> pendingList;
  final List<Payment> paidList;
  final List<TourResponse> recentView;
  final bool isLoading;
  final String? errorMessage;
  final OrderDetail? orderDetail;
  final List<OrderResponse> orders;
  final bool canLoadMore;
  final bool isLoadingLoadMore;
  final int page;
  final int pageSize;
  final String keyWord;

  List<OrderResponse> get paidOrderList {
    return orders
        .where((element) => element.paymentStatus == PaymentStatus.paid)
        .toList();
  }

  List<OrderResponse> get payLaterOrderList {
    return orders
        .where((element) =>
            element.orderStatus == OrderStatus.pending ||
            element.paymentStatus == PaymentStatus.payLater)
        .toList();
  }

  int get qty {
    if (orderDetail == null) {
      return 0;
    }
    return orderDetail?.order
            ?.where((element) => element.subProduct != null)
            .map((e) => e.quantity)
            .fold(0, (a, b) => (a ?? 0) + (b ?? 0)) ??
        0;
  }

  int get aldQty {
    if (orderDetail == null) {
      return 0;
    }
    final subProducts = orderDetail?.order
            ?.where((element) => element.subProduct != null)
            .toList() ??
        [];
    return subProducts.first.quantity ?? 0;
  }

  int get childQty {
    if (orderDetail == null) {
      return 0;
    }
    final subProducts = orderDetail?.order
            ?.where((element) => element.subProduct != null)
            .toList() ??
        [];
    if (subProducts.length == 2) {
      return subProducts.last.quantity ?? 0;
    }
    return 0;
  }

  static PaidTourScreenModel getDefault() {
    return PaidTourScreenModel(recentView: [], pendingList: [], paidList: []);
  }

  PaidTourScreenModel copyWith({
    List<Payment>? pendingList,
    List<Payment>? paidList,
    List<TourResponse>? recentView,
    bool? isLoading,
    String? errorMessage,
    OrderDetail? orderDetail,
    List<OrderResponse>? orders,
    bool? canLoadMore,
    bool? isLoadingLoadMore,
    int? page,
    int? pageSize,
    String? keyWord,
  }) {
    return PaidTourScreenModel(
      pendingList: pendingList ?? this.pendingList,
      recentView: recentView ?? this.recentView,
      paidList: paidList ?? this.paidList,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      orderDetail: orderDetail ?? this.orderDetail,
      orders: orders ?? this.orders,
      canLoadMore: canLoadMore ?? this.canLoadMore,
      isLoadingLoadMore: isLoadingLoadMore ?? this.isLoadingLoadMore,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
      keyWord: keyWord ?? this.keyWord,
    );
  }
}

class PaidTourScreenProvider extends StateNotifier<PaidTourScreenModel> {
  PaidTourScreenProvider(super._state);

  final ApiOrder _api = ApiOrder();

  void addPendingPayment(Payment? payment) {
    if (payment == null) return;
    state = state.copyWith(pendingList: [...state.pendingList, payment]);
  }

  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void refresh() {
    state = state.copyWith(
      page: 1,
      keyWord: state.keyWord,
      canLoadMore: false,
      isLoadingLoadMore: false,
      orders: [],
    );
    getMyOrders();
  }

  void loadMore() {
    if (!state.canLoadMore) return;
    state = state.copyWith(
      page: state.page + 1,
      keyWord: state.keyWord,
      orders: [],
    );
    getMyOrders();
  }

  Future<void> getMyOrders() async {
    setLoading(true);
    try {
      final result = await _api
          .getMyOrders(ListDataRequest(
            page: state.page,
            pageSize: state.pageSize,
            keyWord: state.keyWord,
          ))
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      _appendOrder(result);
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> searchMyOrder(String txt) async {
    setLoading(true);
    try {
      final result = await _api.getMyOrders(ListDataRequest(sku: txt)).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(
          orders: result.data,
          canLoadMore: state.orders.length < (result.total ?? 0));
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  void _appendOrder(MyOrderResponse orderResponse) {
    if (state.orders.isEmpty) {
      state = state.copyWith(
          orders: orderResponse.data,
          canLoadMore: state.orders.length < (orderResponse.total ?? 0));
      return;
    }
    state = state.copyWith(
      orders: [...state.orders, ...orderResponse.data],
      canLoadMore: state.orders.length < (orderResponse.total ?? 0),
    );
  }

  Future<void> getOrderDetail(int orderId) async {
    setLoading(true);
    try {
      final result = await _api.getOrderDetails(orderId: orderId).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(orderDetail: result.data);
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void addPaidPayment(Payment? payment) {
    if (payment == null) return;
    state = state.copyWith(paidList: [...state.paidList, payment]);
  }

  void resetState() {
    state = PaidTourScreenModel.getDefault();
  }
}

final pPaidTourProvider =
    StateNotifierProvider<PaidTourScreenProvider, PaidTourScreenModel>(
        (ref) => PaidTourScreenProvider(PaidTourScreenModel.getDefault()));
