import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum RefundReason {
  issueWithOrderOrVoucher,
  badWeatherOrNaturalDisaster,
  transportationDelaysOrAccidents,
  illnessOrBurstingBubble,
  socialOrPublicDisturbances,
  otherExternalConditions,
  needToCorrectInformation,
  needToCorrectPackage,
  usePromoCodeIssue,
  dissatisfactionWithExperience,
  injuryOrSevereIncident,
  changePlan,
  otherReasons;
}

extension RefundReasonExtension on RefundReason {
  String toLocalizedString(BuildContext context, {String? otherText}) {
    switch (this) {
      case RefundReason.issueWithOrderOrVoucher:
        return context.strings.text_issue_with_order_or_voucher;
      case RefundReason.badWeatherOrNaturalDisaster:
        return context.strings.text_bad_weather_or_natural_disaster;
      case RefundReason.transportationDelaysOrAccidents:
        return context.strings.text_transportation_delays_or_accidents;
      case RefundReason.illnessOrBurstingBubble:
        return context.strings.text_illness_or_bursting_bubble;
      case RefundReason.socialOrPublicDisturbances:
        return context.strings.text_social_or_public_disturbances;
      case RefundReason.otherExternalConditions:
        return context.strings.text_other_external_conditions;
      case RefundReason.needToCorrectInformation:
        return context.strings.text_need_to_correct_information;
      case RefundReason.needToCorrectPackage:
        return context.strings.text_need_to_correct_package;
      case RefundReason.usePromoCodeIssue:
        return context.strings.text_use_promo_code_issue;
      case RefundReason.dissatisfactionWithExperience:
        return context.strings.text_dissatisfaction_with_experience;
      case RefundReason.injuryOrSevereIncident:
        return context.strings.text_injury_or_severe_incident;
      case RefundReason.changePlan:
        return context.strings.text_change_of_plan;
      case RefundReason.otherReasons:
        return '${context.strings.text_other_reasons}: ${otherText ?? ''}';
    }
  }
}