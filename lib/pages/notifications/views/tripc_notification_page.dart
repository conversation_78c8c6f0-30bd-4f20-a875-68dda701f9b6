import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/notifications/components/empty_view.dart';
import 'package:tripc_app/pages/notifications/components/notification_dialog.dart';
import 'package:tripc_app/pages/notifications/components/notification_item.dart';
import 'package:tripc_app/pages/notifications/providers/tripc_notification_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../widgets/app_loading.dart';
import '../../../widgets/commons/app_dialog/tripc_error_dialog.dart';

class TripcNotificationPage extends ConsumerStatefulWidget {
  const TripcNotificationPage({super.key});

  @override
  ConsumerState<TripcNotificationPage> createState() =>
      _TripcNotificationPageState();
}

class _TripcNotificationPageState extends ConsumerState<TripcNotificationPage> {
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      _loadMore();
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(pNotificationProvider.notifier).getNotification();
    });
  }

  void _loadMore() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 100) {
      ref.read(pNotificationProvider.notifier).loadMore();
    }
  }

  Future<void> _markReadAllNotifications() async {
    final result = await ref
        .read(pNotificationProvider.notifier)
        .markReadAllNotifications();

    if (result) {
     await ref.read(pNotificationProvider.notifier).getNotification();
    } else {
      final errorText = ref.read(pNotificationProvider).errorMessage;
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorText));
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final notifications =
        ref.watch(pNotificationProvider.select((value) => value.notifications));
    final isLoadingMore =
        ref.watch(pNotificationProvider.select((value) => value.isLoading));
    return Stack(
      children: [
        TripcScaffold(
          hasBackButton: true,
          leadingWidth: 48,
          toolbarHeight: 53.H,
          backgroundColor: AppAssets.origin().whiteBackgroundColor,
          bottom: PreferredSize(
              preferredSize: Size.fromHeight(0.5.H),
              child: Container(
                height: 0.5.H,
                color: AppAssets.origin().disableButtonColor,
              )),
          titleAppBar: TripcText(
            context.strings.text_notification,
            fontSize: 18,
            fontWeight: FontWeight.w700,
            textColor: AppAssets.origin().blackColor,
          ),
          actions: [
            TripcIconButton(
                onPressed: () => dialogHelpers.show(context,
                    child: NotificationDialog(
                      onTap: () => _markReadAllNotifications(),
                    )),
                child: Padding(
                  padding: EdgeInsets.only(right: 28.55.W),
                  child: AppAssets.origin().icBroom.widget(
                      height: 22.H,
                      width: 15.W,
                      color: AppAssets.origin().blackColor),
                )),
          ],
          body: RefreshIndicator(
            onRefresh: () async {
              ref.read(pNotificationProvider.notifier).getNotification();
            },
            child: Visibility(
              visible: notifications.isNotEmpty,
              replacement: const Center(child: NotificationEmptyView()),
              child: ListView.separated(
                  padding: EdgeInsets.only(bottom: 100.H),
                  controller: _scrollController,
                  shrinkWrap: true,
                  itemCount: notifications.length,
                  separatorBuilder: (context, _) => Divider(
                      thickness: 0.5.H,
                      height: 0,
                      color: AppAssets.origin().disableButtonColor),
                  itemBuilder: (context, index) => NotificationItem(
                      onTap: () => AppRoute.pushNamed(context,
                          routeName: AppRoute.routeNotificationDetailedPage,
                          arguments: notifications[index]),
                      notification: notifications[index])),
            ),
          ),
        ),
        AppLoading(isRequesting: isLoadingMore),
      ],
    );
  }
}
