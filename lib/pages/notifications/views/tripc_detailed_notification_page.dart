import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/notification_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../providers/tripc_notification_provider.dart';

class TripcDetailedNotificationPage extends ConsumerStatefulWidget {
  const TripcDetailedNotificationPage({super.key, required this.notification});
  final NotificationResponse notification;

  @override
  ConsumerState<TripcDetailedNotificationPage> createState() =>
      _TripcDetailedNotificationState();
}

class _TripcDetailedNotificationState
    extends ConsumerState<TripcDetailedNotificationPage> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final result = await ref
          .read(pNotificationProvider.notifier)
          .markReadNotification(notificationId: widget.notification.id);
      if (result) {
        await ref.read(pNotificationProvider.notifier).getNotification();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
        hasBackButton: true,
        centerTitle: true,
        leadingWidth: 48,
        toolbarHeight: 53.H,
        bottom: PreferredSize(
            preferredSize: Size.fromHeight(0.5.H),
            child: Container(
              height: 0.5.H,
              color: AppAssets.origin().disableButtonColor,
            )),
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        titleAppBar: TripcText(
          context.strings.text_detailed_information,
          fontSize: 18,
          fontWeight: FontWeight.w700,
          textColor: AppAssets.origin().blackColor,
          padding: EdgeInsets.only(left: 24.W),
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            final result = await ref
                .read(pNotificationProvider.notifier)
                .markReadNotification(notificationId: widget.notification.id);
            if (result) {
              await ref.read(pNotificationProvider.notifier).getNotification();
            }
          },
          child: ListView(
            padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W)
                .copyWith(bottom: context.spacingBottom)
                .copyWith(bottom: context.spacingBottom),
            children: [
              TripcText(
                widget.notification.title,
                fontSize: 14,
                height: 1.5,
                fontWeight: FontWeight.w700,
                textAlign: TextAlign.start,
                padding: EdgeInsets.only(bottom: 20.H),
              ),
              MarkdownBody(
                data: widget.notification.description ?? '',
                styleSheet: MarkdownStyleSheet(
                    listBullet: AppAssets.init.normalTextStyle
                        .copyWith(fontSize: 14.SP, fontWeight: FontWeight.w300),
                    h1: AppAssets.init.normalTextStyle
                        .copyWith(fontSize: 14.SP, fontWeight: FontWeight.w400),
                    p: AppAssets.init.normalTextStyle
                        .copyWith(fontSize: 14.SP, fontWeight: FontWeight.w300)),
              )
            ],
          ),
        ));
  }
}
