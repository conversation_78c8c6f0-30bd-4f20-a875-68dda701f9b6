import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class NotificationEmptyView extends StatelessWidget {
  const NotificationEmptyView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 173.H),
      child: Column(
        children: [
          AppAssets.origin().icBigNotification.widget(
                height: 120.H,
                width: 120.H,
              ),
          TripcText(
            'Bạn không có thông báo nào!',
            fontSize: 18,
            fontWeight: FontWeight.w500,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(top: 8.H),
          )
        ],
      ),
    );
  }
}
