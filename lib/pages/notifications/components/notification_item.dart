import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../models/remote/notification_response.dart';

class NotificationItem extends StatelessWidget {
  const NotificationItem({super.key, required this.notification, this.onTap});
  final NotificationResponse notification;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
      onPressed: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 2.W, top: 4.H),
                  child: notification.notiType.icon
                      .widget(height: 12.H, width: 12.H),
                ),
                SizedBox(
                  width: 9.W,
                ),
                Expanded(
                  child: TripcText(
                    notification.title ?? '',
                    fontSize: 14,
                    ignorePointer: true,
                    fontWeight: FontWeight.w500,
                    maxLines: 2,
                    height: 1.3,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.start,
                    textColor: AppAssets.origin().blackColor,
                  ),
                ),
                Visibility(
                    visible: (notification.read == 1),
                    child: Container(
                      margin: EdgeInsets.only(left: 14.W),
                      height: 6.H,
                      width: 6.H,
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppAssets.origin().redDotColor),
                    ))
              ],
            ),
            TripcText(
              notification.description ?? '',
              fontSize: 12,
              ignorePointer: true,
              fontWeight: FontWeight.w300,
              textColor: AppAssets.origin().blackColor,
              maxLines: 2,
              height: 1.5,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
              padding:
                  EdgeInsets.only(top: 8.H, bottom: 4.H, left: 2.W, right: 2.W),
            ),
            Row(
              children: [
                AppAssets.origin()
                    .icGrayClock
                    .widget(height: 16.H, width: 16.H),
                SizedBox(
                  width: 7.W,
                ),
                TripcText(
                  notification.hour,
                  fontSize: 12,
                  ignorePointer: true,
                  fontWeight: FontWeight.w300,
                  textColor: AppAssets.origin().darkGreyTextColor,
                  padding: EdgeInsets.only(right: 5.W),
                ),
                TripcText(
                  notification.createAtTime.ddMMyyyy,
                  fontSize: 12,
                  ignorePointer: true,
                  fontWeight: FontWeight.w300,
                  textColor: AppAssets.origin().darkGreyTextColor,
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
