import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/app/list_data_request.dart';
import '../../../models/remote/common_error.dart';
import '../../../models/remote/notification_response.dart';
import '../../../services/apis/auth/api_user_login.dart';

class NotificationScreenModel {
  NotificationScreenModel(
      {this.notifications = const [],
      this.errorMessage,
      this.enableLoadMore = true,
      this.isLoading = false,
      this.page = 1,
      this.pageSize = 20});

  final List<NotificationResponse> notifications;
  String? errorMessage;
  bool enableLoadMore;
  bool isLoading;
  int page;
  int pageSize;

  static NotificationScreenModel getDefault() {
    return NotificationScreenModel();
  }

  NotificationScreenModel copyWith(
      {List<NotificationResponse>? notifications,
      bool? isLoading,
      String? errorMessage,
      bool? enableLoadMore,
      bool? isLoadingMore,
      int? page,
      int? pageSize}) {
    return NotificationScreenModel(
        notifications: notifications ?? this.notifications,
        errorMessage: errorMessage ?? this.errorMessage,
        enableLoadMore: enableLoadMore ?? this.enableLoadMore,
        isLoading: isLoadingMore ?? this.isLoading,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize);
  }

  // List<List<NotificationResponse>> get listNotiBySection {
  //   Map<DateTime, List<NotificationResponse>> groupedByDate =
  //       groupBy(notifications, (NotificationResponse item) {
  //     return DateTime(item.createAtTime.year, item.createAtTime.month,
  //         item.createAtTime.day);
  //   });
  //   final listChatBySection = groupedByDate.values.toList();
  //   return listChatBySection;
  // }

  // String showDaySection(int index) {
  //   return listNotiBySection[index].isNotEmpty
  //       ? listNotiBySection[index].first.createAtTime.ddMMyyyy
  //       : '';
  // }
}

class NotificationScreenProvider
    extends StateNotifier<NotificationScreenModel> {
  NotificationScreenProvider(super._state);
  final ApiUser _api = ApiUser();

  void setLoading(bool value) {
    state = state.copyWith(isLoadingMore: value);
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  Future<void> getNotification() async {
    final request = ListDataRequest(page: 1, pageSize: state.pageSize);
    try {
      final result = await _api.getNotification(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(notifications: result.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
    }
  }

  Future<void> loadMore() async {
    if (!state.enableLoadMore) return;

    final request =
        ListDataRequest(page: state.page + 1, pageSize: state.pageSize);
    setLoading(true);
    try {
      final result = await _api.getNotification(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.data.isNotEmpty) {
        state.notifications.addAll(result.data);
        state = state.copyWith(
            notifications: state.notifications, page: state.page + 1);
      } else {
        state = state.copyWith(enableLoadMore: false);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<bool> markReadAllNotifications() async {
    setLoading(true);
    try {
      final result = await _api.markReadNotifications().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  Future<bool> markReadNotification({required int? notificationId}) async {
    if (notificationId != null) {
      try {
        final result =
            await _api.markReadNotification(id: notificationId).timeout(
                  const Duration(
                    seconds: 30,
                  ),
                );
        return result;
      } catch (exceptionMessage) {
        // if (exceptionMessage is CommonErrorResponse) {
        //   setErrorMessage(exceptionMessage.error);
        // }
        return false;
      }
    }
    return false;
  }
}

final pNotificationProvider =
    StateNotifierProvider<NotificationScreenProvider, NotificationScreenModel>(
        (ref) =>
            NotificationScreenProvider(NotificationScreenModel.getDefault()));
