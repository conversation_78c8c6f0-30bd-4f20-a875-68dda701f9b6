import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/manage_profile/components/reset_password_successfully_dialog.dart';
import 'package:tripc_app/pages/manage_profile/components/reset_password_text_field.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_reset_password.provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/app_show_api_error_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcResetPasswordPage extends ConsumerStatefulWidget {
  const TripcResetPasswordPage({super.key});

  @override
  ConsumerState<TripcResetPasswordPage> createState() =>
      _TripcResetPasswordPageState();
}

class _TripcResetPasswordPageState
    extends ConsumerState<TripcResetPasswordPage> {
  final _newPassController = TextEditingController();
  final _reEnterNewPassController = TextEditingController();

  @override
  void dispose() {
    _newPassController.dispose();
    _reEnterNewPassController.dispose();
    super.dispose();
  }

  Future<void> _resetPass() async {
    unfocusKeyboard();
    final result =
        await ref.read(pResetPasswordProvider.notifier).resetPassword();
    if (result) {
      dialogHelpers.show(context, barrierDismissible: false,
          child: TripcResetPasswordSuccessDialog(onTap: () {
        ref.read(pResetPasswordProvider.notifier).resetState();
        AppRoute.I.routeObserver.isContainRoute(AppRoute.routeSignIn)
            ? Navigator.popUntil(
                context, ModalRoute.withName(AppRoute.routeSignIn))
            : Navigator.popUntil(
                context, ModalRoute.withName(AppRoute.routeManageProfile));
      }));
    } else {
      final error = ref.read(pResetPasswordProvider).errorMessage ?? '';
      dialogHelpers.show(context, child: ErrorDialog(text: error));
    }
  }

  @override
  Widget build(BuildContext context) {
    final errorNewPass =
        ref.watch(pResetPasswordProvider.select((value) => value.errorNewPass));
    final errorReEnterNewPass = ref
        .watch(pResetPasswordProvider.select((value) => value.errorReNewPass));
    final isEnableResetPass = ref.watch(
        pResetPasswordProvider.select((value) => value.isEnableResetPass));
    final isLoading =
        ref.watch(pResetPasswordProvider.select((value) => value.loading));
    return Stack(
      children: [
        TripcScaffold(
            onPopScope: () =>
                ref.read(pResetPasswordProvider.notifier).resetPageResetPass(),
            onLeadingPressed: () {
              ref.read(pResetPasswordProvider.notifier).resetPageResetPass();
              Navigator.pop(context);
            },
            bottom: PreferredSize(
                preferredSize: Size.fromHeight(0.5.H),
                child: Container(
                    height: 0.5.H,
                    width: context.mediaQuery.size.width,
                    color: AppAssets.origin().disableButtonColor)),
            needUnFocus: true,
            toolbarHeight: 53.H,
            backgroundColor: AppAssets.origin().whiteBackgroundColor,
            resizeToAvoidBottomInset: false,
            titleAppBar: TripcText(
              context.strings.text_reset_password,
              fontSize: 16,
              fontWeight: FontWeight.w600,
              textColor: AppAssets.origin().black,
              padding: EdgeInsets.only(top: 6.H),
            ),
            leading: TripcIconButton(
              onPressed: () => Navigator.pop(context),
              child: Padding(
                padding: EdgeInsets.only(top: 6.H),
                child: AppAssets.init.iconArrowleft.widget(
                  color: AppAssets.origin().black,
                ),
              ),
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.W, vertical: 20.H)
                  .copyWith(bottom: context.mediaQuery.padding.bottom),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          TripcText(
                            context.strings.text_pls_set_security_pass,
                            fontSize: 14,
                            height: 1.4,
                            textAlign: TextAlign.start,
                            fontWeight: FontWeight.w300,
                            textColor: AppAssets.origin().darkGreyTextColor,
                          ),
                          SizedBox(
                            height: 32.H,
                          ),
                          TripcResetPasswordTextField(
                              onChanged: (value) => ref
                                  .read(pResetPasswordProvider.notifier)
                                  .typeNewPass(value, context),
                              value: true,
                              controller: _newPassController,
                              label: context.strings.text_enter_new_pass,
                              hintText:
                                  context.strings.text_enter_your_password,
                              errorText: errorNewPass),
                          TripcResetPasswordTextField(
                              onChanged: (value) => ref
                                  .read(pResetPasswordProvider.notifier)
                                  .reTypeNewPass(value, context),
                              value: true,
                              controller: _reEnterNewPassController,
                              label: context.strings.text_re_enter_new_pass,
                              hintText: context.strings.text_re_enter_password,
                              errorText: errorReEnterNewPass),
                        ],
                      ),
                    ),
                  ),
                  TripcButton(
                    onPressed: _resetPass,
                    isButtonDisabled: !isEnableResetPass,
                    title: context.strings.text_confirm,
                  )
                ],
              ),
            )),
        AppLoading(isRequesting: isLoading)
      ],
    );
  }
}
