import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/manage_profile/components/tripc_link_email_component/tripc_link_email_first_page.dart';
import 'package:tripc_app/pages/manage_profile/components/tripc_link_email_component/tripc_link_email_link_new_email.dart';
import 'package:tripc_app/pages/manage_profile/components/tripc_link_email_component/tripc_link_email_verify_otp.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/app_toast/tripc_black_toast.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../services/cache/cache_auth.dart';
import '../components/tripc_link_email_component/tripc_link_email_dialog.dart';

class TripcLinkEmailPage extends ConsumerStatefulWidget {
  const TripcLinkEmailPage({super.key});

  @override
  ConsumerState<TripcLinkEmailPage> createState() => _TripcLinkEmailPageState();
}

class _TripcLinkEmailPageState extends ConsumerState<TripcLinkEmailPage> {
  final _emailController = TextEditingController();
  final _otpController = TextEditingController();
  final _newEmailController = TextEditingController();
  Timer? timer;
  late final PageController pageController;
  bool showError = false;
  @override
  void initState() {
    super.initState();
    _otpController.addListener(() {
      setState(() {
        showError = false;
      });
    });
    _newEmailController.addListener(() {
      ref
          .read(pAccountProvider.notifier)
          .validateNewLinkedEmail(context, email: _newEmailController.text);
    });
    pageController = PageController(
        initialPage: ref.read(pAccountProvider).currentLinkEmailIndex);
    _emailController.text = ref.read(pAccountProvider).user?.email ?? '';
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listenManual(pAccountProvider, (_, state) {
        pageController.jumpToPage(state.currentLinkEmailIndex);
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
    pageController.dispose();
    _emailController.dispose();
    _otpController.dispose();
    _newEmailController.dispose();
    timer?.cancel();
  }

  String _pageTitle(int index) {
    if (index < 2) {
      return context.strings.text_email_link;
    }
    return context.strings.text_new_email_link;
  }

  String _buttonTitle(int index) {
    switch (index) {
      case 0:
        return context.strings.text_send_confirmation_code;
      case 1 || 2:
        return context.strings.text_continue;
      case 3:
        return context.strings.text_change_email_link;
      default:
        return '';
    }
  }

  void _onTapButton({required int index}) {
    switch (index) {
      case 0:
        _sendOTP(
          indexPage: 1,
          email: _emailController.text,
        );
        break;
      case 1:
        _verifyOTPLinkEmail();
        break;
      case 2:
        _sendOtplinkEmail(email: _newEmailController.text, indexPage: 3);
        break;
      case 3:
        _verifyOTPLinkNewEmail(context);
        break;
      default:
        break;
    }
  }

  bool isDisableButton(int index, {String? errorNewEmail}) {
    switch (index) {
      case 0:
        return false;
      case 1 || 3:
        return _otpController.text.length != 6;
      case 2:
        return (errorNewEmail ?? '').isNotEmpty ||
            _newEmailController.text.isEmpty;
      default:
        return false;
    }
  }

  void _showError() {
    final errorText = ref.read(pAccountProvider).errorMessage;
    dialogHelpers.show(context, child: TripcErrorDialog(errorText: errorText));
  }

  void _setCountDown({bool isRefresh = false}) {
    ref.read(pAccountProvider.notifier).setCountDown(isRefresh: isRefresh);
    timer?.cancel();
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      final timeCountDown = ref.read(pAccountProvider).timeCountDown;
      if (timeCountDown == 0) {
        timer.cancel();
        return;
      }
      ref.read(pAccountProvider.notifier).setCountDown();
    });
  }

  Future _sendOTP(
      {required String email,
      bool isResend = false,
      bool isRefresh = false,
      required int indexPage}) async {
    bool result = false;
    if (indexPage == 1) {
      result = await ref.read(pAccountProvider.notifier).sendOtp(email: email);
    } else {
      result = await ref
          .read(pAccountProvider.notifier)
          .sendOtpLinkEmail(email: email);
    }
    if (result) {
      if (isResend) {
        toastHelpers.showCustom(context,
            child: TripcBlackToast(
              message: context.strings.text_confirmation_code_has_been_sent,
              margin: EdgeInsets.only(top: 230.H),
            ));
      } else {
        ref.read(pAccountProvider.notifier).setIndexLinkEmailPage(indexPage);
        _otpController.clear();
      }
      _setCountDown(isRefresh: isRefresh);
    } else {
      _showError();
    }
  }

  Future _verifyOTPLinkEmail() async {
    final result = await ref
        .read(pAccountProvider.notifier)
        .verifyOtp(email: _emailController.text, otp: _otpController.text);
    if (result) {
      ref.read(pAccountProvider.notifier).setIndexLinkEmailPage(2);
    } else {
      setState(() {
        showError = true;
      });
    }
  }

  Future _sendOtplinkEmail(
      {required String email, required int indexPage}) async {
    final result = await ref
        .read(pAccountProvider.notifier)
        .sendOtpLinkEmail(email: email);
    if (result) {
      ref.read(pAccountProvider.notifier).setIndexLinkEmailPage(indexPage);
      _otpController.clear();
    } else {
      _showError();
    }
  }

  Future _verifyOTPLinkNewEmail(BuildContext context) async {
    final result = await ref.read(pAccountProvider.notifier).verifyOtpLinkEmail(
        email: _newEmailController.text, otp: _otpController.text);
    if (result) {
      final currentCacheInfo = globalCacheAuth.savedCacheInfo;

      final updatedCacheInfo = AuthSavedInfo(
        token: currentCacheInfo.token,
        email: _newEmailController.text,
        password: currentCacheInfo.password,
        isRemember: currentCacheInfo.isRemember,
      );

      await globalCacheAuth.saveCacheInfoUser(cacheInfo: updatedCacheInfo);
      await ref.read(pAccountProvider.notifier).getme();
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => TripcLinkEmailDialog(
                onTap: () {
                  Navigator.pop(context);
                },
              ));
    } else {
      setState(() {
        showError = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final timeCountDown =
        ref.watch(pAccountProvider.select((value) => value.timeCountDown));
    final isLoading =
        ref.watch(pAccountProvider.select((value) => value.isLoading));
    final currenIndex = ref
        .watch(pAccountProvider.select((value) => value.currentLinkEmailIndex));
    final errorNewEmail =
        ref.watch(pAccountProvider.select((value) => value.errorNewEmail));
    return Stack(
      children: [
        TripcScaffold(
            onPopScope: () =>
                ref.read(pAccountProvider.notifier).resetLinkEmailPage(),
            onLeadingPressed: () {
              ref.read(pAccountProvider.notifier).resetLinkEmailPage();
              Navigator.pop(context);
            },
            hasBackButton: true,
            needUnFocus: true,
            backgroundColor: AppAssets.origin().whiteBackgroundColor,
            titleAppBar: TripcText(
              _pageTitle(currenIndex),
              fontSize: 18,
              textColor: AppAssets.origin().blackColor,
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(vertical: 20.H)
                  .copyWith(bottom: context.spacingBottom),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: PageView(
                      controller: pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        TripcLinkEmailFirstPage(
                          controller: _emailController,
                          button: _button(currenIndex, errorNewEmail, 120.H),
                        ),
                        TripcLinkEmailVerifyOtp(
                          onTapResend: () => _sendOTP(
                              indexPage: 1,
                              email: _emailController.text,
                              isRefresh: true,
                              isResend: true),
                          otpController: _otpController,
                          email: _emailController.text,
                          timeCountDown: timeCountDown,
                          isError: showError,
                          button: _button(currenIndex, errorNewEmail, 120.H),
                        ),
                        TripcLinkNewEmailPage(
                          controller: _newEmailController,
                          errorText: errorNewEmail,
                          button: _button(currenIndex, errorNewEmail, 60.H),
                        ),
                        TripcLinkEmailVerifyOtp(
                            onTapResend: () => _sendOTP(
                                indexPage: 3,
                                email: _newEmailController.text,
                                isRefresh: true,
                                isResend: true),
                            otpController: _otpController,
                            email: _newEmailController.text,
                            timeCountDown: timeCountDown,
                            isError: showError,
                            button: _button(currenIndex, errorNewEmail, 70.H),
                            isHiddenEmail: false,
                            isShowalidCodeTime: true),
                      ],
                    ),
                  ),
                ],
              ),
            )),
        AppLoading(isRequesting: isLoading)
      ],
    );
  }

  Widget _button(int currenIndex, String errorNewEmail, double padding) {
    return Padding(
      padding: EdgeInsets.only(top: padding),
      child: TripcButton(
        onPressed: () async => _onTapButton(index: currenIndex),
        isButtonDisabled:
            isDisableButton(currenIndex, errorNewEmail: errorNewEmail),
        title: _buttonTitle(currenIndex),
        height: 56,
      ),
    );
  }
}
