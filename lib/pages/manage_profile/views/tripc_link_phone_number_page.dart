import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/manage_profile/components/link_phone_successfully_dialog.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_filed.dart';

class TripcLinkPhoneNumberPage extends ConsumerStatefulWidget {
  const TripcLinkPhoneNumberPage({super.key});

  @override
  ConsumerState<TripcLinkPhoneNumberPage> createState() =>
      _TripcLinkPhoneNumberPageState();
}

class _TripcLinkPhoneNumberPageState
    extends ConsumerState<TripcLinkPhoneNumberPage> {
  final _phoneController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final phone = ref.read(pAccountProvider).user?.getPhoneNumber();
      _phoneController.text = phone ?? '';
    });
  }

  @override
  void dispose() {
    super.dispose();
    _phoneController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final verifyMess =
        ref.watch(pAccountProvider.select((value) => value.verifyPhoneNumber));
    final isLoading =
        ref.watch(pAccountProvider.select((value) => value.isLoading));
    return Stack(
      children: [
        TripcScaffold(
            hasBackButton: true,
            needUnFocus: true,
            backgroundColor: AppAssets.origin().whiteBackgroundColor,
            titleAppBar: TripcText(
              context.strings.text_link_phone_number,
              fontSize: 18,
              fontWeight: FontWeight.w500,
              textColor: AppAssets.origin().blackColor,
            ),
            body: Padding(
              padding: EdgeInsets.only(
                  top: 27.H,
                  left: 24.W,
                  right: 24.W,
                  bottom: context.spacingBottom),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TripcText(
                          context.strings.text_link_phone_description,
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          textAlign: TextAlign.left,
                          textColor: AppAssets.origin().darkGreyTextColor,
                          padding: EdgeInsets.only(bottom: 12.H),
                        ),
                        SizedBox(height: 12.H),
                        TripcText(
                          context.strings.text_phone_number,
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                          textColor: AppAssets.origin().darkGreyTextColor,
                          padding: EdgeInsets.only(bottom: 12.H),
                        ),
                        TripcTextField(
                          isError: verifyMess.isNotEmpty,
                          controller: _phoneController,
                          onChanged: (value) => ref
                              .read(pAccountProvider.notifier)
                              .verifyPhoneNumber(context,
                                  phoneNumber: value, setEmpty: true),
                          hintText: context.strings.text_enter_phone_number,
                          hintTextColor: AppAssets.origin().lightGrayDD4,
                          keyboardType: TextInputType.number,
                          fontSize: 14,
                          prefixIcon: Padding(
                            padding: EdgeInsets.only(left: 13.W, right: 24.W),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                TripcText(
                                  '+84',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  textColor: AppAssets.origin().black,
                                  padding: EdgeInsets.only(right: 12.W),
                                ),
                                AppAssets.origin().icBlackRight.widget(
                                    height: 8.H,
                                    color: AppAssets.origin().lightGrayDD4)
                              ],
                            ),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                              vertical: 15.H, horizontal: 12.5.W),
                        ),
                      ],
                    ),
                    TripcText(
                      verifyMess,
                      fontWeight: FontWeight.w300,
                      fontSize: 12,
                      padding: EdgeInsets.only(top: 8.H),
                      textColor: AppAssets.origin().redDotColor,
                      textAlign: TextAlign.start,
                    ),
                    SizedBox(height: 50.H),
                    TripcButton(
                      onPressed: () async {
                        unfocusKeyboard();
                        final isPhoneUpdated = await ref
                            .read(pAccountProvider.notifier)
                            .verifyPhoneNumber(context,
                                phoneNumber: _phoneController.text);
                        if (isPhoneUpdated) {
                          dialogHelpers.show(context, barrierDismissible: false,
                              child: LinkPhoneSuccessfullyDialog(onTap: () {
                            Navigator.pop(context);
                            ref.read(pAccountProvider.notifier).getme();
                            Navigator.pop(context);
                          }));
                        }
                      },
                      title: context.strings.text_continue,
                      height: 56,
                    )
                  ]),
            )),
        AppLoading(isRequesting: isLoading)
      ],
    );
  }
}
