import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tripc_app/models/app/tripc_persistent_tab_type.dart';
import 'package:tripc_app/pages/manage_profile/components/information_account_box.dart';
import 'package:tripc_app/pages/manage_profile/components/link_account_box.dart';
import 'package:tripc_app/pages/manage_profile/components/warning_delete_account_dialog.dart';
import 'package:tripc_app/pages/profile/components/tripc_avatar.dart';
import 'package:tripc_app/pages/tabbar/app_tabbar.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/services/providers/tripc_reset_password.provider.dart';
import 'package:tripc_app/utils/app_error_string.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../../../widgets/commons/app_dialog/tripc_error_dialog.dart';
import '../components/components.dart';

class TripcManageProfilePage extends ConsumerStatefulWidget {
  const TripcManageProfilePage({super.key});

  @override
  ConsumerState<TripcManageProfilePage> createState() =>
      _TripcManageProfileState();
}

class _TripcManageProfileState extends ConsumerState<TripcManageProfilePage> {
  final ImagePicker _picker = ImagePicker();

  Future<void> _requestAndPick(BuildContext context, ImageSource source) async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo? androidInfo =
        Platform.isIOS ? null : await deviceInfo.androidInfo;
    final sdkInt = Platform.isIOS ? 0 : androidInfo?.version.sdkInt ?? 0;
    final permission = source == ImageSource.camera
        ? Permission.camera
        : Platform.isAndroid
            ? sdkInt >= 33
                ? Permission.photos
                : Permission.storage // or Permission.storage for older devices
            : Permission.photos;

    final status = await permission.request();

    if (status.isGranted) {
      await _pickImage(source);
    } else if (status.isDenied || status.isPermanentlyDenied) {
      // Show custom dialog
      _showDialogPermission(context);
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    final pickedFile = await _picker.pickImage(source: source);

    if (pickedFile != null) {
      final File file = File(pickedFile.path);
      final result =
          await ref.read(pAccountProvider.notifier).uploadAvatar(file: file);
      if (result != null) {
        await ref
            .read(pAccountProvider.notifier)
            .updateUserInfo(avatarUrl: result);
      }
    }
  }

  void _showDialogPermission(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.strings.text_permission_required),
        content: Text(context.strings.text_permission_note),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.strings.text_cancel,
                style: const TextStyle(color: Colors.red)),
          ),
          TextButton(
            onPressed: () {
              openAppSettings();
              Navigator.pop(context);
            },
            child: Text(context.strings.text_open_setting),
          ),
        ],
      ),
    );
  }

  void _logOut(BuildContext context, WidgetRef ref) async {
    globalCacheAuth.logout();
    ref
        .read(pAppBottomNavProvider.notifier)
        .setTab(TripCPersistentTabType.home);
    ref.read(pAccountProvider.notifier).resetState();
    Navigator.pop(context);
  }

  void _showCupertinoActionSheet(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: Text(context.strings.text_upload_image_title),
        message: Text(context.strings.text_choose_image_source),
        actions: <CupertinoActionSheetAction>[
          CupertinoActionSheetAction(
            child: Text(context.strings.text_photo,
                style: const TextStyle(color: Colors.blue)),
            onPressed: () {
              Navigator.pop(context);
              _requestAndPick(context, ImageSource.gallery);
            },
          ),
          CupertinoActionSheetAction(
            child: Text(context.strings.text_camera,
                style: const TextStyle(color: Colors.blue)),
            onPressed: () {
              Navigator.pop(context);
              _requestAndPick(context, ImageSource.camera);
            },
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          isDefaultAction: true,
          child: Text(context.strings.text_cancel,
              style: const TextStyle(color: Colors.red)),
          onPressed: () => Navigator.pop(context),
        ),
      ),
    );
  }

  Future<void> _deteleAccount(BuildContext context, WidgetRef re) async {
    final result =
        await ref.read(pAccountProvider.notifier).deleteAccount(context);
    if (result) {
      _logOut(context, ref);
    } else {
      final errorText = ref.read(pAccountProvider).errorMessage;
      dialogHelpers.show(context,
          child: TripcErrorDialog(
              errorText: ErrorParser.getErrorMessage(
                  errorText ?? context.strings.text_error_something_wrong,
                  context)));
    }
  }

  @override
  Widget build(BuildContext context) {
    final loading =
        ref.watch(pAccountProvider.select((value) => value.isLoading));
    final sendOTPLoading =
        ref.watch(pResetPasswordProvider.select((value) => value.loading));
    final avatarUrl =
        ref.watch(pAccountProvider.select((value) => value.user?.avatarUrl));
    return Stack(
      children: [
        TripcScaffold(
            hasBackButton: true,
            visibleAppBar: true,
            backgroundColor: AppAssets.origin().neutralColor,
            body: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 24.W)
                  .copyWith(bottom: context.spacingBottom),
              child: Column(
                children: [
                  TripcAvatar(
                      onTapAvt: () => _showCupertinoActionSheet(context),
                      height: 60,
                      width: 60,
                      hasEditIcon: true,
                      avatarUrl: avatarUrl),
                  SizedBox(
                    height: 10.H,
                  ),
                  const SettingAccountBox(),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 20.H),
                    child: InformationAccountBox(
                        onTapEdit: () => AppRoute.pushNamed(context,
                            routeName: AppRoute.routeInputProfile)),
                  ),
                  const LinkAccountBox(),
                  SizedBox(
                    height: 20.H,
                  ),
                  TripcButton(
                    onPressed: () => _logOut(context, ref),
                    height: 46,
                    title: context.strings.text_log_out,
                    style: const AppButtonStyle(
                        fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  TripcText(
                      onTap: () => dialogHelpers.show(context,
                              child: WarningDeleteAccountDialog(onTap: () {
                            Navigator.pop(context);
                            _deteleAccount(context, ref);
                          })),
                      context.strings.text_delete_my_account,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      textColor: AppAssets.origin().darkBlueColor,
                      padding: EdgeInsets.symmetric(vertical: 16.H)),
                  TripcText(
                    context.strings.text_delete_my_account_note,
                    fontSize: 12,
                    fontWeight: FontWeight.w300,
                    textAlign: TextAlign.center,
                    height: 1.5,
                    textColor: AppAssets.origin().secondDarkGreyTextColor,
                  )
                ],
              ),
            )),
        AppLoading(isRequesting: loading || sendOTPLoading)
      ],
    );
  }
}
