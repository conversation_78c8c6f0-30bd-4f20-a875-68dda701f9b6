import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/gender_enum.dart';
import 'package:tripc_app/pages/manage_profile/providers/tripc_manage_profile_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_check_box/tripc_check_box.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_field_with_label.dart';

class TripcInputInformationPage extends ConsumerStatefulWidget {
  const TripcInputInformationPage({super.key});

  @override
  ConsumerState<TripcInputInformationPage> createState() =>
      _TripcInputInformationPageState();
}

class _TripcInputInformationPageState
    extends ConsumerState<TripcInputInformationPage> {
  late final TextEditingController controller;
  late final TextEditingController cityController;

  @override
  void initState() {
    super.initState();
    controller = TextEditingController(
        text: ref.read(pAccountProvider).user?.fullname ?? '');
    cityController = TextEditingController(
        text: ref.read(pAccountProvider).user?.city ?? '');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(pAccountProvider.notifier).setCurrentGender();
    });
  }

  @override
  void dispose() {
    controller.dispose();
    cityController.dispose();
    super.dispose();
  }

  Future<void> _updateUserInfo() async {
    final result = await ref
        .read(pAccountProvider.notifier)
        .updateUserInfo(fullname: controller.text, city: cityController.text);
    if (result) {
      Navigator.pop(context);
      await globalcontainer.read(pAccountProvider.notifier).getme();
    } else {
      bottomSheetHelpers.show(context,
          child: TripcErrorDialog(
              errorText: ref.read(pAccountProvider).errorMessage ?? ''));
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentGender =
        ref.watch(pAccountProvider.select((value) => value.currentGender));
    final user = ref.watch(pAccountProvider.select((value) => value.user));
    final loading =
        ref.watch(pAccountProvider.select((value) => value.isLoading));

    final errorName =
        ref.watch(pManageProfileProvider.select((value) => value.errorName));

    return Stack(
      children: [
        TripcScaffold(
            onPressed: () => unfocusKeyboard(),
            resizeToAvoidBottomInset: false,
            hasBackButton: true,
            visibleAppBar: true,
            titleAppBar: TripcText(
              context.strings.text_personal_info,
              fontSize: 16,
              fontWeight: FontWeight.w600,
              textColor: AppAssets.origin().black,
            ),
            backgroundColor: AppAssets.origin().neutralColor,
            appBarColor: AppAssets.origin().whiteBackgroundColor,
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.W)
                  .copyWith(top: 24.H, bottom: context.spacingBottom),
              child: Column(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TripcTextFieldWithLabel(
                        textController: controller,
                        onChanged: (value) => ref
                            .read(pManageProfileProvider.notifier)
                            .editName(context, value),
                        label: context.strings.text_display_name,
                        labelColor: AppAssets.origin().black,
                        fontSize: 12,
                        labelFontSize: 12,
                        labelFontWeight: FontWeight.w400,
                        hintText: context.strings.text_full_name,
                        errorText: errorName,
                        showErrorSameLine: false,
                        error: Visibility(
                          visible: errorName.isNotEmpty,
                          replacement: SizedBox(height: 18.H),
                          child: TripcText(
                            errorName,
                            textAlign: TextAlign.start,
                            enableAutoResize: true,
                            textColor: AppAssets.origin().redDotColor,
                            fontSize: 12,
                            fontStyle: FontStyle.italic,
                            padding: EdgeInsets.only(
                                top: 8.H, bottom: 10.H, left: 8.W),
                          ),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                            vertical: 13.H, horizontal: 12.5.W),
                      ),
                      TripcText(
                        '${context.strings.text_gender}:',
                        fontSize: 12,
                        textColor: AppAssets.origin().black,
                        padding: EdgeInsets.only(top: 20.H, bottom: 20.H),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _inputGender(context,
                              onTap: (gender) => ref
                                  .read(pAccountProvider.notifier)
                                  .setCurrentGender(
                                      gender: GenderEnum.male.value),
                              gender: GenderEnum.male,
                              value:
                                  GenderEnum.getByValue(currentGender ?? 3) ==
                                      GenderEnum.male),
                          _inputGender(context,
                              onTap: (gender) => ref
                                  .read(pAccountProvider.notifier)
                                  .setCurrentGender(
                                      gender: GenderEnum.female.value),
                              gender: GenderEnum.female,
                              value:
                                  GenderEnum.getByValue(currentGender ?? 3) ==
                                      GenderEnum.female),
                          _inputGender(context,
                              onTap: (gender) => ref
                                  .read(pAccountProvider.notifier)
                                  .setCurrentGender(
                                      gender: GenderEnum.other.value),
                              gender: GenderEnum.other,
                              value:
                                  GenderEnum.getByValue(currentGender ?? 3) ==
                                      GenderEnum.other),
                        ],
                      ),
                      SizedBox(
                        height: 20.H,
                      ),
                      Divider(
                        color: AppAssets.origin().lightGray,
                        thickness: 1,
                        height: 1,
                      ),
                      SizedBox(
                        height: 20.H,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TripcText(
                            '${context.strings.text_national}:',
                            fontSize: 12,
                            textColor: AppAssets.origin().black,
                          ),
                          Row(
                            children: [
                              TripcText(
                                user?.national ?? '_',
                                fontSize: 12,
                                textColor: AppAssets.origin().gray63Color,
                              ),
                            ],
                          )
                        ],
                      ),
                      SizedBox(
                        height: 20.H,
                      ),
                      Divider(
                        color: AppAssets.origin().lightGray,
                        thickness: 1,
                        height: 1,
                      ),
                      SizedBox(
                        height: 20.H,
                      ),
                      TripcTextFieldWithLabel(
                        textController: cityController,
                        onChanged: (value) => ref
                            .read(pManageProfileProvider.notifier)
                            .editCity(value),
                        label: context.strings.text_city_of_residence,
                        labelColor: AppAssets.origin().black,
                        fontSize: 12,
                        labelFontSize: 12,
                        labelFontWeight: FontWeight.w400,
                        hintText: context.strings.text_hint_input_city,
                        contentPadding: EdgeInsets.symmetric(
                            vertical: 13.H, horizontal: 12.5.W),
                      ),
                    ],
                  ),
                  const Spacer(),
                  TripcButton(
                    onPressed: _updateUserInfo,
                    isButtonDisabled: errorName.isNotEmpty,
                    height: 46,
                    title: context.strings.text_confirm,
                  )
                ],
              ),
            )),
        AppLoading(isRequesting: loading)
      ],
    );
  }

  Widget _inputGender(BuildContext context,
      {required GenderEnum gender,
      required bool value,
      Function(GenderEnum)? onTap}) {
    return Row(
      children: [
        TripcCheckbox(
          size: 16,
          value: value,
          onTap: () => onTap?.call(gender),
          borderRadius: BorderRadius.circular(3.SP),
          borderColor: AppAssets.origin().lightGrayDD4,
          shape: BoxShape.rectangle,
          type: CheckBoxType.checkmark,
        ),
        TripcText(
          gender.title(context),
          fontSize: 12,
          textColor: AppAssets.origin().black,
          padding: EdgeInsets.only(left: 8.W),
        )
      ],
    );
  }
}
