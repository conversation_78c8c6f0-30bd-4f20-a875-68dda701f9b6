import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/gender_enum.dart';
import 'package:tripc_app/models/app/tripc_user.dart';

import '../../../utils/app_validation.dart';

class ManageProfilePageModel {
  ManageProfilePageModel({
    required this.currentUser,
    this.errorName = ''
  });
  final TripcUser currentUser;
  final String errorName;

  static ManageProfilePageModel getDefault() {
    return ManageProfilePageModel(
        currentUser: TripcUser(
            id: 1,
            national: 'Việt Nam',
            city: 'Đà Nẵng',
            email: '<EMAIL>'));
  }

  ManageProfilePageModel copyWith({
    TripcUser? currentUser,
    String? errorName
  }) {
    return ManageProfilePageModel(
      currentUser: currentUser ?? this.currentUser,
      errorName: errorName ?? this.errorName
    );
  }
}

class ManageProfilePageProvider extends StateNotifier<ManageProfilePageModel> {
  ManageProfilePageProvider(super._state);

  void editName(BuildContext context,String value) {
    state = state.copyWith(
        currentUser: state.currentUser.copyWith(fullName: value),
        errorName: ValidationAccount.fullNameValidation(context, value));
  }

  void editCity(String value) {
    state =
        state.copyWith(currentUser: state.currentUser.copyWith(city: value));
  }

  void editGender(GenderEnum gender) {
    state = state.copyWith(
        currentUser: state.currentUser.copyWith(gender: gender.value));
  }

  void resetState() {
    state = ManageProfilePageModel.getDefault();
  }
}

final pManageProfileProvider =
    StateNotifierProvider<ManageProfilePageProvider, ManageProfilePageModel>(
        (ref) =>
            ManageProfilePageProvider(ManageProfilePageModel.getDefault()));
