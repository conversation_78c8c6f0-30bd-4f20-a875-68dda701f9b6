import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/constants/error_text_const.dart';
import 'package:tripc_app/widgets/commons/tripc_otp_text_field/tripc_otp_text_field.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcLinkEmailVerifyOtp extends StatelessWidget {
  const TripcLinkEmailVerifyOtp(
      {super.key,
      required this.otpController,
      required this.email,
      required this.timeCountDown,
      this.onTapResend,
      this.isError = false,
      required this.button,
      this.isHiddenEmail = true,
      this.isShowalidCodeTime = false});
  final TextEditingController otpController;
  final String email;
  final int timeCountDown;
  final VoidCallback? onTapResend;
  final bool isError;
  final Widget button;
  final bool isHiddenEmail;
  final bool isShowalidCodeTime;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
              text: TextSpan(children: [
            TextSpan(
              text: context.strings.text_we_have_sent_code_to_email,
              style: AppAssets.init.mediumTextStyle.copyWith(
                  fontSize: 14.SP,
                  color: AppAssets.origin().darkGreyTextColor,
                  fontWeight: FontWeight.w400),
            ),
            const TextSpan(
              text: ' ',
            ),
            WidgetSpan(
                child: TripcText(isHiddenEmail ? email.hiddenEmail : email,
                    fontSize: 14.SP, textColor: AppAssets.origin().blackColor)),
            TextSpan(
              text: '. ',
              style: AppAssets.init.mediumTextStyle.copyWith(
                  fontSize: 14.SP,
                  color: AppAssets.origin().blackColor,
                  fontWeight: FontWeight.w400),
            ),
            TextSpan(
              text: context.strings.text_pls_check_inbox_email,
              style: AppAssets.init.mediumTextStyle.copyWith(
                  fontSize: 14.SP,
                  color: AppAssets.origin().darkGreyTextColor,
                  fontWeight: FontWeight.w400),
            ),
          ])),
          Visibility(
            visible: isShowalidCodeTime,
            child: Padding(
              padding: EdgeInsets.only(top: 8.H),
              child: TripcText(
                  context.strings.text_valid_verification_code_for_5_mins,
                  textAlign: TextAlign.start,
                  fontSize: 12,
                  textColor: AppAssets.origin().darkGreyTextColor),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 30.H, bottom: 10.H),
            child: TripcOtpTextField(
              controller: otpController,
              isError: isError,
            ),
          ),
          Visibility(
            visible: isError,
            child: TripcText(
              ErrorMessage.wrongOtp(context),
              fontSize: 12,
              fontStyle: FontStyle.italic,
              textColor: AppAssets.origin().redDotColor,
              padding: EdgeInsets.only(bottom: 12.H),
            ),
          ),
          SizedBox(
            height: 12.H,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              TripcText(context.strings.text_have_not_receive_otp,
                  fontSize: 14,
                  textColor: AppAssets.origin().darkGreyTextColor),
              Visibility(
                visible: timeCountDown > 0,
                replacement: TripcText(
                    onTap: onTapResend,
                    context.strings.text_resend,
                    fontSize: 14,
                    textColor: AppAssets.origin().darkBlue5FF),
                child: TripcText(
                    '${context.strings.text_resend} (${timeCountDown}s)',
                    fontSize: 14,
                    textColor: AppAssets.origin().darkGreyTextColor),
              ),
            ],
          ),
          button
        ],
      ),
    );
  }
}
