import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_field_with_label.dart';

class TripcLinkNewEmailPage extends StatelessWidget {
  const TripcLinkNewEmailPage(
      {super.key,
      required this.controller,
      this.errorText,
      required this.button});
  final TextEditingController controller;
  final String? errorText;
  final Widget button;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TripcText(
            context.strings.text_pls_enter_a_new_email,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            height: 1.5,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().darkGreyTextColor,
            padding: EdgeInsets.only(bottom: 38.H),
          ),
          TripcTextFieldWithLabel(
            textController: controller,
            errorText: errorText,
            showErrorSameLine: false,
            space: 12.H,
            label: context.strings.text_email,
            hintText: context.strings.text_enter_email,
            borderColor: AppAssets.origin().lightGrayDD4,
            labelFontSize: 14,
            fontSize: 14,
            contentPadding:
                EdgeInsets.symmetric(vertical: 13.H, horizontal: 20.W),
          ),
          TripcText(
            errorText,
            fontSize: 10,
            fontWeight: FontWeight.w500,
            fontStyle: FontStyle.italic,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().redDotColor,
            padding: EdgeInsets.only(top: 9.H),
          ),
          button
        ],
      ),
    );
  }
}
