import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_field_with_label.dart';

class TripcLinkEmailFirstPage extends StatelessWidget {
  const TripcLinkEmailFirstPage({super.key, required this.controller, required this.button});
  final TextEditingController controller;
  final Widget button;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TripcText(
            context.strings.text_warning_identify_email_link,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            height: 1.5,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().darkGreyTextColor,
            padding: EdgeInsets.only(bottom: 20.H),
          ),
          TripcTextFieldWithLabel(
            textController: controller,
            readOnly: true,
            space: 12.H,
            label: context.strings.text_email,
            hintText: context.strings.text_enter_email,
            borderColor: AppAssets.origin().darkGreyTextColor,
            labelFontSize: 14,
            fontSize: 14,
            contentPadding:
                EdgeInsets.symmetric(vertical: 13.H, horizontal: 20.W),
          ),
          button
        ],
      ),
    );
  }
}
