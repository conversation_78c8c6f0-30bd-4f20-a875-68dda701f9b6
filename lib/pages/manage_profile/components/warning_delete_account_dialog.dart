import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class WarningDeleteAccountDialog extends StatelessWidget {
  const WarningDeleteAccountDialog({super.key, this.onTap});
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return TripcDialog(
        onTap: onTap,
        title: context.strings.text_as_u_know_delete_account,
        contentPadding:
            EdgeInsets.only(top: 22.H, bottom: 24.H, left: 12.W, right: 16.W),
        titleButton: context.strings.text_agree,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 12.H),
              child: _infoLine(
                  info: context.strings.text_delete_account_dialog_note_1),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 4.H),
              child: _infoLine(
                  info: context.strings.text_delete_account_dialog_note_2),
            ),
            _infoLine(info: context.strings.text_delete_account_dialog_note_3),
            Padding(
              padding: EdgeInsets.only(top: 4.H),
              child: _infoLine(
                  info: context.strings.text_delete_account_dialog_note_4),
            ),
            TripcText(
              context.strings.text_delete_account_dialog_note_5,
              fontSize: 14,
              fontWeight: FontWeight.w300,
              height: 1.5,
              textAlign: TextAlign.start,
              textColor: AppAssets.origin().secondDarkGreyTextColor,
              padding: EdgeInsets.only(top: 13.H, bottom: 26.H),
            )
          ],
        ));
  }

  Widget _infoLine({required String info}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // TripcText(
        //   '\u2022',
        //   fontSize: 10,
        //   fontWeight: FontWeight.w400,
        //   textColor: AppAssets.origin().black,
        //   padding: EdgeInsets.symmetric(horizontal: 8.W),
        // ),
        Expanded(
          child: TripcText(
            info,
            fontSize: 14,
            fontWeight: FontWeight.w300,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().black,
          ),
        )
      ],
    );
  }
}
