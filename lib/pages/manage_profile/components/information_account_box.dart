import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/gender_enum.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'components.dart';

class InformationAccountBox extends ConsumerWidget {
  const InformationAccountBox({super.key, this.onTapEdit});
  final VoidCallback? onTapEdit;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(pAccountProvider.select((value) => value.user));
    return InfoBoxProfileShadow(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _header(context),
        _infoArea(
          context,
          name: user?.fullname,
          national: user?.national,
          city: user?.city,
          gender: user?.gender,
        )
      ],
    ));
  }

  Widget _header(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TripcText(
              context.strings.text_personal_info,
              fontSize: 16,
              textColor: AppAssets.origin().blackColor,
            ),
            TripcIconButton(
              onPressed: onTapEdit,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  AppAssets.origin().icEditPen.widget(
                      height: 12.H,
                      width: 12.H,
                      color: AppAssets.origin().darkBlueColor),
                  Padding(
                    padding: EdgeInsets.only(left: 6.W),
                    child: Text(
                      context.strings.text_edit,
                      style: AppAssets.origin().normalTextStyle.copyWith(
                          color: AppAssets.origin().darkBlueColor,
                          fontSize: 12.SP),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
        SizedBox(
          height: 12.H,
        ),
        Divider(
            height: 0, thickness: 1.H, color: AppAssets.origin().grayDivider)
      ],
    );
  }

  Widget _infoArea(BuildContext context,
      {String? name, String? national, int? gender, String? city}) {
    return Column(
      children: [
        SingleLineInfo(
            title: context.strings.text_display_name,
            child: result(value: name)),
        SingleLineInfo(
            title: context.strings.text_gender,
            child: result(
                value: GenderEnum.getByValue(gender ?? 1).title(context))),
        SingleLineInfo(
            title: context.strings.text_national,
            child: result(value: national)),
        SingleLineInfo(
          title: context.strings.text_city_of_residence,
          hasStroke: false,
          child: result(value: city),
        ),
      ],
    );
  }

  Widget result({required String? value}) {
    return Visibility(
      visible: value.isNotNull,
      replacement: Container(
        width: 11.H,
        height: 1.H,
        color: AppAssets.origin().gray63Color,
      ),
      child: TripcText(
        value,
        fontSize: 12,
        textCase: TextCaseType.none,
        textColor: AppAssets.origin().gray63Color,
      ),
    );
  }
}
