import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/login_sns_enum.dart';
import 'package:tripc_app/models/remote/user_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/services/sns/apple_login/apple_login.dart';
import 'package:tripc_app/services/sns/facebook_login/facebook_login.dart';
import 'package:tripc_app/services/sns/google_login/google_login.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'components.dart';

class LinkAccountBox extends ConsumerStatefulWidget {
  const LinkAccountBox({super.key});

  @override
  ConsumerState<LinkAccountBox> createState() => _LinkAccountBoxState();
}

class _LinkAccountBoxState extends ConsumerState<LinkAccountBox> {
  void _unLinkSocial({required String provider}) async {
    final result = await ref
        .read(pAccountProvider.notifier)
        .unLinkSocial(provider: provider);
    if (!result) {
      dialogHelpers.show(context,
          child: TripcErrorDialog(
              errorText: ref.read(pAccountProvider).errorMessage ?? ''));
    }
  }

  Future<void> _linkSocial(
      {required TripCLoginSns type, required String token}) async {
    if (token.isEmpty) return;
    final result = await ref
        .read(pAccountProvider.notifier)
        .linkSocial(provider: type.providerValue, token: token);
    if (!result) {
      dialogHelpers.show(context,
          child: TripcErrorDialog(
              errorText: ref.read(pAccountProvider).errorMessage ?? ''));
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(pAccountProvider.select((value) => value.user));
    return InfoBoxProfileShadow(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [_header(context), _infoArea(ref, context, user: user)],
    ));
  }

  Widget _header(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_account_linking,
          fontSize: 16,
          textColor: AppAssets.origin().blackColor,
          padding: EdgeInsets.only(bottom: 8.H),
        ),
        TripcText(
          context.strings.text_account_linking_description,
          fontSize: 11,
          fontWeight: FontWeight.w300,
          textColor: AppAssets.origin().darkGreyTextColor,
          textAlign: TextAlign.start,
          padding: EdgeInsets.only(bottom: 16.H),
        ),
        Divider(
            height: 0, thickness: 1.H, color: AppAssets.origin().grayDivider)
      ],
    );
  }

  Widget _infoArea(WidgetRef ref, BuildContext context, {UserResponse? user}) {
    return Column(
      children: [
        SingleLineInfo(
            title: '',
            titleBuilder: _accountType(
                icon: AppAssets.origin().iconGoogle, name: 'Google'),
            child: user?.isLinkedProvider('Google') ?? false
                ? _untLinkButton(
                    onPressed: () => _unLinkSocial(provider: 'Google'),
                  )
                : _linkButton(onPressed: () async {
                    final result = await GoogleServiceLogin().signIn();
                    _linkSocial(
                        type: TripCLoginSns.google, token: result ?? '');
                  })),
        SingleLineInfo(
            title: '',
            titleBuilder: _accountType(
                icon: AppAssets.origin().iconFacebook, name: 'Facebook'),
            child: user?.isLinkedProvider('Facebook') ?? false
                ? _untLinkButton(
                    onPressed: () => _unLinkSocial(provider: 'Facebook'))
                : _linkButton(onPressed: () async {
                    final String? result;
                    if (Platform.isIOS) {
                      result = await FacebookServiceLogin().signIn();
                    } else {
                      result = await FacebookServiceLogin().signInWithAndroid();
                    }
                    _linkSocial(
                        type: TripCLoginSns.facebook, token: result ?? '');
                  })),
        if (Platform.isIOS)
          SingleLineInfo(
              title: '',
              hasStroke: false,
              titleBuilder: _accountType(
                  icon: AppAssets.origin().iconApple,
                  height: 28,
                  name: 'Apple'),
              child: user?.isLinkedProvider('Apple') ?? false
                  ? _untLinkButton(
                      onPressed: () => _unLinkSocial(provider: 'Apple'))
                  : _linkButton(
                      onPressed: () async {
                        final result = await AppleServiceLogin().signIn();
                        _linkSocial(
                            type: TripCLoginSns.apple, token: result ?? '');
                      },
                    )),
      ],
    );
  }

  Widget _linkButton({VoidCallback? onPressed}) {
    return Builder(builder: (context) {
      return TripcButton(
          onPressed: onPressed,
          title: context.strings.text_link,
          height: 30.H,
          width: 70.W,
          style: const AppButtonStyle(
              radius: 4, fontSize: 12, fontWeight: FontWeight.w500));
    });
  }

  Widget _untLinkButton({VoidCallback? onPressed}) {
    return Builder(builder: (context) {
      return TripcButton(
          onPressed: onPressed,
          title: context.strings.text_unlink,
          height: 20.H,
          width: 79.W,
          buttonType: ButtonType.outline,
          style: AppButtonStyle(
              radius: 4,
              fontSize: 10,
              fontWeight: FontWeight.w500,
              borderColor: AppAssets.origin().darkBlueColor,
              textColor: AppAssets.origin().darkBlueColor));
    });
  }

  Widget _accountType(
      {required AppAssetBuilder icon,
      required String name,
      int height = 24,
      int width = 24}) {
    return Row(
      children: [
        SizedBox(
            height: height.H,
            width: width.H,
            child: icon.widget(fit: BoxFit.cover)),
        TripcText(
          name,
          fontSize: 12,
          padding: EdgeInsets.only(left: 20.W),
          textAlign: TextAlign.start,
        )
      ],
    );
  }
}
