import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcResetPasswordSuccessDialog extends StatelessWidget {
  const TripcResetPasswordSuccessDialog({super.key, this.onTap});
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return TripcDialog(
        onTap: onTap ?? () => Navigator.pop(context),
        insetPadding: EdgeInsets.symmetric(horizontal: 16.W)
            .copyWith(bottom: context.mediaQuery.size.height / 2 - 350.H),
        type: TripcDialogType.success,
        title: context.strings.text_password_reset_successfully,
        titleButton: context.strings.text_agree,
        contentPadding: EdgeInsets.symmetric(vertical: 22.H, horizontal: 24.W),
        child: Padding(
          padding: EdgeInsets.only(top: 12.H, bottom: 26.H),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TripcText(
                context.strings.text_use_the_password_you_just_set,
                fontSize: 14,
                fontWeight: FontWeight.w300,
                textAlign: TextAlign.center,
                textColor: AppAssets.origin().black,
              ),
            ],
          ),
        ));
  }
}
