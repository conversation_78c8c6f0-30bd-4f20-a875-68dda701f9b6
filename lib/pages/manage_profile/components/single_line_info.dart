import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class SingleLineInfo extends StatelessWidget {
  const SingleLineInfo(
      {super.key,
      required this.title,
      required this.child,
      this.titleBuilder,
      this.padding,
      this.onTap,
      this.hasStroke = true});
  final String title;
  final Widget child;
  final bool hasStroke;
  final Widget? titleBuilder;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;
  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
      onPressed: onTap,
      child: Container(
        padding: padding ?? EdgeInsets.symmetric(vertical: 13.H),
        decoration: BoxDecoration(
            border: Border(
                bottom: !hasStroke
                    ? BorderSide.none
                    : BorderSide(
                        width: 1.H, color: AppAssets.origin().grayDivider))),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            titleBuilder ??
                TripcText(title,
                    ignorePointer: true,
                    fontSize: 12,
                    textColor: AppAssets.origin().black),
            SizedBox(width: 12.W),
            child
          ],
        ),
      ),
    );
  }
}
