import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/services/providers/tripc_reset_password.provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'components.dart';

class SettingAccountBox extends ConsumerWidget {
  const SettingAccountBox({super.key});

  Future<void> _sendOtp(BuildContext context, WidgetRef ref) async {
    final result =
        await ref.read(pResetPasswordProvider.notifier).sendOtp(context);
    if (result) {
      AppRoute.pushNamed(context, routeName: AppRoute.routeTripcVerifyOtp);
    } else {
      final errorText = ref.read(pResetPasswordProvider).errorMessage;
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorText));
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(pAccountProvider.select((value) => value.user));
    return InfoBoxProfileShadow(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _header(context),
        _infoArea(context, ref, email: user?.email ?? '', phone: user?.phone),
      ],
    ));
  }

  Widget _header(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_account_settings,
          fontSize: 16,
          textColor: AppAssets.origin().blackColor,
          padding: EdgeInsets.only(bottom: 11.H),
        ),
        Divider(
            height: 0, thickness: 1.H, color: AppAssets.origin().grayDivider)
      ],
    );
  }

  Widget _infoArea(BuildContext context, WidgetRef ref,
      {required String email, required String? phone}) {
    return Column(
      children: [
        SingleLineInfo(
            onTap: () => AppRoute.pushNamed(context,
                routeName: AppRoute.routeTripcLinkEmail),
            title: context.strings.text_linked_email,
            child: Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: TripcText(
                      email,
                      fontSize: 12,
                      ignorePointer: true,
                      textAlign: TextAlign.end,
                      textColor: AppAssets.origin().gray63Color,
                      padding: EdgeInsets.only(right: 13.W),
                    ),
                  ),
                  AppAssets.origin()
                      .icArrowRightGray
                      .widget(height: 16.H, width: 16.H)
                ],
              ),
            )),
        SingleLineInfo(
            onTap: () => AppRoute.pushNamed(context,
                routeName: AppRoute.routeTripcLinkPhoneNumber),
            title: context.strings.text_linked_phone,
            child: phone.isNotNull
                ? Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: TripcText(
                            phone ?? '',
                            fontSize: 12,
                            ignorePointer: true,
                            textAlign: TextAlign.end,
                            textColor: AppAssets.origin().gray63Color,
                            padding: EdgeInsets.only(right: 13.W),
                          ),
                        ),
                        AppAssets.origin()
                            .icArrowRightGray
                            .widget(height: 16.H, width: 16.H)
                      ],
                    ),
                  )
                : TripcButton(
                    onPressed: () => AppRoute.pushNamed(context,
                        routeName: AppRoute.routeTripcLinkPhoneNumber),
                    title: context.strings.text_add_phone_number,
                    height: 30.H,
                    width: 130.W,
                    style: const AppButtonStyle(
                        radius: 4, fontSize: 12, fontWeight: FontWeight.w500))),
        SingleLineInfo(
            onTap: () {
              ref.read(pResetPasswordProvider.notifier).resetState();
              ref
                  .read(pResetPasswordProvider.notifier)
                  .setEmailGetOtp(context, globalCacheAuth.user?.email ?? '');
              _sendOtp(context, ref);
            },
            title: context.strings.text_reset_password,
            hasStroke: false,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TripcText(
                  '********',
                  fontSize: 12.SP,
                  height: 1.4,
                  textColor: AppAssets.origin().gray63Color,
                  padding: EdgeInsets.only(right: 13.W),
                  ignorePointer: true,
                ),
                AppAssets.origin()
                    .icArrowRightGray
                    .widget(height: 16.H, width: 16.H)
              ],
            )),
      ],
    );
  }
}
