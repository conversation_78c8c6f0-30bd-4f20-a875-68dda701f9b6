import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

class InfoBoxProfileShadow extends StatelessWidget {
  const InfoBoxProfileShadow({super.key, required this.child, this.padding});
  final Widget child;
  final EdgeInsetsGeometry? padding;
  @override
  Widget build(BuildContext context) {
    return Container(
        padding: padding ?? EdgeInsets.all(12.H),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.SP),
            color: AppAssets.origin().whiteBackgroundColor,
            boxShadow: [
              AppAssets.origin()
                  .infoBoxShadow
                  .copyWith(color: AppAssets.origin().black10)
            ]),
        child: child);
  }
}
