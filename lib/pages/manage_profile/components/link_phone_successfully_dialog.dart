import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';

class LinkPhoneSuccessfullyDialog extends StatelessWidget {
  const LinkPhoneSuccessfullyDialog({super.key, this.onTap});
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return TripcDialog(
      onTap: onTap ?? () => Navigator.pop(context),
      type: TripcDialogType.success,
      title: context.strings.text_link_phone_successfully,
      contentPadding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
      titleButton: context.strings.text_agree,
    );
  }
}
