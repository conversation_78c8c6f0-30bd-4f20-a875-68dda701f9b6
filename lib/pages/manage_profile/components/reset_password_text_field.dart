import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_field_with_label.dart';

class TripcResetPasswordTextField extends StatefulWidget {
  const TripcResetPasswordTextField(
      {super.key,
      required this.value,
      required this.label,
      required this.hintText,
      this.onChanged,
      this.errorText,
      this.controller});
  final bool value;
  final Function(String)? onChanged;
  final String? errorText;
  final String label;
  final String hintText;
  final TextEditingController? controller;

  @override
  State<TripcResetPasswordTextField> createState() =>
      _TripcResetPasswordTextFieldState();
}

class _TripcResetPasswordTextFieldState
    extends State<TripcResetPasswordTextField> {
  late bool _value;
  @override
  void initState() {
    super.initState();
    _value = widget.value;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcTextFieldWithLabel(
          textController: widget.controller,
          space: 12,
          showErrorSameLine: false,
          onChanged: widget.onChanged,
          errorText: widget.errorText,
          obscureText: _value,
          suffix: TripcIconButton(
              onPressed: () {
                setState(() {
                  _value = !_value;
                });
              },
              child: Padding(
                padding: EdgeInsets.only(right: 12.W),
                child: AppAssets.init.iconEye.widget(
                    height: 24.H,
                    width: 24.H,
                    color: _value
                        ? AppAssets.origin().secondaryColor
                        : AppAssets.origin()
                            .secondDarkGreyTextColor
                            .withValues(alpha: 0.5)),
              )),
          label: widget.label,
          labelFontSize: 16,
          fontSize: 14,
          labelColor: AppAssets.origin().darkGreyTextColor,
          hintText: widget.hintText,
          contentPadding:
              EdgeInsets.symmetric(vertical: 17.H, horizontal: 19.W),
          spaceBottom: 32,
          error: Visibility(
            visible: (widget.errorText ?? '').isNotEmpty,
            replacement: SizedBox(height: 32.H),
            child: Row(
              children: [
                Expanded(
                  child: TripcText(
                    widget.errorText ?? '',
                    textColor: AppAssets.origin().redDotColor,
                    textAlign: TextAlign.start,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    fontStyle: FontStyle.italic,
                    padding: EdgeInsets.only(
                        top: 8.H, bottom: 18.H, left: 8.W, right: 8.W),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
