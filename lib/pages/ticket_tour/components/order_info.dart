import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class OrderInfo extends StatelessWidget {
  final String title;

  final List<String> contents;
  final VoidCallback? onTapDetail;
  final Color? backgroundColor;

  const OrderInfo({
    super.key,
    required this.title,
    required this.contents,
    this.onTapDetail,
    this.backgroundColor
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16.W).copyWith(bottom: 16.H),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppAssets.origin().cardColorTourV2,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 9.W,
        vertical: 20.H,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                title,
                fontSize: 16,
                fontWeight: FontWeight.w700,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
              ),
              if (onTapDetail != null)
                Padding(
                  padding: EdgeInsets.only(right: 6.W),
                  child: TripcIconButton(
                    onPressed: onTapDetail,
                    child: TripcText(
                      context.strings.text_detail,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      textAlign: TextAlign.start,
                      textColor: AppAssets.origin().darkBlueColor,
                      ignorePointer: true,
                    ),
                  ),
                )
            ],
          ),
          SizedBox(height: 8.H),
          // Render each content string as a bullet point
          for (int i = 0; i < contents.length; i++) ...[
            _buildBulletText(context, contents[i]),
            if (i < contents.length - 1) SizedBox(height: 4.H),
          ],
        ],
      ),
    );
  }

  /// Helper to build a single bullet point row
  Widget _buildBulletText(BuildContext context, String content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          '\u2022',
          fontSize: 14,
          fontWeight: FontWeight.w400,
          textAlign: TextAlign.start,
          padding: EdgeInsets.only(left: 10.W),
          textColor: AppAssets.origin().blackColor,
        ),
        Expanded(
          child: TripcText(
            content,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            textAlign: TextAlign.start,
            padding: EdgeInsets.only(left: 10.W),
            textColor: AppAssets.origin().blackColor,
          ),
        ),
      ],
    );
    return TripcRichText(
      text: '',
      textAlign: TextAlign.start,
      children: [
        TextSpan(
          text: '\t\t\t\u2022  ',
          style: AppAssets.origin().normalTextStyle.copyWith(
                fontSize: 14.SP,
                fontWeight: FontWeight.w400,
                color: AppAssets.origin().blackColor,
              ),
        ),
        TextSpan(
          text: content,
          style: AppAssets.origin().normalTextStyle.copyWith(
                fontSize: 14.SP,
                fontWeight: FontWeight.w400,
                overflow: TextOverflow.ellipsis,
                color: AppAssets.origin().blackColor,
              ),
        ),
      ],
    );
  }
}
