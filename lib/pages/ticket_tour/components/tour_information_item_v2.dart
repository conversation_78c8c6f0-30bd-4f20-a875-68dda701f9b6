import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

class TourInformationItemV2 extends StatelessWidget {
  const TourInformationItemV2(
      {super.key,
      required this.title,
      required this.value,
      this.padding,
      this.fontWeight,
      this.fontSize = 14,
      this.fontStyle});

  final String title;
  final String value;
  final EdgeInsets? padding;
  final double fontSize;
  final FontWeight? fontWeight;
  final FontStyle? fontStyle;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            '$title:',
            style: AppAssets.origin().normalTextStyle.copyWith(
                  fontSize: fontSize.SP,
                  fontWeight: FontWeight.w500,
                  color: AppAssets.origin().black,
                ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: AppAssets.origin().mediumTextStyle.copyWith(
                  fontSize: fontSize.SP,
                  fontWeight: fontWeight,
                  color: AppAssets.origin().black,
                ),
          ),
        ),
      ],
    );
  }
}
