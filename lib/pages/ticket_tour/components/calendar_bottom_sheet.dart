import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_calendar/tripc_calendar.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcCalendarBottomSheet extends StatelessWidget {
  const TripcCalendarBottomSheet(
      {super.key,
      required this.selectedDay,
      this.onSelectDate,
      this.canSelectRange = false,
      this.startDate,
      this.endDate});
  final DateTime selectedDay;
  final bool canSelectRange;
  final Function(DateTime)? onSelectDate;
  final DateTime? startDate;
  final DateTime? endDate;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppAssets.origin().neutralColor,
      height: 493.H,
      padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 22.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 6.W),
            child: TripcIconButton(
              onPressed: () => Navigator.pop(context),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                      height: 24.H,
                      width: 24.H,
                      child: Center(
                          child:
                              AppAssets.origin().icClose.widget(height: 13.H))),
                  TripcText(
                    context.strings.text_choose_a_tour_date,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    textColor: AppAssets.origin().blackColor,
                  ),
                  SizedBox(
                    width: 24.H,
                  )
                ],
              ),
            ),
          ),
          SizedBox(
            height: 25.H,
          ),
          TripcCalendar(
            onSelectDate: onSelectDate,
            selectedDay: selectedDay,
            canSelectRange: canSelectRange,
            startDate: startDate,
            endDate: endDate,
          )
        ],
      ),
    );
  }
}
