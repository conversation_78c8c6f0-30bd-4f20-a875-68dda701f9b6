import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/remote/api_tour_response/service_type_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/homepage/components/service_item_v2.dart';
import 'package:tripc_app/pages/homepage/providers/tripc_homepage_provider.dart';
import 'package:tripc_app/pages/profile/components/tripc_not_yet_login_dialog.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider_v2.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/status_display_notifier.dart';
import 'package:tripc_app/utils/app_enum.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/tour_shimmer_loading/tour_shimmer_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/commons/view_all_row/view_all_row_v2.dart';

import '../../../models/app/tripc_service_category.dart';

class TripcTourTicketBodyViewV2 extends ConsumerStatefulWidget {
  const TripcTourTicketBodyViewV2({
    super.key,
    this.category = TripCServiceCategory.combo,
  });

  final TripCServiceCategory category;

  @override
  ConsumerState<TripcTourTicketBodyViewV2> createState() =>
      _TripcTourTicketBodyViewState();
}

class _TripcTourTicketBodyViewState
    extends ConsumerState<TripcTourTicketBodyViewV2> {
  final _gridViewScrollController = ScrollController();
  final _gridViewScrollControllerSearch = ScrollController();
  bool loadingData = false;
  final String ferrySlugCategory = 'pha';
  bool isAll = true;

  List<ServiceType> listServiceType = [];

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      forceLoading(true);
      ref.read(pTicketTourProviderV2.notifier).clearCacheData();
      listServiceType = ref.watch(
          pHomepageScreenProvider.select((value) => value.listServiceType));
      ref
          .read(pTicketTourProviderV2.notifier)
          .setPageGetData(page: 1, pageSize: 4);

      _loadServiceTypeTours(
          widget.category.getServiceTypeId(listServiceType) ?? 1);

      // Init data all by parent id defaultCategoryId;
      await ref.read(pTicketTourProviderV2.notifier).getToursByServiceType(
            widget.category.getServiceTypeId(listServiceType) ?? 1,
            ListDataRequest(page: 1, pageSize: 4),
          );

      // Loading my recent tour
      await ref.read(pTicketTourProviderV2.notifier).getMyRecentleTour();
      forceLoading(false);
    });
  }

  void forceLoading(bool value) {
    if (mounted) {
      setState(() {
        loadingData = value;
      });
    }
  }

  Future<void> _loadServiceTypeTours(int serviceTypeId) async {
    await ref
        .read(pTicketTourProviderV2.notifier)
        .getServiceTypesList(serviceTypeId);
  }

  void _onSelectType(ServiceType t) {
    final notifier = ref.read(pTicketTourProviderV2.notifier);
    notifier.onChangeServiceType(t);
    setState(() {
      isAll = t.id == null;
    });
    if (isAll) {
      notifier.getToursByServiceType(
        widget.category.getServiceTypeId(listServiceType) ?? 1,
        ListDataRequest(page: 1, pageSize: 4),
      );
    } else if (t.slug == ferrySlugCategory) {
      notifier.getTourTuanChauCatBa(
        ListDataRequest(page: 1, pageSize: 4),
      );
      notifier.getTourCatBaTuanChau(
        ListDataRequest(page: 1, pageSize: 4),
      );
    } else {
      notifier.getToursByServiceType(
        t.id ?? 0,
        ListDataRequest(page: 1, pageSize: 4),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final types = [
      ServiceType(name: context.strings.text_all),
      ...ref.watch(pTicketTourProviderV2.select((s) => s.listServiceTypes))
    ];

    final selectedServiceType =
        ref.watch(pTicketTourProviderV2.select((value) => value.serviceType));
    if (widget.category == TripCServiceCategory.entertainment) {
      if (types.length > 2) {
        types.removeLast();
      }
    }
    if (widget.category == TripCServiceCategory.moving) {
      if (types.length > 2) {
        types.removeRange(2, types.length);
        setState(() {});
      }
    }

    final isSearching =
        ref.watch(pTicketTourProviderV2.select((value) => value.isSearching));

    return Column(
      children: [
        if (!isSearching)
          SizedBox(
            height: 40.H,
            child: ListView.separated(
              padding: EdgeInsets.symmetric(horizontal: 16.W),
              scrollDirection: Axis.horizontal,
              itemCount: types.length,
              separatorBuilder: (_, __) => SizedBox(width: 24.W),
              itemBuilder: (context, index) {
                final t = types[index];
                final isSel = types[index].id == selectedServiceType.id;
                return GestureDetector(
                  onTap: () => _onSelectType(t),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 9.W),
                    margin: EdgeInsets.symmetric(vertical: 7.H),
                    decoration: BoxDecoration(
                        border: Border(
                      bottom: BorderSide(
                        color: isSel
                            ? AppAssets.origin().primaryColorV2
                            : Colors.transparent,
                        width: 1.0,
                      ),
                    )),
                    child: Text(
                      t.name,
                      style: TextStyle(
                        fontSize: 16.SP,
                        fontWeight: FontWeight.w500,
                        color: isSel
                            ? AppAssets.origin().primaryColorV2
                            : AppAssets.origin().disableColorV2,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        _getBody()
      ],
    );
  }

  Widget _getBody() {
    final searchList =
        ref.watch(pTicketTourProviderV2.select((value) => value.searchList));

    final isSearching =
        ref.watch(pTicketTourProviderV2.select((value) => value.isSearching));

    if (isSearching) {
      if (searchList.isEmpty) {
        return Expanded(child: _searchNotFoundWidget());
      } else {
        return _searchView();
      }
    }
    return _viewMain();
  }

  Widget _searchView() {
    final selectedServiceType =
        ref.watch(pTicketTourProviderV2.select((value) => value.serviceType));
    final searchList =
        ref.watch(pTicketTourProviderV2.select((value) => value.searchList));
    final isLoading =
        ref.watch(pTicketTourProviderV2.select((value) => value.isLoading));
    return Expanded(
      child: Visibility(
        visible: !isLoading,
        replacement: const ListTourGridShimmerLoading(),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.W).copyWith(top: 20.H),
          child: GridView.builder(
            controller: _gridViewScrollControllerSearch,
            itemCount: searchList.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                childAspectRatio: 185.W / 263.H,
                crossAxisCount: 2,
                mainAxisSpacing: 16.H,
                crossAxisSpacing: 16.W),
            itemBuilder: (context, index) => ServiceItemV2(
              onTap: () {
                AppRoute.pushNamed(
                  context,
                  routeName: AppRoute.routeTourDetailViewV2,
                  arguments: {
                    'id': searchList[index].id,
                    'isFerryTour': selectedServiceType.slug == ferrySlugCategory,
                    'listType': ListType.search
                  },
                );
              },
              service: searchList[index],
              onTapLike: () {
                if (globalCacheAuth.isLogged()) {
                  ref
                      .read(pTicketTourProviderV2.notifier)
                      .listSavedTour(searchList[index].id ?? 0, ListType.search);
                } else {
                  dialogHelpers.show(context, child: const NotYetLoginDialog());
                }
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _viewMain() {
    final selectedServiceType =
        ref.watch(pTicketTourProviderV2.select((value) => value.serviceType));

    final entertainmentList = ref.watch(
        pTicketTourProviderV2.select((value) => value.entertainmentList));

    // final myRecentlyTour = ref
    //     .watch(pTicketTourProviderV2.select((value) => value.myRecentleTour));

    final isLoading =
        ref.watch(pTicketTourProviderV2.select((value) => value.isLoading));

    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(height: 17.H),
            Container(
              height: 169.H,
              width: 360.W,
              padding: EdgeInsets.symmetric(horizontal: 16.W),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15.SP),
                image: DecorationImage(
                  image: AssetImage(AppAssets.origin().imHomeBanner.assetPath),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(height: 24.H),
            isAll
                ? Visibility(
                    visible: !isLoading,
                    replacement: const ListTourGridShimmerLoading(),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.W),
                      child: GridView.builder(
                        controller: _gridViewScrollController,
                        itemCount: entertainmentList.length,
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            childAspectRatio: globalReleaseStatusNotifier.isDisplayAll ? 185.W / 259.H : 200.W / 259.H,
                            crossAxisCount: 2,
                            mainAxisSpacing: 16.H,
                            crossAxisSpacing: 16.W),
                        itemBuilder: (context, index) => ServiceItemV2(
                          onTap: () {
                            AppRoute.pushNamed(
                              context,
                              routeName: AppRoute.routeTourDetailViewV2,
                              arguments: {
                                'id': entertainmentList[index].id,
                                'isFerryTour': selectedServiceType.slug ==
                                    ferrySlugCategory,
                                'listType': ListType.all
                              },
                            );
                          },
                          service: entertainmentList[index],
                          onTapLike: () {
                            if (globalCacheAuth.isLogged()) {
                              ref
                                  .read(pTicketTourProviderV2.notifier)
                                  .listSavedTour(
                                      entertainmentList[index].id ?? 0, ListType.all);
                            } else {
                              dialogHelpers.show(context,
                                  child: const NotYetLoginDialog());
                            }
                          },
                        ),
                      ),
                    ),
                  )
                : Column(
                    children: [
                      selectedServiceType.slug == ferrySlugCategory
                          ? Column(
                              children: [
                                _serviceItemList(
                                  title: context
                                      .strings.tuan_chau_cat_ba_ferry_route,
                                  services: ref.watch(
                                      pTicketTourProviderV2.select(
                                          (value) => value.tuanChauCatBaList)),
                                  onTapViewAll: () => AppRoute.pushNamed(
                                    context,
                                    routeName:
                                        AppRoute.routeListCategoriesToursView,
                                    arguments: {
                                      'id': selectedServiceType.id,
                                      'name': selectedServiceType.name,
                                      'ferryTypeIndex':
                                          FerryType.tuanChauCatBa.index,
                                      'listType': ListType.tuanChauCatBa
                                    },
                                  ),
                                  type: ListType.tuanChauCatBa
                                ),
                                _serviceItemList(
                                  title: context
                                      .strings.cat_ba_tuan_chau_ferry_route,
                                  services: ref.watch(
                                      pTicketTourProviderV2.select(
                                          (value) => value.catBaTuanChauList)),
                                  onTapViewAll: () => AppRoute.pushNamed(
                                    context,
                                    routeName:
                                        AppRoute.routeListCategoriesToursView,
                                    arguments: {
                                      'id': selectedServiceType.id,
                                      'name': selectedServiceType.name,
                                      'ferryTypeIndex':
                                          FerryType.catBaTuanChau.index,
                                      'listType': ListType.catBaTuanChau
                                    },
                                  ),
                                  type: ListType.catBaTuanChau
                                )
                              ],
                            )
                          : _serviceItemList(
                              title: selectedServiceType.name,
                              services: entertainmentList,
                              onTapViewAll: () => AppRoute.pushNamed(
                                context,
                                routeName:
                                    AppRoute.routeListCategoriesToursView,
                                arguments: {
                                  'id': selectedServiceType.id,
                                  'name': selectedServiceType.name,
                                  'listType': ListType.all
                                },
                              ),
                              type: ListType.all
                            ),
                          //TODO: recently view
                      // if (myRecentlyTour.isNotEmpty) _serviceItemList(
                      //   title:
                      //       context.strings.text_products_you_have_searched_for,
                      //   services: myRecentlyTour,
                      //   onTapViewAll: () => AppRoute.pushNamed(
                      //     context,
                      //     routeName: AppRoute.routeListCategoriesToursView,
                      //     arguments: {
                      //       'id': -1,
                      //       'name': context
                      //           .strings.text_products_you_have_searched_for,
                      //       'listType': ListType.myRecently
                      //     },
                      //   ),
                      //   type: ListType.myRecently
                      // ),
                    ],
                  ),
            SizedBox(height: 15.H,)
          ],
        ),
      ),
    );
  }

  Widget _serviceItemList({
    required String title,
    required List<TourResponse> services,
    Function()? onTapViewAll,
    required ListType type,
  }) {
    final isLoading =
        ref.watch(pTicketTourProviderV2.select((value) => value.isLoading));
    final selectedServiceType =
        ref.watch(pTicketTourProviderV2.select((value) => value.serviceType));
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ViewAllRowV2(
          title: title,
          onTapViewAll: onTapViewAll,
          buttonText: context.strings.text_see_more,
          fontStyle: FontStyle.normal,
        ),
        SizedBox(
          height: globalReleaseStatusNotifier.isDisplayAll ? 242.H : 220.H,
          child: Visibility(
            visible: !isLoading,
            replacement: const ListTourShimmerLoading(),
            child: ListView.separated(
              clipBehavior: Clip.none,
              padding: EdgeInsets.symmetric(horizontal: 16.W),
              shrinkWrap: true,
              itemCount: services.length,
              scrollDirection: Axis.horizontal,
              separatorBuilder: (context, _) => SizedBox(width: 18.W),
              itemBuilder: (context, index) {
                return ServiceItemV2(
                  service: services[index],
                  onTap: () {
                    AppRoute.pushNamed(
                      context,
                      routeName: AppRoute.routeTourDetailViewV2,
                      arguments: {
                        'id': services[index].id,
                        'isFerryTour':
                            selectedServiceType.slug == ferrySlugCategory,
                        'listType': type
                      },
                    );
                  },
                  onTapLike: () {
                    if (globalCacheAuth.isLogged()) {
                      ref
                          .read(pTicketTourProviderV2.notifier)
                          .listSavedTour(services[index].id ?? 0, type);
                    } else {
                      dialogHelpers.show(context,
                          child: const NotYetLoginDialog());
                    }
                  },
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _searchNotFoundWidget() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AppAssets.origin().imEmptyView.widget(),
          TripcText(context.strings.text_search_not_found)
        ],
      ),
    );
  }
}
