import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';

class ExpandedView extends StatefulWidget {
  final String title;
  final Widget content;

  const ExpandedView({super.key, 
    required this.title,
    required this.content,
  });

  @override
  _ExpandedViewState createState() => _ExpandedViewState();
}

class _ExpandedViewState extends State<ExpandedView> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Container(
            padding: EdgeInsets.only(bottom: 12.H),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppAssets.origin().lightGray,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TripcText(widget.title,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    textAlign: TextAlign.start,
                    textColor: AppAssets.origin().blackColor),
                AnimatedRotation(
                  turns: _isExpanded ? -0.5 : 0,
                  duration: const Duration(milliseconds: 300),
                  child: const Icon(Icons.keyboard_arrow_down, color: Colors.black,),
                ),
              ],
            ),
          ),
        ),
        AnimatedCrossFade(
          firstChild: Container(),
          secondChild: Container(
            padding: EdgeInsets.only(top: 12.H),
            width: double.infinity,
            child: widget.content,
          ),
          crossFadeState: _isExpanded
              ? CrossFadeState.showSecond
              : CrossFadeState.showFirst,
          duration: const Duration(milliseconds: 300),
        ),
      ],
    );
  }
}