import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:tripc_app/pages/ticket_tour/components/image_detailed_tour.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

import '../../../widgets/commons/base_cached_network_image.dart';
import '../../../widgets/presentationals/app_shimmer_loading/image_shimmer_loading.dart';

class TripcImageSlide extends ConsumerStatefulWidget {
  const TripcImageSlide({super.key, required this.images, this.height = 200, this.applyGradient = false,});
  final double height;
  final List<String> images;
  final bool applyGradient;

  @override
  ConsumerState<TripcImageSlide> createState() => _TripcImageSlideState();
}

class _TripcImageSlideState extends ConsumerState<TripcImageSlide> {
  late final PageController _controller;

  @override
  void initState() {
    super.initState();
    _controller = PageController(initialPage: 0);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isLoading =
        ref.watch(pTicketTourProvider.select((value) => value.isLoading));
    return SizedBox(
        height: widget.height.H,
        child: Stack(
          children: [
            Wrap(
              children: widget.images.map((url) {
                return SizedBox.shrink(
                  child: BaseCachedNetworkImage(
                    imageUrl: url,
                    placeholder: (context, _) => ImageShimmerLoading(
                      height: 285.H,
                      fit: BoxFit.scaleDown,
                      color: context.appCustomPallet.buttonBG,
                    ),
                    errorWidget: (context, error, stackTrace) =>
                        AppAssets.origin().icErrorImg.widget(
                              height: 285.H,
                              color: context.appCustomPallet.buttonBG,
                            ),
                    fit: BoxFit.cover,
                  ),
                );
              }).toList(),
            ),
            Visibility(
              visible: !isLoading,
              replacement: Container(
                height: widget.height.H,
                color: AppAssets.origin().lightGrayDD4,
              ),
              child: PageView.builder(
                controller: _controller,
                physics: const ClampingScrollPhysics(),
                itemCount: widget.images.length,
                itemBuilder: (context, index) => Stack(
                  fit: StackFit.expand,
                  children: [
                    ImageDetailedTour(
                      image: widget.images[index],
                    ),
                    if (widget.applyGradient)
                      Container(
                        alignment: Alignment.topCenter,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.black.withValues(alpha: 0.65),
                              Colors.transparent,
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Visibility(
                visible: widget.images.isNotEmpty,
                replacement: const SizedBox.shrink(),
                child: Padding(
                  padding: EdgeInsets.only(bottom: 8.H),
                  child: SmoothPageIndicator(
                      controller: _controller,
                      count: widget.images.length,
                      effect: CustomizableEffect(
                        dotDecoration: DotDecoration(
                          width: 8.H,
                          height: 8.H,
                          color: AppAssets.origin().primaryColor,
                          borderRadius: BorderRadius.circular(8.SP),
                        ),
                        spacing: 8.W,
                        activeDotDecoration: DotDecoration(
                          width: 24.H,
                          height: 8.H,
                          borderRadius: BorderRadius.circular(8.SP),
                          color: AppAssets.origin().secondaryColor,
                        ),
                      )),
                ),
              ),
            ),
          ],
        ));
  }
}
