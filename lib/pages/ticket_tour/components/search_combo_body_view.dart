import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/homepage/components/advertisement.dart';
import 'package:tripc_app/pages/homepage/components/service_item.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/view_all_row/view_all_row.dart';

class TripcSearchComboBodyView extends ConsumerWidget {
  const TripcSearchComboBodyView({super.key, required this.combos});
  final List<TourResponse> combos;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final advertisement =
        ref.watch(pTicketTourProvider.select((value) => value.advertisement));
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.only(top: 20.H, bottom: context.spacingBottom),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Advertisement(advertisement: advertisement),
            Column(children: [
              _serviceItemList(ref,
                  onTapViewAll: () {},
                  title: context.strings.text_super_cheap_combo,
                  services: combos),
              _serviceItemList(ref,
                  onTapViewAll: () {},
                  title: context.strings.text_family_combo,
                  services: combos),
              _serviceItemList(ref,
                  onTapViewAll: () {},
                  title: context.strings.text_favorite_combo,
                  services: combos),
              _serviceItemList(ref,
                  onTapViewAll: () {},
                  title: context.strings.text_exclusively_for_you,
                  services: combos)
            ]),
          ],
        ),
      ),
    );
  }

  Widget _serviceItemList(
    WidgetRef ref, {
    required String title,
    required List<TourResponse> services,
    Function()? onTapViewAll,
  }) {
    return Column(
      children: [
        ViewAllRow(title: title, onTapViewAll: onTapViewAll),
        SizedBox(
            height: 156.H,
            child: ListView.separated(
                clipBehavior: Clip.none,
                padding: EdgeInsets.symmetric(horizontal: 24.W),
                itemCount: services.length,
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, _) => SizedBox(width: 18.W),
                itemBuilder: (context, index) {
                  return ServiceItem(
                      service: services[index],
                      onTap: () {
                        // ref
                        //     .read(pTicketTourProvider.notifier)
                        //     .selectTour(services[index]);
                        // AppRoute.pushNamed(context,
                        //     routeName: AppRoute.routeTourDetailView,
                        //     arguments: services[index].id);
                      });
                })),
      ],
    );
  }
}
