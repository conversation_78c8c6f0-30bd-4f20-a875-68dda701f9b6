import 'package:flutter/material.dart';
import 'package:timelines_plus/timelines_plus.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../services/app/app_assets.dart';


class TripItineraryView extends StatelessWidget {
  const TripItineraryView({super.key, this.trip});
  final List<Trip>? trip;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Timeline.builder(
        padding: EdgeInsets.zero,
        itemCount: trip?.length ?? 0,
        shrinkWrap: true,

        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final item = trip?[index];
          return TimelineTile(
            nodePosition: 0,
            node: TimelineNode(
              indicatorPosition: 0,
              overlap: false,
              indicator: Container(
                width: 24.H,
                height: 24.H,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppAssets.init.darkBlue5FF,
                ),
                child: Center(
                  child: AppAssets.init.iconLocationPin.widget(),
                ),
              ),
              startConnector: index == 0
                  ? null
                  : DashedLineConnector(color: AppAssets.init.tabbarEnableColor, thickness: 1,),
              endConnector: index == (trip?.length ?? 0) - 1
                  ? null
                  : DashedLineConnector(color: AppAssets.init.tabbarEnableColor, thickness: 1,),
            ),
            contents: Padding(
              padding: EdgeInsets.only(left: 16.H, bottom: 24.H),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TripcText(
                    item?.time ?? '',
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  SizedBox(height: 4.H),
                  TripcText(
                    item?.activity ?? '',
                    fontWeight: FontWeight.w300,
                    textAlign: TextAlign.left,
                    fontSize: 14,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}