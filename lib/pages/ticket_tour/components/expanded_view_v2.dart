import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';

class ExpandedViewV2 extends StatefulWidget {
  final String title;
  final Widget content;

  const ExpandedViewV2({super.key,
    required this.title,
    required this.content,
  });

  @override
  _ExpandedViewV2State createState() => _ExpandedViewV2State();
}

class _ExpandedViewV2State extends State<ExpandedViewV2> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 7.H, horizontal: 5.W),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                TripcText(widget.title,
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    textAlign: TextAlign.start,
                    textColor: AppAssets.origin().blackColor),
                AnimatedRotation(
                  turns: _isExpanded ? -0.5 : 0,
                  duration: const Duration(milliseconds: 300),
                  child: const Icon(Icons.keyboard_arrow_down, color: Colors.black,),
                ),
              ],
            ),
          ),
        ),
        AnimatedCrossFade(
          firstChild: Container(),
          secondChild: Container(
            padding: EdgeInsets.only(top: 12.H),
            width: double.infinity,
            child: widget.content,
          ),
          crossFadeState: _isExpanded
              ? CrossFadeState.showSecond
              : CrossFadeState.showFirst,
          duration: const Duration(milliseconds: 300),
        ),
      ],
    );
  }
}