import 'package:flutter/cupertino.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/homepage/components/rating_widget.dart';
import 'package:tripc_app/pages/homepage/components/red_tag_v2.dart';
import 'package:tripc_app/pages/ticket_tour/components/tour_information_item_v2.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import 'address_link_widget.dart';

class HeaderTourTicketArea extends StatelessWidget {
  final TourResponse tour;
  const HeaderTourTicketArea({super.key, required this.tour});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.W).copyWith(bottom: 0),
      padding: EdgeInsets.all(10.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().whiteBackgroundColor,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            clipBehavior: Clip.none,
            children: [
              TripcText(
                tour.name,
                fontSize: 20,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().textColorForNameTourV2,
              ),
              if (tour.sale != null && tour.sale != 0)
                Positioned(
                  right: -23.W,
                  child: RedSaleTagV2(
                    text: tour.sale.toString(),
                  ),
                ),
            ],
          ),
          RatingWidget(
            model: tour,
            iconSize: Size(12.W, 12.W),
            fontSize: 12.SP,
            addOnTextFontSize: 12.SP,
            padding: EdgeInsets.symmetric(vertical: 8.H),
            onReviewTap: () => AppRoute.pushNamed(
              context,
              routeName: AppRoute.routeRating,
            ),
          ),

          Padding(
            padding: EdgeInsets.only(left: 16.W, bottom: 8.H),
            child: TripcRichText(
              text: '',
              children: [
                TextSpan(
                  text: context.strings.text_powered_by,
                  style: AppAssets.origin()
                      .normalTextStyle
                      .copyWith(fontSize: 12.SP, color: AppAssets.origin().black, fontWeight: FontWeight.w400),
                ),
                TextSpan(
                  text: tour.supplier?.name ?? '',
                  style: AppAssets.origin()
                      .superBoldTextStyle
                      .copyWith(fontSize: 14.SP, color: AppAssets.origin().darkBlueColor),
                ),
              ],
            ),
          ),
          AddressLink(address: tour.address ?? ''),
          SizedBox(height: 10.H),
          Padding(
            padding: EdgeInsets.only(left: 16.W, bottom: 7.H),
            child: TourInformationItemV2(
              title: context.strings.duration,
              value: tour.duration ?? '-',
              fontStyle: FontStyle.normal,
              fontWeight: FontWeight.w400,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.W),
            child: TourInformationItemV2(
              title: context.strings.location,
              value: tour.departureLocation ?? '-',
              fontStyle: FontStyle.normal,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: 5.H),
        ],
      ),
    );
  }
}
