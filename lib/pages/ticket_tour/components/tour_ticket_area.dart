import 'package:flutter/cupertino.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/ticket_tour/components/expanded_view_v2.dart';
import 'package:tripc_app/pages/ticket_tour/components/header_tour_ticket_area.dart';
import 'package:tripc_app/pages/ticket_tour/components/trip_itinerary_view.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:markdown/markdown.dart' as md;
import 'package:html2md/html2md.dart' as html2md;

class TourInfoArea extends StatelessWidget {
  final TourResponse tour;
  // final ScrollController scrollController;

  const TourInfoArea({
    super.key,
    required this.tour,
    // required this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    final content = html2md.convert(tour.description ?? '');
    return Column(
      children: [
        _buildHeader(context),
        Si<PERSON><PERSON><PERSON>(
          height: 16.H,
        ),
        _buildIntroduction(context, content),
        SizedBox(
          height: 16.H,
        )
        // _buildTripSection(context),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return HeaderTourTicketArea(tour: tour);
  }

  Widget _buildIntroduction(BuildContext context, String content) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().whiteBackgroundColor,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      child: ExpandedViewV2(
        title: context.strings.introduction,
        content: Markdown(
          padding: EdgeInsets.symmetric(horizontal: 5.W, vertical: 7.H)
              .copyWith(top: 0),
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          data: content,
          extensionSet: md.ExtensionSet(
            md.ExtensionSet.gitHubFlavored.blockSyntaxes,
            <md.InlineSyntax>[
              md.EmojiSyntax(),
              ...md.ExtensionSet.gitHubFlavored.inlineSyntaxes,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTripSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().whiteBackgroundColor,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      child: ExpandedViewV2(
        title: context.strings.text_trip,
        content: TripItineraryView(trip: tour.trip),
      ),
    );
  }
}
