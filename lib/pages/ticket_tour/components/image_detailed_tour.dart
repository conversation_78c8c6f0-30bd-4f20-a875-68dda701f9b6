import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

class ImageDetailedTour extends StatefulWidget {
  const ImageDetailedTour({super.key, required this.image});
  final String image;

  @override
  State<ImageDetailedTour> createState() => _ImageDetailedTourState();
}

class _ImageDetailedTourState extends State<ImageDetailedTour>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BaseCachedNetworkImage(
        placeholder: (context, _) => Container(
              color: AppAssets.origin().lightGrayDD4,
            ),
        errorWidget: (context, error, stackTrace) =>
            AppAssets.origin().icErrorImg.widget(
                  height: 285.H,
                  color: context.appCustomPallet.buttonBG,
                ),
        fit: BoxFit.cover,
        imageUrl: widget.image);
  }
}
