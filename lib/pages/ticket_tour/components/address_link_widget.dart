import 'dart:io';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

class AddressLink extends StatelessWidget {
  final String address;
  final Color? textColor;
  final double? spacing;
  final TextDecoration textDecoration;
  const AddressLink({super.key, required this.address, this.textColor, this.spacing, this.textDecoration = TextDecoration.underline});

  Future<void> _openMap() async {
    final encoded = Uri.encodeComponent(address);

    if (Platform.isIOS) {
      final appleUrl = Uri.parse('https://maps.apple.com/?q=$encoded');
      if (await canLaunchUrl(appleUrl)) {
        await launchUrl(appleUrl);
        return;
      }
    }

    final googleAppUrl = Uri.parse('comgooglemaps://?q=$encoded');
    if (await canLaunchUrl(googleAppUrl)) {
      await launchUrl(googleAppUrl);
      return;
    }

    final googleWebUrl = Uri.parse(
      'https://www.google.com/maps/search/?api=1&query=$encoded',
    );
    if (await canLaunchUrl(googleWebUrl)) {
      await launchUrl(googleWebUrl);
      return;
    }

    debugPrint('Could not launch map for $address');
  }


  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _openMap,
      child: Row(
        spacing: spacing?.W ?? 0,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.location_on_sharp,
            color: AppAssets.origin().textForLocationTourV2,
            size: 16.W,
          ),
          Expanded(
            child: Text(
              address,
              textAlign: TextAlign.start,
              style: TextStyle(
                color: textColor ?? AppAssets.origin().textForLocationTourV2,
                fontSize: 12.SP,
                fontWeight: FontWeight.w400,
                decoration: textDecoration,
                decorationColor: AppAssets.origin().textForLocationTourV2,
                decorationStyle: TextDecorationStyle.solid,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
