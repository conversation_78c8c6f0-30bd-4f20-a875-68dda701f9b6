import 'package:flutter/material.dart';
import 'package:tripc_app/widgets/commons/tripc_dropdown/tripc_dropdown.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class CityDropdown extends StatelessWidget {
  const CityDropdown(
      {super.key, required this.cities, required this.currentValue});
  final List<String> cities;
  final String currentValue;

  @override
  Widget build(BuildContext context) {
    return TripcDropdownButton<String>(
        currentValue: currentValue,
        items: cities
            .map((item) => DropdownMenuItem<String>(
                  value: item,
                  child: TripcText(
                    item,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ))
            .toList(),
        selectedItemBuilder: (_) {
          return cities.map(
            (item) {
              return DropdownMenuItem<String>(
                value: item,
                child: TripcText(
                  item,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              );
            },
          ).toList();
        });
  }
}
