import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/service_model.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class ComboDetailView extends ConsumerWidget {
  const ComboDetailView(
      {super.key,
      required this.combo,
      this.hideNote = false,
      this.padding,
      this.periodTimeFontSize = 10,
      this.hideSpecialInfo = false});
  final ServiceModel? combo;
  final bool hideNote;
  final bool hideSpecialInfo;
  final EdgeInsetsGeometry? padding;
  final double periodTimeFontSize;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: padding ?? EdgeInsets.only(left: 24.W, right: 9.W),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            _tourInfoArea(context, combo),
            SizedBox(height: 16.H),
            _information(
                title: context.strings.text_details_included,
                value: combo?.listInfomation ?? []),
            SizedBox(height: 20.H),
            Visibility(
              visible: !hideSpecialInfo,
              child: _information(
                  title: context.strings.text_special,
                  value: combo?.specialInfomation ?? []),
            ),
            Visibility(
                visible: !hideNote,
                child: _notes(context, value: combo?.notes ?? [])),
          ]),
    );
  }

  Widget _tourInfoArea(BuildContext context, ServiceModel? selectedTour) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        TripcText(
          selectedTour?.nameTour,
          fontSize: 16,
          textAlign: TextAlign.start,
          height: 1.5,
          textColor: AppAssets.origin().blackColor,
        ),
        Stack(clipBehavior: Clip.none, children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 3.H, bottom: 8.H),
                child: Row(
                  children: [
                    AppAssets.origin()
                        .icClock
                        .widget(height: 16.H, width: 16.H),
                    Expanded(
                      child: TripcText(
                        '${context.strings.text_application_period} ${selectedTour?.applicationPeriod}',
                        fontSize: periodTimeFontSize,
                        fontWeight: FontWeight.w700,
                        textAlign: TextAlign.start,
                        textColor: AppAssets.origin().blackColor,
                        padding: EdgeInsets.only(left: 4.W),
                      ),
                    ),
                  ],
                ),
              ),
              TripcRichText(
                text: '',
                children: [
                  TextSpan(
                    text: context.strings.text_powered_by,
                    style: AppAssets.origin().normalTextStyle.copyWith(
                        fontSize: 10, color: AppAssets.origin().black),
                  ),
                  TextSpan(
                    text: selectedTour?.supplier,
                    style: AppAssets.origin().boldTextStyle.copyWith(
                        fontSize: 10, color: AppAssets.origin().black),
                  ),
                ],
              ),
            ],
          ),
          Positioned(
            right: 130.W,
            top: 26.H,
            child: Transform.rotate(
                angle: -19.15,
                transformHitTests: false,
                child: Stack(
                  alignment: Alignment.centerLeft,
                  children: [
                    AppAssets.origin()
                        .icRedTag
                        .widget(width: 49.W, height: 11.H),
                    TripcText(
                        context.strings
                            .text_days_left(selectedTour?.daysLeft ?? 0),
                        fontSize: 6,
                        textColor: AppAssets.origin().whiteBackgroundColor,
                        fontWeight: FontWeight.w700,
                        padding: EdgeInsets.only(left: 5.W),
                        textAlign: TextAlign.start)
                  ],
                )),
          )
        ])
      ],
    );
  }

  Widget _information({required String title, required List<String> value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          title,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          textColor: AppAssets.origin().blackColor,
          padding: EdgeInsets.only(bottom: 12.H),
        ),
        Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: List.generate(
                value.length,
                (int index) => Padding(
                      padding: EdgeInsets.only(bottom: 4.H),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TripcText('\u2022',
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              textColor: AppAssets.origin().blackColor,
                              padding: EdgeInsets.symmetric(horizontal: 5.W)
                                  .copyWith(top: 1.5.H)),
                          Expanded(
                            child: TripcText(
                              value[index],
                              fontSize: 12,
                              height: 1.5.H,
                              fontWeight: FontWeight.w400,
                              textAlign: TextAlign.start,
                              textColor: AppAssets.origin().blackColor,
                            ),
                          ),
                        ],
                      ),
                    ))),
      ],
    );
  }

  Widget _notes(BuildContext context, {required List<String> value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            AppAssets.origin().icICircle.widget(
                  height: 16.H,
                  width: 16.H,
                  color: AppAssets.origin().darkOrange,
                ),
            TripcText(
              context.strings.text_note,
              fontSize: 12,
              fontWeight: FontWeight.w700,
              textColor: AppAssets.origin().darkYellow,
              padding: EdgeInsets.only(bottom: 6.H, left: 4.W),
            ),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(left: 24.W),
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: List.generate(
                  value.length,
                  (int index) => Padding(
                        padding: EdgeInsets.only(bottom: 4.H),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TripcText('\u2022',
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                textColor: AppAssets.origin().blackColor,
                                padding: EdgeInsets.symmetric(horizontal: 5.W)
                                    .copyWith(top: 1.5.H)),
                            Expanded(
                              child: TripcText(
                                value[index],
                                fontSize: 12,
                                height: 1.5,
                                fontWeight: FontWeight.w400,
                                textAlign: TextAlign.start,
                                textColor: AppAssets.origin().blackColor,
                              ),
                            ),
                          ],
                        ),
                      ))),
        ),
      ],
    );
  }
}
