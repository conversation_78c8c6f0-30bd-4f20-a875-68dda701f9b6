import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/homepage/components/service_item.dart';
import 'package:tripc_app/pages/homepage/providers/tripc_homepage_provider.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/status_display_notifier.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/tour_shimmer_loading/tour_shimmer_loading.dart';
import 'package:tripc_app/widgets/commons/view_all_row/view_all_row.dart';

import '../../../models/app/tripc_service_category.dart';

class TripcTourTicketBodyView extends ConsumerStatefulWidget {
  const TripcTourTicketBodyView({
    super.key,
    this.category = TripCServiceCategory.combo,
  });
  final TripCServiceCategory category;

  @override
  ConsumerState<TripcTourTicketBodyView> createState() =>
      _TripcTourTicketBodyViewState();
}

class _TripcTourTicketBodyViewState
    extends ConsumerState<TripcTourTicketBodyView> {
  bool loadingData = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      forceLoading(true);
      ref
          .read(pTicketTourProvider.notifier)
          .setPageGetData(page: 1, pageSize: 4, keyword: '');
      await ref.read(pTicketTourProvider.notifier).getTourBestSeller(
            categoryId: widget.category.getServiceTypeId(
                  ref.watch(
                    pHomepageScreenProvider
                        .select((value) => value.listServiceType),
                  ),
                ) ??
                1,
          );
      await ref.read(pTicketTourProvider.notifier).getDisCount(
            categoryId: widget.category.getServiceTypeId(
                  ref.watch(
                    pHomepageScreenProvider
                        .select((value) => value.listServiceType),
                  ),
                ) ??
                1,
          );
      // await ref.read(pTicketTourProvider.notifier).getTourBuyMoreEarnMore();
      forceLoading(false);
    });
  }

  void forceLoading(bool value) {
    if (mounted) {
      setState(() {
        loadingData = value;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // final advertisement =
    //     ref.watch(pTicketTourProvider.select((value) => value.advertisement));
    final discounts =
        ref.watch(pTicketTourProvider.select((value) => value.discounts));
    final bestSeller =
        ref.watch(pTicketTourProvider.select((value) => value.bestSeller));
    // final buyMoreEarnMore =
    //     ref.watch(pTicketTourProvider.select((value) => value.buyMoreEarnMore));
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: 20.H, bottom: context.spacingBottom),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // Advertisement(advertisement: advertisement),
          Container(
              height: 169.H,
              width: 345.W,
              padding: EdgeInsets.symmetric(horizontal: 15.W),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15.SP),
                image: DecorationImage(
                  image: AssetImage(AppAssets.origin().imHomeBanner.assetPath),
                  fit: BoxFit.cover,
                ),
              )),
          Column(children: [
            SizedBox(
              height: 30.H,
            ),
            _serviceItemList(
                onTapViewAll: () => AppRoute.pushNamed(context,
                    routeName: AppRoute.routeListToursView,
                  arguments: {
                        'category':
                            widget.category.getSuperCheapTourType(context),
                        'categoryParent': widget.category.getServiceTypeId(
                          ref.watch(
                            pHomepageScreenProvider
                                .select((value) => value.listServiceType),
                          ),
                        )
                      },
                    ),
                title: widget.category.getSuperCheapTitle(context),
                services: discounts),
            SizedBox(
              height: 30.H,
            ),
            _serviceItemList(
                onTapViewAll: () => AppRoute.pushNamed(context,
                    routeName: AppRoute.routeListToursView,
                  arguments: {
                        'category':
                            widget.category.getFavouriteTourType(context),
                        'categoryParent': widget.category.getServiceTypeId(
                          ref.watch(
                            pHomepageScreenProvider
                                .select((value) => value.listServiceType),
                          ),
                        ),
                      },
                    ),
                title: widget.category.getFavouriteTitle(context),
                services: bestSeller),
            // _serviceItemList(
            //     onTapViewAll: () => AppRoute.pushNamed(context,
            //         routeName: AppRoute.routeListToursView,
            //         arguments: ServiceCategories.buyMoreEarnMore),
            //     title: context.strings.text_buy_more_get_rewards,
            //     services: buyMoreEarnMore),
          ]),
        ],
      ),
    );
  }

  Widget _serviceItemList({
    required String title,
    required List<TourResponse> services,
    Function()? onTapViewAll,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ViewAllRow(
          title: title,
          onTapViewAll: onTapViewAll,
          buttonText: context.strings.text_see_more,
          fontStyle: FontStyle.normal,
        ),
        SizedBox(
            height: globalReleaseStatusNotifier.isDisplayAll ? 259.H : 220.H,
            child: Visibility(
              visible: !loadingData,
              replacement: const ListTourShimmerLoading(),
              child: ListView.separated(
                  clipBehavior: Clip.none,
                  padding: EdgeInsets.symmetric(horizontal: 24.W),
                  shrinkWrap: true,
                  itemCount: services.length,
                  scrollDirection: Axis.horizontal,
                  separatorBuilder: (context, _) => SizedBox(width: 18.W),
                  itemBuilder: (context, index) {
                    return ServiceItem(
                        service: services[index],
                        onTap: () {
                          AppRoute.pushNamed(context,
                              routeName: AppRoute.routeTourDetailView,
                              arguments: services[index].id);
                        });
                  }),
            )),
      ],
    );
  }
}
