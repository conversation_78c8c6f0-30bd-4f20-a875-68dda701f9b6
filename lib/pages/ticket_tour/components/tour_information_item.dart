import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';

class TourInformationItem extends StatelessWidget {
  const TourInformationItem(
      {super.key,
      required this.title,
      required this.value,
      this.padding,
      this.fontWeight,
      this.fontSize = 14,
      this.fontStyle});
  final String title;
  final String value;
  final EdgeInsets? padding;
  final double fontSize;
  final FontWeight? fontWeight;
  final FontStyle? fontStyle;

  @override
  Widget build(BuildContext context) {
    return TripcRichText(
      text: '',
      textAlign: TextAlign.start,
      padding: padding ?? EdgeInsets.zero,
      children: [
        TextSpan(
          text: title,
          style: AppAssets.origin().normalTextStyle.copyWith(
              fontSize: fontSize.SP,
              fontWeight: FontWeight.w300,
              color: AppAssets.origin().black),
        ),
        const TextSpan(text: ' '),
        TextSpan(
          text: value,
          style: AppAssets.origin().mediumTextStyle.copyWith(
                fontSize: fontSize.SP,
                fontWeight: fontWeight,
                color: AppAssets.origin().black,
              ),
        ),
      ],
    );
  }
}
