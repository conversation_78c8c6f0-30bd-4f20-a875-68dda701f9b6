import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:html2md/html2md.dart' as html2md;

class OrderInfoHtml extends StatelessWidget {
  final String title;

  final String htmlContent;

  const OrderInfoHtml({
    super.key,
    required this.title,
    required this.htmlContent,
  });

  @override
  Widget build(BuildContext context) {
    final markdown = html2md.convert(htmlContent);
    final processed = markdown.replaceAllMapped(
      RegExp(r'^\*+\s*', multiLine: true),
          (match) => '\u2022\t\t',
    );
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().cardColorTourV2,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      padding: EdgeInsets.symmetric(horizontal: 9.W, vertical: 20.H,),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TripcText(
            title,
            fontSize: 16,
            fontWeight: FontWeight.w700,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
          ),
          SizedBox(height: 8.H),
          // Render each content string as a bullet point
          TripcText(
            processed,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            textAlign: TextAlign.start,
            padding: EdgeInsets.only(left: 10.W),
            textColor: AppAssets.origin().blackColor,
          ),
        ],
      ),
    );
  }
}
