import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/api_tour_response/applicable_time_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/ticket_tour/components/tour_information_view.dart';
import 'package:tripc_app/pages/ticket_tour/components/trip_itinerary_view.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_option_button.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../../../services/app/app_route.dart';
import '../providers/providers.dart';
import 'cancel_payment_bottom_sheet.dart';
import 'expanded_view.dart';
import 'components.dart';
import 'package:markdown/markdown.dart' as md;
import 'package:html2md/html2md.dart' as html2md;

class TourTicketDetailView extends ConsumerStatefulWidget {
  const TourTicketDetailView({super.key, required this.selectedTour});

  final TourResponse? selectedTour;

  @override
  ConsumerState<TourTicketDetailView> createState() =>
      _TourTicketDetailViewState();
}

class _TourTicketDetailViewState extends ConsumerState<TourTicketDetailView> {
  final _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _tourInfoArea(context, widget.selectedTour),
          // _pickServiceArea(selectedTour: selectedTour, context, ref),
          _orderInfo(onTapDetail: () {
            bottomSheetHelpers.show(context,
                borderRadius: 12,
                backgroundColor: Colors.white,
                child: const CancelPaymentBottomSheet());
          }, context),
        ]);
  }

  Widget _tourInfoArea(BuildContext context, TourResponse? selectedTour) {
    final content = html2md.convert(selectedTour?.description ?? '');
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TripcText(
            selectedTour?.name,
            fontSize: 16,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
          ),
          Padding(
            padding: EdgeInsets.only(top: 8.H, bottom: 8.H),
            child: GestureDetector(
              onTap: () => AppRoute.pushNamed(
                context,
                routeName: AppRoute.routeRating,
              ),
              child: Row(
                children: [
                  AppAssets.origin().icStar.widget(height: 12.H, width: 12.H),
                  SizedBox(
                    width: 4.W,
                  ),
                  TripcRichText(
                    text: '',
                    children: [
                      TextSpan(
                        text: selectedTour?.rating != 0
                            ? selectedTour?.rating.toString()
                            : '',
                        style: AppAssets.origin().boldTextStyle.copyWith(
                            fontSize: 12.SP,
                            color: AppAssets.origin().secondDarkYellow),
                      ),
                      TextSpan(
                        text: (selectedTour?.reviews ?? 0) == 0
                            ? context.strings.text_no_review
                            : '/5-${selectedTour?.reviews ?? 0} ${context.strings.text_rating}',
                        style: AppAssets.origin().normalTextStyle.copyWith(
                            fontSize: 12.SP,
                            color: AppAssets.origin().secondDarkGreyTextColor),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
          TripcRichText(
            text: '',
            children: [
              TextSpan(
                text: context.strings.text_powered_by,
                style: AppAssets.origin().normalTextStyle.copyWith(
                    fontSize: 12.SP,
                    color: AppAssets.origin().black,
                    fontWeight: FontWeight.w300),
              ),
              TextSpan(
                text: selectedTour?.supplier?.name ?? '',
                style: AppAssets.origin().boldTextStyle.copyWith(
                    fontSize: 14.SP, color: AppAssets.origin().darkBlueColor),
              ),
            ],
          ),
          TripcText(context.strings.text_general_information,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              textAlign: TextAlign.start,
              textColor: AppAssets.origin().blackColor,
              padding: EdgeInsets.only(top: 16.H, bottom: 16.H)),
          TourInformationView(
            selectedTour: selectedTour,
          ),
          SizedBox(
            height: 24.H,
          ),
          ExpandedView(
            title: context.strings.text_detailed_information,
            content: Markdown(
              padding: EdgeInsets.zero,
              controller: _scrollController,
              shrinkWrap: true,
              data: content,
              extensionSet: md.ExtensionSet(
                md.ExtensionSet.gitHubFlavored.blockSyntaxes,
                <md.InlineSyntax>[
                  md.EmojiSyntax(),
                  ...md.ExtensionSet.gitHubFlavored.inlineSyntaxes
                ],
              ),
            ),
          ),
          SizedBox(
            height: 16.H,
          ),
          ExpandedView(
            title: context.strings.text_trip,
            content: TripItineraryView(
              trip: selectedTour?.trip,
            ),
          ),
          SizedBox(height: 18.H)
        ],
      ),
    );
  }

  Widget _pickServiceArea(BuildContext context, WidgetRef ref,
      {TourResponse? selectedTour}) {
    final selectedDate =
        ref.watch(pTicketTourProvider.select((value) => value.selectedDate));
    final selectedTime =
        ref.watch(pTicketTourProvider.select((value) => value.selectedTime));
    final availableDates = selectedTour?.applicableTime;
    final seatAttributes =
        ref.watch(pTicketTourProvider.select((value) => value.seatAttributes));

    return Column(
      children: [
        _selectPackage(
          context,
          selectedDate: selectedDate,
          availableDates: availableDates,
          onPickDate: (value) =>
              ref.read(pTicketTourProvider.notifier).selectDate(date: value),
          onTapCalendar: () => bottomSheetHelpers.show(context,
              child: TripcCalendarBottomSheet(
                  onSelectDate: (date) {
                    DateTime? selectedDate;
                    if (availableDates == null) return;
                    if (!date.isBefore(availableDates.endDateTime) &&
                        !date.isAfter(availableDates.endDateTime)) {
                      selectedDate = date;
                    } else {
                      selectedDate = null;
                    }
                    ref
                        .read(pTicketTourProvider.notifier)
                        .selectDate(date: selectedDate);
                  },
                  selectedDay: selectedDate ?? DateTime.now())),
        ),
        SizedBox(
          height: 16.H,
        ),
        _selectTimeFrame(
            onTap: (value) => ref
                .read(pTicketTourProvider.notifier)
                .selectTime(timeFrame: value),
            context,
            selectedTimeFrame: selectedTime ?? '',
            timeFrame: availableDates?.time ?? []),
        SizedBox(
          height: 16.H,
        ),
        _selectSeatType(
            onTap: (value) => ref
                .read(pTicketTourProvider.notifier)
                .selectSeatType(seatType: value),
            context,
            selectedSeatType: ref.watch(pTicketTourProvider.select(
              (value) => value.selectedSeatType,
            )),
            seatType: seatAttributes)
      ],
    );
  }

  Widget _selectPackage(BuildContext context,
      {required DateTime? selectedDate,
      required ApplicableTimeResponse? availableDates,
      Function(DateTime)? onPickDate,
      VoidCallback? onTapCalendar}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TripcText(context.strings.text_select_service_package,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              textAlign: TextAlign.start,
              textColor: AppAssets.origin().blackColor,
              padding: EdgeInsets.only(bottom: 12.H)),
          Container(
            margin: EdgeInsets.symmetric(vertical: 4.H).copyWith(bottom: 6.H),
            height: 40.H,
            child: Row(
              children: [
                Expanded(
                  child: ListView.separated(
                      itemCount: availableDates?.listApplicableTime.length ?? 0,
                      scrollDirection: Axis.horizontal,
                      separatorBuilder: (context, _) => SizedBox(
                            width: 10.W,
                          ),
                      itemBuilder: (context, index) {
                        return TripcOptionButton(
                            onPressed: () {
                              onPickDate?.call(
                                  availableDates.listApplicableTime[index]);
                            },
                            isActive: DateUtils.isSameDay(
                                availableDates!.listApplicableTime[index],
                                selectedDate),
                            value: availableDates
                                .listApplicableTime[index].dateTimeVi);
                      }),
                ),
                SizedBox(
                  width: 16.W,
                ),
                TripcIconButton(
                  onPressed: onTapCalendar,
                  child: Row(
                    children: [
                      AppAssets.origin()
                          .icCalendar
                          .widget(height: 16.H, width: 16.H),
                      SizedBox(
                        width: 5.W,
                      ),
                      AppAssets.origin()
                          .icBlueRight
                          .widget(height: 16.H, width: 16.H),
                    ],
                  ),
                )
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              AppAssets.origin().icICircle.widget(height: 16.H, width: 16.H),
              SizedBox(
                width: 8.W,
              ),
              Expanded(
                child: TripcText(
                  context.strings.text_note_price_at_local_time,
                  fontSize: 12,
                  enableAutoResize: true,
                  maxLines: 2,
                  fontWeight: FontWeight.w400,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().secondDarkGreyTextColor,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _selectTimeFrame(BuildContext context,
      {required List<String> timeFrame,
      required String selectedTimeFrame,
      Function(String)? onTap}) {
    return Padding(
      padding: EdgeInsets.only(left: 24.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TripcText(
            context.strings.text_time_frame,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(bottom: 16.H),
          ),
          SizedBox(
            height: 40.H,
            child: ListView.separated(
                itemCount: timeFrame.length,
                padding: EdgeInsets.only(right: 24.W),
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, _) => SizedBox(
                      width: 10.W,
                    ),
                itemBuilder: (context, index) {
                  return TripcOptionButton(
                      onPressed: () {
                        onTap?.call(timeFrame[index]);
                      },
                      isActive: selectedTimeFrame == timeFrame[index],
                      value: timeFrame[index]);
                }),
          ),
        ],
      ),
    );
  }

  Widget _selectSeatType(BuildContext context,
      {required List<AttributeValue> seatType,
      AttributeValue? selectedSeatType,
      Function(AttributeValue)? onTap}) {
    return Padding(
      padding: EdgeInsets.only(left: 24.W, bottom: 18.H),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TripcText(
            context.strings.text_seat_type,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(bottom: 16.H),
          ),
          SizedBox(
            height: 40.H,
            child: ListView.separated(
                itemCount: seatType.length,
                padding: EdgeInsets.only(right: 24.W),
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, _) => SizedBox(
                      width: 10.W,
                    ),
                itemBuilder: (context, index) {
                  return TripcOptionButton(
                      onPressed: () {
                        onTap?.call(seatType[index]);
                      },
                      isActive: selectedSeatType == seatType[index],
                      value: seatType[index].seatType ?? '');
                }),
          ),
        ],
      ),
    );
  }

  Widget _orderInfo(BuildContext context, {VoidCallback? onTapDetail}) {
    return Stack(
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 8.H, horizontal: 24.W),
          color: AppAssets.origin().lightCream,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppAssets.origin().icICircle.widget(
                      height: 16.H,
                      width: 16.H,
                      color: AppAssets.origin().black),
                  SizedBox(
                    width: 8.W,
                  ),
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TripcText(
                          context.strings.text_order_information,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          textAlign: TextAlign.start,
                          textColor: AppAssets.origin().blackColor,
                        ),
                        SizedBox(
                          height: 8.H,
                        ),
                        TripcRichText(
                          text: '',
                          maxLines: 1,
                          children: [
                            TextSpan(
                              text: '\u2022  ',
                              style: AppAssets.origin()
                                  .normalTextStyle
                                  .copyWith(
                                      fontSize: 12.SP,
                                      fontWeight: FontWeight.w300,
                                      color: AppAssets.origin().blackColor),
                            ),
                            TextSpan(
                              text: context
                                  .strings.text_order_information_first_row,
                              style:
                                  AppAssets.origin().normalTextStyle.copyWith(
                                        fontSize: 12.SP,
                                        fontWeight: FontWeight.w300,
                                        overflow: TextOverflow.ellipsis,
                                        color: AppAssets.origin().blackColor,
                                      ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 4.H,
                        ),
                        TripcRichText(
                          text: '',
                          maxLines: 1,
                          children: [
                            TextSpan(
                              text: '\u2022  ',
                              style: AppAssets.origin()
                                  .normalTextStyle
                                  .copyWith(
                                      fontSize: 12.SP,
                                      fontWeight: FontWeight.w300,
                                      color: AppAssets.origin().blackColor),
                            ),
                            TextSpan(
                              text: context
                                  .strings.text_order_information_second_row,
                              style:
                                  AppAssets.origin().normalTextStyle.copyWith(
                                        fontSize: 12.SP,
                                        fontWeight: FontWeight.w300,
                                        overflow: TextOverflow.ellipsis,
                                        color: AppAssets.origin().blackColor,
                                      ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 4.H,
                        ),
                        TripcRichText(
                          text: '',
                          maxLines: 1,
                          children: [
                            TextSpan(
                              text: '\u2022  ',
                              style: AppAssets.origin()
                                  .normalTextStyle
                                  .copyWith(
                                      fontSize: 12.SP,
                                      fontWeight: FontWeight.w300,
                                      color: AppAssets.origin().blackColor),
                            ),
                            TextSpan(
                              text: context
                                  .strings.text_order_information_third_row,
                              style: AppAssets.origin()
                                  .normalTextStyle
                                  .copyWith(
                                      overflow: TextOverflow.ellipsis,
                                      fontSize: 12.SP,
                                      fontWeight: FontWeight.w300,
                                      color: AppAssets.origin().blackColor),
                            ),
                          ],
                        ),
                      ])
                ],
              )
            ],
          ),
        ),
        Positioned(
          top: 6.H,
          right: 33.W,
          child: TripcIconButton(
            onPressed: onTapDetail,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                TripcText(
                  context.strings.text_detail,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().darkBlueColor,
                  padding: EdgeInsets.only(right: 0.W),
                  ignorePointer: true,
                ),
                AppAssets.origin().icBlueRight.widget(height: 14.H, width: 14.H)
              ],
            ),
          ),
        )
      ],
    );
  }
}
