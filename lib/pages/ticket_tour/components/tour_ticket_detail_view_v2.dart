import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/api_tour_response/applicable_time_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/ticket_tour/components/order_info.dart';
import 'package:tripc_app/pages/ticket_tour/components/tour_ticket_area.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_option_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../providers/providers.dart';
import 'cancel_payment_bottom_sheet.dart';
import 'components.dart';

class TourTicketDetailViewV2 extends ConsumerStatefulWidget {
  const TourTicketDetailViewV2({super.key, required this.selectedTour});

  final TourResponse? selectedTour;

  @override
  ConsumerState<TourTicketDetailViewV2> createState() =>
      _TourTicketDetailViewState();
}

class _TourTicketDetailViewState extends ConsumerState<TourTicketDetailViewV2> {
  @override
  Widget build(BuildContext context) {
    return Column(children: [
      if (widget.selectedTour != null)
        TourInfoArea(
          tour: widget.selectedTour!,
        ),
      _orderInfo(onTapDetail: () {
        dialogHelpers.show(
          context,
          child: Dialog(
            insetPadding: EdgeInsets.only(
                left: 16.W,
                right: 16.W,
                bottom: context.mediaQuery.size.height / 2 - 350.H),
            elevation: 0,
            backgroundColor: AppAssets.init.whiteBackgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.SP),
            ),
            child: const CancelPaymentBottomSheet(),
          ),
        );
      }, context),
    ]);
  }

  Widget _pickServiceArea(BuildContext context, WidgetRef ref,
      {TourResponse? selectedTour}) {
    final selectedDate =
        ref.watch(pTicketTourProvider.select((value) => value.selectedDate));
    final selectedTime =
        ref.watch(pTicketTourProvider.select((value) => value.selectedTime));
    final availableDates = selectedTour?.applicableTime;
    final seatAttributes =
        ref.watch(pTicketTourProvider.select((value) => value.seatAttributes));

    return Column(
      children: [
        _selectPackage(
          context,
          selectedDate: selectedDate,
          availableDates: availableDates,
          onPickDate: (value) =>
              ref.read(pTicketTourProvider.notifier).selectDate(date: value),
          onTapCalendar: () => bottomSheetHelpers.show(context,
              child: TripcCalendarBottomSheet(
                  onSelectDate: (date) {
                    DateTime? selectedDate;
                    if (availableDates == null) return;
                    if (!date.isBefore(availableDates.endDateTime) &&
                        !date.isAfter(availableDates.endDateTime)) {
                      selectedDate = date;
                    } else {
                      selectedDate = null;
                    }
                    ref
                        .read(pTicketTourProvider.notifier)
                        .selectDate(date: selectedDate);
                  },
                  selectedDay: selectedDate ?? DateTime.now())),
        ),
        SizedBox(
          height: 16.H,
        ),
        _selectTimeFrame(
            onTap: (value) => ref
                .read(pTicketTourProvider.notifier)
                .selectTime(timeFrame: value),
            context,
            selectedTimeFrame: selectedTime ?? '',
            timeFrame: availableDates?.time ?? []),
        SizedBox(
          height: 16.H,
        ),
        _selectSeatType(
          onTap: (value) => ref
              .read(pTicketTourProvider.notifier)
              .selectSeatType(seatType: value),
          context,
          selectedSeatType: ref.watch(pTicketTourProvider.select(
            (value) => value.selectedSeatType,
          )),
          seatType: seatAttributes,
        )
      ],
    );
  }

  Widget _selectPackage(BuildContext context,
      {required DateTime? selectedDate,
      required ApplicableTimeResponse? availableDates,
      Function(DateTime)? onPickDate,
      VoidCallback? onTapCalendar}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TripcText(context.strings.text_select_service_package,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              textAlign: TextAlign.start,
              textColor: AppAssets.origin().blackColor,
              padding: EdgeInsets.only(bottom: 12.H)),
          Container(
            margin: EdgeInsets.symmetric(vertical: 4.H).copyWith(bottom: 6.H),
            height: 40.H,
            child: Row(
              children: [
                Expanded(
                  child: ListView.separated(
                      itemCount: availableDates?.listApplicableTime.length ?? 0,
                      scrollDirection: Axis.horizontal,
                      separatorBuilder: (context, _) => SizedBox(
                            width: 10.W,
                          ),
                      itemBuilder: (context, index) {
                        return TripcOptionButton(
                            onPressed: () {
                              onPickDate?.call(
                                  availableDates.listApplicableTime[index]);
                            },
                            isActive: DateUtils.isSameDay(
                                availableDates!.listApplicableTime[index],
                                selectedDate),
                            value: availableDates
                                .listApplicableTime[index].dateTimeVi);
                      }),
                ),
                SizedBox(
                  width: 16.W,
                ),
                TripcIconButton(
                  onPressed: onTapCalendar,
                  child: Row(
                    children: [
                      AppAssets.origin()
                          .icCalendar
                          .widget(height: 16.H, width: 16.H),
                      SizedBox(
                        width: 5.W,
                      ),
                      AppAssets.origin()
                          .icBlueRight
                          .widget(height: 16.H, width: 16.H),
                    ],
                  ),
                )
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              AppAssets.origin().icICircle.widget(height: 16.H, width: 16.H),
              SizedBox(
                width: 8.W,
              ),
              Expanded(
                child: TripcText(
                  context.strings.text_note_price_at_local_time,
                  fontSize: 12,
                  enableAutoResize: true,
                  maxLines: 2,
                  fontWeight: FontWeight.w400,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().secondDarkGreyTextColor,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _selectTimeFrame(BuildContext context,
      {required List<String> timeFrame,
      required String selectedTimeFrame,
      Function(String)? onTap}) {
    return Padding(
      padding: EdgeInsets.only(left: 24.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TripcText(
            context.strings.text_time_frame,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(bottom: 16.H),
          ),
          SizedBox(
            height: 40.H,
            child: ListView.separated(
                itemCount: timeFrame.length,
                padding: EdgeInsets.only(right: 24.W),
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, _) => SizedBox(
                      width: 10.W,
                    ),
                itemBuilder: (context, index) {
                  return TripcOptionButton(
                      onPressed: () {
                        onTap?.call(timeFrame[index]);
                      },
                      isActive: selectedTimeFrame == timeFrame[index],
                      value: timeFrame[index]);
                }),
          ),
        ],
      ),
    );
  }

  Widget _selectSeatType(BuildContext context,
      {required List<AttributeValue> seatType,
      AttributeValue? selectedSeatType,
      Function(AttributeValue)? onTap}) {
    return Padding(
      padding: EdgeInsets.only(left: 24.W, bottom: 18.H),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TripcText(
            context.strings.text_seat_type,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(bottom: 16.H),
          ),
          SizedBox(
            height: 40.H,
            child: ListView.separated(
                itemCount: seatType.length,
                padding: EdgeInsets.only(right: 24.W),
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, _) => SizedBox(
                      width: 10.W,
                    ),
                itemBuilder: (context, index) {
                  return TripcOptionButton(
                      onPressed: () {
                        onTap?.call(seatType[index]);
                      },
                      isActive: selectedSeatType == seatType[index],
                      value: seatType[index].seatType ?? '');
                }),
          ),
        ],
      ),
    );
  }

  Widget _orderInfo(BuildContext context, {VoidCallback? onTapDetail}) {
    return OrderInfo(
      title: context.strings.text_order_information.toSentenceCase(),
      contents: [
        context.strings.text_order_information_first_row,
        context.strings.text_order_information_second_row,
        context.strings.text_order_information_third_row,
      ],
      onTapDetail: onTapDetail,
    );
  }
}
