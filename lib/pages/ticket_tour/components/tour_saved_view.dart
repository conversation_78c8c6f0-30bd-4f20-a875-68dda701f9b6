import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TourSavedView extends StatelessWidget {
  const TourSavedView({
    super.key,
    this.onPressed,
  });

  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 165.W,
        height: 44.H,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(4.H),
        ),
        child: Padding(
          padding: EdgeInsets.all(8.H),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  AppAssets.init.icHeartFill.widget(
                    width: 24.W,
                  ),
                  SizedBox(width: 4.W),
                  TripcText(
                    context.strings.text_saved,
                    fontSize: 14.SP,
                    fontWeight: FontWeight.w500,
                    textColor: Colors.white,
                  ),
                ],
              ),
              TripcButton(
                onPressed: onPressed,
                title: context.strings.text_see,
                height: 22.H,
                width: 50.W,
                style: AppButtonStyle(
                  backgroundColor: AppAssets.init.darkBlue5FF,
                  radius: 4.H,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
