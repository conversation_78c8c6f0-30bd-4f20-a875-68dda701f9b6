import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class CancelPaymentBottomSheet extends StatelessWidget {
  const CancelPaymentBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.H, horizontal: 16.W)
          .copyWith(bottom: 0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                context.strings.text_order_information,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              TripcIconButton(
                onPressed: () => Navigator.pop(context),
                child: AppAssets.origin().icClose.widget(
                    width: 13.H,
                    height: 13.H,
                    color: AppAssets.origin().blackColor),
              )
            ],
          ),
          TripcText(
            context.strings.text_available_omorrow,
            fontSize: 14,
            fontWeight: FontWeight.w300,
            padding: EdgeInsets.only(top: 16.H),
          ),
          TripcText(
            context.strings.text_must_purchase_before_six,
            fontSize: 12,
            textAlign: TextAlign.start,
            fontWeight: FontWeight.w300,
            padding: EdgeInsets.symmetric(vertical: 12.H),
          ),
          TripcText(
            context.strings.text_ticket_change_is_not_required,
            fontSize: 14,
            fontWeight: FontWeight.w300,
          ),
          TripcText(
            context.strings.text_need_e_invoice,
            textAlign: TextAlign.start,
            fontSize: 12,
            fontWeight: FontWeight.w300,
            padding: EdgeInsets.symmetric(vertical: 12.H),
          ),
          TripcText(
            context.strings.text_conditional_cancellation,
            fontSize: 14,
            fontWeight: FontWeight.w300,
            padding: EdgeInsets.only(bottom: 14.H),
          ),
          Table(
            border: TableBorder.all(
                width: 0.5.H,
                color: AppAssets.origin().secondDarkGreyTextColor,
                borderRadius: BorderRadius.circular(8.SP)),
            children: [
              _buildRow([
                context.strings.text_cancellation_time,
                context.strings.text_fee_text
              ], isHeader: true),
              _buildRow(
                  [context.strings.text_day_before_the_previous_use_date, "0"]),
              _buildRow([
                context.strings.text_day_before_the_next_use_date,
                "100.0%"
              ]),
            ],
          ),
          TripcText(
            context.strings.text_cannot_be_canceled_one_actived,
            fontSize: 12,
            textAlign: TextAlign.start,
            fontWeight: FontWeight.w300,
            padding: EdgeInsets.only(top: 16.H),
          ),
          TripcText(
            context.strings.text_if_a_discount_is_applied,
            fontSize: 12,
            textAlign: TextAlign.start,
            fontWeight: FontWeight.w300,
            height: 1.6,
          ),
          SizedBox(
            height: context.spacingBottom,
          )
        ],
      ),
    );
  }

  TableRow _buildRow(List<String> cells, {bool isHeader = false}) {
    return TableRow(
      children: cells.map((cell) {
        return Container(
          color: isHeader ? AppAssets.origin().gray1F1 : null,
          padding: EdgeInsets.symmetric(horizontal: 15.W, vertical: 10.H),
          child: Text(
            cell,
            textAlign: isHeader ? TextAlign.center : TextAlign.start,
            style: AppAssets.origin().normalTextStyle.copyWith(
                  fontWeight: isHeader ? FontWeight.w400 : FontWeight.w300,
                  fontSize: 14.SP,
                ),
          ),
        );
      }).toList(),
    );
  }
}
