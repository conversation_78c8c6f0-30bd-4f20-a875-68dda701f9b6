import 'package:flutter/material.dart';
import 'package:tripc_app/pages/ticket_tour/components/tour_information_item.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../models/remote/api_tour_response/tour_response.dart';
class TourInformationView extends StatelessWidget {
  const TourInformationView({super.key, this.selectedTour});
  final TourResponse? selectedTour;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TourInformationItem(
          title: context.strings.text_tour_time,
          value: selectedTour?.tourTime ?? '-',
          fontStyle: FontStyle.normal,
        ),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 12.H,
        ),
        TourInformationItem(
          padding: EdgeInsets.zero,
          title: context.strings.text_departure_location,
          value: selectedTour?.departureLocation ?? '-',
          fontStyle: FontStyle.normal,
        ),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 12.H,
        ),
        TourInformationItem(
          title: context.strings.text_restaurant_space,
          value: selectedTour?.restaurant ?? '-',
          fontStyle: FontStyle.normal,
        ),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 12.H,
        ),
        TourInformationItem(
          title: context.strings.text_shuttle_service,
          value: selectedTour?.supplier?.name ?? '-',
          fontStyle: FontStyle.normal,
        ),
      ],
    );
  }
}
