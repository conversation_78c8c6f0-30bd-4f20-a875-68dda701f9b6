import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/advertisement_model.dart';
import 'package:tripc_app/models/app/gender_enum.dart';
import 'package:tripc_app/models/app/tour_type_enum.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/app/passenger.dart';
import 'package:tripc_app/models/app/passenger_information.dart';
import 'package:tripc_app/models/app/payment.dart';
import 'package:tripc_app/models/remote/api_tour_response/api_tour_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/applicable_time_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/service_type_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/sub_product_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/models/remote/search_response/keyword_response.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/services/apis/contact/api_contact.dart';
import 'package:tripc_app/services/apis/tours/api_tours.dart';
import 'package:tripc_app/utils/app_enum.dart';
import 'package:tripc_app/utils/app_extension.dart';
import '../../../models/remote/contact_response.dart';
import '../../../utils/app_validation.dart';

enum ListType {
  search,
  all,
  catBaTuanChau,
  tuanChauCatBa,
  myRecently
}

class TicketTourModelV2 {
  TicketTourModelV2(
      {this.selectedTour,
      this.aldultQuantity = 0,
      this.childQuantity = 0,
      this.passengerInfo,
      this.selectedDate,
      this.selectedSeatType,
      this.keyWord = '',
      required this.advertisement,
      this.errorMessage,
      this.listServiceTypes = const [],
      this.entertainmentList = const [],
      this.searchList = const [],
      this.tuanChauCatBaList = const [],
      this.catBaTuanChauList = const [],
      this.myRecentleTour = const [],
      this.parkList = const [],
      this.zooAndAquariumList = const [],
      this.playgroundList = const [],
      this.sightseeingTicketList = const [],
      this.museumList = const [],
      this.canLoadMore = false,
      this.isLoadingLoadMore = false,
      this.isLoading = false,
      this.page = 1,
      this.pageSize = 20,
      this.selectedTime,
      this.visiblePortal = false,
      this.isSave = false,
      this.contactFullName = '',
      this.contactEmail = '',
      this.contactPhoneNumber = '',
      this.contactErrorEmail = '',
      this.passengerEmail = '',
      this.passengerEmailError = '',
      this.passengerFullName = '',
      this.passengerPhoneNumber = '',
      this.shuttleHotel = '',
      this.shuttleContact = '',
      this.shuttleExpected = '',
      this.shuttleTime = '',
      this.contactResponse,
      this.guardianName = '',
      this.isSearching = false,
      this.currentType = TicketType.aldult,
      this.isSavedTourSuccess = false,
      this.suggestTypingSearch,
      this.contactErrorPhoneNumber = '',
      this.phoneError = '',
      this.serviceType = const ServiceType(name: 'Tất cả'),
      this.isFerryTour = false,
      });
  TourResponse? selectedTour;
  int aldultQuantity;
  int childQuantity;
  PassengerInfo? passengerInfo;
  DateTime? selectedDate;
  String? selectedTime;
  AttributeValue? selectedSeatType;
  final String keyWord;
  final AdvertisementModel advertisement;
  final List<ServiceType> listServiceTypes;
  final List<TourResponse> entertainmentList;
  final List<TourResponse> searchList;
  final List<TourResponse> tuanChauCatBaList;
  final List<TourResponse> catBaTuanChauList;
  final List<TourResponse> parkList;
  final List<TourResponse> zooAndAquariumList;
  final List<TourResponse> playgroundList;
  final List<TourResponse> sightseeingTicketList;
  final List<TourResponse> museumList;
  final List<TourResponse> myRecentleTour;
  final bool isLoading;
  final bool canLoadMore;
  final bool isLoadingLoadMore;
  final int page;
  final int pageSize;
  final String? errorMessage;
  final bool visiblePortal;
  final bool isSave;
  final String contactFullName;
  final String contactEmail;
  final String contactErrorEmail;
  final String contactPhoneNumber;
  final String passengerFullName;
  final String passengerEmail;
  final String passengerPhoneNumber;
  final String passengerEmailError;
  final String shuttleHotel;
  final String shuttleTime;
  final String shuttleContact;
  final String shuttleExpected;
  final String guardianName;
  final ContactResponse? contactResponse;
  final TicketType currentType;
  final bool isSavedTourSuccess;
  final String contactErrorPhoneNumber;
  final String phoneError;
  final bool isSearching;
  final ServiceType serviceType;
  final bool isFerryTour;
  List<KeywordResponse>? suggestTypingSearch;

  static TicketTourModelV2 getDefault() {
    return TicketTourModelV2(
      advertisement:
          AdvertisementModel(discount: 10, advertisement: 'ON FLIGHT TICKETS'),
      passengerInfo: PassengerInfo(passengers: []),
    );
  }

  TicketTourModelV2 copyWith({
    List<TourResponse>? serviceItems,
    TourResponse? selectedTour,
    DateTime? startDate,
    int? aldultQuantity,
    int? childQuantity,
    PassengerInfo? passengerInfo,
    DateTime? selectedDate,
    String? selectedTime,
    AttributeValue? selectedSeatType,
    String? keyWord,
    bool? isLoading,
    bool? isSearching,
    ServiceType? serviceType,
    List<TourResponse>? entertainmentList,
    List<TourResponse>? searchList,
    List<TourResponse>? tuanChauCatBaList,
    List<TourResponse>? catBaTuanChauList,
    List<TourResponse>? myRecentleTour,
    List<TourResponse>? parkList,
    List<TourResponse>? zooAndAquariumList,
    List<TourResponse>? playgroundList,
    List<TourResponse>? sightseeingTicketList,
    List<TourResponse>? museumList,
    bool? canLoadmore,
    bool? isLoadingLoadMore,
    int? page,
    int? pageSize,
    String? errorMessage,
    AdvertisementModel? advertisement,
    bool? visiblePortal,
    List<TourResponse>? saveTour,
    bool? isSave,
    String? contactFullName,
    String? contactEmail,
    String? contactPhoneNumber,
    String? contactErrorEmail,
    String? passengerEmail,
    String? passengerFullName,
    String? passengerPhoneNumber,
    String? passengerEmailError,
    String? shuttleHotel,
    String? shuttleTime,
    String? shuttleContact,
    String? shuttleExpected,
    ContactResponse? contactResponse,
    String? guardianName,
    TicketType? currentType,
    bool? isSavedTourSuccess,
    String? contactErrorPhoneNumber,
    String? phoneError,
    List<ServiceType>? listServiceTypes,
    List<KeywordResponse>? suggestTypingSearch
  }) {
    return TicketTourModelV2(
      aldultQuantity: aldultQuantity ?? this.aldultQuantity,
      childQuantity: childQuantity ?? this.childQuantity,
      passengerInfo: passengerInfo ?? this.passengerInfo,
      selectedTour: selectedTour ?? this.selectedTour,
      selectedDate: selectedDate ?? this.selectedDate,
      selectedTime: selectedTime ?? this.selectedTime,
      selectedSeatType: selectedSeatType ?? this.selectedSeatType,
      advertisement: advertisement ?? this.advertisement,
      entertainmentList: entertainmentList ?? this.entertainmentList,
      tuanChauCatBaList: tuanChauCatBaList ?? this.tuanChauCatBaList,
      catBaTuanChauList: catBaTuanChauList ?? this.catBaTuanChauList,
      myRecentleTour: myRecentleTour ?? this.myRecentleTour,
      parkList: parkList ?? this.parkList,
      zooAndAquariumList: zooAndAquariumList ?? this.zooAndAquariumList,
      playgroundList: playgroundList ?? this.playgroundList,
      sightseeingTicketList: playgroundList ?? this.sightseeingTicketList,
      museumList: playgroundList ?? this.museumList,
      isLoading: isLoading ?? this.isLoading,
      canLoadMore: canLoadmore ?? canLoadMore,
      isLoadingLoadMore: isLoadingLoadMore ?? this.isLoadingLoadMore,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
      errorMessage: errorMessage ?? this.errorMessage,
      keyWord: keyWord ?? this.keyWord,
      visiblePortal: visiblePortal ?? this.visiblePortal,
      isSave: isSave ?? this.isSave,
      contactFullName: contactFullName ?? this.contactFullName,
      contactEmail: contactEmail ?? this.contactEmail,
      contactPhoneNumber: contactPhoneNumber ?? this.contactPhoneNumber,
      contactErrorEmail: contactErrorEmail ?? this.contactErrorEmail,
      passengerEmail: passengerEmail ?? this.passengerEmail,
      passengerEmailError: passengerEmailError ?? this.passengerEmailError,
      passengerFullName: passengerFullName ?? this.passengerFullName,
      passengerPhoneNumber: passengerPhoneNumber ?? this.passengerPhoneNumber,
      shuttleHotel: shuttleHotel ?? this.shuttleHotel,
      shuttleContact: shuttleContact ?? this.shuttleContact,
      shuttleExpected: shuttleExpected ?? this.shuttleExpected,
      shuttleTime: shuttleTime ?? this.shuttleTime,
      contactResponse: contactResponse ?? this.contactResponse,
      guardianName: guardianName ?? this.guardianName,
      currentType: currentType ?? this.currentType,
      isSavedTourSuccess: isSavedTourSuccess ?? this.isSavedTourSuccess,
      contactErrorPhoneNumber:
          contactErrorPhoneNumber ?? this.contactErrorPhoneNumber,
      phoneError: phoneError ?? this.phoneError,
      listServiceTypes: listServiceTypes ?? this.listServiceTypes,
      isSearching: isSearching ?? this.isSearching,
      serviceType: serviceType ?? this.serviceType,
      suggestTypingSearch: suggestTypingSearch ?? this.suggestTypingSearch,
      searchList: searchList ?? this.searchList
    );
  }

  List<AttributeValue> get seatAttributes {
    if (selectedTour == null) return [];
    final attributeResponse = selectedTour!.attributes?.firstWhereOrNull(
        (element) =>
            element.visibility == 1 &&
            element.type == TourResponseAttibuteValueType.text);
    return attributeResponse?.values ?? [];
  }

  AttributeResponse? get hotDateTime {
    if (selectedTour == null || selectedTour?.attributes == []) return null;
    return selectedTour!.attributes!
        .firstWhereOrNull((element) => element.visibility == 0);
  }

  bool get isButtonConfirmDisable {
    if (currentType == TicketType.aldult) {
      return !(passengerEmailError.isEmpty &&
          passengerEmail.isNotEmpty &&
          phoneError.isEmpty &&
          passengerPhoneNumber.isNotEmpty &&
          passengerFullName.isNotEmpty);
    } else {
      return !(guardianName.isNotEmpty &&
          passengerPhoneNumber.isNotEmpty &&
          phoneError.isEmpty &&
          passengerFullName.isNotEmpty);
    }
  }

  bool get isGoToPaymentDisable {
    return !(contactErrorEmail.isEmpty &&
        contactEmail.isNotEmpty &&
        contactPhoneNumber.isNotEmpty &&
        contactErrorPhoneNumber.isEmpty &&
        contactFullName.isNotEmpty);
  }

  bool get isAllFieldPassenger {
    final passengers = passengerInfo?.passengers.toList();
    return passengers?.any((element) => element.isAllFieldEmpty) ?? true;
  }

  int? get adultId {
    final subProducts = selectedTour?.subProducts?.toList() ?? [];
    if (subProducts.isEmpty) {
      return null;
    }
    return subProducts.first.id;
  }

  int? get childId {
    final subProducts = selectedTour?.subProducts?.toList() ?? [];
    if (subProducts.length < 2) {
      return null;
    }
    return subProducts.last.id;
  }

  String ticketQuantityText(BuildContext context) {
    int aldQty = 0;
    int childQty = 0;
    final cacheList = selectedTour?.subProducts?.toList() ?? [];
    for (var i = 0; i < cacheList.length; i++) {
      if (i == 0) {
        aldQty = cacheList[i].stock;
      } else {
        childQty = cacheList[i].stock;
      }
    }
    return '$aldQty ${context.strings.text_aldult}, $childQty ${context.strings.text_children}';
  }

  int get minTicketPrice {
    if (selectedTour == null) return 0;
    final cacheList = selectedTour?.subProducts?.toList() ?? [];
    if (cacheList.length == 1) {
      return cacheList.first.price ?? 0;
    }
    return cacheList
            .reduce((a, b) => (a.price ?? 0) < (b.price ?? 0) ? a : b)
            .price ??
        0;
  }

  AttributeValue? get selectHotTime {
    if (selectedDate == null || selectedTime == null) return null;
    final time = selectedTour?.attributes
        ?.firstWhereOrNull((element) => element.visibility == 0);
    if (time == null) return null;
    AttributeValue? value;
    time.values?.forEach(
      (e) {
        final applicableTime = applicableTimeResponseFromJson(e.seatType ?? '');
        final times = applicableTime.time ?? [];
        if (!selectedDate!.isBefore(applicableTime.startDateTime) &&
            !selectedDate!.isAfter(applicableTime.endDateTime) &&
            times.contains(selectedTime)) {
          value = e;
        }
      },
    );
    return value;
  }

  int get totalPriceOfSeatType {
    if (selectedTour == null || selectedSeatType == null) return 0;
    final totalQty = selectedTour?.subProducts
            ?.map((e) => e.stock)
            .reduce((a, b) => a + b) ??
        0;
    return totalQty * int.parse(selectedSeatType!.price ?? '0');
  }

  int get totalPriceOfSpeacialTimeFrame {
    if (selectedDate == null || selectedTime == null) return 0;

    final totalQty = selectedTour?.subProducts
            ?.map((e) => e.stock)
            .reduce((a, b) => a + b) ??
        0;

    final time = selectedTour?.attributes?.firstWhereOrNull(
        (element) => element.type == TourResponseAttibuteValueType.time);

    if (time == null) return 0;

    int total = 0;
    time.values?.forEach(
      (e) {
        final applicableTime = e.specialPriceTimeFrame;
        final times = applicableTime?.time ?? [];
        if (applicableTime != null &&
            !selectedDate!.isBefore(applicableTime.startDateTime) &&
            !selectedDate!.isAfter(applicableTime.endDateTime) &&
            times.contains(selectedTime)) {
          total += int.parse(e.price ?? '0');
        }
      },
    );
    return totalQty * total;
  }

  List<int> get totalDiscount {
    if (selectedTour == null) return [];
    final discounts = selectedTour?.discounts?.toList() ?? [];
    final List<int> listTotalDiscount = [];
    selectedTour?.subProducts?.forEach(
      (e) {
        final discount = discounts.firstWhereOrNull((element) =>
            (element.maxQuantity ?? 0) >= e.stock &&
            (element.minQuantity ?? 0) <= e.stock &&
            element.audienceType == e.audience?.id);
        listTotalDiscount.add(e.ticketPrice -
            (int.parse(discount?.discountValue ?? '0') * e.ticketPrice / 100)
                .toInt());
      },
    );
    return listTotalDiscount;
  }

  int get totalPayment {
    if (totalDiscount.isEmpty) return 0;
    final totalPriceOfTicket = totalDiscount.reduce((a, b) => a + b);
    return totalPriceOfSpeacialTimeFrame +
        totalPriceOfTicket +
        totalPriceOfSeatType;
  }

  bool get selectButtonEnable {
    if (seatAttributes.isNotEmpty) {
      return selectedDate != null &&
          selectedTime != null &&
          selectedSeatType != null;
    }
    return selectedDate != null && selectedTime != null;
  }

  Payment get payment {
    return Payment(
        bookingCode: 'HLB012',
        aldultQuantity: aldultQuantity,
        childQuantity: childQuantity,
        bookingDate: DateTime.now(),
        departureDate: DateTime.now(),
        service: selectedTour,
        totalPrice: totalPayment,
        passengerInfo: passengerInfo,
        selectHotTime: selectHotTime,
        selectSeat: selectedSeatType,
        selectedDate: selectedDate,
        selectedTime: selectedTime);
  }
}

class TicketTourScreenProviderV2 extends StateNotifier<TicketTourModelV2> {
  TicketTourScreenProviderV2(super._state);
  final ApiTours _api = ApiTours();
  final ApiContact _apiContact = ApiContact();

  // Cache map to store API responses
  final Map<int, List<TourResponse>> _toursByCategoryCache = {};
  List<TourResponse>? _myRecentTourCache;
  final Map<int, TourResponse> _tourDetailCache = {};
  List<TourResponse>? _listTourCatBaTuanChauCache;
  List<TourResponse>? _listTourTuanChauCatBaCache;

  void clearCacheData() {
    // Clear internal cache
    _toursByCategoryCache.clear();
    _myRecentTourCache = null;
    _listTourCatBaTuanChauCache = null;
    _listTourTuanChauCatBaCache = null;
    _tourDetailCache.clear();


    state = state.copyWith(
      listServiceTypes: [],
      entertainmentList: [],
      tuanChauCatBaList: [],
      catBaTuanChauList: [],
      myRecentleTour: [],
      isSearching: false,
      searchList: [],
      keyWord: '',
      serviceType: const ServiceType(name: 'Tất cả'),

    );
  }

  void selectTour(TourResponse value) {
    state = state.copyWith(selectedTour: value);
  }

  void onCloseSavedPopup() {
    state = state.copyWith(visiblePortal: false);
  }

  void setEmail(BuildContext context, {required String value}) {
    state = state.copyWith(
        contactEmail: value,
        contactErrorEmail: ValidationAccount.emailValidation(context, value));
  }

  void setCurrentTicketType(TicketType type) {
    state = state.copyWith(
      currentType: type,
    );
  }

  Future<void> getSuggestSearch() async {
    try {
      final response = await _api.getSearchSuggest(txt: state.keyWord).timeout(
        const Duration(
          seconds: 30,
        ),
      );
      state = state.copyWith(suggestTypingSearch: response.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        // setErrorMessage(exceptionMessage.error);
      }
    }
  }

  void clearSuggestions() {
    state = state.copyWith(suggestTypingSearch: []);
  }

  void setPhoneNumber(String value, BuildContext context) {
    state = state.copyWith(
      contactPhoneNumber: value,
      contactErrorEmail: state.contactErrorEmail,
      contactErrorPhoneNumber:
          value.length < 9 ? context.strings.text_error_phone_9 : '',
    );
  }

  void setFullName(String value) {
    state = state.copyWith(
      contactFullName: value,
      contactErrorEmail: state.contactErrorEmail,
    );
  }

  void setPassengerEmail(BuildContext context, {required String value}) {
    state = state.copyWith(
      passengerEmail: value,
      passengerEmailError: ValidationAccount.emailValidation(context, value),
    );
  }

  void setPassengerFullName(String value) {
    state = state.copyWith(
      passengerFullName: value,
    );
  }

  void setGuardianName(String value) {
    state = state.copyWith(
      guardianName: value,
    );
  }

  void setPassengerPhoneNumber(String value, BuildContext context) {
    state = state.copyWith(
      passengerPhoneNumber: value,
      phoneError: value.length < 10 ? context.strings.text_error_phone_10 : '',
    );
  }

  void resetPassengerInfo() {
    state = state.copyWith(
        passengerPhoneNumber: '',
        passengerFullName: '',
        passengerEmailError: '',
        passengerEmail: '',
        guardianName: '',
        phoneError: '');
  }

  void setShuttleHotel(String value) {
    state = state.copyWith(
      shuttleHotel: value,
    );
  }

  void setShuttleTime(String value) {
    state = state.copyWith(
      shuttleTime: value,
    );
  }

  void setShuttleContact(String value) {
    state = state.copyWith(
      shuttleContact: value,
    );
  }

  void setShuttleExpected(String value) {
    state = state.copyWith(
      shuttleExpected: value,
    );
  }

  void updatePassenger({Passenger? passenger}) {
    final index = state.passengerInfo?.passengers
            .indexWhere((e) => e.id == passenger?.id) ??
        -1;
    if (index < 0) {
      return;
    }
    final cachePassengers = [...state.passengerInfo?.passengers ?? []];
    cachePassengers[index] = cachePassengers[index].copyWith(
      name: state.passengerFullName.isEmpty
          ? passenger?.name
          : state.passengerFullName,
      email: state.passengerEmail.isEmpty
          ? passenger?.email
          : state.passengerEmail,
      phone: state.passengerPhoneNumber.isEmpty
          ? passenger?.phone
          : state.passengerPhoneNumber,
      guardianName: state.guardianName.isEmpty
          ? passenger?.guardianName
          : state.guardianName,
    );
    state = state.copyWith(
        passengerInfo:
            state.passengerInfo?.copyWith(passengers: [...cachePassengers]));
  }

  void removePassenger(int? subProductId) {
    PassengerInfo cachePassengerInfo =
        state.passengerInfo ?? PassengerInfo(passengers: []);
    final List<Passenger> updatedPassengers =
        cachePassengerInfo.passengers.toList();
    final lastSubProductIndex = updatedPassengers
        .lastIndexWhere((element) => element.subProductId == subProductId);
    if (lastSubProductIndex != -1) {
      updatedPassengers.removeAt(lastSubProductIndex);
    }
    cachePassengerInfo = cachePassengerInfo.copyWith(
      passengers: updatedPassengers,
    );
    state = state.copyWith(
      passengerInfo: cachePassengerInfo,
    );
  }

  void addPassenger({int? subProductId, TicketType type = TicketType.aldult}) {
    PassengerInfo cachePassengerInfo =
        state.passengerInfo ?? PassengerInfo(passengers: []);
    final List<Passenger> updatedPassengers =
        cachePassengerInfo.passengers.toList();
    updatedPassengers.add(
      Passenger(
        gender: GenderEnum.male.value,
        name: state.passengerFullName,
        email: state.passengerEmail,
        phone: state.passengerPhoneNumber,
        guardianName: state.guardianName,
        id: DateTime.now().millisecondsSinceEpoch,
        subProductId: subProductId ?? 1,
        type: type,
      ),
    );
    cachePassengerInfo = cachePassengerInfo.copyWith(
      passengers: updatedPassengers,
    );

    state = state.copyWith(
      passengerInfo: cachePassengerInfo,
    );
  }

  void updateToTalInfomation({required String specicalNote}) {
    state = state.copyWith(
        passengerInfo: state.passengerInfo?.copyWith(
            name: state.contactFullName,
            email: state.contactEmail,
            phoneNumber: state.contactPhoneNumber,
            contactResponse: state.contactResponse,
            specialRequest: specicalNote,
            shuttleInfo: ShuttleInfo(
              hotel: state.shuttleHotel,
              contact: state.shuttleContact,
              expected: state.shuttleExpected,
              time: state.shuttleTime,
            )));
  }

  void addQuantityTicket({required TicketType ticketType}) {
    if (ticketType == TicketType.aldult) {
      state = state.copyWith(aldultQuantity: state.aldultQuantity + 1);
      addPassenger(subProductId: state.adultId, type: TicketType.aldult);
    } else {
      state = state.copyWith(childQuantity: state.childQuantity + 1);
      addPassenger(subProductId: state.childId, type: TicketType.child);
    }
  }

  void reduceQuantityTicket({required TicketType ticketType}) {
    if (ticketType == TicketType.aldult) {
      state = state.copyWith(
          aldultQuantity:
              state.aldultQuantity > 0 ? state.aldultQuantity - 1 : 0);
      removePassenger(state.adultId);
    } else {
      state = state.copyWith(
          childQuantity: state.childQuantity > 0 ? state.childQuantity - 1 : 0);
      removePassenger(state.childId);
    }
  }

  void setLoading(bool value) {
    state = state.copyWith(
      isLoading: value,
    );
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void forceLoadingLoadMore(bool value) {
    state = state.copyWith(isLoadingLoadMore: value);
  }

  void setPageGetData(
      {required int page, required int pageSize, String? keyword}) {
    state = state.copyWith(page: page, pageSize: pageSize, keyWord: keyword);
  }

  void viewMoreTours({required TourType category}) {
    switch (category) {
      case TourType.favoriteTour || TourType.favoriteCombo:
        getTourBestSeller(viewMore: true);
        break;
      case TourType.superCheapCombo || TourType.superCheapTour:
        getDisCount(viewMore: true);
        break;
      case TourType.buyMoreEarnMore:
        getTourBuyMoreEarnMore(viewMore: true);
        break;
      case TourType.dealsAroundHere:
        getTourDealsAroundHere();
        break;
      case TourType.tourSaved:
        getSaveTour();
        break;
      default:
        break;
    }
  }

  Future<void> listSavedTour(int tourId, ListType type) async {
    try {
      final result = await _api.savedTour(tourId: tourId).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      updateListTour(tourId, result.data?.isSave ?? false, type);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  void updateListTour(int tourId, bool isSave, ListType type) {
    final cacheList = [
      if (type == ListType.all) ...state.entertainmentList,
      if (type == ListType.search) ...state.searchList,
      if (type == ListType.tuanChauCatBa) ...state.tuanChauCatBaList,
      if (type == ListType.catBaTuanChau) ...state.catBaTuanChauList,
      if (type == ListType.myRecently) ...state.myRecentleTour
    ];
    final index = cacheList.indexWhere((element) => element.id == tourId);
    if (index == -1) {
      return;
    }
    cacheList[index] = cacheList[index].copyWith(
      like: isSave,
    );
    if (type == ListType.all) state = state.copyWith(entertainmentList: cacheList);
    if (type == ListType.search) state = state.copyWith(searchList: cacheList);
    if (type == ListType.tuanChauCatBa) state = state.copyWith(tuanChauCatBaList: cacheList);
    if (type == ListType.catBaTuanChau) state = state.copyWith(catBaTuanChauList: cacheList);
    if (type == ListType.myRecently) state = state.copyWith(myRecentleTour: cacheList);
  }

  void appendListViewMoreTour(ListTourResponse result) {
    // if (state.viewMoreTours.isEmpty) {
    //   state = state.copyWith(
    //     // viewMoreTours: result.data,
    //   );
    //   state = state.copyWith(
    //       // canLoadmore: state.viewMoreTours.length < (result.total ?? 0));
    //   return;
    // }
    state =
        // state.copyWith(viewMoreTours: [...state.viewMoreTours, ...result.data]);
        state = state.copyWith(
            // canLoadmore: state.viewMoreTours.length < (result.total ?? 0),
            );
    forceLoadingLoadMore(false);
  }

  Future<void> getDisCount(
      {bool viewMore = false, bool useLoading = true}) async {
    final request = ListDataRequest(
        page: state.page, pageSize: state.pageSize, keyWord: state.keyWord);
    setLoading(true);
    try {
      final result = await _api.getTourDiscounts(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (viewMore) {
        appendListViewMoreTour(result);
      } else {
        // state = state.copyWith(discounts: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getTourBestSeller(
      {bool viewMore = false, bool useLoading = true}) async {
    setLoading(useLoading);
    final request = ListDataRequest(
        page: state.page, pageSize: state.pageSize, keyWord: state.keyWord);
    try {
      final result = await _api.getTourBestSeller(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (viewMore) {
        // appendListViewMoreTour(result);
      } else {
        // state = state.copyWith(bestSeller: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getServiceTypesList(int id) async {
    setLoading(true);
    try {
      final result = await _api.getServiceTypeByParentId(id: id).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.status) {
        // Store in cache
        state = state.copyWith(listServiceTypes: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getToursByServiceType(
      int categoryId, ListDataRequest request) async {
    // Check cache first
    final cacheKey = categoryId;
    request = request.copyWith(keyWord: state.keyWord);
    if (_toursByCategoryCache.containsKey(cacheKey)) {
      state =
          state.copyWith(entertainmentList: _toursByCategoryCache[cacheKey]);
      return;
    }
    setLoading(true);

    try {
      final result = await _api
          .getToursListByCategory(request: request, categoryId: categoryId)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );

      if (result.status) {
        // Store in cache
        _toursByCategoryCache[cacheKey] = result.data;

        if (result.data.isEmpty) {
          state = state.copyWith(entertainmentList: []);
          setLoading(false);
          return;
        }

        state = state.copyWith(entertainmentList: result.data);
      }

      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> searchTours(
      int categoryId, ListDataRequest request) async {
    setLoading(true);
    try {
      final result = await _api
          .getToursListByCategory(request: request, categoryId: categoryId)
          .timeout(
        const Duration(
          seconds: 30,
        ),
      );

      if (result.status) {
        state = state.copyWith(searchList: result.data);
      }

      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getTourTuanChauCatBa(ListDataRequest request) async {
    // Check cache first
    if (_listTourTuanChauCatBaCache != null) {
      state = state.copyWith(tuanChauCatBaList: _listTourTuanChauCatBaCache);
      return;
    }

    setLoading(true);
    try {
      final result = await _api
          .getToursFerry(request: ListDataRequest(page: 1, pageSize: 4, ferrySlug: FerryType.tuanChauCatBa.slug))
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.status) {
        // Store in cache
        _listTourTuanChauCatBaCache = result.data;
        state = state.copyWith(tuanChauCatBaList: result.data);
      }

      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getTourCatBaTuanChau(ListDataRequest request) async {
    // Check cache first
    if (_listTourCatBaTuanChauCache != null) {
      state = state.copyWith(catBaTuanChauList: _listTourCatBaTuanChauCache);
      return;
    }

    setLoading(true);
    try {
      final result = await _api
          .getToursFerry(request: ListDataRequest(page: 1, pageSize: 4, ferrySlug: FerryType.catBaTuanChau.slug))
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.status) {
        // Store in cache
        _listTourCatBaTuanChauCache = result.data;
        state = state.copyWith(catBaTuanChauList: result.data);
      }

      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getMyRecentleTour() async {
    // Check cache first
    if (_myRecentTourCache != null) {
      state = state.copyWith(myRecentleTour: _myRecentTourCache);
      return;
    }

    setLoading(true);
    try {
      final result = await _api
          .getRecentlyViewed(request: ListDataRequest(page: 1, pageSize: 4))
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.status) {
        // Store in cache
        _myRecentTourCache = result.data;
        state = state.copyWith(myRecentleTour: result.data);
      }

      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getDetailedTour({required int? tourId}) async {
    // Check cache first
    if (tourId != null && _tourDetailCache.containsKey(tourId)) {
      final cachedTour = _tourDetailCache[tourId];
      state = state.copyWith(
          selectedTour: cachedTour, isSave: cachedTour?.isLike ?? false);
      if (cachedTour?.applicableTime != null) {
        selectDate(date: cachedTour?.applicableTime?.currentDate);
      }
      return;
    }

    setLoading(true);
    try {
      final result = await _api.getDetailedTour(tourId: tourId ?? 0).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);

      // Store in cache
      if (tourId != null) {
        _tourDetailCache[tourId] = result.data!;
      }

      state = state.copyWith(
          selectedTour: result.data, isSave: result.data?.isLike ?? false);
      if (result.data?.applicableTime != null) {
        selectDate(date: result.data?.applicableTime?.currentDate);
      }
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getTourBuyMoreEarnMore(
      {bool viewMore = false, bool useLoading = true}) async {
    setLoading(useLoading);
    final request = ListDataRequest(
        page: state.page, pageSize: state.pageSize, keyWord: state.keyWord);
    try {
      final result =
          await _api.getTourBuyMoreEarnMore(request: request).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      if (viewMore) {
        // appendListViewMoreTour(result);
      } else {
        // state = state.copyWith(buyMoreEarnMore: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getTourDealsAroundHere(
      {bool useLoading = true, bool viewMore = false}) async {
    final request = ListDataRequest(page: state.page, pageSize: state.pageSize);
    setLoading(useLoading);
    try {
      final result =
          await _api.getTourDealsAroundHere(request: request).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      // appendListViewMoreTour(result);
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getSaveTour(
      {bool viewMore = false, bool useLoading = true}) async {
    final request = ListDataRequest(page: state.page, pageSize: state.pageSize);
    if (!viewMore) {
      // state = state.copyWith(saveTour: [], viewMoreTours: []);
    }
    setLoading(useLoading);
    try {
      final result = await _api.getSaveTour(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      // appendListViewMoreTour(result);
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<bool> savedTour() async {
    state = state.copyWith(
      isSave: !state.isSave,
      visiblePortal: true,
      errorMessage: '',
    );
    try {
      final result =
          await _api.savedTour(tourId: state.selectedTour?.id ?? 0).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      state = state.copyWith(isSave: result.data?.isSave);
      return true;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      state = state.copyWith(isSave: state.selectedTour?.isLike ?? false);
      return false;
    }
  }

  Future<void> getMyContact() async {
    setLoading(true);
    try {
      final result = await _apiContact.getMyContact().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      final contact =
          result.data.firstWhereOrNull((element) => element.isDefault == 1);
      state = state.copyWith(
        contactResponse: contact,
        contactFullName: contact?.fullname ?? '',
        contactEmail: contact?.email ?? '',
        contactPhoneNumber: contact?.phone ?? '',
      );
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      state = state.copyWith();
    }
  }

  void loadMore({required TourType category}) {
    if (!state.canLoadMore) return;
    forceLoadingLoadMore(true);
    setPageGetData(page: state.page + 1, pageSize: 20);
    switch (category) {
      case TourType.dealsAroundHere:
        getTourDealsAroundHere(useLoading: false);
        break;
      case TourType.hotTetHoliday:
        break;
      case TourType.favoriteCombo || TourType.favoriteTour:
        getTourBestSeller(viewMore: true, useLoading: false);
        break;
      case TourType.superCheapCombo || TourType.superCheapTour:
        // getDisCount(viewMore: true, useLoading: false);
        break;
      case TourType.buyMoreEarnMore:
        getTourBuyMoreEarnMore(viewMore: true, useLoading: false);
      case TourType.tourSaved:
        getSaveTour(viewMore: true, useLoading: false);
        break;
    }
  }

  void onTapMinusQtt(SubProductResponse subProduct) {
    TourResponse? cacheTourDetail = state.selectedTour;
    final cacheSubProducts = state.selectedTour?.subProducts?.toList() ?? [];
    final idx = cacheSubProducts.indexWhere((element) => element == subProduct);
    cacheSubProducts[idx] = cacheSubProducts[idx].copyWith(
      stock: subProduct.stock - 1,
    );
    cacheTourDetail = cacheTourDetail?.copyWith(
      subProducts: cacheSubProducts,
    );
    removePassenger(subProduct.id);
    state = state.copyWith(
      selectedTour: cacheTourDetail,
    );
  }

  void onTapAddQtt(SubProductResponse subProduct,
      {TicketType ticketType = TicketType.aldult}) {
    TourResponse? cacheTourDetail = state.selectedTour;
    final cacheSubProducts = state.selectedTour?.subProducts?.toList() ?? [];
    final idx = cacheSubProducts.indexWhere((element) => element == subProduct);
    cacheSubProducts[idx] = cacheSubProducts[idx].copyWith(
      stock: subProduct.stock + 1,
    );
    cacheTourDetail = cacheTourDetail?.copyWith(
      subProducts: cacheSubProducts,
    );
    addPassenger(subProductId: subProduct.id, type: ticketType);
    state = state.copyWith(
      selectedTour: cacheTourDetail,
    );
  }

  void selectDate({required DateTime? date}) {
    if (date == null) return;
    state = state.copyWith(selectedDate: date);
  }

  void selectTime({required String timeFrame}) {
    state = state.copyWith(selectedTime: timeFrame);
  }

  void selectSeatType({required AttributeValue seatType}) {
    state = state.copyWith(selectedSeatType: seatType);
  }

  void resetQuantity() {
    state = state.copyWith(
        aldultQuantity: 0,
        childQuantity: 0,
        passengerInfo: PassengerInfo(passengers: []));
  }

  void resetState() {
    state = TicketTourModelV2.getDefault();
  }

  void onChangeSearchMode(String value) {
    state = state.copyWith(isSearching: value != '', keyWord: value);
  }

  void onChangeServiceType(ServiceType newType) {
    if (state.serviceType != newType) {
      state = state.copyWith(serviceType: newType);
    }
  }

  void resetListTour() {
    state = state.copyWith();
  }

  void resetDetailedPage() {
    resetPassengerInfo();
    resetQuantity();
    state.selectedDate = null;
    state.passengerInfo = null;
    state.selectedTime = null;
    state.selectedSeatType = null;
    state = state.copyWith(
      aldultQuantity: 0,
      childQuantity: 0,
      selectedDate: state.selectedDate,
      selectedTime: state.selectedTime,
      passengerInfo: state.passengerInfo,
      visiblePortal: false,
      isSave: false,
      isSavedTourSuccess: false,
    );
  }
}

final pTicketTourProviderV2 =
    StateNotifierProvider<TicketTourScreenProviderV2, TicketTourModelV2>(
        (ref) => TicketTourScreenProviderV2(TicketTourModelV2.getDefault()));
