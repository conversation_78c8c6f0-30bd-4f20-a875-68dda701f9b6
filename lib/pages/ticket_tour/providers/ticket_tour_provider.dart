import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/advertisement_model.dart';
import 'package:tripc_app/models/app/gender_enum.dart';
import 'package:tripc_app/models/app/tour_type_enum.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/app/passenger.dart';
import 'package:tripc_app/models/app/passenger_information.dart';
import 'package:tripc_app/models/app/payment.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/api_tour_response/api_tour_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/applicable_time_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/sub_product_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/apis/contact/api_contact.dart';
import 'package:tripc_app/services/apis/tours/api_tours.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_enum.dart';
import 'package:tripc_app/utils/app_extension.dart';
import '../../../models/app/service_model.dart';
import '../../../models/remote/contact_response.dart';
import '../../../utils/app_validation.dart';
import '../../../models/app/service_type_enum/service_type_enum.dart';

enum TicketType { aldult, child }

final exampleService = ServiceModel(
    name: 'Perhentian Islands',
    nameTour: 'Khám phá Vịnh Hạ Long - Tour 1 ngày',
    location: 'Đà Nẵng',
    type: TripCServiceCategory.tourSightSeeing.value,
    startTime: timeOfDayText(const TimeOfDay(hour: 9, minute: 0)),
    endTime: timeOfDayText(const TimeOfDay(hour: 16, minute: 0)),
    score: 4.8,
    rating: 200,
    price: 1290000,
    salePrice: 499000,
    aldultPrice: 1500000,
    childPrice: 500000,
    tcent: 50,
    discount: 60,
    startTimeCombo: DateTime(2025, 24, 01, 0, 0),
    endTimeCombo: DateTime(2025, 02, 25, 23, 59),
    package: ServicePackage(days: 1, seatType: [
      0,
      1,
      2
    ], availableDates: [
      DateTime(2025, 1, 20),
      DateTime(2025, 1, 21),
      DateTime(2025, 1, 22),
      DateTime(2025, 1, 23),
      DateTime(2025, 1, 24),
      DateTime(2025, 1, 25),
      DateTime(2025, 1, 26),
    ], timeFrame: [
      '15:00',
      '16:00',
      '17:20',
      '18:20',
      '19:20',
      '20:20'
    ]),
    supplier: 'Tuấn Châu Travel',
    departureLocation: 'Âu tàu số 01',
    hotel: 'Mường Thanh Hotel',
    restaurant: 'Hạ Long Restaurant',
    guide: 'Tuấn Châu Travel',
    noteInformation: [
      'Có thể hủy vé trước ngày 20/01/2025',
      'Không thể đổi lịch',
      'Đã bao gồm bảo hiểm chuyến đi',
      'Đã bao gồm chi phí hướng dẫn viên du lịch',
      '01 Bữa trưa',
      'Phòng điều hòa',
      'Vui lòng liên hệ hướng dẫn viên hoặc ban quản lý cảng nếu có sự cố về hộ chiếu',
    ],
    detailInfomation:
        'Vịnh Hạ Long là một trong những điểm đến du lịch nổi tiếng ở Việt Nam, nằm ở tỉnh Quảng Ninh, thuộc vùng Đông Bắc. Vịnh Hạ Long được UNESCO công nhận là Di sản Thiên nhiên Thế giới và nổi bật với cảnh quan thiên nhiên tuyệt đẹp, với hàng nghìn đảo đá vôi lớn nhỏ, được mây trời bao phủ quanh năm.Vịnh Hạ Long là một trong những điểm đến du lịch nổi tiếng ở Việt Nam, nằm ở tỉnh Quảng Ninh, thuộc vùng Đông Bắc. Vịnh Hạ Long được UNESCO công nhận là Di sản Thiên nhiên Thế giới và nổi bật với cảnh quan thiên nhiên tuyệt đẹp, với hàng nghìn đảo đá vôi lớn nhỏ, được mây trời bao phủ quanh năm.');

class TicketTourModel {
  TicketTourModel(
      {required this.serviceItems,
      this.selectedTour,
      this.aldultQuantity = 0,
      this.childQuantity = 0,
      this.passengerInfo,
      this.selectedDate,
      this.selectedSeatType,
      this.keyWord = '',
      required this.advertisement,
      required this.hotels,
      required this.combos,
      this.errorMessage,
      this.discounts = const [],
      this.bestSeller = const [],
      this.buyMoreEarnMore = const [],
      this.viewMoreTours = const [],
      this.saveTour = const [],
      this.canLoadmore = false,
      this.isLoadingLoadMore = false,
      this.isLoading = false,
      this.page = 1,
      this.pageSize = 20,
      this.selectedTime,
      this.visiblePortal = false,
      this.isSave = false,
      this.contactFullName = '',
      this.contactEmail = '',
      this.numberPlatte = '',
      this.contactPhoneNumber = '',
      this.contactErrorEmail = '',
      this.passengerEmail = '',
      this.passengerEmailError = '',
      this.passengerFullName = '',
      this.passengerPhoneNumber = '',
      this.shuttleHotel = '',
      this.shuttleContact = '',
      this.shuttleExpected = '',
      this.shuttleTime = '',
      this.contactResponse,
      this.guardianName = '',
      this.currentType = TicketType.aldult,
      this.isSavedTourSuccess = false,
      this.contactErrorPhoneNumber = '',
      this.contactErrorPhoneNumber10c = '',
      this.fullNameErrorText = '',
      this.phoneError = ''});
  final List<TourResponse> serviceItems;
  final List<TourResponse> hotels;
  TourResponse? selectedTour;
  int aldultQuantity;
  int childQuantity;
  PassengerInfo? passengerInfo;
  DateTime? selectedDate;
  String? selectedTime;
  AttributeValue? selectedSeatType;
  final String keyWord;
  final AdvertisementModel advertisement;
  final List<TourResponse> combos;
  final List<TourResponse> discounts;
  final List<TourResponse> bestSeller;
  final List<TourResponse> buyMoreEarnMore;
  final List<TourResponse> saveTour;
  final bool isLoading;
  final List<TourResponse> viewMoreTours;
  final bool canLoadmore;
  final bool isLoadingLoadMore;
  final int page;
  final int pageSize;
  final String? errorMessage;
  final bool visiblePortal;
  final bool isSave;
  final String contactFullName;
  final String contactEmail;
  final String numberPlatte;
  final String contactErrorEmail;
  final String contactPhoneNumber;
  final String passengerFullName;
  final String passengerEmail;
  final String passengerPhoneNumber;
  final String passengerEmailError;
  final String shuttleHotel;
  final String shuttleTime;
  final String shuttleContact;
  final String shuttleExpected;
  final String guardianName;
  final ContactResponse? contactResponse;
  final TicketType currentType;
  final bool isSavedTourSuccess;
  final String contactErrorPhoneNumber;
  final String contactErrorPhoneNumber10c;
  final String fullNameErrorText;
  final String phoneError;

  static TicketTourModel getDefault() {
    return TicketTourModel(
        advertisement: AdvertisementModel(
            discount: 10, advertisement: 'ON FLIGHT TICKETS'),
        passengerInfo: PassengerInfo(passengers: []),
        serviceItems: [],
        hotels: [],
        combos: []);
  }

  TicketTourModel copyWith(
      {List<TourResponse>? serviceItems,
      TourResponse? selectedTour,
      DateTime? startDate,
      int? aldultQuantity,
      int? childQuantity,
      PassengerInfo? passengerInfo,
      DateTime? selectedDate,
      String? selectedTime,
      AttributeValue? selectedSeatType,
      String? keyWord,
      bool? isLoading,
      List<TourResponse>? viewMoreTours,
      List<TourResponse>? combos,
      List<TourResponse>? hotels,
      List<TourResponse>? discounts,
      List<TourResponse>? bestSeller,
      List<TourResponse>? buyMoreEarnMore,
      bool? canLoadmore,
      bool? isLoadingLoadMore,
      int? page,
      int? pageSize,
      String? errorMessage,
      AdvertisementModel? advertisement,
      bool? visiblePortal,
      List<TourResponse>? saveTour,
      bool? isSave,
      String? contactFullName,
      String? contactEmail,
      String? numberPlatte,
      String? contactPhoneNumber,
      String? contactErrorEmail,
      String? passengerEmail,
      String? passengerFullName,
      String? passengerPhoneNumber,
      String? passengerEmailError,
      String? shuttleHotel,
      String? shuttleTime,
      String? shuttleContact,
      String? shuttleExpected,
      ContactResponse? contactResponse,
      String? guardianName,
      TicketType? currentType,
      bool? isSavedTourSuccess,
      String? contactErrorPhoneNumber,
      String? contactErrorPhoneNumber10c,
      String? fullNameErrorText,
      String? phoneError}) {
    return TicketTourModel(
      viewMoreTours: viewMoreTours ?? this.viewMoreTours,
      combos: combos ?? this.combos,
      serviceItems: serviceItems ?? this.serviceItems,
      aldultQuantity: aldultQuantity ?? this.aldultQuantity,
      childQuantity: childQuantity ?? this.childQuantity,
      passengerInfo: passengerInfo ?? this.passengerInfo,
      selectedTour: selectedTour ?? this.selectedTour,
      selectedDate: selectedDate ?? this.selectedDate,
      selectedTime: selectedTime ?? this.selectedTime,
      selectedSeatType: selectedSeatType ?? this.selectedSeatType,
      advertisement: advertisement ?? this.advertisement,
      hotels: hotels ?? this.hotels,
      discounts: discounts ?? this.discounts,
      bestSeller: bestSeller ?? this.bestSeller,
      buyMoreEarnMore: buyMoreEarnMore ?? this.buyMoreEarnMore,
      isLoading: isLoading ?? this.isLoading,
      canLoadmore: canLoadmore ?? this.canLoadmore,
      isLoadingLoadMore: isLoadingLoadMore ?? this.isLoadingLoadMore,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
      errorMessage: errorMessage ?? this.errorMessage,
      keyWord: keyWord ?? this.keyWord,
      visiblePortal: visiblePortal ?? this.visiblePortal,
      saveTour: saveTour ?? this.saveTour,
      isSave: isSave ?? this.isSave,
      contactFullName: contactFullName ?? this.contactFullName,
      numberPlatte: numberPlatte ?? this.numberPlatte,
      contactEmail: contactEmail ?? this.contactEmail,
      contactPhoneNumber: contactPhoneNumber ?? this.contactPhoneNumber,
      contactErrorEmail: contactErrorEmail ?? this.contactErrorEmail,
      passengerEmail: passengerEmail ?? this.passengerEmail,
      passengerEmailError: passengerEmailError ?? this.passengerEmailError,
      passengerFullName: passengerFullName ?? this.passengerFullName,
      passengerPhoneNumber: passengerPhoneNumber ?? this.passengerPhoneNumber,
      shuttleHotel: shuttleHotel ?? this.shuttleHotel,
      shuttleContact: shuttleContact ?? this.shuttleContact,
      shuttleExpected: shuttleExpected ?? this.shuttleExpected,
      shuttleTime: shuttleTime ?? this.shuttleTime,
      contactResponse: contactResponse ?? this.contactResponse,
      guardianName: guardianName ?? this.guardianName,
      currentType: currentType ?? this.currentType,
      isSavedTourSuccess: isSavedTourSuccess ?? this.isSavedTourSuccess,
      contactErrorPhoneNumber:
          contactErrorPhoneNumber ?? this.contactErrorPhoneNumber,
      contactErrorPhoneNumber10c:
          contactErrorPhoneNumber10c ?? this.contactErrorPhoneNumber10c,
      fullNameErrorText: fullNameErrorText ?? this.fullNameErrorText,
      phoneError: phoneError ?? this.phoneError,
    );
  }

  List<AttributeValue> get seatAttributes {
    if (selectedTour == null) return [];
    final attributeResponse = selectedTour!.attributes?.firstWhereOrNull(
        (element) =>
            element.visibility == 1 &&
            element.type == TourResponseAttibuteValueType.text);
    return attributeResponse?.values ?? [];
  }

  AttributeResponse? get hotDateTime {
    if (selectedTour == null || selectedTour?.attributes == []) return null;
    return selectedTour!.attributes!
        .firstWhereOrNull((element) => element.visibility == 0);
  }

  bool get isButtonConfirmDisable {
    if (currentType == TicketType.aldult) {
      return !(passengerEmailError.isEmpty &&
          passengerEmail.isNotEmpty &&
          phoneError.isEmpty &&
          passengerPhoneNumber.isNotEmpty &&
          passengerFullName.isNotEmpty);
    } else {
      return !(guardianName.isNotEmpty &&
          passengerPhoneNumber.isNotEmpty &&
          phoneError.isEmpty &&
          passengerFullName.isNotEmpty);
    }
  }

  bool get isGoToPaymentDisable {
    return !(contactErrorEmail.isEmpty &&
        contactEmail.isNotEmpty &&
        contactPhoneNumber.isNotEmpty &&
        contactErrorPhoneNumber.isEmpty &&
        contactFullName.isNotEmpty);
  }

  bool get isAllFieldPassenger {
    final passengers = passengerInfo?.passengers.toList();
    return passengers?.any((element) => element.isAllFieldEmpty) ?? true;
  }

  int? get adultId {
    final subProducts = selectedTour?.subProducts?.toList() ?? [];
    if (subProducts.isEmpty) {
      return null;
    }
    return subProducts.first.id;
  }

  int? get childId {
    final subProducts = selectedTour?.subProducts?.toList() ?? [];
    if (subProducts.length < 2) {
      return null;
    }
    return subProducts.last.id;
  }

  String ticketQuantityText(BuildContext context) {
    int aldQty = 0;
    int childQty = 0;
    final cacheList = selectedTour?.subProducts?.toList() ?? [];
    for (var i = 0; i < cacheList.length; i++) {
      if (i == 0) {
        aldQty = cacheList[i].stock;
      } else {
        childQty = cacheList[i].stock;
      }
    }
    return '$aldQty ${context.strings.text_aldult}, $childQty ${context.strings.text_children}';
  }

  int get minTicketPrice {
    if (selectedTour == null) return 0;
    final cacheList = selectedTour?.subProducts?.toList() ?? [];
    if (cacheList.length == 1) {
      return cacheList.first.price ?? 0;
    }
    if (cacheList.isEmpty) {
      return selectedTour?.totalPrice ?? 0;
    }
    return cacheList
            .reduce((a, b) => (a.price ?? 0) < (b.price ?? 0) ? a : b)
            .price ??
        0;
  }

  AttributeValue? get selectHotTime {
    if (selectedDate == null || selectedTime == null) return null;
    final time = selectedTour?.attributes
        ?.firstWhereOrNull((element) => element.visibility == 0);
    if (time == null) return null;
    AttributeValue? value;
    time.values?.forEach(
      (e) {
        final applicableTime = applicableTimeResponseFromJson(e.seatType ?? '');
        final times = applicableTime.time ?? [];
        if (!selectedDate!.isBefore(applicableTime.startDateTime) &&
            !selectedDate!.isAfter(applicableTime.endDateTime) &&
            times.contains(selectedTime)) {
          value = e;
        }
      },
    );
    return value;
  }

  int get totalPriceOfSeatType {
    if (selectedTour == null || selectedSeatType == null) return 0;
    final totalQty = selectedTour?.subProducts
            ?.map((e) => e.stock)
            .reduce((a, b) => a + b) ??
        0;
    return totalQty * int.parse(selectedSeatType!.price ?? '0');
  }

  int get totalPriceOfSpeacialTimeFrame {
    if (selectedDate == null || selectedTime == null) return 0;

    final totalQty = selectedTour?.subProducts
            ?.map((e) => e.stock)
            .reduce((a, b) => a + b) ??
        0;

    final time = selectedTour?.attributes?.firstWhereOrNull(
        (element) => element.type == TourResponseAttibuteValueType.time);

    if (time == null) return 0;

    int total = 0;
    time.values?.forEach(
      (e) {
        final applicableTime = e.specialPriceTimeFrame;
        final times = applicableTime?.time ?? [];
        if (applicableTime != null &&
            !selectedDate!.isBefore(applicableTime.startDateTime) &&
            !selectedDate!.isAfter(applicableTime.endDateTime) &&
            times.contains(selectedTime)) {
          total += int.parse(e.price ?? '0');
        }
      },
    );
    return totalQty * total;
  }

  List<int> get totalDiscount {
    if (selectedTour == null) return [];
    final discounts = selectedTour?.discounts?.toList() ?? [];
    final List<int> listTotalDiscount = [];
    selectedTour?.subProducts?.forEach(
      (e) {
        final discount = discounts.firstWhereOrNull((element) =>
            (element.maxQuantity ?? 0) >= e.stock &&
            (element.minQuantity ?? 0) <= e.stock &&
            element.audienceType == e.audience?.id);
        listTotalDiscount.add(e.ticketPrice -
            (int.parse(discount?.discountValue ?? '0') * e.ticketPrice / 100)
                .toInt());
      },
    );
    return listTotalDiscount;
  }

  int get totalPayment {
    if (totalDiscount.isEmpty) return 0;
    final totalPriceOfTicket = totalDiscount.reduce((a, b) => a + b);
    return totalPriceOfSpeacialTimeFrame +
        totalPriceOfTicket +
        totalPriceOfSeatType;
  }

  bool get selectButtonEnable {
    if (seatAttributes.isNotEmpty) {
      return selectedDate != null &&
          selectedTime != null &&
          selectedSeatType != null;
    }
    return selectedDate != null && selectedTime != null;
  }

  Payment get payment {
    return Payment(
        bookingCode: 'HLB012',
        aldultQuantity: aldultQuantity,
        childQuantity: childQuantity,
        bookingDate: DateTime.now(),
        departureDate: DateTime.now(),
        service: selectedTour,
        totalPrice: totalPayment,
        passengerInfo: passengerInfo,
        selectHotTime: selectHotTime,
        selectSeat: selectedSeatType,
        selectedDate: selectedDate,
        selectedTime: selectedTime);
  }
}

class TicketTourScreenProvider extends StateNotifier<TicketTourModel> {
  TicketTourScreenProvider(super._state);
  final ApiTours _api = ApiTours();
  final ApiContact _apiContact = ApiContact();

  void selectTour(TourResponse value) {
    state = state.copyWith(selectedTour: value);
  }

  void onCloseSavedPopup() {
    state = state.copyWith(visiblePortal: false);
  }

  void setEmail(BuildContext context, {required String value}) {
    state = state.copyWith(
        contactEmail: value,
        contactErrorEmail: ValidationAccount.emailValidation(context, value));
  }

  void setCurrentTicketType(TicketType type) {
    state = state.copyWith(
      currentType: type,
    );
  }

  void setPhoneNumber(String value, BuildContext context) {
    state = state.copyWith(
      contactPhoneNumber: value,
      contactErrorEmail: state.contactErrorEmail,
      contactErrorPhoneNumber:
          value.length < 9 ? context.strings.text_error_phone_9 : '',
    );
  }

  void setPhoneNumber10c(String value, BuildContext context) {
    state = state.copyWith(
      contactPhoneNumber: value,
      contactErrorEmail: state.contactErrorEmail,
      contactErrorPhoneNumber10c:
          value.length < 10 ? context.strings.text_error_phone_10 : '',
    );
  }

  void setFullName(BuildContext context, String value) {
    state = state.copyWith(
      contactFullName: value,
      fullNameErrorText: ValidationAccount.fullNameValidation(context, value),
      contactErrorEmail: state.contactErrorEmail,
    );
  }

  void setNumberPlate(String value) {
    state = state.copyWith(
      numberPlatte: value,
    );
  }

  void setPassengerEmail(BuildContext context, {required String value}) {
    state = state.copyWith(
      passengerEmail: value,
      passengerEmailError: ValidationAccount.emailValidation(context, value),
    );
  }

  void setPassengerFullName(String value) {
    state = state.copyWith(
      passengerFullName: value,
    );
  }

  void setGuardianName(String value) {
    state = state.copyWith(
      guardianName: value,
    );
  }

  void setPassengerPhoneNumber(String value, BuildContext context) {
    state = state.copyWith(
      passengerPhoneNumber: value,
      phoneError: value.length < 10 ? context.strings.text_error_phone_10 : '',
    );
  }

  void resetPassengerInfo() {
    state = state.copyWith(
        passengerPhoneNumber: '',
        passengerFullName: '',
        passengerEmailError: '',
        passengerEmail: '',
        guardianName: '',
        phoneError: '');
  }

  void setShuttleHotel(String value) {
    state = state.copyWith(
      shuttleHotel: value,
    );
  }

  void setShuttleTime(String value) {
    state = state.copyWith(
      shuttleTime: value,
    );
  }

  void setShuttleContact(String value) {
    state = state.copyWith(
      shuttleContact: value,
    );
  }

  void setShuttleExpected(String value) {
    state = state.copyWith(
      shuttleExpected: value,
    );
  }

  void updatePassenger({Passenger? passenger}) {
    final index = state.passengerInfo?.passengers
            .indexWhere((e) => e.id == passenger?.id) ??
        -1;
    if (index < 0) {
      return;
    }
    final cachePassengers = [...state.passengerInfo?.passengers ?? []];
    cachePassengers[index] = cachePassengers[index].copyWith(
      name: state.passengerFullName.isEmpty
          ? passenger?.name
          : state.passengerFullName,
      email: state.passengerEmail.isEmpty
          ? passenger?.email
          : state.passengerEmail,
      phone: state.passengerPhoneNumber.isEmpty
          ? passenger?.phone
          : state.passengerPhoneNumber,
      guardianName: state.guardianName.isEmpty
          ? passenger?.guardianName
          : state.guardianName,
    );
    state = state.copyWith(
        passengerInfo:
            state.passengerInfo?.copyWith(passengers: [...cachePassengers]));
  }

  void removePassenger(int? subProductId) {
    PassengerInfo cachePassengerInfo =
        state.passengerInfo ?? PassengerInfo(passengers: []);
    final List<Passenger> updatedPassengers =
        cachePassengerInfo.passengers.toList();
    final lastSubProductIndex = updatedPassengers
        .lastIndexWhere((element) => element.subProductId == subProductId);
    if (lastSubProductIndex != -1) {
      updatedPassengers.removeAt(lastSubProductIndex);
    }
    cachePassengerInfo = cachePassengerInfo.copyWith(
      passengers: updatedPassengers,
    );
    state = state.copyWith(
      passengerInfo: cachePassengerInfo,
    );
  }

  void addPassenger({int? subProductId, TicketType type = TicketType.aldult}) {
    PassengerInfo cachePassengerInfo =
        state.passengerInfo ?? PassengerInfo(passengers: []);
    final List<Passenger> updatedPassengers =
        cachePassengerInfo.passengers.toList();
    updatedPassengers.add(
      Passenger(
        gender: GenderEnum.male.value,
        name: state.passengerFullName,
        email: state.passengerEmail,
        phone: state.passengerPhoneNumber,
        guardianName: state.guardianName,
        id: DateTime.now().millisecondsSinceEpoch,
        subProductId: subProductId ?? 1,
        type: type,
      ),
    );
    cachePassengerInfo = cachePassengerInfo.copyWith(
      passengers: updatedPassengers,
    );

    state = state.copyWith(
      passengerInfo: cachePassengerInfo,
    );
  }

  void updateToTalInfomation({required String specicalNote}) {
    state = state.copyWith(
        passengerInfo: state.passengerInfo?.copyWith(
            name: state.contactFullName,
            email: state.contactEmail,
            phoneNumber: state.contactPhoneNumber,
            contactResponse: state.contactResponse,
            specialRequest: specicalNote,
            shuttleInfo: ShuttleInfo(
              hotel: state.shuttleHotel,
              contact: state.shuttleContact,
              expected: state.shuttleExpected,
              time: state.shuttleTime,
            )));
  }

  void addQuantityTicket({required TicketType ticketType}) {
    if (ticketType == TicketType.aldult) {
      state = state.copyWith(aldultQuantity: state.aldultQuantity + 1);
      addPassenger(subProductId: state.adultId, type: TicketType.aldult);
    } else {
      state = state.copyWith(childQuantity: state.childQuantity + 1);
      addPassenger(subProductId: state.childId, type: TicketType.child);
    }
  }

  void reduceQuantityTicket({required TicketType ticketType}) {
    if (ticketType == TicketType.aldult) {
      state = state.copyWith(
          aldultQuantity:
              state.aldultQuantity > 0 ? state.aldultQuantity - 1 : 0);
      removePassenger(state.adultId);
    } else {
      state = state.copyWith(
          childQuantity: state.childQuantity > 0 ? state.childQuantity - 1 : 0);
      removePassenger(state.childId);
    }
  }

  void setLoading(bool value) {
    state = state.copyWith(
      isLoading: value,
    );
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void forceLoadingLoadMore(bool value) {
    state = state.copyWith(isLoadingLoadMore: value);
  }

  void setPageGetData({required int page, int pageSize = 20, String? keyword}) {
    state = state.copyWith(page: page, pageSize: pageSize, keyWord: keyword);
  }

  void viewMoreTours({required TourType category, int? categoryId}) {
    state = state.copyWith(viewMoreTours: []);
    switch (category) {
      case TourType.favoriteTour || TourType.favoriteCombo:
        getTourBestSeller(viewMore: true, categoryId: categoryId);
        break;
      case TourType.superCheapCombo || TourType.superCheapTour:
        getDisCount(viewMore: true, categoryId: categoryId);
        break;
      case TourType.buyMoreEarnMore:
        getTourBuyMoreEarnMore(viewMore: true);
        break;
      case TourType.dealsAroundHere:
        getTourDealsAroundHere();
        break;
      case TourType.tourSaved:
        getSaveTour();
        break;
      default:
        break;
    }
  }

  void viewMoreCategoriesTours(
      {required int id, FerryType? ferryType = FerryType.catBaTuanChau}) {
    state = state.copyWith(viewMoreTours: []);
    // this a my recently tour
    if (id == -1) {
      getMyRecentleTour(viewMore: true);
    }
    if (ferryType != null) {
      getToursFerry(viewMore: true, type: ferryType);
    } else {
      getToursByServiceType(id, viewMore: true);
    }
  }

  Future<void> getToursByServiceType(int id,
      {bool viewMore = false, bool useLoading = true}) async {
    setLoading(useLoading);
    final request = ListDataRequest(
        page: state.page, pageSize: state.pageSize, keyWord: state.keyWord);
    try {
      final result = await _api
          .getToursListByCategory(request: request, categoryId: id)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (viewMore) {
        appendListViewMoreTour(result);
      } else {
        state = state.copyWith(bestSeller: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getMyRecentleTour(
      {bool viewMore = false, bool useLoading = true}) async {
    setLoading(useLoading);
    final request = ListDataRequest(
        page: state.page, pageSize: state.pageSize, keyWord: state.keyWord);
    try {
      final result = await _api.getRecentlyViewed(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (viewMore) {
        appendListViewMoreTour(result);
      } else {
        state = state.copyWith(bestSeller: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getToursFerry(
      {bool viewMore = false, bool useLoading = true, FerryType? type}) async {
    setLoading(useLoading);
    final request = ListDataRequest(
        page: state.page,
        pageSize: state.pageSize,
        keyWord: state.keyWord,
        ferrySlug: type?.slug);
    try {
      final result = await _api.getToursFerry(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (viewMore) {
        appendListViewMoreTour(result);
      } else {
        state = state.copyWith(bestSeller: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> listSavedTour(int tourId) async {
    try {
      final result = await _api.savedTour(tourId: tourId).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      updateListTour(tourId, result.data?.isSave ?? false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  void updateListTour(int tourId, bool isSave) {
    final cacheList = state.viewMoreTours.toList();
    final index = cacheList.indexWhere((element) => element.id == tourId);
    if (index == -1) {
      return;
    }
    cacheList[index] = cacheList[index].copyWith(
      like: isSave,
    );
    state = state.copyWith(viewMoreTours: cacheList);
  }

  void search(String keyWord) {
    debounceHelpers.action(
      callBack: () async {
        state = state.copyWith(keyWord: keyWord);
        getTourBestSeller();
        getDisCount();
        // getTourBuyMoreEarnMore();
      },
    );
  }

  void appendListViewMoreTour(ListTourResponse result) {
    if (state.viewMoreTours.isEmpty) {
      state = state.copyWith(
        viewMoreTours: result.data,
      );
      state = state.copyWith(
          canLoadmore: state.viewMoreTours.length < (result.total ?? 0));
      return;
    }
    state =
        state.copyWith(viewMoreTours: [...state.viewMoreTours, ...result.data]);
    state = state.copyWith(
        canLoadmore: state.viewMoreTours.length < (result.total ?? 0));
    forceLoadingLoadMore(false);
  }

  Future<void> getDisCount(
      {bool viewMore = false, bool useLoading = true, int? categoryId}) async {
    final request = ListDataRequest(
        page: state.page,
        pageSize: state.pageSize,
        keyWord: state.keyWord,
        categoryId: categoryId);
    setLoading(true);
    try {
      final result = await _api.getTourDiscounts(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (viewMore) {
        appendListViewMoreTour(result);
      } else {
        state = state.copyWith(discounts: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getTourBestSeller(
      {bool viewMore = false, bool useLoading = true, int? categoryId}) async {
    setLoading(useLoading);
    final request = ListDataRequest(
        page: state.page,
        pageSize: state.pageSize,
        keyWord: state.keyWord,
        categoryId: categoryId);
    try {
      final result = await _api.getTourBestSeller(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (viewMore) {
        appendListViewMoreTour(result);
      } else {
        state = state.copyWith(bestSeller: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getDetailedTour({required int? tourId}) async {
    setLoading(true);
    try {
      final result = await _api.getDetailedTour(tourId: tourId ?? 0).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      state = state.copyWith(
          selectedTour: result.data, isSave: result.data?.isLike ?? false);
      if (result.data?.applicableTime != null) {
        selectDate(date: result.data?.applicableTime?.currentDate);
      }
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getTourBuyMoreEarnMore(
      {bool viewMore = false, bool useLoading = true}) async {
    setLoading(useLoading);
    final request = ListDataRequest(
        page: state.page, pageSize: state.pageSize, keyWord: state.keyWord);
    try {
      final result =
          await _api.getTourBuyMoreEarnMore(request: request).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      if (viewMore) {
        appendListViewMoreTour(result);
      } else {
        state = state.copyWith(buyMoreEarnMore: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getTourDealsAroundHere(
      {bool useLoading = true, bool viewMore = false}) async {
    final request = ListDataRequest(page: state.page, pageSize: state.pageSize);
    setLoading(useLoading);
    try {
      final categoryBySlug = await _api
          .getServiceCategoryBySlug(
              slug: ParentServiceTypeEnum.tourTraiNghiem.slug)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (categoryBySlug.status == true) {
        final result = await _api
            .getTourDealsAroundHere(
                request: request, categoryId: categoryBySlug.data?.id)
            .timeout(
              const Duration(
                seconds: 30,
              ),
            );
        appendListViewMoreTour(result);
      }

      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getSaveTour(
      {bool viewMore = false, bool useLoading = true}) async {
    final request = ListDataRequest(page: state.page, pageSize: state.pageSize);
    if (!viewMore) {
      state = state.copyWith(saveTour: [], viewMoreTours: []);
    }
    setLoading(useLoading);
    try {
      final result = await _api.getSaveTour(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      appendListViewMoreTour(result);
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<bool> savedTour() async {
    state = state.copyWith(
      isSave: !state.isSave,
      visiblePortal: true,
      errorMessage: '',
    );
    try {
      final result =
          await _api.savedTour(tourId: state.selectedTour?.id ?? 0).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      state = state.copyWith(isSave: result.data?.isSave);
      return true;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      state = state.copyWith(isSave: state.selectedTour?.isLike ?? false);
      return false;
    }
  }

  Future<void> getMyContact() async {
    setLoading(true);
    try {
      final result = await _apiContact.getMyContact().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      final contact =
          result.data.firstWhereOrNull((element) => element.isDefault == 1);
      state = state.copyWith(
        contactResponse: contact,
        contactFullName: contact?.fullname ?? '',
        contactEmail: contact?.email ?? '',
        contactPhoneNumber: contact?.phone ?? '',
      );
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      state = state.copyWith();
    }
  }

  void loadMore({required TourType category}) {
    if (!state.canLoadmore) return;
    forceLoadingLoadMore(true);
    setPageGetData(page: state.page + 1, pageSize: 20);
    switch (category) {
      case TourType.dealsAroundHere:
        getTourDealsAroundHere(useLoading: false);
        break;
      case TourType.hotTetHoliday:
        break;
      case TourType.favoriteCombo || TourType.favoriteTour:
        getTourBestSeller(viewMore: true, useLoading: false);
        break;
      case TourType.superCheapCombo || TourType.superCheapTour:
        getDisCount(viewMore: true, useLoading: false);
        break;
      case TourType.buyMoreEarnMore:
        getTourBuyMoreEarnMore(viewMore: true, useLoading: false);
      case TourType.tourSaved:
        getSaveTour(viewMore: true, useLoading: false);
        break;
    }
  }

  void loadMoreCategories(
      {required int id, FerryType? ferryType = FerryType.catBaTuanChau}) {
    if (!state.canLoadmore) return;
    forceLoadingLoadMore(true);
    setPageGetData(page: state.page + 1, pageSize: 20);
    if (id == -1) {
      getMyRecentleTour(viewMore: true, useLoading: false);
    }
    if (id == 21) {
      getToursFerry(viewMore: true, type: ferryType);
    } else {
      getToursByServiceType(id, viewMore: true, useLoading: false);
    }
  }

  void onTapMinusQtt(SubProductResponse subProduct) {
    TourResponse? cacheTourDetail = state.selectedTour;
    final cacheSubProducts = state.selectedTour?.subProducts?.toList() ?? [];
    final idx = cacheSubProducts.indexWhere((element) => element == subProduct);
    cacheSubProducts[idx] = cacheSubProducts[idx].copyWith(
      stock: subProduct.stock - 1,
    );
    cacheTourDetail = cacheTourDetail?.copyWith(
      subProducts: cacheSubProducts,
    );
    removePassenger(subProduct.id);
    state = state.copyWith(
      selectedTour: cacheTourDetail,
    );
  }

  void onTapAddQtt(SubProductResponse subProduct,
      {TicketType ticketType = TicketType.aldult}) {
    TourResponse? cacheTourDetail = state.selectedTour;
    final cacheSubProducts = state.selectedTour?.subProducts?.toList() ?? [];
    final idx = cacheSubProducts.indexWhere((element) => element == subProduct);
    cacheSubProducts[idx] = cacheSubProducts[idx].copyWith(
      stock: subProduct.stock + 1,
    );
    cacheTourDetail = cacheTourDetail?.copyWith(
      subProducts: cacheSubProducts,
    );
    addPassenger(subProductId: subProduct.id, type: ticketType);
    state = state.copyWith(
      selectedTour: cacheTourDetail,
    );
  }

  void selectDate({required DateTime? date}) {
    if (date == null) return;
    state = state.copyWith(selectedDate: date);
  }

  void selectTime({required String timeFrame}) {
    state = state.copyWith(selectedTime: timeFrame);
  }

  void selectSeatType({required AttributeValue seatType}) {
    state = state.copyWith(selectedSeatType: seatType);
  }

  void resetQuantity() {
    state = state.copyWith(
        aldultQuantity: 0,
        childQuantity: 0,
        passengerInfo: PassengerInfo(passengers: []));
  }

  void resetState() {
    state = TicketTourModel.getDefault();
  }

  void resetListTour() {
    state = state.copyWith(
      viewMoreTours: [],
    );
  }

  void resetDetailedPage() {
    resetPassengerInfo();
    resetQuantity();
    state.selectedDate = null;
    state.passengerInfo = null;
    state.selectedTime = null;
    state.selectedSeatType = null;
    state = state.copyWith(
      aldultQuantity: 0,
      childQuantity: 0,
      selectedDate: state.selectedDate,
      selectedTime: state.selectedTime,
      passengerInfo: state.passengerInfo,
      visiblePortal: false,
      isSave: false,
      isSavedTourSuccess: false,
    );
  }
}

final pTicketTourProvider =
    StateNotifierProvider<TicketTourScreenProvider, TicketTourModel>(
        (ref) => TicketTourScreenProvider(TicketTourModel.getDefault()));
