import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/passenger.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_box_input/tripc_box_input.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_filed.dart';

import '../../../services/providers/providers.dart';
import '../../../widgets/app_show_api_error_dialog.dart';
import '../../../widgets/tripc_text_filed/tripc_text_field_with_label.dart';
import '../../tour-payment/providers/payment_provider.dart';
import '../components/components.dart';
import '../providers/providers.dart';

class TripcInputPassengerInformation extends ConsumerStatefulWidget {
  const TripcInputPassengerInformation({super.key, required this.tour});

  final TourResponse tour;

  @override
  ConsumerState<TripcInputPassengerInformation> createState() =>
      _TripcInputPassengerInformationState();
}

class _TripcInputPassengerInformationState
    extends ConsumerState<TripcInputPassengerInformation> {
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _noteController = TextEditingController();
  final _hotelController = TextEditingController();
  final _timeController = TextEditingController();
  final _contactController = TextEditingController();
  final _expectedController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(pTicketTourProvider.notifier).getMyContact();
      _emailController.text = ref.watch(pTicketTourProvider.select(
        (value) => value.contactEmail,
      ));
      _phoneController.text = ref.watch(pTicketTourProvider.select(
        (value) => value.contactPhoneNumber,
      ));
      _fullNameController.text = ref.watch(pTicketTourProvider.select(
        (value) => value.contactFullName,
      ));
      _hotelController.text = ref.watch(pTicketTourProvider.select(
        (value) => value.shuttleHotel,
      ));
      _expectedController.text = ref.watch(pTicketTourProvider.select(
        (value) => value.shuttleExpected,
      ));
      _contactController.text = ref.watch(pTicketTourProvider.select(
        (value) => value.shuttleContact,
      ));
      _timeController.text = ref.watch(pTicketTourProvider.select(
        (value) => value.shuttleTime,
      ));
      ref.listenManual(pTicketTourProvider.select((state) => state),
          (_, state) {
        _emailController.value = _emailController.value.copyWith(
          text: state.contactEmail,
        );
        _phoneController.value = _phoneController.value.copyWith(
          text: state.contactPhoneNumber,
        );
        _fullNameController.value = _fullNameController.value.copyWith(
          text: state.contactFullName,
        );
      });
      ref.listenManual(
          pTicketTourProvider.select((value) => value.errorMessage),
          (_, error) {
        if (error == null || error.isEmpty) {
          return;
        }
        dialogHelpers.show(context, child: ErrorDialog(text: error));
      });
    });
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  void onTapAddPassenger() {
    unfocusKeyboard();
    AppRoute.pushNamed(context, routeName: AppRoute.routeAddPassengers);
  }

  @override
  Widget build(BuildContext context) {
    final selectedTour =
        ref.watch(pTicketTourProvider.select((value) => value.selectedTour));
    final selectedDate =
        ref.watch(pTicketTourProvider.select((value) => value.selectedDate));
    final ticketQuantity = ref.watch(pTicketTourProvider
        .select((value) => value.ticketQuantityText(context)));
    final isAllFieldPassenger = ref.watch(
        pTicketTourProvider.select((value) => value.isAllFieldPassenger));
    final passengerInfo =
        ref.watch(pTicketTourProvider.select((value) => value.passengerInfo));
    final isEnableButton = !ref.watch(pTicketTourProvider.select(
          (value) => value.isGoToPaymentDisable,
        )) &&
        !isAllFieldPassenger;
    final contactErrorEmail = ref.watch(pTicketTourProvider.select(
      (value) => value.contactErrorEmail,
    ));
    final isLoading =
        ref.watch(pTicketTourProvider.select((value) => value.isLoading));
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        hasBackButton: true,
        titleAppBar: TripcText(
          context.strings.text_fill_in_passenger_info,
          fontWeight: FontWeight.w600,
          fontSize: 16,
          textColor: AppAssets.origin().black,
        ),
        bottom: PreferredSize(
            preferredSize: const Size(double.infinity, 16),
            child: Divider(
              height: 1,
              color: AppAssets.init.lightGray,
            )),
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        body: Stack(
          children: [
            SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 24.W, vertical: 20.H)
                  .copyWith(bottom: context.mediaQuery.padding.bottom),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _tourInformation(
                      selectedTour: selectedTour,
                      startDate: selectedDate,
                      ticketQuantity: ticketQuantity),
                  SizedBox(
                    height: 20.H,
                  ),
                  _inputArea(context,
                      passengers: passengerInfo?.passengers ?? [],
                      contactErrorEmail: contactErrorEmail,
                      selectedTour: selectedTour),
                  SizedBox(
                    height: 38.H,
                  ),
                  TripcButton(
                    onPressed: () {
                      ref
                          .read(pTicketTourProvider.notifier)
                          .updateToTalInfomation(
                              specicalNote: _noteController.text);
                      final payment = ref.watch(pTicketTourProvider.select(
                        (value) => value.payment,
                      ));
                      ref
                          .read(pPaymentProvider.notifier)
                          .createPayment(payment);
                      AppRoute.pushNamed(context,
                          routeName: AppRoute.routeTourPaymentV2,
                          arguments: {
                            'tour': widget.tour,
                            'isFerryTour': false
                          });
                    },
                    isButtonDisabled: !isEnableButton,
                    title: context.strings.text_go_to_payment_page,
                  ),
                  SizedBox(
                    height: 10.H,
                  ),
                ],
              ),
            ),
            AppLoading(
                isRequesting: isLoading, backgroundColor: Colors.transparent)
          ],
        ));
  }

  Widget _tourInformation(
      {required TourResponse? selectedTour,
      required DateTime? startDate,
      required String ticketQuantity}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          selectedTour?.name ?? '',
          fontSize: 16,
          fontWeight: FontWeight.w500,
          textAlign: TextAlign.start,
          textColor: AppAssets.origin().blackColor,
        ),
        TourInformationItem(
          title: context.strings.text_departure_date,
          value: startDate.formatddMMYYY,
          fontSize: 14,
          fontWeight: FontWeight.w300,
          fontStyle: FontStyle.normal,
          padding: EdgeInsets.only(top: 10.H, bottom: 5.H),
        ),
        TripcText(
          ticketQuantity,
          fontSize: 14,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w300,
          textColor: AppAssets.origin().blackColor,
        ),
      ],
    );
  }

  Widget _inputArea(BuildContext context,
      {required List<Passenger> passengers,
      required String contactErrorEmail,
      TourResponse? selectedTour}) {
    final childId =
        ref.watch(pTicketTourProvider.select((value) => value.childId));
    final contact =
        ref.watch(pTicketTourProvider.select((value) => value.contactResponse));
    final contactErrorPhoneNumber = ref.watch(
        pTicketTourProvider.select((value) => value.contactErrorPhoneNumber));
    passengers
        .sort((a, b) => (a.subProductId ?? 0).compareTo(b.subProductId ?? 0));
    int adultIdx = 0;
    int childIdx = 0;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcBoxInput(
            headerTitle: context.strings.text_contact_information,
            child: Column(
              children: [
                TripcTextField(
                  controller: _fullNameController,
                  hintText: context.strings.text_full_name,
                  readOnly: contact != null,
                  fontSize: 12,
                  onChanged: (value) => ref
                      .read(pTicketTourProvider.notifier)
                      .setFullName(context, value),
                  textInputAction: TextInputAction.next,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 20.H),
                  child: TripcTextField(
                    controller: _emailController,
                    onChanged: (value) => ref
                        .read(pTicketTourProvider.notifier)
                        .setEmail(context, value: value),
                    hintText: context.strings.text_email,
                    readOnly: contact != null,
                    errorText:
                        contactErrorEmail.isEmpty ? null : contactErrorEmail,
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.emailAddress,
                    hintTextColor: AppAssets.origin().lightGrayDD4,
                    fontSize: 12,
                    contentPadding: EdgeInsets.symmetric(
                        vertical: 13.H, horizontal: 12.5.W),
                  ),
                ),
                TripcTextField(
                  errorText: contactErrorPhoneNumber.isEmpty
                      ? null
                      : contactErrorPhoneNumber,
                  controller: _phoneController,
                  hintText: context.strings.text_mobile_phone,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  keyboardType: TextInputType.number,
                  readOnly: contact != null,
                  fontSize: 12,
                  maxLength: 9,
                  onChanged: (value) {
                    ref
                        .read(pTicketTourProvider.notifier)
                        .setPhoneNumber(value, context);
                  },
                  textInputAction: TextInputAction.done,
                  prefixIcon: Padding(
                    padding: EdgeInsets.only(left: 13.W, right: 24.W),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TripcText(
                          '+84',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          textColor: AppAssets.origin().black,
                          padding: EdgeInsets.only(right: 12.W),
                        ),
                        AppAssets.origin().icBlackRight.widget(height: 8.H)
                      ],
                    ),
                  ),
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
                ),
                SizedBox(
                  height: 6.H,
                )
              ],
            )),
        TripcBoxInput(
            headerTitle: context.strings.text_passenger_information_2,
            margin: EdgeInsets.symmetric(vertical: 20.H),
            child: Column(
              children: [
                Column(
                    children: List.generate(passengers.length, (index) {
                  if (childId == passengers[index].subProductId) {
                    childIdx += 1;
                  } else {
                    adultIdx += 1;
                  }
                  final passengerQty =
                      (childId == passengers[index].subProductId
                              ? childIdx
                              : adultIdx)
                          .toString()
                          .padLeft(2, '0');
                  return Padding(
                    padding: EdgeInsets.only(bottom: 20.H),
                    child: TripcTextField(
                      fillColor: passengers[index].isAllFieldEmpty
                          ? null
                          : AppAssets.origin().lightBlueCFF,
                      borderColor: passengers[index].isAllFieldEmpty
                          ? AppAssets.origin().buttonStrokeColor93
                          : AppAssets.origin().darkBlueColor,
                      hintText: selectedTour?.subProducts?.length != 2
                          ? '${context.strings.text_passenger_information} ${(index + 1).toString().padLeft(2, '0')}'
                          : childId == passengers[index].subProductId
                              ? context.strings.text_child_ticket(passengerQty)
                              : context.strings.text_adult_ticket(passengerQty),
                      hintTextColor: passengers[index].isAllFieldEmpty
                          ? AppAssets.origin().greyTextColorC8
                          : AppAssets.origin().darkBlueColor,
                      focusedBorderColor: AppAssets.origin().darkBlueColor,
                      fontSize: 12,
                      textColor: passengers[index].isAllFieldEmpty
                          ? AppAssets.origin().greyTextColorC8
                          : AppAssets.origin().darkBlueColor,
                      contentPadding: EdgeInsets.symmetric(
                          vertical: 13.H, horizontal: 12.5.W),
                      readOnly: true,
                      suffix: Padding(
                          padding: EdgeInsets.only(right: 16.W),
                          child: TripcIconButton(
                            onPressed: () {
                              unfocusKeyboard();
                              AppRoute.pushNamed(context,
                                  routeName: AppRoute.routeEditPassenger,
                                  arguments: passengers[index]);
                            },
                            child: AppAssets.origin()
                                .icEditPen
                                .widget(height: 16.H, width: 16.H),
                          )),
                    ),
                  );
                })),
                SizedBox(
                  height: 4.H,
                )
              ],
            )),
        if (selectedTour?.supplier == null)
          TripcBoxInput(
              headerTitle: context.strings.text_shuttle_information,
              margin: EdgeInsets.only(bottom: 25.H),
              child: Column(
                children: [
                  TripcTextFieldWithLabel(
                    textController: _hotelController,
                    label: context.strings.text_hotel_address,
                    labelColor: AppAssets.origin().blackColor,
                    onChanged: (value) =>
                        ref.read(pTicketTourProvider.notifier).setShuttleHotel,
                    labelFontSize: 12,
                    labelFontWeight: FontWeight.w300,
                    textInputAction: TextInputAction.next,
                    hintText: context.strings.text_fill_in_hotel_address,
                    hintTextColor: AppAssets.origin().lightGrayDD4,
                    fontSize: 12,
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 13.H, horizontal: 14.W),
                  ),
                  TripcTextFieldWithLabel(
                    textController: _timeController,
                    label: context.strings.text_pick_up_time_at_hotel,
                    labelColor: AppAssets.origin().blackColor,
                    textInputAction: TextInputAction.next,
                    onChanged:
                        ref.read(pTicketTourProvider.notifier).setShuttleTime,
                    labelFontSize: 12,
                    labelFontWeight: FontWeight.w300,
                    hintText: context.strings.text_pick_up_time_at_hotel_hint,
                    hintTextColor: AppAssets.origin().lightGrayDD4,
                    fontSize: 12,
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 13.H, horizontal: 14.W),
                    padding: EdgeInsets.only(top: 16.H),
                  ),
                  TripcTextFieldWithLabel(
                    textController: _contactController,
                    label: context.strings.text_contact_method,
                    labelColor: AppAssets.origin().blackColor,
                    onChanged: (value) => ref
                        .read(pTicketTourProvider.notifier)
                        .setShuttleContact(value),
                    labelFontSize: 12,
                    labelFontWeight: FontWeight.w300,
                    textInputAction: TextInputAction.next,
                    hintText: context.strings.text_contact_method_hint,
                    hintTextColor: AppAssets.origin().lightGrayDD4,
                    fontSize: 12,
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 13.H, horizontal: 14.W),
                    padding: EdgeInsets.only(top: 16.H),
                    labelPadding: EdgeInsets.zero,
                  ),
                  SizedBox(height: 16.H),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TripcText(
                        context.strings.text_proposed_itinerary,
                        fontWeight: FontWeight.w300,
                        fontSize: 14,
                        textColor: Colors.black,
                      ),
                      SizedBox(height: 8.H),
                      Container(
                        height: 106.H,
                        margin: EdgeInsets.only(bottom: 2.H),
                        child: TripcTextField(
                          controller: _expectedController,
                          hintText:
                              context.strings.text_proposed_itinerary_hint,
                          hintTextColor: AppAssets.origin().lightGrayDD4,
                          onChanged: ref
                              .read(pTicketTourProvider.notifier)
                              .setShuttleExpected,
                          fontSize: 12,
                          expands: true,
                          maxLines: null,
                          textAlignVertical: TextAlignVertical.top,
                          contentPadding: EdgeInsets.symmetric(
                              vertical: 13.H, horizontal: 12.5.W),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 25.H,
                  )
                ],
              )),
        TripcBoxInput(
          headerTitle: context.strings.text_special_requests,
          child: Container(
            height: 79.H,
            margin: EdgeInsets.only(bottom: 2.H),
            child: TripcTextField(
              controller: _noteController,
              hintText: context.strings.text_add_special_request,
              hintTextColor: AppAssets.origin().lightGrayDD4,
              fontSize: 12,
              expands: true,
              maxLines: null,
              textAlignVertical: TextAlignVertical.top,
              contentPadding:
                  EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
            ),
          ),
        )
      ],
    );
  }
}
