import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/pages/homepage/providers/tripc_homepage_provider.dart';
import 'package:tripc_app/pages/ticket_tour/components/search_ticket_body_view_v2.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider_v2.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_search_scafford_v2.dart';
import '../../../models/app/tripc_service_category.dart';
import '../../../utils/app_extension.dart';

class TripcSearchEntertainmentView extends ConsumerStatefulWidget {
  const TripcSearchEntertainmentView(
      {super.key, required this.category});
  final TripCServiceCategory category;

  @override
  ConsumerState<TripcSearchEntertainmentView> createState() =>
      _TripcSearchEntertainmentViewState();
}

class _TripcSearchEntertainmentViewState extends ConsumerState<TripcSearchEntertainmentView> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // void _onSuggestionTap(String keyword) {
  //   _controller.text = keyword;
  //   ref.read(pTicketTourProviderV2.notifier).onChangeSearchMode(keyword);
  //   ref.read(pTicketTourProviderV2.notifier).getToursByServiceType(
  //     widget.category.getServiceTypeId,
  //     ListDataRequest(page: 1, pageSize: 4, keyWord: keyword),
  //   );
  //   ref.read(pTicketTourProviderV2.notifier).clearSuggestions();
  // }

  @override
  Widget build(BuildContext context) {

    final keyWord =
      ref.watch(pTicketTourProviderV2.select((value) => value.keyWord));
    // final suggestions = ref.watch(
    //   pTicketTourProviderV2.select((s) => s.suggestTypingSearch ?? []),
    // );

    final listServiceType = ref
        .watch(pHomepageScreenProvider.select((value) => value.listServiceType),);

    return TripcSearchScaffoldV2(
        controller: _controller,
        hintText: widget.category.name(context),
        keyword: keyWord,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        titleAppBar: widget.category.name(context),
        onChanged: (value) async {
          debounceHelpers.action(
            callBack: () async {
              ref
                  .read(pTicketTourProviderV2.notifier)
                  .onChangeSearchMode(value);
              ref.read(pTicketTourProviderV2.notifier).searchTours(
                widget.category.getServiceTypeId(listServiceType) ?? 0,
                ListDataRequest(page: 1, pageSize: 20, keyWord: value),
              );
              // await ref.read(pTicketTourProviderV2.notifier).getSuggestSearch();
            },
          );
        },
        onTapSearch: () => unfocusKeyboard(),
        body: Column(
          children: [
            Expanded(
              child: TripcTourTicketBodyViewV2(
                category: widget.category,
              ),
            ),
          ],
        ));
  }
}
