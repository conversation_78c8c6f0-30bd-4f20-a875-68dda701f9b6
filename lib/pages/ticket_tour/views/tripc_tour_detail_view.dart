import 'package:flutter/material.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/profile/components/tripc_not_yet_login_dialog.dart';
import 'package:tripc_app/pages/homepage/homepage_export.dart';
import 'package:tripc_app/pages/ticket_tour/components/tour_saved_view.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/note/tripc_tcent_receive_note.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_portal/tripc_portal.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../../../models/app/tour_type_enum.dart';
import '../../../status_display_notifier.dart';
import '../../../widgets/app_show_api_error_dialog.dart';
import '../../homepage/providers/providers.dart';
import '../components/components.dart';

class TripcTourDetailView extends ConsumerStatefulWidget {
  const TripcTourDetailView({super.key, required this.tourId});

  final int tourId;

  @override
  ConsumerState<TripcTourDetailView> createState() =>
      _TripcTourDetailViewState();
}

class _TripcTourDetailViewState extends ConsumerState<TripcTourDetailView> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ref.read(pTicketTourProvider.notifier).resetDetailedPage();
      ref
          .read(pTicketTourProvider.notifier)
          .getDetailedTour(tourId: widget.tourId);
      ref.listenManual(
          pTicketTourProvider.select((value) => value.errorMessage),
          (_, error) {
        if (error == null || error.isEmpty) {
          return;
        }
        dialogHelpers.show(context, child: ErrorDialog(text: error));
      });

      ref.listenManual(
          pTicketTourProvider.select((value) => value.isSavedTourSuccess),
          (_, state) {
        if (!state) {
          return;
        }
        final isSave = ref.watch(pTicketTourProvider).isSave;
        ref
            .read(pHomepageScreenProvider.notifier)
            .updateTourResponse(widget.tourId, isSave);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final selectedTour =
        ref.watch(pTicketTourProvider.select((value) => value.selectedTour));
    final selectButtonEnable = ref
        .watch(pTicketTourProvider.select((value) => value.selectButtonEnable));
    final visiblePortal =
        ref.watch(pTicketTourProvider.select((value) => value.visiblePortal));
    final isSave =
        ref.watch(pTicketTourProvider.select((value) => value.isSave));
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        onPopScope: () =>
            ref.read(pTicketTourProvider.notifier).resetDetailedPage(),
        extendBodyBehindAppBar: true,
        hasBackButton: true,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        leading: AppAssets.init.iconArrowleft.widget(
          color: AppAssets.origin().whiteBackgroundColor,
        ),
        onLeadingPressed: () {
          ref.read(pTicketTourProvider.notifier).resetDetailedPage();
          Navigator.pop(context);
        },
        actions: [
          // GestureDetector(
          //   onTap: () {
          //     //TODO: share
          //   },
          //   child: AppAssets.init.icShare.widget(
          //     width: 24.H,
          //   ),
          // ),
          SizedBox(width: 16.W),
          TripCPortal(
              visible: isSave && visiblePortal,
              aligned: const Aligned(
                  follower: Alignment.topRight,
                  target: Alignment.bottomRight,
                  offset: Offset(0, 5)),
              isBlur: false,
              icon: isSave
                  ? AppAssets.init.icHeartFill.widget(width: 24.H)
                  : AppAssets.init.icHeart.widget(width: 24.H),
              onClose: () {
                ref.read(pTicketTourProvider.notifier).onCloseSavedPopup();
              },
              onPressed: () async {
                if (globalCacheAuth.isLogged()) {
                  final result =
                      await ref.read(pTicketTourProvider.notifier).savedTour();
                  if (result) {
                    ref
                        .read(pHomepageScreenProvider.notifier)
                        .updatedDealsAroundHere(widget.tourId, !isSave);
                  }
                } else {
                  dialogHelpers.show(context, child: NotYetLoginDialog(
                    resultHandler: () {
                      ref
                          .read(pTicketTourProvider.notifier)
                          .getDetailedTour(tourId: widget.tourId);
                    },
                  ));
                }
              },
              follower: TourSavedView(
                onPressed: () {
                  ref.read(pTicketTourProvider.notifier).onCloseSavedPopup();
                  AppRoute.navigateToRoute(
                    context,
                    routeName: AppRoute.routeListToursView,
                    arguments: {
                      'category': TourType.tourSaved
                    },
                    resultHandler: (value) {
                      ref
                          .read(pTicketTourProvider.notifier)
                          .getDetailedTour(tourId: widget.tourId);
                    },
                  );
                },
              )),
          SizedBox(
            width: 26.W,
          )
        ],
        body: Stack(
          children: [
            SingleChildScrollView(
              padding: EdgeInsets.zero,
              physics: const ClampingScrollPhysics(),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  TripcImageSlide(
                      height: 285.H,
                      images: selectedTour?.images
                              .map((e) => e.url ?? '')
                              .toList() ??
                          []),
                  Container(
                      width: double.infinity,
                      color: AppAssets.origin().whiteBackgroundColor,
                      padding: EdgeInsets.symmetric(vertical: 10.H)
                          .copyWith(bottom: context.spacingBottom),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          TourTicketDetailView(selectedTour: selectedTour),
                          // Visibility(
                          //     visible: selectedTour?.serviceType ==
                          //         TripcServiceCategory.tourSightSeeing,
                          //     // replacement: ComboDetailView(combo: selectedTour),
                          //     child:
                          //         TourTicketDetailView(selectedTour: selectedTour)),
                          _selectButton(
                            isButtonEnable: selectedTour?.serviceType ==
                                    TripCServiceCategory.combo
                                ? true
                                : selectButtonEnable,
                            context,
                            selectedTour: selectedTour,
                            onTapSelect: () => AppRoute.pushNamed(
                              context,
                              routeName: AppRoute.routeAddPassengerQuantity,
                              arguments: selectedTour,
                            ),
                            minTicketPrice:
                                ref.watch(pTicketTourProvider).minTicketPrice,
                          )
                        ],
                      ))
                ],
              ),
            ),
          ],
        ));
  }

  Widget _selectButton(BuildContext context,
      {required bool isButtonEnable,
      TourResponse? selectedTour,
      VoidCallback? onTapSelect,
      required int minTicketPrice}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.W, vertical: 17.H)
          .copyWith(bottom: 0),
      child: SizedBox(
        height: 46.H,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(top: 5.H),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TripcText(
                      // minTicketPrice.vnd,
                      (selectedTour?.totalPrice ?? 0).vndong,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      textAlign: TextAlign.start,
                      textColor: AppAssets.origin().blackColor,
                      padding: EdgeInsets.only(bottom: 4.H),
                    ),
                    Visibility(
                      visible: globalReleaseStatusNotifier.isDisplayAll,
                      child: TripcTcentReceiveNote(
                        fontWeight: FontWeight.w400,
                        tcent: selectedTour?.returnTcent ?? 0,
                        title:
                            '${context.strings.text_receive_now} ${minTicketPrice.vndToTcent} ${context.strings.text_tcent}',
                        hasExpand: true,
                      ),
                    )
                  ],
                ),
              ),
            ),
            SizedBox(
              width: 6.W,
            ),
            TripcButton(
                onPressed: onTapSelect,
                showSuggestLoginDialog: true,
                isLogin: globalCacheAuth.isLogged(),
                resultHandler: () {
                  ref
                      .read(pTicketTourProvider.notifier)
                      .getDetailedTour(tourId: widget.tourId);
                },
                // isButtonDisabled: !isButtonEnable,
                height: 46,
                width: 107,
                title: context.strings.text_select)
          ],
        ),
      ),
    );
  }
}
