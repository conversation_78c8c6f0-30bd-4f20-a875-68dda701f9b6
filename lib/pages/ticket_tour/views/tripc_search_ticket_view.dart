import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_search_scafford.dart';
import '../../../models/app/tripc_service_category.dart';
import '../../../utils/app_extension.dart';
import '../components/components.dart';
import '../providers/providers.dart';

class TripcSearchTicketView extends ConsumerStatefulWidget {
  const TripcSearchTicketView(
      {super.key, required this.category});
  final TripCServiceCategory category;

  @override
  ConsumerState<TripcSearchTicketView> createState() =>
      _TripcSearchTicketViewState();
}

class _TripcSearchTicketViewState extends ConsumerState<TripcSearchTicketView> {
  final TextEditingController _controller = TextEditingController();

  // Widget _buildTabs({
  //   required List<TourResponse> services,
  // }) {
  //   return TripcTourTicketBodyView(serviceItems: services);
  // [
  //   TripcTabData(
  //       index: 0,
  //       title: context.strings.text_all,
  //       screen: ),
  //   TripcTabData(
  //       index: 1,
  //       title: context.strings.text_tour_ticket,
  //       screen: TripcTourTicketBodyView(serviceItems: tourTicket)),
  //   TripcTabData(
  //     index: 2,
  //     title: context.strings.text_combo,
  //     screen: TripcTourTicketBodyView(serviceItems: tourTicket),
  //     // TripcSearchComboBodyView(combos: combos)
  //   ),
  //   TripcTabData(
  //       index: 3,
  //       title: context.strings.text_hotel,
  //       screen: TripcTourTicketBodyView(serviceItems: hotels)),
  // ];
  // }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final keyWord =
        ref.watch(pTicketTourProvider.select((value) => value.keyWord));
    // final tourTicket =
    //     ref.watch(pTicketTourProvider.select((value) => value.serviceItems));
    // final combos =
    //     ref.watch(pTicketTourProvider.select((value) => value.combos));
    // final hotels =
    //     ref.watch(pTicketTourProvider.select((value) => value.hotels));
    return TripcSearchScafford(
        controller: _controller,
        hintText: widget.category.name(context),
        keyword: keyWord,
        onChanged: (value) =>
            ref.read(pTicketTourProvider.notifier).search(value),
        onTapSearch: () => unfocusKeyboard(),
        body: TripcTourTicketBodyView(
          category: widget.category,
        ));

    // TripcDynamicTabbar(
    //     initialIndex: 0,
    //     tabs: _buildTabs(
    //         tourTicket: tourTicket, combos: combos, hotels: hotels),
    //     padding: EdgeInsets.only(top: 10.H)));
  }
}
