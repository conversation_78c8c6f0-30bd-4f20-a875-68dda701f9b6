import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/ticket_tour/components/header_tour_ticket_area.dart';
import 'package:tripc_app/pages/ticket_tour/components/order_info_v2.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/pages/tour-payment/providers/payment_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/status_display_notifier.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_math_button.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_math_button_v2.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_option_button_v2.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_filed.dart';
import '../../../models/remote/api_tour_response/applicable_time_response.dart';
import '../../../widgets/commons/tripc_button/tripc_icon_button.dart';
import '../components/components.dart';

class TripcAddPassengerQuantityViewV2 extends ConsumerStatefulWidget {
  const TripcAddPassengerQuantityViewV2({super.key, required this.tour, this.isFerryTour = false});

  final TourResponse tour;
  final bool isFerryTour;

  @override
  ConsumerState<TripcAddPassengerQuantityViewV2> createState() =>
      _TripcAddPassengerQuantityViewV2State();
}

class _TripcAddPassengerQuantityViewV2State
    extends ConsumerState<TripcAddPassengerQuantityViewV2> {
  static final ScrollController scrollController = ScrollController();

  late final TextEditingController _fullNameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _noteController;
  late final TextEditingController _numberPlateController;

  @override
  void initState() {
    super.initState();
    _fullNameController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();
    _noteController = TextEditingController();
    _numberPlateController = TextEditingController();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _noteController.dispose();
    _numberPlateController.dispose();
    super.dispose();
  }

  void scrollToSelectedDate(
      DateTime? selectedDate, ApplicableTimeResponse? availableDates) {
    if (selectedDate == null || availableDates == null) return;

    final index = availableDates.listApplicableTime
        .indexWhere((date) => DateUtils.isSameDay(date, selectedDate));

    if (index != -1) {
      final itemWidth = 95.W;
      final separatorWidth = 10.W;
      final scrollPosition = index * (itemWidth + separatorWidth);

      final maxScroll = scrollController.position.maxScrollExtent;
      final finalScrollPosition = scrollPosition.clamp(0.0, maxScroll);

      Future.delayed(const Duration(milliseconds: 100), () {
        if (scrollController.hasClients) {
          scrollController.animateTo(
            finalScrollPosition,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  bool isBeforeCurrentTime({required String current, required String time}) {
    return current.compareTo(time) >= 0;
  }

  @override
  Widget build(BuildContext context) {
    final selectedTour =
        ref.watch(pTicketTourProvider.select((value) => value.selectedTour));
    final totalPayment =
        ref.watch(pTicketTourProvider.select((value) => value.totalPayment));
    return TripcScaffold(
      onPressed: () => unfocusKeyboard(),
      onPopScope: () => ref.read(pTicketTourProvider.notifier).resetQuantity(),
      onLeadingPressed: () {
        ref.read(pTicketTourProvider.notifier).resetQuantity();
        Navigator.pop(context);
      },
      hasBackButton: true,
      titleAppBar: TripcText(
        context.strings.order_information.toSentenceCase(),
        fontWeight: FontWeight.w600,
        fontSize: 16,
        textColor: AppAssets.origin().black,
      ),
      appBarColor: AppAssets.origin().whiteBackgroundColor,
      bottom: PreferredSize(
          preferredSize: const Size(double.infinity, 16),
          child: Divider(
            height: 1,
            color: AppAssets.init.lightGray,
          )),
      backgroundColor: AppAssets.origin().bgDetailTourColorV2,
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            bottom: 151.H,
            child: SingleChildScrollView(
              child: Column(
                spacing: 16.H,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (selectedTour != null)
                    HeaderTourTicketArea(tour: widget.tour),
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 16.W),
                    decoration: BoxDecoration(
                      color: AppAssets.origin().whiteBackgroundColor,
                      borderRadius: BorderRadius.circular(12.SP),
                    ),
                    padding:
                        EdgeInsets.symmetric(horizontal: 9.W, vertical: 20.H),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 16.H,
                      children: [
                        _pickServiceArea(
                            selectedTour: selectedTour, context, ref),
                        _addPassengersArea(
                          context,
                          ref,
                          selectedTour: selectedTour,
                        ),
                        // TODO: Try to remove hard code, this is id of ferry category
                        _contactInformation(context, ref, isFerryTour: widget.isFerryTour),
                        _otherRequiredNote(context, ref),
                      ],
                    ),
                  ),
                  _infoToNote(
                    context,
                    selectedTour: selectedTour,
                  ),
                  SizedBox(
                    height: 15.H,
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(
                    color: Colors.grey.shade300,
                    width: 1.0,
                  ),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    offset: const Offset(0, -2),
                    blurRadius: 8.0,
                    spreadRadius: 1.0,
                  ),
                ],
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 8.H).copyWith(bottom: 14.H),
              child: _selectButton(context,
                  totalPay: totalPayment,
                  selectedTour: selectedTour, onTapSelect: () {
                ref.read(pTicketTourProvider.notifier).updateToTalInfomation(
                      specicalNote: _noteController.text,
                    );
                final payment = ref.watch(pTicketTourProvider.select(
                  (value) => value.payment,
                ));
                ref.read(pPaymentProvider.notifier).createPayment(payment);
                AppRoute.pushNamed(
                  context,
                  routeName: AppRoute.routeTourPaymentV2,
                  arguments: {
                    'tour': selectedTour,
                    'isFerryTour': widget.isFerryTour
                  },
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _contactInformation(BuildContext context, WidgetRef ref, {bool isFerryTour = false}) {
    final contactErrorPhoneNumber = ref.watch(
      pTicketTourProvider.select((value) => value.contactErrorPhoneNumber10c),
    );
    final contactErrorEmail = ref.watch(
      pTicketTourProvider.select(
        (value) => value.contactErrorEmail,
      ),
    );
    final fullNameErrorText = ref.watch(
      pTicketTourProvider.select(
            (value) => value.fullNameErrorText,
      ),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 11.H,
      children: [
        TripcText(
          context.strings.text_contact_information.toSentenceCase(),
          fontSize: 16,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w600,
          padding: EdgeInsets.only(bottom: 4.H),
          textColor: AppAssets.origin().textColorForNameTourV2,
        ),
        Padding(
          padding: EdgeInsets.only(left: 10.W),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  TripcText(
                    context.strings.text_first_name_and_last_name
                        .toSentenceCase(),
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(bottom: 3.H),
                    textColor: AppAssets.origin().black,
                  ),
                  TripcText(
                    '*',
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(left: 3.H),
                    textColor: AppAssets.origin().redDotColor,
                  ),
                ],
              ),
              TripcTextField(
                controller: _fullNameController,
                hintText: context.strings.text_full_name,
                fontSize: 12,
                errorText: fullNameErrorText.isEmpty
                    ? null
                    : fullNameErrorText,
                scrollPadding: EdgeInsets.zero,
                onChanged: (value) => ref.read(pTicketTourProvider.notifier).setFullName(context, value),
                textInputAction: TextInputAction.next,
                hintTextColor: AppAssets.origin().lightGrayDD4,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 10.W),
          child: Column(
            children: [
              Row(
                children: [
                  TripcText(
                    context.strings.text_linked_phone,
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(bottom: 3.H),
                    textColor: AppAssets.origin().black,
                  ),
                  TripcText(
                    '*',
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(left: 3.H),
                    textColor: AppAssets.origin().redDotColor,
                  ),
                ],
              ),
              TripcTextField(
                errorText: contactErrorPhoneNumber.isEmpty
                    ? null
                    : contactErrorPhoneNumber,
                controller: _phoneController,
                hintText: context.strings.text_mobile_phone,
                hintTextColor: AppAssets.origin().lightGrayDD4,
                keyboardType: TextInputType.number,
                fontSize: 12,
                maxLength: 10,
                onChanged: (value) {
                  ref
                      .read(pTicketTourProvider.notifier)
                      .setPhoneNumber10c(value, context);
                },
                textInputAction: TextInputAction.done,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 10.W),
          child: Column(
            children: [
              Row(
                children: [
                  TripcText(
                    context.strings.text_email,
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(bottom: 3.H),
                    textColor: AppAssets.origin().black,
                  ),
                  TripcText(
                    '*',
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(left: 3.H),
                    textColor: AppAssets.origin().redDotColor,
                  ),
                ],
              ),
              TripcTextField(
                controller: _emailController,
                onChanged: (value) => ref
                    .read(pTicketTourProvider.notifier)
                    .setEmail(context, value: value),
                hintText: context.strings.text_email,
                errorText: contactErrorEmail.isEmpty ? null : contactErrorEmail,
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.emailAddress,
                hintTextColor: AppAssets.origin().lightGrayDD4,
                fontSize: 12,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
              ),
            ],
          ),
        ),
        if (isFerryTour)
          Padding(
            padding: EdgeInsets.only(left: 10.W),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TripcText(
                  context.strings.number_plate,
                  fontSize: 14,
                  textAlign: TextAlign.start,
                  fontWeight: FontWeight.w500,
                  padding: EdgeInsets.only(bottom: 3.H),
                  textColor: AppAssets.origin().black,
                ),
                TripcTextField(
                  controller: _numberPlateController,
                  // TODO: Q/A BE the key argument for number plate
                  onChanged: (value) => ref
                      .read(pTicketTourProvider.notifier)
                      .setNumberPlate(value),
                  hintText: context.strings.enter_number_plate,
                  textInputAction: TextInputAction.next,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  fontSize: 12,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _otherRequiredNote(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_special_requests.toSentenceCase(),
          fontSize: 16,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w600,
          padding: EdgeInsets.only(bottom: 15.H),
          textColor: AppAssets.origin().textColorForNameTourV2,
        ),
        Container(
          height: 79.H,
          padding: EdgeInsets.only(left: 10.W),
          margin: EdgeInsets.only(bottom: 2.H),
          child: TripcTextField(
            controller: _noteController,
            hintText: context.strings.text_add_special_request,
            hintTextColor: AppAssets.origin().lightGrayDD4,
            fontSize: 12,
            expands: true,
            maxLines: null,
            textAlignVertical: TextAlignVertical.top,
            contentPadding:
                EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
          ),
        ),
      ],
    );
  }

  Widget _addPassengersArea(
    BuildContext context,
    WidgetRef ref, {
    required TourResponse? selectedTour,
  }) {
    final totalDiscount = ref.watch(pTicketTourProvider).totalDiscount;
    if (totalDiscount.isNotEmpty) {
      return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.quantity,
          fontSize: 16,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w600,
          padding: EdgeInsets.only(bottom: 15.H),
          textColor: AppAssets.origin().textColorForNameTourV2,
        ),
        Column(
          spacing: 8.H,
          children: selectedTour?.subProducts?.mapIndexed((idx, e) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 10.W),
                    child: TripcText(
                      selectedTour.subProducts?[idx].audience?.name ?? context.strings.text_passenger_title,
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      textColor: AppAssets.origin().black,
                    ),
                  ),
                ),
                Row(
                  spacing: 8.W,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    TripcText(
                      totalDiscount[idx].vndong,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      textColor: AppAssets.origin().textColorForNameTourV2,
                    ),
                    Visibility(
                      visible:
                      e.ticketPrice != totalDiscount[idx],
                      child: TripcText(
                        e.ticketPrice.vndong,
                        fontSize: 8,
                        fontWeight: FontWeight.w400,
                        textColor:
                        AppAssets.origin().textColorForNameTourV2,
                        decoration: TextDecoration.lineThrough,
                      ),
                    ),
                    _addAndMinusArea(
                        quantity: e.quantity,
                        result: e.stock, onTapMinus: () => ref
                        .read(pTicketTourProvider.notifier)
                        .onTapMinusQtt(e),
                        onTapPlus: () => ref
                            .read(pTicketTourProvider.notifier)
                            .onTapAddQtt(e,
                            ticketType: idx == 0
                                ? TicketType.aldult
                                : TicketType.child),
                    )
                  ],
                )
              ],
            );
          }).toList() ?? [],
        ),
      ],
    );
    }
    return const SizedBox();
  }

  Widget _pickServiceArea(BuildContext context, WidgetRef ref,
      {TourResponse? selectedTour}) {
    final selectedDate =
        ref.watch(pTicketTourProvider.select((value) => value.selectedDate));
    final availableDates = selectedTour?.applicableTime;

    return _selectPackage(
      context,
      selectedDate: selectedDate,
      availableDates: availableDates,
      onPickDate: (value) =>
          ref.read(pTicketTourProvider.notifier).selectDate(date: value),
      onTapCalendar: () {
        bottomSheetHelpers.show(
          context,
          child: Builder(
            builder: (dialogContext) {
              return TripcCalendarBottomSheet(
                  onSelectDate: (date) {
                    Navigator.pop(dialogContext);

                    if (availableDates == null) {
                      Navigator.pop(dialogContext);
                      return;
                    }

                    final pickedDate = date.atDate();
                    final endDate = availableDates.endDateTime.atDate();
                    final startDate = availableDates.startDateTime.atDate();

                    if (pickedDate.isBefore(endDate) &&
                            pickedDate.isAfter(startDate) ||
                        pickedDate.isAtSameMomentAs(startDate) ||
                        pickedDate.isAtSameMomentAs(endDate)) {
                      ref
                          .read(pTicketTourProvider.notifier)
                          .selectDate(date: date);
                    }
                  },
                  selectedDay: selectedDate ?? DateTime.now());
            },
          ),
        );
      },
    );
  }

  Widget _selectPackage(BuildContext context,
      {required DateTime? selectedDate,
      required ApplicableTimeResponse? availableDates,
      Function(DateTime)? onPickDate,
      VoidCallback? onTapCalendar}) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      scrollToSelectedDate(selectedDate, availableDates);
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        TripcText(
          context.strings.select_date,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          textAlign: TextAlign.start,
          textColor: AppAssets.origin().textColorForNameTourV2,
          padding: EdgeInsets.only(bottom: 15.H),
        ),
        SizedBox(
          height: 36.H,
          child: Row(
            children: [
              SizedBox(width: 10.W),
              Expanded(
                child: ListView.separated(
                    controller: scrollController,
                    itemCount: availableDates?.listApplicableTime.length ?? 0,
                    scrollDirection: Axis.horizontal,
                    separatorBuilder: (context, _) => SizedBox(width: 10.W),
                    itemBuilder: (context, index) {
                      final date = availableDates!.listApplicableTime[index];
                      return TripcOptionButtonV2(
                        onPressed: () {
                          onPickDate?.call(date);
                        },
                        isActive: DateUtils.isSameDay(date, selectedDate),
                        value: date.dateTimeVi,
                      );
                    }),
              ),
              SizedBox(width: 10.W),
              TripcIconButton(
                onPressed: onTapCalendar,
                child: AppAssets.origin()
                    .icCalendar
                    .widget(height: 20.W, width: 20.W),
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _addAndMinusArea({
    required int result,
    int? quantity,
    required VoidCallback onTapMinus,
    required VoidCallback onTapPlus,
  }) {
    return Row(
      children: [
        TripcMathButtonV2(
          isActive: result > 0,
          onPressed: onTapMinus,
          symbol: MathSymbol.minus,
        ),
        TripcText(
          result.toString(),
          fontSize: 14,
          fontWeight: FontWeight.w400,
          textColor: AppAssets.origin().blackColor,
          padding: EdgeInsets.symmetric(horizontal: 8.W),
        ),
        TripcMathButtonV2(
          isActive: quantity == null ? true : result < quantity,
          onPressed: onTapPlus,
          symbol: MathSymbol.add,
        ),
      ],
    );
  }

  Widget _infoToNote(BuildContext context,
      {required TourResponse? selectedTour}) {
    return OrderInfoHtml(
      title: context.strings.text_information_to_note,
      htmlContent: selectedTour?.bookingInformation ?? '',
    );
  }

  Widget _selectButton(BuildContext context,
      {TourResponse? selectedTour,
        VoidCallback? onTapSelect,
        required int totalPay}) {
    final contactErrorPhoneNumber = ref.watch(
        pTicketTourProvider.select((value) => value.contactErrorPhoneNumber));
    final contactErrorEmail = ref.watch(pTicketTourProvider.select(
          (value) => value.contactErrorEmail,
    ));
    final fullNameErrorText = ref.watch(
      pTicketTourProvider.select(
            (value) => value.fullNameErrorText,
      ),
    );
    return Padding(
      padding: EdgeInsets.all(10.W),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                context.strings.ticket_price,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
              ),
              TripcText(
                totalPay.vnd,
                fontSize: 24,
                fontWeight: FontWeight.w700,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().priceServiceItemColor,
              ),
            ],),
          SizedBox(height: 5.H,),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                context.strings.surcharge_included,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().disableColorV2,
              ),
              if (globalReleaseStatusNotifier.isDisplayAll)
                Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 8, vertical: 4
                  ),
                  decoration: BoxDecoration(
                    color: AppAssets.origin().bgTcentServiceItemColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      AppAssets.origin().icTcentV2.widget(height: 20.W, width: 20.W),
                      TripcText(
                        '${context.strings.text_receive_now} ${(totalPay.vndToTcent).tcent}',
                        ignorePointer: true,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        textColor: AppAssets.origin().tcentServiceItemColor,
                        padding: EdgeInsets.only(left: 4.W),
                      ),
                    ],
                  ),
                ),
            ],),
          SizedBox(height: 8.H,),
          TripcButton(
            onPressed: onTapSelect,
            showSuggestLoginDialog: true,
            textCase: TextCaseType.none,
            style: AppButtonStyle(
                backgroundColor: AppAssets.origin().primaryColorV2),
            isLogin: globalCacheAuth.isLogged(),
            isButtonDisabled: !(totalPay != 0 &&
                contactErrorPhoneNumber.isEmpty &&
                contactErrorEmail.isEmpty &&
                fullNameErrorText.isEmpty &&
                _fullNameController.text.isNotEmpty &&
                _emailController.text.isNotEmpty &&
                _phoneController.text.isNotEmpty),
            height: 46,
            title: context.strings.text_pay,
          )
        ],
      ),
    );
  }
}
