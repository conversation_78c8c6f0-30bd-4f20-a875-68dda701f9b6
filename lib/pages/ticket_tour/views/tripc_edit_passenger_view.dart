import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/passenger.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_field_with_label.dart';
import '../providers/providers.dart';

class TripcEditPassenger extends ConsumerStatefulWidget {
  const TripcEditPassenger({super.key, this.passenger});
  final Passenger? passenger;

  @override
  ConsumerState<TripcEditPassenger> createState() => _TripcEditPassengerState();
}

class _TripcEditPassengerState extends ConsumerState<TripcEditPassenger> {
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _guardianNameController = TextEditingController();

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        ref
            .read(pTicketTourProvider.notifier)
            .setCurrentTicketType(widget.passenger?.type ?? TicketType.aldult);
        _fullNameController.text = widget.passenger?.name ?? '';
        _phoneController.text = widget.passenger?.phone ?? '';
        _emailController.text = widget.passenger?.email ?? '';
        _guardianNameController.text = widget.passenger?.guardianName ?? '';
        ref
            .read(pTicketTourProvider.notifier)
            .setPassengerEmail(context, value: widget.passenger?.email ?? '');
        if (widget.passenger?.phone?.isNotEmpty ?? false) {
          ref
              .read(pTicketTourProvider.notifier)
              .setPassengerPhoneNumber(widget.passenger?.phone ?? '', context);
        }
        ref
            .read(pTicketTourProvider.notifier)
            .setPassengerFullName(widget.passenger?.name ?? '');
        ref
            .read(pTicketTourProvider.notifier)
            .setGuardianName(widget.passenger?.guardianName ?? '');
      },
    );
    super.initState();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _guardianNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final errorEmail = ref.watch(pTicketTourProvider.select(
      (value) => value.passengerEmailError,
    ));
    final isEnableConfirm = ref.watch(pTicketTourProvider.select(
      (value) => value.isButtonConfirmDisable,
    ));
    final phoneError = ref.watch(pTicketTourProvider.select((value) => value.phoneError));
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        onPopScope: () {
          ref.read(pTicketTourProvider.notifier).resetPassengerInfo();
        },
        onLeadingPressed: () {
          Navigator.pop(context);
          ref.read(pTicketTourProvider.notifier).resetPassengerInfo();
        },
        hasBackButton: true,
        resizeToAvoidBottomInset: false,
        titleAppBar: TripcText(
          context.strings.text_edit_passenger,
          fontWeight: FontWeight.w600,
          fontSize: 16,
          textColor: AppAssets.origin().black,
        ),
        bottom: PreferredSize(
            preferredSize: const Size(double.infinity, 16),
            child: Divider(
              height: 1,
              color: AppAssets.init.lightGray,
            )),
        actions: [
          TripcText(
            onTap: isEnableConfirm
                ? null
                : () {
                    ref
                        .read(pTicketTourProvider.notifier)
                        .updatePassenger(passenger: widget.passenger);
                    ref.read(pTicketTourProvider.notifier).resetPassengerInfo();
                    Navigator.pop(context);
                  },
            context.strings.text_confirm,
            fontWeight: FontWeight.w500,
            fontSize: 14,
            textColor: !isEnableConfirm
                ? AppAssets.origin().black
                : AppAssets.origin().secondDarkGreyTextColor,
            padding: EdgeInsets.only(right: 11.W),
          ),
        ],
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.W, vertical: 18.H),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TripcText(
                context.strings.text_passenger_name_rule,
                fontSize: 12,
                height: 1.5,
                textAlign: TextAlign.start,
                fontWeight: FontWeight.w300,
              ),
              // SizedBox(
              //   height: 8.H,
              // ),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.start,
              //   children: [
              //     TripcIconButton(
              //         onPressed: () {},
              //         child: AppAssets.origin()
              //             .icIOutLine
              //             .widget(height: 16.H, width: 16.H)),
              //     SizedBox(width: 5.W),
              //     TripcText(
              //       context.strings.text_principles_on_passenger_names,
              //       fontSize: 12,
              //       fontWeight: FontWeight.w400,
              //       textColor: AppAssets.origin().darkBlueColor,
              //     ),
              //   ],
              // ),
              SizedBox(
                height: 32.H,
              ),
              TripcTextFieldWithLabel(
                textController: _fullNameController,
                label: widget.passenger?.type == TicketType.aldult
                    ? context.strings.text_first_name_and_last_name
                    : context.strings.text_child_name,
                isEnableLabelTextCaseType: false,
                labelColor: AppAssets.origin().blackColor,
                labelFontSize: 12,
                textInputAction: TextInputAction.next,
                onChanged:
                    ref.read(pTicketTourProvider.notifier).setPassengerFullName,
                labelFontWeight: FontWeight.w300,
                hintText: widget.passenger?.type == TicketType.aldult
                    ? context.strings.text_enter_fullname
                    : context.strings.text_child_name_hint,
                hintTextColor: AppAssets.origin().lightGrayDD4,
                fontSize: 12,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 13.H, horizontal: 14.W),
              ),
              Visibility(
                visible: widget.passenger?.type == TicketType.child,
                child: TripcTextFieldWithLabel(
                  textController: _guardianNameController,
                  label: context.strings.text_guardian_name,
                  labelColor: AppAssets.origin().blackColor,
                  labelFontSize: 12,
                  textInputAction: TextInputAction.next,
                  onChanged:
                      ref.read(pTicketTourProvider.notifier).setGuardianName,
                  labelFontWeight: FontWeight.w300,
                  hintText: context.strings.text_guardian_name_hint,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  fontSize: 12,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 14.W),
                  padding: EdgeInsets.only(top: 20.H),
                ),
              ),
              TripcTextFieldWithLabel(
                textController: _phoneController,
                label: widget.passenger?.type == TicketType.aldult
                    ? context.strings.text_phone_number
                    : context.strings.text_guardian_phone_number,
                isEnableLabelTextCaseType: false,
                labelColor: AppAssets.origin().blackColor,
                labelFontSize: 12,
                error: phoneError.isNotEmpty
                    ? TripcText(
                  phoneError,
                  textAlign: TextAlign.start,
                  enableAutoResize: true,
                  textColor: AppAssets.origin().redDotColor,
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                  padding: EdgeInsets.only(
                      top: 8.H, bottom: 10.H, left: 8.W),
                )
                    : null,
                textInputAction: TextInputAction.next,
                labelFontWeight: FontWeight.w300,
                onChanged: (value) => ref
                    .read(pTicketTourProvider.notifier)
                    .setPassengerPhoneNumber(value, context),
                hintText: widget.passenger?.type == TicketType.aldult
                    ? context.strings.text_enter_contact_phone_num
                    : context.strings.text_guardian_phone_number_hint,
                hintTextColor: AppAssets.origin().lightGrayDD4,
                showErrorSameLine: false,
                errorText: phoneError.isEmpty ? null : phoneError,
                keyboardType: TextInputType.number,
                fontSize: 12,
                maxLength: 10,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 13.H, horizontal: 14.W),
                padding: EdgeInsets.symmetric(vertical: 20.H),
              ),
              Visibility(
                visible: widget.passenger?.type == TicketType.aldult,
                child: TripcTextFieldWithLabel(
                  textController: _emailController,
                  label: '${context.strings.text_email}:',
                  hintText: context.strings.text_enter_email,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  labelColor: AppAssets.origin().blackColor,
                  labelFontSize: 12,
                  textInputAction: TextInputAction.done,
                  onChanged: (value) => ref
                      .read(pTicketTourProvider.notifier)
                      .setPassengerEmail(context, value: value),
                  labelFontWeight: FontWeight.w300,
                  errorText: errorEmail,
                  keyboardType: TextInputType.emailAddress,
                  showErrorSameLine: false,
                  error: errorEmail.isNotEmpty
                      ? TripcText(
                          errorEmail,
                          textAlign: TextAlign.start,
                          enableAutoResize: true,
                          textColor: AppAssets.origin().redDotColor,
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                          padding: EdgeInsets.only(
                              top: 8.H, bottom: 10.H, left: 8.W),
                        )
                      : null,
                  fontSize: 12,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 14.W),
                ),
              ),
            ],
          ),
        ));
  }
}
