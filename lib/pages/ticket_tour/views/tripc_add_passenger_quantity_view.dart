import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/total_price/total_price_area.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_math_button.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_option_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../../../models/remote/api_tour_response/applicable_time_response.dart';
import '../../../widgets/commons/tripc_button/tripc_icon_button.dart';
import '../components/components.dart';
import '../components/tour_information_view.dart';
import '../providers/providers.dart';
import 'package:html2md/html2md.dart' as html2md;

class TripcAddPassengerQuantityView extends ConsumerWidget {
  const TripcAddPassengerQuantityView({super.key, required this.tour});
  final TourResponse tour;

  static final ScrollController scrollController = ScrollController();

  void scrollToSelectedDate(
      DateTime? selectedDate, ApplicableTimeResponse? availableDates) {
    if (selectedDate == null || availableDates == null) return;

    final index = availableDates.listApplicableTime
        .indexWhere((date) => DateUtils.isSameDay(date, selectedDate));

    if (index != -1) {
      final itemWidth = 95.W;
      final separatorWidth = 10.W;
      final scrollPosition = index * (itemWidth + separatorWidth);

      final maxScroll = scrollController.position.maxScrollExtent;
      final finalScrollPosition = scrollPosition.clamp(0.0, maxScroll);

      Future.delayed(const Duration(milliseconds: 100), () {
        if (scrollController.hasClients) {
          scrollController.animateTo(
            finalScrollPosition,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  bool isBeforeCurrentTime({required String current, required String time}) {
    return current.compareTo(time) >= 0;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTour =
        ref.watch(pTicketTourProvider.select((value) => value.selectedTour));
    final aldultQuantity =
        ref.watch(pTicketTourProvider.select((value) => value.aldultQuantity));
    final childQuantity =
        ref.watch(pTicketTourProvider.select((value) => value.childQuantity));
    final totalPayment =
        ref.watch(pTicketTourProvider.select((value) => value.totalPayment));
    final selectButtonEnable = ref
        .watch(pTicketTourProvider.select((value) => value.selectButtonEnable));
    return TripcScaffold(
      onPressed: () => unfocusKeyboard(),
      onPopScope: () => ref.read(pTicketTourProvider.notifier).resetQuantity(),
      onLeadingPressed: () {
        ref.read(pTicketTourProvider.notifier).resetQuantity();
        Navigator.pop(context);
      },
      hasBackButton: true,
      titleAppBar: TripcText(
        context.strings.text_add_quantity_passengers,
        fontWeight: FontWeight.w500,
        fontSize: 18,
        textColor: AppAssets.origin().black,
      ),
      bottom: PreferredSize(
          preferredSize: const Size(double.infinity, 16),
          child: Divider(
            height: 1,
            color: AppAssets.init.lightGray,
          )),
      backgroundColor: AppAssets.origin().whiteBackgroundColor,
      body: Padding(
        padding: EdgeInsets.only(
            left: 24.W,
            right: 9.W,
            top: 20.H,
            bottom: context.mediaQuery.padding.bottom),
        child: Column(mainAxisAlignment: MainAxisAlignment.start, children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TripcText(
                    selectedTour?.name ?? '',
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    textAlign: TextAlign.start,
                    textColor: AppAssets.origin().blackColor,
                    padding: EdgeInsets.only(bottom: 12.H),
                  ),
                  _tourInfoArea(context, selectedTour: selectedTour),
                  SizedBox(
                    height: 10.H,
                  ),
                  _pickServiceArea(selectedTour: selectedTour, context, ref),
                  _addPassengersArea(
                    context,
                    ref,
                    aldultQuantity: aldultQuantity,
                    childQuantity: childQuantity,
                    selectedTour: selectedTour,
                    onTapAddAldultQtt: () => ref
                        .read(pTicketTourProvider.notifier)
                        .addQuantityTicket(ticketType: TicketType.aldult),
                    onTapMinusAldultQtt: () => ref
                        .read(pTicketTourProvider.notifier)
                        .reduceQuantityTicket(ticketType: TicketType.aldult),
                    onTapAddChildQtt: () => ref
                        .read(pTicketTourProvider.notifier)
                        .addQuantityTicket(ticketType: TicketType.child),
                    onTapMinusChildQtt: () => ref
                        .read(pTicketTourProvider.notifier)
                        .reduceQuantityTicket(ticketType: TicketType.child),
                  ),
                  SizedBox(
                    height: 20.H,
                  ),
                  _infoToNote(
                    context,
                    selectedTour: selectedTour,
                  ),
                  SizedBox(
                    height: 20.H,
                  ),
                  TotalPriceArea(
                      isDisable: !(totalPayment != 0 && selectButtonEnable),
                      onTap: () => AppRoute.pushNamed(context,
                          routeName: AppRoute.routeInputPassenger,
                          arguments: tour),
                      total: totalPayment)
                ],
              ),
            ),
          ),
        ]),
      ),
    );
  }

  Widget _tourInfoArea(BuildContext context,
      {required TourResponse? selectedTour}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        TourInformationView(
          selectedTour: selectedTour,
        ),
      ],
    );
  }

  Widget _addPassengersArea(
    BuildContext context,
    WidgetRef ref, {
    required TourResponse? selectedTour,
    required int aldultQuantity,
    required int childQuantity,
    required Function() onTapAddAldultQtt,
    required Function() onTapMinusAldultQtt,
    required Function() onTapAddChildQtt,
    required Function() onTapMinusChildQtt,
  }) {
    final bool showAddAdult = selectedTour?.subProducts?.length == 2;
    final totalDiscount = ref.watch(pTicketTourProvider).totalDiscount;
    return totalDiscount.isNotEmpty
        ? Column(
            children: selectedTour?.subProducts?.mapIndexed(
                  (idx, e) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TripcText(
                          showAddAdult
                              ? idx == 0
                                  ? context.strings.text_adult_above_10
                                  : context.strings.text_children_5_to_9
                              : context.strings.text_passenger_title,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          textColor: AppAssets.origin().blackColor,
                          padding: EdgeInsets.only(top: idx == 0 ? 0 : 18.H),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              left: 32.W, top: 12.H, right: 14.W),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                children: [
                                  // if (e.ticketPrice != totalDiscount[idx])
                                  Visibility(
                                    visible:
                                        e.ticketPrice != totalDiscount[idx],
                                    child: Padding(
                                      padding: EdgeInsets.only(bottom: 2.H),
                                      child: TripcText(
                                        e.ticketPrice.vndong,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        textColor:
                                            AppAssets.origin().blackColor,
                                        decoration: TextDecoration.lineThrough,
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 2.H),
                                  TripcText(
                                    totalDiscount[idx].vndong,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    textColor: e.ticketPrice ==
                                            totalDiscount[idx]
                                        ? AppAssets.origin().blackColor
                                        : AppAssets.origin().secondDarkYellow,
                                  ),
                                ],
                              ),
                              _addAndMinusArea(
                                  result: e.stock,
                                  onTapMinus: () => ref
                                      .read(pTicketTourProvider.notifier)
                                      .onTapMinusQtt(e),
                                  onTapPlus: () => ref
                                      .read(pTicketTourProvider.notifier)
                                      .onTapAddQtt(e,
                                          ticketType: idx == 0
                                              ? TicketType.aldult
                                              : TicketType.child))
                            ],
                          ),
                        )
                      ],
                    );
                  },
                ).toList() ??
                [],
          )
        : const SizedBox.shrink();
  }

  Widget _pickServiceArea(BuildContext context, WidgetRef ref,
      {TourResponse? selectedTour}) {
    final selectedDate =
        ref.watch(pTicketTourProvider.select((value) => value.selectedDate));
    final selectedTime =
        ref.watch(pTicketTourProvider.select((value) => value.selectedTime));
    final availableDates = selectedTour?.applicableTime;
    final seatAttributes =
        ref.watch(pTicketTourProvider.select((value) => value.seatAttributes));

    return Column(
      children: [
        _selectPackage(
          context,
          selectedDate: selectedDate,
          availableDates: availableDates,
          onPickDate: (value) =>
              ref.read(pTicketTourProvider.notifier).selectDate(date: value),
          onTapCalendar: () {
            bottomSheetHelpers.show(context,
                child: Builder(builder: (dialogContext) {
              return TripcCalendarBottomSheet(
                  onSelectDate: (date) {
                    Navigator.pop(dialogContext);

                    if (availableDates == null) {
                      Navigator.pop(dialogContext);
                      return;
                    }

                    final pickedDate = date.atDate();
                    final endDate = availableDates.endDateTime.atDate();
                    final startDate = availableDates.startDateTime.atDate();

                    if (pickedDate.isBefore(endDate) &&
                            pickedDate.isAfter(startDate) ||
                        pickedDate.isAtSameMomentAs(startDate) ||
                        pickedDate.isAtSameMomentAs(endDate)) {
                      ref
                          .read(pTicketTourProvider.notifier)
                          .selectDate(date: date);
                    }
                  },
                  selectedDay: selectedDate ?? DateTime.now());
            }));
          },
        ),
        SizedBox(
          height: 16.H,
        ),
        _selectTimeFrame(
            onTap: (value) => ref
                .read(pTicketTourProvider.notifier)
                .selectTime(timeFrame: value),
            context,
            selectedTimeFrame: selectedTime ?? '',
            timeFrame: availableDates?.time ?? [],
            selectedDate: selectedDate),
        SizedBox(
          height: 16.H,
        ),
        Visibility(
          visible: seatAttributes.isNotEmpty,
          child: _selectSeatType(
              onTap: (value) => ref
                  .read(pTicketTourProvider.notifier)
                  .selectSeatType(seatType: value),
              context,
              selectedSeatType: ref.watch(pTicketTourProvider.select(
                (value) => value.selectedSeatType,
              )),
              seatType: seatAttributes),
        )
      ],
    );
  }

  Widget _selectPackage(BuildContext context,
      {required DateTime? selectedDate,
      required ApplicableTimeResponse? availableDates,
      Function(DateTime)? onPickDate,
      VoidCallback? onTapCalendar}) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      scrollToSelectedDate(selectedDate, availableDates);
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        TripcText(context.strings.text_select_service_package,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(bottom: 12.H)),
        Container(
          margin: EdgeInsets.symmetric(vertical: 4.H).copyWith(bottom: 6.H),
          height: 40.H,
          child: Row(
            children: [
              Expanded(
                child: ListView.separated(
                    controller: scrollController,
                    itemCount: availableDates?.listApplicableTime.length ?? 0,
                    scrollDirection: Axis.horizontal,
                    separatorBuilder: (context, _) => SizedBox(width: 10.W),
                    itemBuilder: (context, index) {
                      final date = availableDates!.listApplicableTime[index];
                      return TripcOptionButton(
                          onPressed: () {
                            onPickDate?.call(date);
                          },
                          isActive: DateUtils.isSameDay(date, selectedDate),
                          value: date.dateTimeVi);
                    }),
              ),
              SizedBox(width: 16.W),
              TripcIconButton(
                onPressed: onTapCalendar,
                child: Row(
                  children: [
                    AppAssets.origin()
                        .icCalendar
                        .widget(height: 16.H, width: 16.H),
                    SizedBox(width: 5.W),
                    AppAssets.origin()
                        .icBlueRight
                        .widget(height: 16.H, width: 16.H),
                  ],
                ),
              )
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            AppAssets.origin().icICircle.widget(height: 16.H, width: 16.H),
            SizedBox(width: 8.W),
            Expanded(
              child: TripcText(
                context.strings.text_note_price_at_local_time,
                fontSize: 12,
                enableAutoResize: true,
                maxLines: 2,
                fontWeight: FontWeight.w400,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().secondDarkGreyTextColor,
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _selectTimeFrame(BuildContext context,
      {required List<String> timeFrame,
      required String selectedTimeFrame,
      required DateTime? selectedDate,
      Function(String)? onTap}) {
    bool isDisablePassedTime = false;

    final currentDateTime = DateTime.now().toLocal();
    final currentTime = currentDateTime.toHourMinuteString();

    if (selectedDate != null &&
        selectedDate.atDate().isAtSameMomentAs(currentDateTime.atDate())) {
      isDisablePassedTime = true;
    }

    return Padding(
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TripcText(
            context.strings.text_time_frame,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(bottom: 16.H),
          ),
          SizedBox(
            height: 40.H,
            child: ListView.separated(
                itemCount: timeFrame.length,
                padding: EdgeInsets.only(right: 24.W),
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, _) => SizedBox(
                      width: 10.W,
                    ),
                itemBuilder: (context, index) {
                  bool isDisable = isDisablePassedTime
                      ? isBeforeCurrentTime(
                          current: currentTime, time: timeFrame[index])
                      : false;

                  return TripcOptionButton(
                      onPressed: isDisable
                          ? null
                          : () {
                              onTap?.call(timeFrame[index]);
                            },
                      isActive: selectedTimeFrame == timeFrame[index],
                      value: timeFrame[index],
                      isDisable: isDisable);
                }),
          ),
        ],
      ),
    );
  }

  Widget _selectSeatType(BuildContext context,
      {required List<AttributeValue> seatType,
      AttributeValue? selectedSeatType,
      Function(AttributeValue)? onTap}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 18.H),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TripcText(
            context.strings.text_seat_type,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(bottom: 16.H),
          ),
          SizedBox(
            height: 40.H,
            child: ListView.separated(
                itemCount: seatType.length,
                padding: EdgeInsets.only(right: 24.W),
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, _) => SizedBox(
                      width: 10.W,
                    ),
                itemBuilder: (context, index) {
                  return TripcOptionButton(
                      onPressed: () {
                        onTap?.call(seatType[index]);
                      },
                      isActive: selectedSeatType == seatType[index],
                      value: seatType[index].seatType ?? '');
                }),
          ),
        ],
      ),
    );
  }

  Widget _addAndMinusArea({
    required int result,
    required VoidCallback onTapMinus,
    required VoidCallback onTapPlus,
  }) {
    return Row(
      children: [
        TripcMathButton(
            isActive: result > 0,
            onPressed: onTapMinus,
            symbol: MathSymbol.minus),
        TripcText(
          result.toString(),
          fontSize: 16,
          fontWeight: FontWeight.w500,
          textColor: AppAssets.origin().blackColor,
          padding: EdgeInsets.symmetric(horizontal: 20.W),
        ),
        TripcMathButton(
            isActive: true, onPressed: onTapPlus, symbol: MathSymbol.add),
      ],
    );
  }

  Widget _infoToNote(BuildContext context,
      {required TourResponse? selectedTour}) {
    List<String> termsList = selectedTour?.generalTerms
            ?.split('\n')
            .where((term) => term.isNotEmpty)
            .toList() ??
        [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_information_to_note,
          fontSize: 16,
          fontWeight: FontWeight.w500,
          textColor: AppAssets.origin().blackColor,
          padding: EdgeInsets.only(bottom: 12.H),
        ),
        ...termsList.map((term) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TripcText(
                '\u2022',
                fontSize: 10,
                fontWeight: FontWeight.w600,
                textColor: AppAssets.origin().blackColor,
                padding:
                    EdgeInsets.symmetric(horizontal: 5.W).copyWith(top: 3.H),
                height: 1.5,
              ),
              Expanded(
                child: TripcText(
                  html2md.convert(term),
                  fontSize: 14,
                  fontWeight: FontWeight.w300,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().blackColor,
                  height: 1.5,
                ),
              ),
            ],
          );
        }),
      ],
    );
  }
}
