import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_field_with_label.dart';
import '../providers/providers.dart';

class TripcAddPassenger extends ConsumerStatefulWidget {
  const TripcAddPassenger({super.key});

  @override
  ConsumerState<TripcAddPassenger> createState() => _TripcAddPassengerState();
}

class _TripcAddPassengerState extends ConsumerState<TripcAddPassenger> {
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  @override
  void initState() {
    super.initState();
    _fullNameController.addListener(() {
      setState(() {});
    });
    _phoneController.addListener(() {
      setState(() {});
    });
    _emailController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEnableConfirm = ref.watch(pTicketTourProvider.select(
      (value) => value.isButtonConfirmDisable,
    ));
    final errorEmail = ref.watch(
        pTicketTourProvider.select((value) => value.passengerEmailError));
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        onPopScope: () {
          ref.read(pTicketTourProvider.notifier).resetPassengerInfo();
        },
        onLeadingPressed: () {
          Navigator.pop(context);
          ref.read(pTicketTourProvider.notifier).resetPassengerInfo();
        },
        hasBackButton: true,
        resizeToAvoidBottomInset: false,
        titleAppBar: TripcText(
          context.strings.text_add_passenger,
          fontWeight: FontWeight.w600,
          fontSize: 16,
          textColor: AppAssets.origin().black,
        ),
        bottom: PreferredSize(
            preferredSize: const Size(double.infinity, 16),
            child: Divider(
              height: 1,
              color: AppAssets.init.lightGray,
            )),
        actions: [
          TripcText(
            onTap: isEnableConfirm
                ? null
                : () {
                    ref.read(pTicketTourProvider.notifier).addPassenger();
                    ref.read(pTicketTourProvider.notifier).resetPassengerInfo();
                    Navigator.pop(context);
                  },
            context.strings.text_confirm,
            fontWeight: FontWeight.w500,
            fontSize: 14,
            textColor: !isEnableConfirm
                ? AppAssets.origin().black
                : AppAssets.origin().secondDarkGreyTextColor,
            padding: EdgeInsets.only(right: 11.W),
          ),
        ],
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.W, vertical: 18.H),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TripcText(
                context.strings.text_passenger_name_rule,
                fontSize: 12,
                height: 1.5,
                textAlign: TextAlign.start,
                fontWeight: FontWeight.w300,
              ),
              // SizedBox(
              //   height: 8.H,
              // ),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.start,
              //   children: [
              //     TripcIconButton(
              //         onPressed: () {},
              //         child: AppAssets.origin()
              //             .icIOutLine
              //             .widget(height: 16.H, width: 16.H)),
              //     SizedBox(width: 5.W),
              //     TripcText(
              //       context.strings.text_principles_on_passenger_names,
              //       fontSize: 12,
              //       fontWeight: FontWeight.w400,
              //       textColor: AppAssets.origin().darkBlueColor,
              //     ),
              //   ],
              // ),
              SizedBox(
                height: 32.H,
              ),
              TripcTextFieldWithLabel(
                textController: _fullNameController,
                label: context.strings.text_first_name_and_last_name,
                labelColor: AppAssets.origin().blackColor,
                labelFontSize: 12,
                labelFontWeight: FontWeight.w300,
                onChanged:
                    ref.read(pTicketTourProvider.notifier).setPassengerFullName,
                hintText: context.strings.text_enter_fullname,
                hintTextColor: AppAssets.origin().lightGrayDD4,
                textInputAction: TextInputAction.next,
                fontSize: 12,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 13.H, horizontal: 14.W),
              ),
              TripcTextFieldWithLabel(
                textController: _phoneController,
                label: context.strings.text_phone_number,
                labelColor: AppAssets.origin().blackColor,
                onChanged: (value) => ref
                    .read(pTicketTourProvider.notifier)
                    .setPassengerPhoneNumber(value, context),
                labelFontSize: 12,
                labelFontWeight: FontWeight.w300,
                hintText: context.strings.text_enter_contact_phone_num,
                hintTextColor: AppAssets.origin().lightGrayDD4,
                fontSize: 12,
                maxLength: 10,
                keyboardType: TextInputType.number,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 13.H, horizontal: 14.W),
                padding: EdgeInsets.symmetric(vertical: 20.H),
              ),
              TripcTextFieldWithLabel(
                textController: _emailController,
                label: '${context.strings.text_email}:',
                hintText: context.strings.text_enter_email,
                hintTextColor: AppAssets.origin().lightGrayDD4,
                labelColor: AppAssets.origin().blackColor,
                labelFontSize: 12,
                labelFontWeight: FontWeight.w300,
                onChanged: (value) => ref
                    .read(pTicketTourProvider.notifier)
                    .setPassengerEmail(context, value: value),
                errorText: errorEmail,
                keyboardType: TextInputType.emailAddress,
                showErrorSameLine: false,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 13.H, horizontal: 14.W),
                error: errorEmail.isNotEmpty
                    ? TripcText(
                        errorEmail,
                        textAlign: TextAlign.start,
                        enableAutoResize: true,
                        textColor: AppAssets.origin().redDotColor,
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                        padding: EdgeInsets.only(
                          top: 8.H,
                          bottom: 10.H,
                        ),
                      )
                    : null,
              ),
            ],
          ),
        ));
  }
}
