import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/sign_up_request.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/apis/auth/api_user_sign_up.dart';
import 'package:tripc_app/utils/app_validation.dart';

import '../../../utils/app_error_string.dart';

class SignUpPageModel {
  final String email;
  final String password;
  final String fullname;
  final String errorPassword;
  final String errorEmail;
  final bool isLoading;
  final String? errorMessage;
  final String errorFullName;

  static SignUpPageModel getDefault() {
    return SignUpPageModel(
      email: '',
      password: '',
      fullname: '',
      errorPassword: '',
      errorEmail: '',
      errorFullName: '',
      isLoading: false,
    );
  }

  SignUpPageModel(
      {required this.email,
      required this.password,
      required this.fullname,
      this.errorPassword = '',
      this.errorEmail = '',
      this.errorMessage,
      this.errorFullName = '',
      this.isLoading = false});

  SignUpPageModel copyWith(
      {String? email,
      String? password,
      String? fullname,
      String? errorPassword,
      String? errorEmail,
      bool? isLoading,
      String? errorFullName,
      String? errorMessage}) {
    return SignUpPageModel(
      email: email ?? this.email,
      password: password ?? this.password,
      fullname: fullname ?? this.fullname,
      errorPassword: errorPassword ?? this.errorPassword,
      errorEmail: errorEmail ?? this.errorEmail,
      isLoading: isLoading ?? this.isLoading,
      errorFullName: errorFullName ?? this.errorFullName,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  bool get isSignUpDisable {
    return !(errorFullName.isEmpty &&
        errorPassword.isEmpty &&
        errorEmail.isEmpty &&
        password.isNotEmpty &&
        fullname.isNotEmpty &&
        email.isNotEmpty);
  }
}

class TripcSignUpProvider extends StateNotifier<SignUpPageModel> {
  TripcSignUpProvider(super.state);

  final ApiSignUp _api = ApiSignUp();

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void forceLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setPassword(BuildContext context, String password) {
    state = state.copyWith(
      password: password,
      errorPassword:
          ValidationAccount.createPasswordValidation(context, password),
    );
  }

  void setEmail(BuildContext context, String value) {
    state = state.copyWith(
        email: value,
        errorEmail: ValidationAccount.emailValidation(context, value));
  }

  void setFullname(BuildContext context, String value) {
    state = state.copyWith(
      fullname: value,
      errorFullName: ValidationAccount.fullNameValidation(context, value),
    );
  }

  Future<bool> register(BuildContext context) async {
    try {
      forceLoading(true);
      final request = SignUpRequest(
          fullname: state.fullname,
          email: state.email,
          password: state.password);
      final result = await _api.register(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      forceLoading(true);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(
            ErrorParser.getErrorMessage(exceptionMessage.error ?? '', context));
      }
      forceLoading(false);
      return false;
    }
  }

  void resetState() {
    state = SignUpPageModel.getDefault();
  }
}
