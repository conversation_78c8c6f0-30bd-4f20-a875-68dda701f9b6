import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/auth_response/api_login_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/apis/auth/api_user_login.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_validation.dart';

class SignInPageModel {
  final String email;
  final String password;
  final bool isRememberMe;
  final String errorPassword;
  final String errorEmail;
  final bool loading;
  final String? errorMessage;

  static SignInPageModel getDefault() {
    return SignInPageModel(
      email: globalCacheAuth.savedCacheInfo.email,
      password: globalCacheAuth.savedCacheInfo.password,
      isRememberMe: globalCacheAuth.savedCacheInfo.isRemember,
    );
  }

  SignInPageModel({
    required this.email,
    required this.password,
    this.loading = false,
    this.isRememberMe = false,
    this.errorPassword = '',
    this.errorMessage,
    this.errorEmail = '',
  });

  SignInPageModel copyWith({
    String? email,
    String? password,
    bool? loading,
    bool? isRememberMe,
    String? errorPassword,
    String? errorEmail,
    bool? isRemember,
    String? errorMessage,
  }) {
    return SignInPageModel(
      email: email ?? this.email,
      password: password ?? this.password,
      loading: loading ?? this.loading,
      isRememberMe: isRememberMe ?? this.isRememberMe,
      errorPassword: errorPassword ?? this.errorPassword,
      errorEmail: errorEmail ?? this.errorEmail,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  bool get isLoginDisable {
    return !(errorPassword.isEmpty &&
        errorEmail.isEmpty &&
        password.isNotEmpty &&
        email.isNotEmpty);
  }
}

class TripcSignInProvider extends StateNotifier<SignInPageModel> {
  TripcSignInProvider(super.state);

  final ApiUser _api = ApiUser();

  void toogleRememberMe(bool value) {
    state = state.copyWith(isRememberMe: value);
  }

  void forceLoading(bool value) {
    state = state.copyWith(loading: value);
  }

  void setErrorEmail(String value) {
    state = state.copyWith(errorEmail: value);
  }

  void setErrorPassword(String value) {
    state = state.copyWith(errorPassword: value);
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void setToggleRemember() {
    state = state.copyWith(isRememberMe: !state.isRememberMe);
  }

  void setPassword(BuildContext context, String password) {
    state = state.copyWith(
      password: password,
      errorPassword: ValidationAccount.passwordValidation(context, password),
    );
  }

  void setEmail(BuildContext context, {required String value}) {
    state = state.copyWith(
        email: value,
        errorEmail: ValidationAccount.emailValidation(context, value));
  }

  Future<bool> login() async {
    forceLoading(true);
    try {
      forceLoading(true);

      final result = await _api
          .logInWithInputCredential(
            email: state.email,
            password: state.password,
          )
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      forceLoading(false);
      return cacheAccessTokenAndInfo(result);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error); 
      }
      forceLoading(false);
      return false;
    }
  }

  Future<bool> loginSns(
      {required String provider, required String token}) async {
    try {
      forceLoading(true);
      final result = await _api
          .loginSns(
            provider: provider,
            token: token,
          )
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      forceLoading(false);
      return cacheAccessTokenAndInfo(result);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      forceLoading(false);
      return false;
    }
  }

  Future<bool> cacheAccessTokenAndInfo(LoginResponse? result) async {
    if (result?.token != null) {
      await globalCacheAuth.saveAuth(
        newInfo: result!,
      );
      return true;
    } else {
      return false;
    }
  }

  void resetState() {
    if (state.isRememberMe) {
      state = SignInPageModel.getDefault().copyWith(
          email: state.email,
          password: state.password,
          isRememberMe: state.isRememberMe);
      return;
    }
    state = SignInPageModel.getDefault();
  }
}
