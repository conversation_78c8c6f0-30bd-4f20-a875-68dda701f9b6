import 'dart:io';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/login_sns_enum.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/authencation/providers/tripc_sign_up_provider.dart';
import 'package:tripc_app/pages/authencation/views/tripc_sign_in.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/services/sns/facebook_login/facebook_login.dart';
import 'package:tripc_app/services/sns/google_login/google_login.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_field_with_label.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../services/app/app_constants.dart';
import '../../../widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import '../components/components.dart';

final pSignUpPageProvider =
    StateNotifierProvider<TripcSignUpProvider, SignUpPageModel>((ref) {
  final signUpProvider = TripcSignUpProvider(SignUpPageModel.getDefault());
  return signUpProvider;
});

class TripcSignUpPage extends ConsumerStatefulWidget {
  const TripcSignUpPage({super.key});

  @override
  ConsumerState<TripcSignUpPage> createState() => _TripcSignUpPageState();
}

class _TripcSignUpPageState extends ConsumerState<TripcSignUpPage> {
  bool isChecked = false;

  void resetProvider() {
    ref.read(pSignInPageProvider.notifier).resetState();
    ref.read(pSignUpPageProvider.notifier).resetState();
  }

  void _actionAfterSignIn() {
    resetProvider();
    final hasNullPasscode = !(globalCacheAuth.user?.hasPascode ?? false);
    ref.read(pAccountProvider.notifier).getme();
    AppRoute.pushReplacement(context,
        routeName: isFirstTimeLogin ?? true
            ? AppRoute.routeMembershipCode
            : hasNullPasscode
                ? AppRoute.routeMembershipSignIn
                : AppRoute.routeHome,
        arguments: false);
  }

  Future<void> _signUp(BuildContext context, WidgetRef ref) async {
    final result =
        await ref.watch(pSignUpPageProvider.notifier).register(context);
    if (result) {
      final email = ref.read(pSignUpPageProvider).email;
      final password = ref.read(pSignUpPageProvider).password;
      ref.read(pSignInPageProvider.notifier).setEmail(context, value: email);
      ref.read(pSignInPageProvider.notifier).setPassword(context, password);
      Navigator.pop(context);
    } else {
      final errorText = ref.read(pSignUpPageProvider).errorMessage;
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorText));
    }
  }

  Future<void> _logInSNS(
      {required TripCLoginSns type, required String token}) async {
    if (token.isEmpty) return;
    final result = await ref
        .read(pSignInPageProvider.notifier)
        .loginSns(provider: type.providerValue, token: token);
    if (result) {
      _actionAfterSignIn();
    } else {
      final errorText = ref.watch(pSignInPageProvider).errorMessage;
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorText));
    }
  }

  void openDocxInWeb(String rawUrl) async {
    final path = '${AppConstants.googleDocsViewerBaseUrl}$rawUrl';
    final url = Uri.parse(path);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLoading =
        ref.watch(pSignUpPageProvider.select((value) => value.isLoading));
    return Stack(
      children: [
        TripcScaffold(
            titleAppBar: TripcText(
              context.strings.text_create_account,
              fontSize: 28,
              fontWeight: FontWeight.w600,
              textCase: TextCaseType.none,
              padding: EdgeInsets.only(top: 32.H),
            ),
            leading: Padding(
              padding: EdgeInsets.only(top: 32.H),
              child: AppAssets.init.iconArrowleft.widget(
                color: AppAssets.origin().black,
              ),
            ),
            onPopScope: () =>
                ref.read(pSignUpPageProvider.notifier).resetState(),
            onLeadingPressed: () {
              ref.read(pSignUpPageProvider.notifier).resetState();
              Navigator.pop(context);
            },
            toolbarHeight: 73.H,
            needUnFocus: true,
            resizeToAvoidBottomInset: true,
            backgroundColor: AppAssets.origin().whiteBackgroundColor,
            body: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 16.W).copyWith(
                  top: 32.H, bottom: context.mediaQuery.viewPadding.bottom),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildInput(context, ref),
                  _buildCheckbox(),
                  _buildButton(context, ref)
                ],
              ),
            )),
        AppLoading(isRequesting: isLoading)
      ],
    );
  }

  Widget _buildInput(BuildContext context, WidgetRef ref) {
    final errorEmail =
        ref.watch(pSignUpPageProvider.select((value) => value.errorEmail));
    final errorPassword =
        ref.watch(pSignUpPageProvider.select((value) => value.errorPassword));
    final errorFullName =
        ref.watch(pSignUpPageProvider.select((value) => value.errorFullName));
    return Column(children: [
      TripcTextFieldWithLabel(
          onChanged: (value) => ref
              .read(pSignUpPageProvider.notifier)
              .setFullname(context, value),
          label: context.strings.text_first_name_and_last_name,
          hintText: context.strings.text_enter_your_name,
          labelPadding: EdgeInsets.only(left: 8.W),
          error: Visibility(
            visible: errorFullName.isNotEmpty,
            replacement: SizedBox(height: 18.H),
            child: TripcText(
              errorFullName,
              textAlign: TextAlign.start,
              enableAutoResize: true,
              textColor: AppAssets.origin().redDotColor,
              fontSize: 12,
              fontStyle: FontStyle.italic,
              padding: EdgeInsets.only(top: 8.H, bottom: 10.H, left: 8.W),
            ),
          ),
          showErrorSameLine: false,
          errorText: errorFullName),
      TripcTextFieldWithLabel(
        onChanged: (value) =>
            ref.read(pSignUpPageProvider.notifier).setEmail(context, value),
        errorText: errorEmail,
        label: context.strings.text_email,
        hintText: context.strings.text_enter_email,
        error: Visibility(
          visible: errorEmail.isNotEmpty,
          replacement: SizedBox(height: 18.H),
          child: TripcText(
            errorEmail,
            textAlign: TextAlign.start,
            enableAutoResize: true,
            textColor: AppAssets.origin().redDotColor,
            fontSize: 12,
            fontStyle: FontStyle.italic,
            padding: EdgeInsets.only(top: 8.H, bottom: 10.H, left: 8.W),
          ),
        ),
        labelPadding: EdgeInsets.only(left: 8.W),
        showErrorSameLine: false,
      ),
      TripcPasswordTextField(
        value: false,
        errorText: errorPassword,
        error: Visibility(
          visible: errorPassword.isNotEmpty,
          replacement: SizedBox(height: 30.H),
          child: TripcText(
            errorPassword,
            textAlign: TextAlign.start,
            enableAutoResize: true,
            textColor: AppAssets.origin().redDotColor,
            fontSize: 12,
            fontStyle: FontStyle.italic,
            padding: EdgeInsets.only(top: 8.H, bottom: 8.H, left: 8.W),
          ),
        ),
        spaceBottom: 0,
        onChanged: (value) => ref
            .read(pSignUpPageProvider.notifier)
            .setPassword(context, value ?? ''),
      ),
    ]);
  }

  Widget _buildCheckbox() {
    double fontSize = 14.SP;
    const FontWeight fontWeight = FontWeight.w400;
    return Padding(
      padding: EdgeInsets.only(bottom: 24.H),
      child: Row(
        children: [
          Checkbox(
            value: isChecked,
            onChanged: (value) => setState(() => isChecked = value ?? false),
            activeColor: AppAssets.origin().lightBlueFD,
            side: BorderSide(color: AppAssets.origin().secondDarkGreyTextColor),
          ),
          Expanded(
            child: TripcRichText(
              text: '',
              maxLines: 2,
              lineHeight: 1.4,
              textAlign: TextAlign.start,
              children: [
                TextSpan(
                    text: context.strings.i_agree_with,
                    style: TextStyle(
                      fontSize: 14.SP,
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                    )),
                TextSpan(
                    text: context.strings.text_terms_of_use,
                    style: AppAssets.origin().normalTextStyle.copyWith(
                          fontWeight: fontWeight,
                          fontSize: fontSize,
                          color: AppAssets.origin().darkBlueColor,
                          decoration: TextDecoration.underline,
                        ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        openDocxInWeb(AppConstants.tripcTermAndPolicy);
                      }),
                TextSpan(
                    text: ' & ',
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: fontWeight,
                      color: Colors.black,
                    )),
                TextSpan(
                    text: context.strings.text_conditions_and_privacy,
                    style: AppAssets.origin().normalTextStyle.copyWith(
                          fontWeight: fontWeight,
                          fontSize: fontSize,
                          color: AppAssets.origin().darkBlueColor,
                          decoration: TextDecoration.underline,
                        ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        openDocxInWeb(AppConstants.tripcTermAndPolicy);
                      }),
                TextSpan(
                    text: context.strings.of_TripC,
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: fontWeight,
                      color: Colors.black,
                    )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButton(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        TripcButton(
          onPressed: () => _signUp(context, ref),
          isButtonDisabled: !isChecked ||
              ref.watch(pSignUpPageProvider.select(
                (value) => value.isSignUpDisable,
              )),
          title: context.strings.text_sign_up,
        ),
        Padding(
            padding: EdgeInsets.only(bottom: 2.H, top: 24.H),
            child: Stack(fit: StackFit.loose, children: [
              Positioned.fill(
                child: Divider(
                  thickness: 1.0.H,
                  color: AppAssets.init.disableButtonColor,
                ),
              ),
              Align(
                child: Container(
                    color: AppAssets.init.whiteBackgroundColor,
                    padding: EdgeInsets.symmetric(horizontal: 21.W),
                    child: TripcText(
                      context.strings.text_or,
                      textColor: AppAssets.init.secondDarkGreyTextColor,
                      fontWeight: FontWeight.w500,
                    )),
              ),
            ])),
        TripcSnsButton(
          onPressed: () async {
            final result = await GoogleServiceLogin().signIn();
            _logInSNS(type: TripCLoginSns.google, token: result ?? '');
          },
          icon: AppAssets.origin().iconGoogle,
          title: context.strings.text_sign_up_with_google,
        ),
        TripcSnsButton(
            onPressed: () async {
              final String? result;
              if (Platform.isIOS) {
                result = await FacebookServiceLogin().signIn();
              } else {
                result = await FacebookServiceLogin().signInWithAndroid();
              }
              _logInSNS(type: TripCLoginSns.facebook, token: result ?? '');
            },
            icon: AppAssets.origin().iconFacebook,
            title: context.strings.text_sign_up_with_facebook),
        Visibility(
          visible: Platform.isIOS,
          child: TripcSnsButton(
              onPressed: () {},
              iconHeight: 29.63,
              iconWidth: 24,
              icon: AppAssets.origin().iconApple,
              title: context.strings.text_sign_up_with_apple),
        ),
        _signIn()
      ],
    );
  }

  Widget _signIn() {
    return Builder(builder: (context) {
      return Padding(
        padding: EdgeInsets.only(top: 18.H),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            TripcText(
              context.strings.text_do_have_account,
              fontSize: 14,
              fontWeight: FontWeight.w400,
              textCase: TextCaseType.title,
              textColor: AppAssets.origin().secondDarkGreyTextColor,
            ),
            const TripcText(' '),
            TripcText(
              onTap: () => Navigator.pop(context),
              context.strings.text_sign_in,
              textCase: TextCaseType.title,
              fontSize: 16,
              textColor: AppAssets.origin().secondaryColor,
            ),
          ],
        ),
      );
    });
  }
}
