import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/tripc_reset_password.provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_field_with_label.dart';

import '../../../utils/app_error_string.dart';

class TripcForgotPass extends ConsumerWidget {
  const TripcForgotPass({super.key});

  Future<void> _sendOtp(BuildContext context, WidgetRef ref) async {
    final result =
        await ref.read(pResetPasswordProvider.notifier).sendOtp(context);
    if (result) {
      AppRoute.pushNamed(context, routeName: AppRoute.routeTripcVerifyOtp);
    } else {
      final errorText = ref.read(pResetPasswordProvider).errorMessage;
      ref.read(pResetPasswordProvider.notifier).setErrorEmailGetOtp(
          ErrorParser.getErrorMessage(errorText ?? '', context));
      // dialogHelpers.show(context,
      //     child: TripcErrorDialog(errorText: errorText));
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final errorTypingEmail = ref.watch(
        pResetPasswordProvider.select((value) => value.errorEmailGetOtp));
    final isSendOTPDisable = ref.watch(
        pResetPasswordProvider.select((value) => value.isSendOTPDisable));
    final loading =
        ref.watch(pResetPasswordProvider.select((value) => value.loading));
    return Stack(
      children: [
        TripcScaffold(
            onLeadingPressed: () {
              ref.read(pResetPasswordProvider.notifier).resetState();
              Navigator.pop(context);
            },
            onPopScope: () {
              ref.read(pResetPasswordProvider.notifier).resetState();
            },
            toolbarHeight: 73.H,
            leadingWidth: 40,
            leading: Padding(
              padding: EdgeInsets.only(top: 32.H),
              child: AppAssets.init.iconArrowleft.widget(
                color: AppAssets.origin().black,
              ),
            ),
            titleAppBar: TripcText(
              context.strings.text_forgot_password,
              fontSize: 28,
              fontWeight: FontWeight.w600,
              textCase: TextCaseType.none,
              padding: EdgeInsets.only(top: 32.H),
            ),
            needUnFocus: true,
            resizeToAvoidBottomInset: true,
            backgroundColor: AppAssets.origin().whiteBackgroundColor,
            body: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.W),
                child: Column(
                  children: [
                    TripcText(
                      context.strings.text_enter_mail_reset_pass,
                      fontSize: 16,
                      fontWeight: FontWeight.w300,
                      textCase: TextCaseType.none,
                      textAlign: TextAlign.center,
                      height: 1.5,
                      textColor: AppAssets.origin().darkGreyTextColor,
                      padding: EdgeInsets.only(top: 8.H),
                    ),
                    TripcTextFieldWithLabel(
                      onChanged: (value) => ref
                          .read(pResetPasswordProvider.notifier)
                          .setEmailGetOtp(context, value),
                      label: context.strings.text_email,
                      contentPadding: EdgeInsets.symmetric(
                          vertical: 17.H, horizontal: 18.W),
                      hintText: context.strings.text_enter_email,
                      errorText: errorTypingEmail,
                      space: 12,
                      showErrorSameLine: false,
                      padding: EdgeInsets.only(top: 40.H),
                      labelPadding: EdgeInsets.only(left: 8.W),
                      spaceBottom: 40,
                      error: errorTypingEmail.isEmpty
                          ? null
                          : TripcText(
                              errorTypingEmail,
                              textAlign: TextAlign.start,
                              enableAutoResize: true,
                              textColor: AppAssets.origin().redDotColor,
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                              fontWeight: FontWeight.w300,
                              padding: EdgeInsets.only(
                                  top: 8.H,
                                  bottom: 30.H,
                                  left: 8.W,
                                  right: 8.W),
                            ),
                    ),
                    TripcButton(
                      onPressed: () => _sendOtp(context, ref),
                      isButtonDisabled: isSendOTPDisable,
                      title: context.strings.text_send_otp_code,
                      style: const AppButtonStyle(textCase: TextCaseType.none),
                    ),
                  ],
                ),
              ),
            )),
        AppLoading(isRequesting: loading)
      ],
    );
  }
}
