import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/authencation_error_key_enum.dart';
import 'package:tripc_app/models/app/login_sns_enum.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/authencation/authencation.dart';
import 'package:tripc_app/pages/authencation/providers/tripc_sign_in_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/cache/cache_auth.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/services/sns/apple_login/apple_login.dart';
import 'package:tripc_app/services/sns/facebook_login/facebook_login.dart';
import 'package:tripc_app/services/sns/google_login/google_login.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_check_box/tripc_check_box.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_field_with_label.dart';
import '../../../utils/app_log.dart';

final pSignInPageProvider =
    StateNotifierProvider<TripcSignInProvider, SignInPageModel>((ref) {
  final signInProvider = TripcSignInProvider(SignInPageModel.getDefault());
  return signInProvider;
});

class TripcSignInPage extends ConsumerStatefulWidget {
  const TripcSignInPage({super.key});

  @override
  ConsumerState<TripcSignInPage> createState() => _TripcSignInPageState();
}

class _TripcSignInPageState extends ConsumerState<TripcSignInPage> {
  late final TextEditingController _emailController;
  late final TextEditingController _passwordController;

  @override
  void initState() {
    super.initState();
    _emailController =
        TextEditingController(text: ref.read(pSignInPageProvider).email);
    _passwordController =
        TextEditingController(text: ref.read(pSignInPageProvider).password);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listenManual(pSignInPageProvider, (_, state) {
        _emailController.text = state.email;
        _passwordController.text = state.password;
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
    _emailController.dispose();
    _passwordController.dispose();
  }

  void resetProvider() {
    ref.read(pSignInPageProvider.notifier).resetState();
    ref.read(pSignUpPageProvider.notifier).resetState();
  }

  Future<void> _actionAfterSignIn() async {
    resetProvider();
    final hasNullPasscode = !(globalCacheAuth.user?.hasPascode ?? false);
    await ref.read(pAccountProvider.notifier).getme();
    final userInfo = ref.watch(pAccountProvider).user;
    if (hasNullPasscode) {
      AppRoute.pushReplacement(context,
          routeName: (userInfo?.isSkipInviteCodeBool ?? false)
              ? AppRoute.routeMembershipSignIn
              : AppRoute.routeMembershipCode,
          arguments: false);
    } else {
      Navigator.pop(context);
    }
  }

  Future<void> _signIn() async {
    final result = await ref.read(pSignInPageProvider.notifier).login();
    final isRememberMe = ref.read(pSignInPageProvider).isRememberMe;

    if (result) {
      globalCacheAuth.saveCacheInfoUser(
          cacheInfo: AuthSavedInfo(
              email: isRememberMe ? _emailController.text : '',
              password: isRememberMe ? _passwordController.text : '',
              isRemember: isRememberMe));
      _actionAfterSignIn();
    } else {
      final errorText = ref.read(pSignInPageProvider).errorMessage;
      _handleShowError(AuthencationErrorKey.getByValue(errorText ?? ''));
    }
  }

  Future<void> _logInSNS(
      {required TripCLoginSns type, required String token}) async {
    if (token.isEmpty) return;
    final result = await ref
        .read(pSignInPageProvider.notifier)
        .loginSns(provider: type.providerValue, token: token);
    if (result) {
      _actionAfterSignIn();
    } else {
      final errorText = ref.read(pSignInPageProvider).errorMessage;
      _handleShowError(AuthencationErrorKey.getByValue(errorText ?? ''));
    }
  }

  _handleShowError(AuthencationErrorKey key) {
    switch (key) {
      case AuthencationErrorKey.emailNotFound:
        ref
            .read(pSignInPageProvider.notifier)
            .setErrorEmail(key.message(context));
        break;
      case AuthencationErrorKey.wrongPass:
        ref
            .read(pSignInPageProvider.notifier)
            .setErrorPassword(key.message(context));
        break;
      case AuthencationErrorKey.lockLogin:
        final errorText = ref.read(pSignInPageProvider).errorMessage ?? '';
        final timeParts = errorText.split(':');

        String countdownText = '';
        if (timeParts.length > 1) {
          try {
            final seconds = int.parse(timeParts[1]);
            countdownText = seconds.covertResultCountDown2;
          } catch (e) {
            logger.d(e);
          }
        }

        dialogHelpers.show(context,
            child: TripcErrorDialog(
                title: context.strings.text_been_term_locked,
                contentPadding: EdgeInsets.only(
                    top: 24.H, bottom: 19.H, left: 16.74.W, right: 16.74.W),
                errorText: key.message(
                  context,
                ),
                child: SizedBox(
                  width: double.infinity,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TripcText(
                        context.strings
                            .text_warning_lock_sign_in(countdownText),
                        textAlign: TextAlign.start,
                        fontSize: 14,
                        height: 1.4,
                        fontWeight: FontWeight.w400,
                        textColor: AppAssets.origin().black,
                        padding: EdgeInsets.only(top: 18.H, bottom: 24.H),
                      )
                    ],
                  ),
                )));
        break;
      case AuthencationErrorKey.inactiveAccount:
        dialogHelpers.show(context,
            child: TripcErrorDialog(errorText: key.message(context)));
        break;
      case AuthencationErrorKey.noneError:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLoading =
        ref.watch(pSignInPageProvider.select((value) => value.loading));
    final errorEmail =
        ref.watch(pSignInPageProvider.select((value) => value.errorEmail));
    final errorPassword =
        ref.watch(pSignInPageProvider.select((value) => value.errorPassword));
    return Stack(
      children: [
        TripcScaffold(
            visibleAppBar: true,
            canPop: false,
            needUnFocus: true,
            hasBackButton: true,
            resizeToAvoidBottomInset: false,
            leadingColor: Colors.white,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _buildHeader(context),
                Expanded(
                    child: _buildSignInArea(context,
                        errorEmail: errorEmail, errorPassword: errorPassword))
              ],
            )),
        AppLoading(isRequesting: isLoading),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Padding(
        padding: EdgeInsets.only(top: 32.H, bottom: 35.H),
        child: Column(children: [
          TripcText(
            context.strings.text_welcome_to_tripC,
            fontSize: 28,
            fontWeight: FontWeight.w600,
            textCase: TextCaseType.none,
            textColor: AppAssets.origin().whiteBackgroundColor,
          ),
          TripcText(
            context.strings.text_pls_enter_details,
            textCase: TextCaseType.none,
            textColor: AppAssets.origin().whiteBackgroundColor,
            padding: EdgeInsets.only(top: 20.H),
          )
        ]),
      ),
    );
  }

  Widget _buildSignInArea(BuildContext context,
      {String? errorEmail, String? errorPassword}) {
    return Container(
        padding: EdgeInsets.symmetric(vertical: 24.H, horizontal: 16.W)
            .copyWith(bottom: context.mediaQuery.viewPadding.bottom),
        decoration: BoxDecoration(
          color: AppAssets.origin().whiteBackgroundColor,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24.SP),
              topRight: Radius.circular(24.SP)),
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              TripcTextFieldWithLabel(
                  textController: _emailController,
                  onChanged: (value) => ref
                      .watch(pSignInPageProvider.notifier)
                      .setEmail(context, value: value),
                  label: context.strings.text_email,
                  hintText: context.strings.text_enter_email,
                  errorText: errorEmail,
                  space: 12,
                  spaceBottom: 18,
                  showErrorSameLine: false,
                  labelPadding: EdgeInsets.only(left: 8.W),
                  error: (errorEmail ?? '').isNotEmpty
                      ? TripcText(
                          errorEmail,
                          textAlign: TextAlign.start,
                          enableAutoResize: true,
                          textColor: AppAssets.origin().redDotColor,
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                          padding: EdgeInsets.only(
                              top: 8.H, bottom: 10.H, left: 8.W),
                        )
                      : null),
              TripcPasswordTextField(
                controller: _passwordController,
                value: true,
                errorText: errorPassword,
                spaceBottom: 18,
                error: (errorPassword ?? '').isNotEmpty
                    ? TripcText(
                        errorPassword,
                        textAlign: TextAlign.start,
                        enableAutoResize: true,
                        textColor: AppAssets.origin().redDotColor,
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                        padding:
                            EdgeInsets.only(top: 8.H, bottom: 10.H, left: 8.W),
                      )
                    : null,
                onChanged: (value) => ref
                    .watch(pSignInPageProvider.notifier)
                    .setPassword(context, value ?? ''),
              ),
              _buildRemember(context),
              TripcButton(
                isButtonDisabled: ref.watch(pSignInPageProvider
                    .select((value) => value.isLoginDisable)),
                onPressed: _signIn,
                title: context.strings.text_sign_in,
                margin: EdgeInsets.only(top: 30.H),
              ),
              Padding(
                  padding: EdgeInsets.only(top: 24.H, bottom: 2.H),
                  child: Stack(fit: StackFit.loose, children: [
                    Positioned.fill(
                      child: Divider(
                        thickness: 1.0.H,
                        color: AppAssets.init.disableButtonColor,
                      ),
                    ),
                    Align(
                      child: Container(
                          color: AppAssets.init.whiteBackgroundColor,
                          padding: EdgeInsets.symmetric(horizontal: 21.W),
                          child: TripcText(
                            context.strings.text_or,
                            textColor: AppAssets.init.secondDarkGreyTextColor,
                            fontWeight: FontWeight.w500,
                          )),
                    ),
                  ])),
              TripcSnsButton(
                onPressed: () async {
                  final result = await GoogleServiceLogin().signIn();
                  _logInSNS(type: TripCLoginSns.google, token: result ?? '');
                },
                icon: AppAssets.origin().iconGoogle,
                title: context.strings.text_continue_with_google,
              ),
              TripcSnsButton(
                  onPressed: () async {
                    final String? result;
                    if (Platform.isIOS) {
                      result = await FacebookServiceLogin().signIn();
                    } else {
                      result = await FacebookServiceLogin().signInWithAndroid();
                    }
                    _logInSNS(
                        type: TripCLoginSns.facebook, token: result ?? '');
                  },
                  icon: AppAssets.origin().iconFacebook,
                  title: context.strings.text_continue_with_facebook),
              Visibility(
                visible: Platform.isIOS,
                child: TripcSnsButton(
                    onPressed: () async {
                      final result = await AppleServiceLogin().signIn();
                      _logInSNS(type: TripCLoginSns.apple, token: result ?? '');
                    },
                    iconHeight: 29.63,
                    iconWidth: 24,
                    icon: AppAssets.origin().iconApple,
                    title: context.strings.text_continue_with_apple),
              ),
              _signUp()
            ],
          ),
        ));
  }

  Widget _buildRemember(BuildContext context) {
    final isRememberMe =
        ref.watch(pSignInPageProvider.select((value) => value.isRememberMe));
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            TripcCheckbox(
              value: isRememberMe,
              onChanged: (value) => ref
                  .read(pSignInPageProvider.notifier)
                  .toogleRememberMe(value),
              size: 16,
              type: CheckBoxType.circular,
            ),
            SizedBox(width: 8.W),
            TripcText(
              context.strings.text_remember_me,
              textCase: TextCaseType.title,
              fontSize: 12,
              textColor: AppAssets.origin().darkGreyTextColor,
            )
          ],
        ),
        TripcText(
          onTap: () =>
              AppRoute.pushNamed(context, routeName: AppRoute.routeForgotPass),
          context.strings.text_forgot_password,
          textCase: TextCaseType.none,
          fontSize: 12,
          textColor: AppAssets.origin().secondaryColor,
        ),
      ],
    );
  }

  Widget _signUp() {
    return Builder(builder: (context) {
      return Padding(
        padding: EdgeInsets.only(top: 18.H),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TripcText(
              context.strings.text_dont_have_account,
              fontSize: 14,
              fontWeight: FontWeight.w400,
              textCase: TextCaseType.title,
              textColor: AppAssets.origin().secondDarkGreyTextColor,
            ),
            const TripcText(' '),
            TripcText(
              onTap: () =>
                  AppRoute.pushNamed(context, routeName: AppRoute.routeSignUp),
              context.strings.text_sign_up,
              textCase: TextCaseType.title,
              fontSize: 16,
              // decoration: TextDecoration.underline,
              textColor: AppAssets.origin().secondaryColor,
            ),
          ],
        ),
      );
    });
  }
}
