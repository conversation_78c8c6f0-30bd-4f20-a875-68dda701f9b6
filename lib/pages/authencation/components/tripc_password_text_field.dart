import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_field_with_label.dart';

class TripcPasswordTextField extends StatefulWidget {
  const TripcPasswordTextField(
      {super.key,
      required this.value,
      this.onChanged,
      this.errorText,
      this.spaceBottom = 0,
      this.error,
      this.controller});
  final bool value;
  final Function(String?)? onChanged;
  final String? errorText;
  final TextEditingController? controller;
  final double spaceBottom;
  final Widget? error;

  @override
  State<TripcPasswordTextField> createState() => _TripcPasswordTextFieldState();
}

class _TripcPasswordTextFieldState extends State<TripcPasswordTextField> {
  late bool _value;
  @override
  void initState() {
    super.initState();
    _value = widget.value;
  }

  @override
  Widget build(BuildContext context) {
    return TripcTextFieldWithLabel(
      textController: widget.controller,
      onChanged: widget.onChanged,
      space: 12,
      labelPadding: EdgeInsets.only(left: 8.W),
      errorText: widget.errorText,
      obscureText: _value,
      suffix: TripcIconButton(
          onPressed: () {
            setState(() {
              _value = !_value;
            });
          },
          child: Padding(
            padding: EdgeInsets.only(right: 24.W),
            child: AppAssets.init.iconEye.widget(
                height: 24.H,
                width: 24.H,
                color: _value
                    ? AppAssets.origin().secondaryColor
                    : AppAssets.origin().lightBlueColor),
          )),
      label: context.strings.text_password,
      hintText: context.strings.text_enter_your_password,
      spaceBottom: widget.spaceBottom,
      error: widget.error,
      showErrorSameLine: false,
    );
  }
}
