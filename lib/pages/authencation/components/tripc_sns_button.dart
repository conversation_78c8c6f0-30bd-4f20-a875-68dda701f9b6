import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcSnsButton extends StatelessWidget {
  const TripcSnsButton(
      {super.key,
      this.iconHeight = 24,
      this.iconWidth = 24,
      required this.icon,
      required this.title,
      this.onPressed});
  final double iconWidth;
  final double iconHeight;
  final AppAssetBuilder icon;
  final String title;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return TripcButton(
      onPressed: onPressed,
      buttonType: ButtonType.outline,
      titleBuilder: (title) => Padding(
        padding: EdgeInsets.only(left: 66.W),
        child: Row(
          children: [
            icon.widget(
              height: iconHeight.H,
              width: iconWidth.H,
            ),
            <PERSON><PERSON><PERSON><PERSON>(width: 22.W),
            TripcText(
              ignorePointer: true,
              title,
              textCase: TextCaseType.title,
              fontWeight: FontWeight.w500,
            ),
          ],
        ),
      ),
      title: title,
      margin: EdgeInsets.only(top: 16.H),
    );
  }
}
