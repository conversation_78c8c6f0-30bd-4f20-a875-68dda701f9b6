import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class NoInternetScreen extends StatelessWidget {
  const NoInternetScreen({super.key});

  Future<bool> _checkNoInternet() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult.contains(ConnectivityResult.none);
  }

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
      backgroundColor: AppAssets.origin().whiteBackgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppAssets.init.imNoInternet.widget(
              width: 218.W,
              height: 190.H,
              fit: BoxFit.cover,
            ),
            Sized<PERSON><PERSON>(height: 37.H),
            TripcText(context.strings.text_no_internet,
            fontSize: 16,
            fontWeight: FontWeight.w300,
            textColor: Colors.black),
            SizedBox(height: 37.H),
            TripcButton(
              title: context.strings.text_try_again,
              onPressed: () async {
                final isNoInternet = await _checkNoInternet();
                if (isNoInternet) {
                  return;
                }
                Navigator.pop(context, true);
              },
              height: 50.H,
              width: 165.W,
              style: AppButtonStyle(
                textColor: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                radius: 12,
                backgroundColor: AppAssets.init.tabbarEnableColor,
              ),
            ),
            SizedBox(height: 100.H,),
          ],
        ),
      ),
    );
  }
}
