import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/stay/providers/trip_c_stay_search_detail_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_search/tripc_search.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../components/search_gradient_background.dart';

class TripCSearchDetailScreen extends ConsumerStatefulWidget {
  const TripCSearchDetailScreen({super.key});

  @override
  ConsumerState<TripCSearchDetailScreen> createState() =>
      _TripCSearchDetailScreenState();
}

class _TripCSearchDetailScreenState
    extends ConsumerState<TripCSearchDetailScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SearchGradientBackground(
        child: _mainView(),
      ),
    );
  }

  Widget _mainView() {
    final state = ref.watch(pStaySearchDetailProvider);
    final notifier = ref.read(pStaySearchDetailProvider.notifier);
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15.W),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              alignment: Alignment.topCenter,
              margin: EdgeInsets.only(bottom: 5.H, top: 15.H),
              height: 40.H,
              child: TripcSearch(
                fillColor: AppAssets.origin().whiteBackgroundColor,
                fontWeight: FontWeight.w400,
                hintText: context.strings.enter_place,
                controller: _searchController,
                onChanged: (e) {},
                keyword: '',
                onSubmit: () {},
                keyboardType: TextInputType.text,
                contentPadding: EdgeInsets.zero,
                prefixIcon: GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: const Icon(Icons.keyboard_arrow_left)),
                iconSuffix: const Icon(Icons.close),
                radius: 12.SP,
                suffixIconColor: AppAssets.origin().secondDarkGreyTextColor,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 8.W, top: 16.H),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                spacing: 6.W,
                children: [
                  AppAssets.origin().icMyLocation.widget(),
                  TripcText(
                    context.strings.recent_searches,
                    textColor: AppAssets.origin().whiteBackgroundColor,
                    fontSize: 14.SP,
                    height: 1.42,
                  ),
                ],
              ),
            ),
            TripcText(
              context.strings.recent_searches,
              textColor: AppAssets.origin().colorTextFoodName,
              fontSize: 16.SP,
              height: 1.375,
              fontWeight: FontWeight.w600,
              padding: EdgeInsets.symmetric(vertical: 16.H),
            ),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                final searchHotel = state.listSearchHotels[index];
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.H),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 8.H,
                    children: [
                      TripcText(
                        searchHotel.name,
                        textColor: AppAssets.origin().hotelSearchNameColor,
                        fontSize: 14.SP,
                        height: 1.42,
                        fontWeight: FontWeight.w500,
                      ),
                      TripcText(
                        searchHotel.address,
                        textColor: AppAssets.origin().hotelSearchAddressColor,
                        fontSize: 12.SP,
                        height: 1.33,
                        fontWeight: FontWeight.w400,
                      ),
                    ],
                  ),
                );
              },
              separatorBuilder: (context, index) => Container(
                height: 1.H,
                color: AppAssets.origin().grayC4C8,
              ),
              itemCount: state.listSearchHotels.length,
            )
          ],
        ),
      ),
    );
  }
}
