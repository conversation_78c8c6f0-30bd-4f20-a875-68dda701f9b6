import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/stay/components/stay_refund_item.dart';
import 'package:tripc_app/pages/stay/providers/trip_c_stay_refund_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../components/search_gradient_background.dart';
import '../components/upload_evidence_bottom_sheet.dart';

class TripCStayRefundScreen extends ConsumerStatefulWidget {
  const TripCStayRefundScreen({super.key});

  @override
  ConsumerState<TripCStayRefundScreen> createState() =>
      _TripCSearchDetailScreenState();
}

class _TripCSearchDetailScreenState
    extends ConsumerState<TripCStayRefundScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SearchGradientBackground(
        child: _mainView(),
      ),
    );
  }

  Widget _mainView() {
    final state = ref.watch(pStayRefundProvider);
    final notifier = ref.read(pStayRefundProvider.notifier);
    return SafeArea(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.W),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 16.H,
            children: [
              _appBar(),
              SizedBox(
                height: 10.H,
              ),
              _informationRefundCard(),
              TripcText(
                context.strings.refund_history,
                textColor: AppAssets.origin().colorTextFoodName,
                fontSize: 18.SP,
                height: 1.33,
                fontWeight: FontWeight.w500,
              ),
              ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    final refundStay = state.listRefunds[index];
                    return StayRefundItem(refundStay);
                  },
                  separatorBuilder: (_, __) => SizedBox(
                        height: 16.H,
                      ),
                  itemCount: state.listRefunds.length,
              ),
              SizedBox(
                height: 20.H,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _appBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
          onTap: () => Navigator.pop(context),
          child: AppAssets.init.iconArrowleft.widget(
            color: AppAssets.origin().whiteBackgroundColor,
          ),
        ),
        TripcText(
          context.strings.refund,
          fontWeight: FontWeight.w600,
          textColor: AppAssets.origin().whiteBackgroundColor,
        ),
        GestureDetector(
          onTap: () => showUploadEvidenceBottomSheet(context),
          child: AppAssets.init.icUploadEvidence.widget(
            color: AppAssets.origin().whiteBackgroundColor,
          ),
        ),
      ],
    );
  }

  Widget _informationRefundCard() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16.SP),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(12.W),
            decoration:
                BoxDecoration(color: AppAssets.origin().refundCardColor),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.only(bottom: 10.H),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: AppAssets.origin().blue191,
                        width: 0.5.W,
                      ),
                    ),
                  ),
                  child: Row(
                    spacing: 3.W,
                    children: [
                      TripcText(
                        '${context.strings.total_refund_amount}:',
                        textColor: AppAssets.origin().blue191,
                        fontSize: 12.SP,
                        height: 1.33,
                      ),
                      TripcText(
                        // TODO: Fill data for this
                        123456075.vnd,
                        textColor: AppAssets.origin().blue191,
                        fontSize: 16.SP,
                        fontWeight: FontWeight.w600,
                        height: 1.375,
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.H),
                  child: Row(
                    children: [
                      // TODO: Fill data for this
                      Expanded(
                        child:
                            _priceCard(context.strings.withdrawn_amount, 0.vnd),
                      ),
                      Container(
                        width: 0.5.W,
                        height: 44.H,
                        color: AppAssets.origin().blue191,
                      ),
                      // TODO: Fill data for this
                      Expanded(
                        child:
                            _priceCard(context.strings.text_processing, 0.vnd),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(12.W),
            decoration: BoxDecoration(color: AppAssets.origin().darkBlueColor),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 2.H,
                  children: [
                    TripcText(
                      context.strings.amount_available_for_withdrawal,
                      textColor: AppAssets.origin().whiteBackgroundColor,
                      fontSize: 12.SP,
                      height: 1.33,
                    ),
                    // TODO: Fill data for this
                    TripcText(
                      123456075.vnd,
                      textColor: AppAssets.origin().whiteBackgroundColor,
                      fontSize: 16.SP,
                      fontWeight: FontWeight.w600,
                      height: 1.375,
                    ),
                  ],
                ),
                GestureDetector(
                  onTap: () {
                    // TODO: Handle event for draw button click
                  },
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.W, vertical: 6.H),
                    decoration: BoxDecoration(
                      color: AppAssets.origin().whiteBackgroundColor,
                      borderRadius: BorderRadius.circular(8.SP),
                    ),
                    child: TripcText(
                      context.strings.withdraw_money,
                      textColor: AppAssets.origin().textForLocationTourV2,
                      fontSize: 14.SP,
                      fontWeight: FontWeight.w500,
                      height: 1.42,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _priceCard(String title, String price) {
    return Column(
      spacing: 4.H,
      children: [
        TripcText(
          title,
          textColor: AppAssets.origin().blue191,
          fontSize: 12.SP,
          height: 1.33,
        ),
        TripcText(
          price,
          textColor: AppAssets.origin().blue191,
          fontSize: 16.SP,
          fontWeight: FontWeight.w600,
          height: 1.375,
        ),
      ],
    );
  }
}
