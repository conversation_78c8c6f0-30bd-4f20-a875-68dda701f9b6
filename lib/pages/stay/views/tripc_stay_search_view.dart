import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/app/app_route.dart';

import '../../../services/app/app_assets.dart';
import '../../../utils/app_extension.dart';
import '../../../widgets/commons/tripc_button/tripc_icon_button.dart';
import '../../../widgets/commons/tripc_scafford/tripc_scafford.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';
import '../components/dropdown_search_bar.dart';
import '../components/stay_filter_category_scroll_tabs.dart';
import '../components/tripc_stay_item.dart';
import '../providers/tripc_stay_search_provider.dart';

class TripcStaySearchView extends ConsumerStatefulWidget {
  const TripcStaySearchView({super.key});

  @override
  ConsumerState<TripcStaySearchView> createState() => _TripcStayViewState();
}

class _TripcStayViewState extends ConsumerState<TripcStaySearchView> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(pStaySearchProvider);
    final notifier = ref.read(pStaySearchProvider.notifier);

    return TripcScaffold(
      onPressed: () => unfocusKeyboard(),
      extendBodyBehindAppBar: true,
      hasBackButton: true,
      visibleAppBar: true,
      hasAppbarBottomLine: false,
      backgroundColor: AppAssets.origin().whiteSmokeColor,
      leading: AppAssets.init.iconArrowleft.widget(
        color: AppAssets.origin().whiteBackgroundColor,
      ),
      titleAppBar: TripcText(
        context.strings.text_stay,
        fontSize: 16,
        fontWeight: FontWeight.w600,
        textAlign: TextAlign.start,
        textColor: AppAssets.origin().whiteBackgroundColor,
      ),
      actions: [
        TripcIconButton(
          onPressed: () {
            AppRoute.pushNamed(context,
                routeName: AppRoute.routeStayRefund);
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.W),
            child: AppAssets.origin()
                .icListBoard
                .widget(height: 24.H, width: 24.H),
          ),
        )
      ],
      body: Stack(
        children: [
          //banner
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SizedBox(
              height: 198.H,
              child: AppAssets.origin().imStayBanner.widget(
                  height: 198.H, width: double.infinity, fit: BoxFit.fitWidth),
            ),
          ),
          Positioned(
              top: 180.H,
              left: 0,
              right: 0,
              bottom: 0,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //search field
                  DropdownSearchBar(
                    selectedLocation: state.selectedLocation,
                    locations: state.locations,
                    onlocationSelected: (value) {
                      notifier.setSelectedLocation(value);
                      //TODO: change list hotels data
                    },
                    onTapSearch: () {
                      AppRoute.pushNamed(context,
                          routeName: AppRoute.routeStaySearch);
                    },
                  ),

                  //title
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.W)
                        .copyWith(top: 16.H, bottom: 8.H),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TripcText(
                          context.strings.featured_accommodation,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                        AppAssets.origin()
                            .icArrange
                            .widget(height: 24.H, width: 24.H),
                      ],
                    ),
                  ),

                  //list categories
                  StayFilterCategoryScrollTabs(
                    categories: state.categories,
                    selected: state.selectedCategory,
                    onSelected: (value) {
                      notifier.setSelectedCategory(value);
                      //TODO: call api get list hotels
                    },
                  ),
                  SizedBox(
                    height: 16.H,
                  ),

                  Expanded(
                    child: ListView.separated(
                      padding: EdgeInsets.symmetric(horizontal: 16.W)
                          .copyWith(bottom: 16.H),
                      itemCount: state.hotels.length,
                      separatorBuilder: (_, __) => SizedBox(height: 16.H),
                      itemBuilder: (context, index) {
                        final hotel = state.hotels[index];
                        return TripcStayItem(
                          stay: hotel,
                        );
                      },
                    ),
                  ),
                ],
              ))
        ],
      ),
    );
  }
}
