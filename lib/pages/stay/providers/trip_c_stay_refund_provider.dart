import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/stay_response.dart/refund_model.dart';
import '../../../models/remote/stay_response.dart/stay_response.dart';

class StayRefundModel {
  final List<RefundModel> listRefunds;

  final bool isLoading;

  const StayRefundModel({
    this.listRefunds = const [],
    this.isLoading = false,
  });

  static StayRefundModel getDefault() {
    return StayRefundModel(
      listRefunds: [
        RefundModel(
            id: 1,
            name: 'Agoda',
            image:
                'https://images.seeklogo.com/logo-png/37/1/agoda-logo-png_seeklogo-371025.png',
            amount: 1500000,
            returnAmount: 120000,
            statusCode: '01',
            estimateTime: DateTime.now(),
        ),
        RefundModel(
          id: 2,
          name: 'Agoda',
          image:
              'https://images.seeklogo.com/logo-png/37/1/agoda-logo-png_seeklogo-371025.png',
          amount: 1500000,
          returnAmount: 120000,
          statusCode: '02',
          estimateTime: DateTime.now(),
        ),
        RefundModel(
          id: 3,
          name: 'Agoda',
          image:
              'https://images.seeklogo.com/logo-png/37/1/agoda-logo-png_seeklogo-371025.png',
          amount: 1500000,
          returnAmount: 120000,
          statusCode: '03',
          estimateTime: DateTime.now(),
        ),
        RefundModel(
          id: 4,
          name: 'Agoda',
          image:
              'https://images.seeklogo.com/logo-png/37/1/agoda-logo-png_seeklogo-371025.png',
          amount: 1500000,
          returnAmount: 120000,
          statusCode: '04',
          estimateTime: DateTime.now(),
        )
      ],
      isLoading: false,
    );
  }

  StayRefundModel copyWith({
    List<RefundModel>? listRefunds,
    bool? isLoading,
  }) {
    return StayRefundModel(
      listRefunds: listRefunds ?? this.listRefunds,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class StayRefundProvider extends StateNotifier<StayRefundModel> {
  StayRefundProvider(super._state);

  void resetState() {
    state = StayRefundModel.getDefault();
  }

  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }
}

final pStayRefundProvider =
    StateNotifierProvider<StayRefundProvider, StayRefundModel>(
        (ref) => StayRefundProvider(StayRefundModel.getDefault()));
