import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/remote/stay_response.dart/stay_response.dart';

class StaySearchDetailsModel {

  final List<StayResponse> listSearchHotels;

  final bool isLoading;

  const StaySearchDetailsModel({
    this.listSearchHotels = const [],
    this.isLoading = false,
  });

  static StaySearchDetailsModel getDefault() {
    return const StaySearchDetailsModel(
      listSearchHotels: [
        StayResponse(
            id: 1,
            name: 'The Da Nang Oceanview',
            rating: 4.4,
            address:
            'Thành phố Đà Nẵng',
            price: 1500000,
            image:
            'https://lp-cms-production.imgix.net/2024-12/GettyRF178407725.jpg'),
        StayResponse(
            id: 1,
            name: 'The Da Nang Oceanview',
            rating: 4.4,
            address:
            'Thành phố Đà Nẵng',
            price: 1500000,
            image:
            'https://lp-cms-production.imgix.net/2024-12/GettyRF178407725.jpg'),
        StayResponse(
            id: 1,
            name: 'The Da Nang Oceanview',
            rating: 4.4,
            address:
            'Thành phố Đà Nẵng',
            price: 1500000,
            image:
            'https://lp-cms-production.imgix.net/2024-12/GettyRF178407725.jpg'),
        StayResponse(
            id: 1,
            name: 'The Da Nang Oceanview',
            rating: 4.4,
            address:
            'Thành phố Đà Nẵng',
            price: 1500000,
            image:
            'https://lp-cms-production.imgix.net/2024-12/GettyRF178407725.jpg'),
        StayResponse(
            id: 1,
            name: 'The Da Nang Oceanview',
            rating: 4.4,
            address:
            'Thành phố Đà Nẵng',
            price: 1500000,
            image:
            'https://lp-cms-production.imgix.net/2024-12/GettyRF178407725.jpg'),
      ],
      isLoading: false,
    );
  }

  StaySearchDetailsModel copyWith({
    List<StayResponse>? listSearchHotels,
    bool? isLoading,
  }) {
    return StaySearchDetailsModel(
      listSearchHotels: listSearchHotels ?? this.listSearchHotels,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class StaySearchDetailProvider extends StateNotifier<StaySearchDetailsModel> {
  StaySearchDetailProvider(super._state);

  void resetState() {
    state = StaySearchDetailsModel.getDefault();
  }

  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }
}

final pStaySearchDetailProvider =
StateNotifierProvider<StaySearchDetailProvider, StaySearchDetailsModel>(
        (ref) => StaySearchDetailProvider(StaySearchDetailsModel.getDefault()));
