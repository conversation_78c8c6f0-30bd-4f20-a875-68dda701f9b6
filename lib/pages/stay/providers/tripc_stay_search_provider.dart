import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../models/remote/stay_response.dart/stay_categories_response.dart';
import '../../../models/remote/stay_response.dart/stay_location_response.dart';
import '../../../models/remote/stay_response.dart/stay_response.dart';

class StaySearchModel {
  final List<StayLocationResponse> locations;
  final StayLocationResponse? selectedLocation;

  final List<StayCategoriesResponse> categories;
  final StayCategoriesResponse? selectedCategory;

  final List<StayResponse> hotels;

  final bool isLoading;

  const StaySearchModel({
    this.locations = const [],
    this.selectedLocation,
    this.categories = const [],
    this.selectedCategory,
    this.hotels = const [],
    this.isLoading = false,
  });

  static StaySearchModel getDefault() {
    return const StaySearchModel(
      locations: [
        StayLocationResponse(slug: 'da-nang', name: 'Đà Nẵng'),
        StayLocationResponse(slug: 'ha-noi', name: '<PERSON><PERSON>ội'),
        StayLocationResponse(slug: 'ho-chi-minh', name: 'TP.HCM'),
      ],
      selectedLocation: StayLocationResponse(slug: 'da-nang', name: 'Đà Nẵng'),
      categories: [
        StayCategoriesResponse(id: 0, slug: 'all', name: 'Tất cả'),
        StayCategoriesResponse(id: 1, slug: 'resort', name: 'Resort'),
        StayCategoriesResponse(id: 2, slug: 'hotel', name: 'Khách sạn'),
        StayCategoriesResponse(id: 3, slug: 'homestay', name: 'Homestay'),
        StayCategoriesResponse(id: 4, slug: 'villa', name: 'Villa'),
      ],
      selectedCategory:
          StayCategoriesResponse(id: 0, slug: 'all', name: 'Tất cả'),
      hotels: [
        StayResponse(
            id: 1,
            name: 'The Da Nang Oceanview',
            rating: 4.4,
            address:
                '22 Trần Hưng Đạo, Phường Mỹ An, Quận Ngũ Hành Sơn, Thành phố Đà Nẵng',
            price: 1500000,
            image:
                'https://lp-cms-production.imgix.net/2024-12/GettyRF178407725.jpg'),
        StayResponse(
            id: 1,
            name: 'The Da Nang Oceanview',
            rating: 4.4,
            address:
                '22 Trần Hưng Đạo, Phường Mỹ An, Quận Ngũ Hành Sơn, Thành phố Đà Nẵng',
            price: 1500000,
            image:
                'https://lp-cms-production.imgix.net/2024-12/GettyRF178407725.jpg'),
        StayResponse(
            id: 1,
            name: 'The Da Nang Oceanview',
            rating: 4.4,
            address:
                '22 Trần Hưng Đạo, Phường Mỹ An, Quận Ngũ Hành Sơn, Thành phố Đà Nẵng',
            price: 1500000,
            image:
                'https://lp-cms-production.imgix.net/2024-12/GettyRF178407725.jpg'),
        StayResponse(
            id: 1,
            name: 'The Da Nang Oceanview',
            rating: 4.4,
            address:
                '22 Trần Hưng Đạo, Phường Mỹ An, Quận Ngũ Hành Sơn, Thành phố Đà Nẵng',
            price: 1500000,
            image:
                'https://lp-cms-production.imgix.net/2024-12/GettyRF178407725.jpg'),
        StayResponse(
            id: 1,
            name: 'The Da Nang Oceanview',
            rating: 4.4,
            address:
                '22 Trần Hưng Đạo, Phường Mỹ An, Quận Ngũ Hành Sơn, Thành phố Đà Nẵng',
            price: 1500000,
            image:
                'https://lp-cms-production.imgix.net/2024-12/GettyRF178407725.jpg'),
      ],
      isLoading: false,
    );
  }

  StaySearchModel copyWith({
    List<StayLocationResponse>? locations,
    StayLocationResponse? selectedLocation,
    List<StayCategoriesResponse>? categories,
    StayCategoriesResponse? selectedCategory,
    List<StayResponse>? hotels,
    bool? isLoading,
  }) {
    return StaySearchModel(
      locations: locations ?? this.locations,
      selectedLocation: selectedLocation ?? this.selectedLocation,
      categories: categories ?? this.categories,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      hotels: hotels ?? this.hotels,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class StaySearchProvider extends StateNotifier<StaySearchModel> {
  StaySearchProvider(super._state);

  void resetState() {
    state = StaySearchModel.getDefault();
  }

  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setSelectedLocation(StayLocationResponse location) {
    state = state.copyWith(selectedLocation: location);
  }

  void setSelectedCategory(StayCategoriesResponse category) {
    state = state.copyWith(selectedCategory: category);
  }
}

final pStaySearchProvider =
    StateNotifierProvider<StaySearchProvider, StaySearchModel>(
        (ref) => StaySearchProvider(StaySearchModel.getDefault()));
