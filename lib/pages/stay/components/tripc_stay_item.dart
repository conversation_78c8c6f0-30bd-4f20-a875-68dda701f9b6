import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

import '../../../models/remote/stay_response.dart/stay_response.dart';
import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/base_cached_network_image.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';

class TripcStayItem extends StatelessWidget {
  const TripcStayItem({super.key, required this.stay});
  final StayResponse stay;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 128.H,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          ClipRRect(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.SP),
                  bottomLeft: Radius.circular(12.SP)),
              child: BaseCachedNetworkImage(
                imageUrl: stay.image,
                height: 128.H,
                width: 88.W,
                placeholder: (context, _) => Container(
                  color: AppAssets.origin().lightGrayDD4,
                ),
                errorWidget: (context, error, stackTrace) =>
                    AppAssets.origin().icErrorImg.widget(
                          color: context.appCustomPallet.buttonBG,
                        ),
                fit: BoxFit.fitHeight,
              )),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                  top: 12.H, left: 8.H, right: 12.W, bottom: 12.W),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: TripcText(
                              stay.name,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              textColor: AppAssets.origin().colorTextFoodName,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.start,
                            ),
                          ),
                          AppAssets.origin().icHeart.widget(
                              height: 20.W,
                              width: 20.W,
                              color: AppAssets.origin().gray474)
                        ],
                      ),
                      Row(
                        children: [
                          AppAssets.origin().icStarV2.widget(),
                          TripcText(
                            stay.rating.toString(),
                            fontWeight: FontWeight.w300,
                            fontSize: 14,
                            textColor: AppAssets.origin().darkYellow,
                            padding: EdgeInsets.symmetric(
                              horizontal: 4.W,
                            ),
                          ),
                          TripcText('(Rất tốt)',
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              textColor: AppAssets.origin().darkGreyTextColor),
                        ],
                      ),
                      SizedBox(
                        height: 4.H,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppAssets.origin()
                              .iconLocation
                              .widget(height: 16.H, width: 16.W),
                          Expanded(
                            child: TripcText(
                              stay.address,
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              textColor: AppAssets.origin().gray474,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.start,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TripcText(
                        stay.price.vnd,
                        fontWeight: FontWeight.bold,
                        textColor: AppAssets.origin().colorTextFoodName,
                        fontSize: 16,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
