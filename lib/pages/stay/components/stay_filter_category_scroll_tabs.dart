import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../models/remote/stay_response.dart/stay_categories_response.dart';

class StayFilterCategoryScrollTabs extends StatefulWidget {
  final List<StayCategoriesResponse> categories;
  final StayCategoriesResponse? selected;
  final ValueChanged<StayCategoriesResponse> onSelected;

  const StayFilterCategoryScrollTabs({
    super.key,
    required this.categories,
    required this.selected,
    required this.onSelected,
  });

  @override
  State<StayFilterCategoryScrollTabs> createState() =>
      _StayFilterCategoryScrollTabsState();
}

class _StayFilterCategoryScrollTabsState
    extends State<StayFilterCategoryScrollTabs> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40.H,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.W),
        itemCount: widget.categories.length,
        separatorBuilder: (_, __) => SizedBox(width: 8.W),
        itemBuilder: (context, index) {
          final category = widget.categories[index];
          final isSelected = category == widget.selected;

          return GestureDetector(
            onTap: () => widget.onSelected(category),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.W),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppAssets.origin().textForLocationTourV2
                    : Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                category.name,
                style: TextStyle(
                  fontSize: 14,
                  color: isSelected ? Colors.white : AppAssets.origin().blue191,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
