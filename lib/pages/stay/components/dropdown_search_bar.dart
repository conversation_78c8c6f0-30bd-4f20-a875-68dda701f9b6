import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../models/remote/stay_response.dart/stay_location_response.dart';
import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';

class DropdownSearchBar extends StatelessWidget {
  final StayLocationResponse? selectedLocation;
  final List<StayLocationResponse> locations;
  final Function(StayLocationResponse) onlocationSelected;
  final VoidCallback onTapSearch;

  const DropdownSearchBar(
      {super.key,
      required this.selectedLocation,
      required this.locations,
      required this.onlocationSelected,
      required this.onTapSearch});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48.H,
      padding: EdgeInsets.symmetric(horizontal: 12.W, vertical: 12.H),
      margin: EdgeInsets.symmetric(horizontal: 16.W),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade300,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Flexible(
            child: DropdownButtonHideUnderline(
              child: DropdownButton2<StayLocationResponse>(
                value: selectedLocation,
                isExpanded: true,
                iconStyleData: IconStyleData(
                  icon: Icon(
                    Icons.keyboard_arrow_down_rounded,
                    color: AppAssets.origin().primaryColorV2,
                  ),
                  iconSize: 24,
                ),
                onChanged: (value) =>
                    value.isNotNull ? onlocationSelected(value!) : null,
                selectedItemBuilder: (context) {
                  return locations.map((location) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TripcText(
                          location.name,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          textColor: AppAssets.origin().black3C4,
                          overflow: TextOverflow.clip,
                          maxLines: 1,
                          textAlign: TextAlign.left,
                          ignorePointer: true,
                        ),
                      ],
                    );
                  }).toList();
                },
                items: locations
                    .map((location) => DropdownMenuItem<StayLocationResponse>(
                          value: location,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              TripcText(
                                location.name,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                overflow: TextOverflow.clip,
                                maxLines: 1,
                              ),
                              Padding(
                                padding: EdgeInsets.only(top: 8.H),
                                child: Divider(
                                  color: location != locations.last
                                      ? AppAssets.origin().grayC4C8
                                      : Colors.transparent,
                                  height: 1,
                                  thickness: 0.5,
                                ),
                              ),
                            ],
                          ),
                        ))
                    .toList(),
                dropdownStyleData: DropdownStyleData(
                  maxHeight: 250.H,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: const [
                      BoxShadow(
                        blurRadius: 6,
                        offset: Offset(0, 2),
                        color: Colors.black26,
                      ),
                    ],
                  ),
                  scrollbarTheme: ScrollbarThemeData(
                    radius: const Radius.circular(8),
                    thickness: WidgetStateProperty.all(2),
                  ),
                ),
                menuItemStyleData: MenuItemStyleData(
                  // height: 40.H,
                  padding: EdgeInsets.symmetric(horizontal: 16.W),
                ),
              ),
            ),
          ),
          VerticalDivider(color: AppAssets.origin().grayC4C8, thickness: 1),
          Expanded(
            child: GestureDetector(
              onTap: () => onTapSearch(),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: TripcText(
                      context.strings.hotel_name,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      textColor: AppAssets.origin().grayC4C8,
                    ),
                  ),
                  AppAssets.origin().iconSearch.widget(
                        color: AppAssets.origin().primaryColorV2,
                      ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
