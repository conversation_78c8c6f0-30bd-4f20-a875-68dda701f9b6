import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';

class UploadEvidenceBottomSheet extends StatefulWidget {
  const UploadEvidenceBottomSheet({super.key});

  @override
  State<UploadEvidenceBottomSheet> createState() => _UploadEvidenceBottomSheetState();
}

class _UploadEvidenceBottomSheetState extends State<UploadEvidenceBottomSheet> {
  String? selectedPlatform;
  final TextEditingController _messageController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  List<File> selectedImages = [];
  static const int maxImages = 5;

  final List<String> platforms = [
    'Chọn nền tảng',
    'Agoda',
    'Booking.com',
    'Expedia',
    'Hotels.com',
    'Traveloka',
  ];

  @override
  void initState() {
    super.initState();
    selectedPlatform = platforms.first;
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.SP),
          topRight: Radius.circular(20.SP),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: EdgeInsets.only(top: 12.H),
            width: 60.W,
            height: 5.H,
            decoration: BoxDecoration(
              color: AppAssets.origin().grayC4C8,
              borderRadius: BorderRadius.circular(2.SP),
            ),
          ),
          
          SizedBox(height: 15.H),
          // Title
          TripcText(
            'Yêu cầu hoàn tiền',
            fontSize: 18.SP,
            fontWeight: FontWeight.w600,
            height: 1.3,
            textColor: AppAssets.origin().blue191,
          ),
          
          SizedBox(height: 24.H),
          
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.W),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Upload section
                _buildUploadSection(),
                
                SizedBox(height: 24.H),
                
                // Platform dropdown
                _buildPlatformDropdown(),
                
                SizedBox(height: 24.H),
                
                // Message input
                _buildMessageInput(),
                
                SizedBox(height: 32.H),
                
                // Submit button
                _buildSubmitButton(),
                
                SizedBox(height: 24.H),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          'Tải ảnh lên',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          height: 1.42,
          textColor: AppAssets.origin().colorTextFoodName,
        ),
        SizedBox(height: 4.H),
        TripcText(
          'Tải lên tối đa 5 tệp.',
          fontSize: 12,
          fontWeight: FontWeight.w400,
          textColor: AppAssets.origin().hotelSearchNameColor,
        ),
        SizedBox(height: 12.H),

        // Show upload area if no images, otherwise show horizontal list
        selectedImages.isEmpty ? _buildUploadArea() : _buildImageList(),
      ],
    );
  }

  Widget _buildPlatformDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            TripcText(
              'Nền tảng',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              textColor: AppAssets.origin().black3C4,
            ),
            SizedBox(width: 4.W),
            const TripcText(
              '*',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              textColor: Colors.red,
            ),
          ],
        ),
        SizedBox(height: 8.H),
        Container(
          width: double.infinity,
          height: 40.H,
          padding: EdgeInsets.symmetric(horizontal: 16.W),
          decoration: BoxDecoration(
            border: Border.all(color: AppAssets.origin().colorBorderTextFieldRefund),
            borderRadius: BorderRadius.circular(12.SP),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selectedPlatform,
              isExpanded: true,
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: AppAssets.origin().colorTextFoodName,
              ),
              items: platforms.map((String platform) {
                return DropdownMenuItem<String>(
                  value: platform,
                  child: TripcText(
                    platform,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    textColor: AppAssets.origin().hotelSearchNameColor,
                  ),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  selectedPlatform = newValue;
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMessageInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          'Lời nhắn',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          textColor: AppAssets.origin().black3C4,
        ),
        SizedBox(height: 8.H),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppAssets.origin().colorBorderTextFieldRefund),
            borderRadius: BorderRadius.circular(12.SP),
          ),
          child: TextField(
            controller: _messageController,
            maxLines: 2,
            decoration: InputDecoration(
              hintText: 'Nhập thêm bất cứ thông tin để giúp chúng tôi kiểm tra yêu cầu của bạn được nhanh hơn (nếu có)',
              hintStyle: TextStyle(
                fontSize: 14.SP,
                fontWeight: FontWeight.w400,
                height: 1.42,
                color: AppAssets.origin().grayC4C8,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(12.W),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: TripcButton(
        height: 48.H,
        onPressed: _handleSubmit,
        style: AppButtonStyle(backgroundColor: AppAssets.origin().darkBlueColor),
        title: 'Gửi yêu cầu',
        textCase: TextCaseType.none,
      ),
    );
  }

  Widget _buildUploadArea() {
    return GestureDetector(
      onTap: _showImagePickerOptions,
      child: Container(
        width: double.infinity,
        height: 150.H,
        decoration: BoxDecoration(
          color: AppAssets.origin().grayE5,
          borderRadius: BorderRadius.circular(16.SP),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppAssets.origin().icUploadPhoto.widget(),
            TripcText(
              'PNG/JPEG/JPG',
              fontSize: 12,
              height: 1.42,
              textColor: AppAssets.origin().colorTextFoodName,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageList() {
    return SizedBox(
      height: 150.H,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: selectedImages.length + (selectedImages.length < maxImages ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == selectedImages.length) {
            // Add button
            return _buildAddImageButton();
          }

          // Image item
          return _buildImageItem(selectedImages[index], index);
        },
      ),
    );
  }

  Widget _buildImageItem(File imageFile, int index) {
    return Container(
      margin: EdgeInsets.only(right: 12.W),
      child: Stack(
        children: [
          Container(
            width: 80.W,
            height: 150.H,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.SP),
              border: Border.all(
                color: AppAssets.origin().hotelSearchNameColor.withValues(alpha: 0.3),
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.SP),
              child: Image.file(
                imageFile,
                fit: BoxFit.cover,
              ),
            ),
          ),
          // Remove button
          Positioned(
            top: 4.H,
            right: 4.W,
            child: GestureDetector(
              onTap: () => _removeImage(index),
              child: Container(
                width: 20.W,
                height: 20.H,
                decoration: BoxDecoration(
                  color: AppAssets.origin().grayE5,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  color: AppAssets.origin().blue001,
                  size: 12.W,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _showImagePickerOptions,
      child: Container(
        width: 80.W,
        height: 150.H,
        decoration: BoxDecoration(
          color: AppAssets.origin().grayE5,
          borderRadius: BorderRadius.circular(8.SP),
          border: Border.all(
            color: AppAssets.origin().hotelSearchNameColor.withValues(alpha: 0.3),
            style: BorderStyle.solid,
          ),
        ),
        child: Icon(
          Icons.add,
          color: AppAssets.origin().hotelSearchNameColor,
          size: 24.W,
        ),
      ),
    );
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: Icon(Icons.photo_library),
                title: Text('Chọn từ thư viện'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImageFromGallery();
                },
              ),
              ListTile(
                leading: Icon(Icons.photo_camera),
                title: Text('Chụp ảnh'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImageFromCamera();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickImageFromGallery() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        selectedImages.add(File(image.path));
      });
    }
  }

  Future<void> _pickImageFromCamera() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      setState(() {
        selectedImages.add(File(image.path));
      });
    }
  }

  void _removeImage(int index) {
    setState(() {
      selectedImages.removeAt(index);
    });
  }

  void _handleSubmit() {
    if (selectedPlatform == platforms.first) {
      // Show error - platform not selected
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Vui lòng chọn nền tảng')),
      );
      return;
    }
    
    // TODO: Implement submit functionality
    debugPrint('Submit tapped');
    debugPrint('Platform: $selectedPlatform');
    debugPrint('Message: ${_messageController.text}');
    
    Navigator.of(context).pop();
  }
}

// Helper function to show the bottom sheet
void showUploadEvidenceBottomSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: const UploadEvidenceBottomSheet(),
    ),
  );
}
