import 'package:flutter/material.dart';
import 'package:readmore/readmore.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

import '../../../models/app/rating.dart';
import '../../../widgets/commons/base_cached_network_image.dart';
import '../../../widgets/commons/tripc_triangle_painter/tripc_triangle_painter.dart';
import '../../../widgets/presentationals/app_shimmer_loading/image_shimmer_loading.dart';
import '../../profile/components/tripc_avatar.dart';

class RatingItem extends StatefulWidget {
  final Rating rating;

  const RatingItem({
    super.key,
    required this.rating,
  });

  @override
  State<RatingItem> createState() => _RatingItemState();
}

class _RatingItemState extends State<RatingItem> {
  bool _isLiked = false;
  bool _isDisliked = false;

  void _toggleLike() {
    setState(() {
      _isLiked = !_isLiked;
      if (_isLiked) {
        _isDisliked = false;
      }
    });
  }

  void _toggleDislike() {
    setState(() {
      _isDisliked = !_isDisliked;
      if (_isDisliked) {
        _isLiked = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.H),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _userInfor(),
          SizedBox(height: 14.H),
          _reactButton(),
          Padding(
            padding: EdgeInsets.only(left: 16.W),
            child: Divider(
              color: AppAssets.origin().disableButtonColor,
              thickness: 0.5.H,
            ),
          ),
          SizedBox(height: 8.H),
          _reviewContent(),
          if (widget.rating.supplierResponse != null) _responseContent(),
          Padding(
            padding: EdgeInsets.only(left: 16.W),
            child: Divider(
              color: AppAssets.origin().disableButtonColor,
              thickness: 0.5.H,
            ),
          ),
        ],
      ),
    );
  }

  Widget _userInfor() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.W),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TripcAvatar(
            avatarUrl: widget.rating.user.avatar,
          ),
          SizedBox(width: 8.W),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TripcText(
                  widget.rating.user.name,
                  fontWeight: FontWeight.w400,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4.H),
                _buildStarRating(widget.rating.ratingScore)
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStarRating(int rating) {
    List<Widget> stars = [];
    for (int i = 1; i <= 5; i++) {
      if (i <= rating) {
        stars.add(
            Icon(Icons.star, color: AppAssets.origin().yellow444, size: 16.H));
      } else {
        stars.add(Icon(Icons.star_border,
            color: AppAssets.origin().yellow444, size: 16.H));
      }
    }
    return Row(children: stars);
  }

  Widget _reactButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.W),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TripcText(
            '${context.strings.text_post_at} ${widget.rating.ratingDate.formatddMMYYY()}',
            fontWeight: FontWeight.w300,
            textColor: AppAssets.origin().secondDarkGreyTextColor,
          ),
          Row(
            children: [
              TripcText(
                '${widget.rating.totalLikes}',
                fontWeight: FontWeight.w400,
              ),
              GestureDetector(
                onTap: _toggleLike,
                child: Padding(
                  padding: EdgeInsets.all(4.H),
                  child: Icon(
                    Icons.thumb_up_alt_outlined,
                    size: 20,
                    color: _isLiked
                        ? AppAssets.origin().darkBlueColor
                        : AppAssets.origin().black,
                  ),
                ),
              ),
              SizedBox(width: 18.W),
              TripcText(
                '${widget.rating.totalDislikes}',
                fontWeight: FontWeight.w400,
              ),
              GestureDetector(
                onTap: _toggleDislike,
                child: Padding(
                  padding: EdgeInsets.all(4.H),
                  child: Icon(
                    Icons.thumb_down_off_alt_outlined,
                    size: 20,
                    color: _isDisliked
                        ? AppAssets.origin().redDotColor
                        : AppAssets.origin().black,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _reviewContent() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.W),
      child: Column(
        children: [
          TripcText(
            widget.rating.content,
            fontWeight: FontWeight.w300,
            textAlign: TextAlign.left,
          ),
          SizedBox(height: 8.H),
          if (widget.rating.images != null && widget.rating.images!.isNotEmpty)
            _reviewImages(widget.rating.images!),
        ],
      ),
    );
  }

  Widget _reviewImages(List<String> images) {
    return Wrap(
      spacing: 12.W,
      runSpacing: 10.H,
      children: images.map((imageUrl) {
        return ClipRRect(
            child: BaseCachedNetworkImage(
          imageUrl: imageUrl,
          height: 104.H,
          width: 110.W,
          placeholder: (context, _) => ImageShimmerLoading(
            height: 104.H,
            fit: BoxFit.scaleDown,
            color: context.appCustomPallet.buttonBG,
          ),
          errorWidget: (context, error, stackTrace) =>
              AppAssets.origin().icErrorImg.widget(
                    height: 104.H,
                    width: 110.W,
                    color: context.appCustomPallet.buttonBG,
                  ),
          fit: BoxFit.cover,
        ));
      }).toList(),
    );
  }

  Widget _responseContent() {
    return Padding(
      padding: EdgeInsets.all(16.H),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
              padding: EdgeInsetsDirectional.symmetric(
                  vertical: 12.H, horizontal: 16.W),
              decoration: BoxDecoration(
                color: AppAssets.origin().grayF7,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TripcText(
                    context.strings.text_supplier_response,
                    textAlign: TextAlign.left,
                  ),
                  SizedBox(
                    height: 8.H,
                  ),
                  ReadMoreText(
                    '${widget.rating.supplierResponse ?? ''}  ',
                    trimMode: TrimMode.Line,
                    trimLines: 2,
                    colorClickableText: AppAssets.origin().darkBlueColor,
                    trimCollapsedText: context.strings.text_view_more,
                    trimExpandedText: context.strings.text_collapse,
                    style: TextStyle(
                      fontSize: 16.SP,
                      fontWeight: FontWeight.w300,
                      color: AppAssets.origin().blackColor,
                    ),
                    moreStyle: TextStyle(
                        fontSize: 16.SP,
                        fontWeight: FontWeight.w300,
                        color: AppAssets.origin().darkBlueColor),
                    lessStyle: TextStyle(
                        fontSize: 16.SP,
                        fontWeight: FontWeight.w300,
                        color: AppAssets.origin().darkBlueColor),
                  ),
                ],
              )),
          Positioned(
            top: -9.H,
            left: 22.W,
            child: CustomPaint(
              size: Size(16.H, 16.H),
              painter: TrianglePainter(
                color: AppAssets.origin().grayF7,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
