import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../models/app/rating.dart';
import '../../../models/app/rating_level_enum.dart';
import '../../../services/app/app_assets.dart';
import '../../../utils/app_extension.dart';
import '../../../widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import '../../../widgets/commons/tripc_scafford/tripc_scafford.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';
import '../component/rating_item.dart';
import '../provider/rating_provider.dart';

class TripcRatingPage extends ConsumerStatefulWidget {
  const TripcRatingPage({super.key});

  @override
  ConsumerState<TripcRatingPage> createState() => _TripcRatingPageState();
}

class _TripcRatingPageState extends ConsumerState<TripcRatingPage> {
  int _currentTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    final double ratingScore =
        ref.watch(pRatingProvider.select((value) => value.ratingScore));
    final int totalReviews =
        ref.watch(pRatingProvider.select((value) => value.totalReviews));
    final int ratingLevelStatus =
        ref.watch(pRatingProvider.select((value) => value.ratingLevelStatus));
    final allRatingList =
        ref.watch(pRatingProvider.select((value) => value.allRatingList));
    final ratingWithImageList =
        ref.watch(pRatingProvider.select((value) => value.ratingWithImageList));
    final negativeRatingList =
        ref.watch(pRatingProvider.select((value) => value.negativeRatingList));

    return DefaultTabController(
      length: 3,
      child: TripcScaffold(
        titleAppBar: TripcText(
          context.strings.text_review,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          textColor: AppAssets.origin().black,
        ),
        onPressed: () => unfocusKeyboard(),
        hasBackButton: true,
        toolbarHeight: 38.H,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        body: Padding(
          padding: EdgeInsets.symmetric(vertical: 12.H),
          child: Visibility(
            visible: allRatingList.isNotEmpty,
            replacement: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppAssets.origin().imEmptyView.widget(),
                  TripcText(
                    context.strings.text_not_found,
                    fontWeight: FontWeight.w300,
                  )
                ],
              ),
            ),
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.W),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      TripcRichText(
                        text: '',
                        children: [
                          TextSpan(
                            text: '$ratingScore ',
                            style: AppAssets.origin().normalTextStyle.copyWith(
                                fontWeight: FontWeight.w600,
                                fontSize: 22,
                                color: AppAssets.origin().darkBlue5FF),
                          ),
                          TextSpan(
                            text: RatingLevel.getByValue(ratingLevelStatus)
                                .statusText(context),
                            style: AppAssets.origin().normalTextStyle.copyWith(
                                fontWeight: FontWeight.w600,
                                fontSize: 18,
                                color: AppAssets.origin().darkBlue5FF),
                          ),
                        ],
                      ),
                      SizedBox(
                        width: 16.W,
                      ),
                      TripcText(
                        '$totalReviews ${context.strings.text_reviews}',
                        textColor: AppAssets.origin().secondDarkGreyTextColor,
                        fontWeight: FontWeight.w400,
                      )
                    ],
                  ),
                ),
                Divider(
                  color: AppAssets.origin().disableButtonColor,
                  thickness: 0.5.H,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.H),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(left: 16.W),
                          child: _tabContainer(0, context.strings.text_all),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.W),
                          child: _tabContainer(1,
                              '${context.strings.text_reviews_with_images} (${ratingWithImageList.length})'),
                        ),
                        Padding(
                          padding: EdgeInsets.only(right: 16.W),
                          child: _tabContainer(2,
                              '${context.strings.text_neutral_and_negative_reviews} (${negativeRatingList.length})'),
                        ),
                      ],
                    ),
                  ),
                ),
                Divider(
                  color: AppAssets.origin().disableButtonColor,
                  thickness: 0.5.H,
                ),
                Expanded(
                  child: Stack(
                    children: [
                      Offstage(
                        offstage: _currentTabIndex != 0,
                        child: _listReviews(allRatingList),
                      ),
                      Offstage(
                        offstage: _currentTabIndex != 1,
                        child: _listReviews(ratingWithImageList),
                      ),
                      Offstage(
                        offstage: _currentTabIndex != 2,
                        child: _listReviews(negativeRatingList),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _tabContainer(int index, String label) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentTabIndex = index;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10.H, horizontal: 20.W),
        decoration: BoxDecoration(
          color: _currentTabIndex == index
              ? AppAssets.origin().blueCAE
              : AppAssets.origin().whiteBackgroundColor,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: _currentTabIndex == index
                ? Colors.transparent
                : AppAssets.origin().disableButtonColor,
            width: 0.5,
          ),
        ),
        child: Center(
          child: TripcText(label,
              ignorePointer: true,
              fontWeight: FontWeight.w400,
              textColor: _currentTabIndex == index
                  ? AppAssets.origin().darkBlueColor
                  : AppAssets.origin().blackColor),
        ),
      ),
    );
  }

  Widget _listReviews(List<Rating> ratings) {
    return ListView.builder(
      itemCount: ratings.length,
      itemBuilder: (context, index) {
        return RatingItem(
          rating: ratings[index],
        );
      },
    );
  }
}
