import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../models/app/rating.dart';

class RatingModel {
  RatingModel({
    this.ratingScore = 0,
    this.totalReviews = 0,
    this.ratingLevelStatus = 0,
    this.allRatingList = const [],
    this.ratingWithImageList = const [],
    this.negativeRatingList = const [],
  });

  final double ratingScore;
  final int totalReviews;
  final int ratingLevelStatus;
  final List<Rating> allRatingList;
  final List<Rating> ratingWithImageList;
  final List<Rating> negativeRatingList;

  static RatingModel getDefault() {
    return RatingModel(
        // ratingScore: 4.5,
        // totalReviews: 120,
        // ratingLevelStatus: 3,
        // allRatingList: [
        //   Rating(
        //     user: User(
        //         name: '<PERSON><PERSON> <PERSON><PERSON>',
        //         avatar:
        //             'https://static.vecteezy.com/system/resources/thumbnails/005/346/410/small_2x/close-up-portrait-of-smiling-handsome-young-caucasian-man-face-looking-at-camera-on-isolated-light-gray-studio-background-photo.jpg'),
        //     ratingDate: DateTime(2025, 1, 20),
        //     ratingScore: 4,
        //     totalLikes: 10,
        //     totalDislikes: 1,
        //     content:
        //         'Tôi đã có cơ hội trải nghiệm dịch vụ đặt tour của TripC và thực sự ấn tượng với chất lượng mà họ mang lại. Từ quá trình đặt tour cho đến khi hoàn thành chuyến đi, mọi thứ đều diễn ra suôn sẻ, chuyên nghiệp và vô cùng thuận tiện.',
        //     supplierResponse:
        //         'TripC chân thành cảm ơn bạn vì những đánh giá tích cực và sự tin tưởng dành cho chúng tôi!',
        //   ),
        //   Rating(
        //       user: User(
        //           name: 'Nguyen Nam Hai',
        //           avatar:
        //               'https://www.advancedmensdevelopment.com.au/wp-content/uploads/2022/02/qualities-of-a-good-man.jpg'),
        //       ratingDate: DateTime(2025, 1, 20),
        //       ratingScore: 5,
        //       totalLikes: 1,
        //       totalDislikes: 1,
        //       content:
        //           'Tôi đã có cơ hội trải nghiệm dịch vụ đặt tour của TripC và thực sự ấn tượng với chất lượng mà họ mang lại. ',
        //       supplierResponse:
        //           'TripC chân thành cảm ơn bạn vì những đánh giá tích cực và sự tin tưởng dành cho chúng tôi!',
        //       images: [
        //         'https://res.klook.com/image/upload/c_fill,w_750,h_750/q_80/w_80,x_15,y_15,g_south_west,l_Klook_water_br_trans_yhcmh3/activities/e7waraff45cbnn5j959r.jpg',
        //         'https://authenticadventure.com.vn/wp-content/uploads/2024/02/kinh-nghiem-du-lich-ba-na-hill-tron-ven-tu-a-z-202206041049510078.jpg',
        //         'https://stcd02265632633.cloud.edgevnpay.vn/website-vnpay-public/fill/2023/11/0w7bv2w8sq41698943375210.png',
        //         'https://danangfantasticity.com/wp-content/uploads/2023/09/sun-world-ba-na-hills-lan-thu-4-dat-giai-cong-vien-chu-de-hang-dau-chau-a-2023-05.jpg',
        //         'https://cdn.khamphadanang.vn/wp-content/uploads/2024/02/ba-na-hill-da-nang.jpeg?strip=all&lossy=1&ssl=1',
        //         'https://banahills.sunworld.vn/wp-content/uploads/2024/01/le-hoi-o-ba-na-hills-24.jpg'
        //       ]),
        // ],
        // ratingWithImageList: [
        //   Rating(
        //       user: User(
        //           name: 'Nguyen Nam Hai',
        //           avatar:
        //               'https://www.advancedmensdevelopment.com.au/wp-content/uploads/2022/02/qualities-of-a-good-man.jpg'),
        //       ratingDate: DateTime(2025, 1, 20),
        //       ratingScore: 5,
        //       totalLikes: 1,
        //       totalDislikes: 1,
        //       content:
        //           'Tôi đã có cơ hội trải nghiệm dịch vụ đặt tour của TripC và thực sự ấn tượng với chất lượng mà họ mang lại. ',
        //       supplierResponse:
        //           'TripC chân thành cảm ơn bạn vì những đánh giá tích cực và sự tin tưởng dành cho chúng tôi!',
        //       images: [
        //         'https://res.klook.com/image/upload/c_fill,w_750,h_750/q_80/w_80,x_15,y_15,g_south_west,l_Klook_water_br_trans_yhcmh3/activities/e7waraff45cbnn5j959r.jpg',
        //         'https://authenticadventure.com.vn/wp-content/uploads/2024/02/kinh-nghiem-du-lich-ba-na-hill-tron-ven-tu-a-z-202206041049510078.jpg',
        //         'https://stcd02265632633.cloud.edgevnpay.vn/website-vnpay-public/fill/2023/11/0w7bv2w8sq41698943375210.png',
        //         'https://danangfantasticity.com/wp-content/uploads/2023/09/sun-world-ba-na-hills-lan-thu-4-dat-giai-cong-vien-chu-de-hang-dau-chau-a-2023-05.jpg',
        //         'https://cdn.khamphadanang.vn/wp-content/uploads/2024/02/ba-na-hill-da-nang.jpeg?strip=all&lossy=1&ssl=1',
        //         'https://banahills.sunworld.vn/wp-content/uploads/2024/01/le-hoi-o-ba-na-hills-24.jpg'
        //       ]),
        // ],
        // negativeRatingList: [
        //   Rating(
        //     user: User(
        //         name: 'Bui Xuan Quyen',
        //         avatar:
        //             'https://static.vecteezy.com/system/resources/thumbnails/005/346/410/small_2x/close-up-portrait-of-smiling-handsome-young-caucasian-man-face-looking-at-camera-on-isolated-light-gray-studio-background-photo.jpg'),
        //     ratingDate: DateTime(2025, 1, 20),
        //     ratingScore: 5,
        //     totalLikes: 10,
        //     totalDislikes: 1,
        //     content:
        //         'Tôi đã có cơ hội trải nghiệm dịch vụ đặt tour của TripC và thực sự ấn tượng với chất lượng mà họ mang lại. Từ quá trình đặt tour cho đến khi hoàn thành chuyến đi, mọi thứ đều diễn ra suôn sẻ, chuyên nghiệp và vô cùng thuận tiện.',
        //     supplierResponse:
        //         'TripC chân thành cảm ơn bạn vì những đánh giá tích cực và sự tin tưởng dành cho chúng tôi!',
        //   ),
        // ],
        );
  }
}

class RatingProvider extends StateNotifier<RatingModel> {
  RatingProvider(super._state);

  // void addPendingPayment(Payment? payment) {
  //   if (payment == null) return;
  //   state = state.copyWith(pendingList: [...state.pendingList, payment]);
  // }

  // void resetState() {
  //   state = PaidTourScreenModel.getDefault();
  // }
}

final pRatingProvider = StateNotifierProvider<RatingProvider, RatingModel>(
    (ref) => RatingProvider(RatingModel.getDefault()));
