import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/cache/cache_search.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';

import '../../../services/app/app_route.dart';
import '../../../services/providers/providers.dart';
import '../../tutorial/providers/first_time_on_app_provider.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({
    super.key,
  });

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initRoute();
  }

  Future<void> _initRoute() async {
    final delayFuture =
        await Future.delayed(const Duration(milliseconds: 4000));

    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final isFirstTimeOnApp =
        await globalcontainer.read(pFirstTimeOnAppProvider).getState(prefs);
    await globalCacheAuth.getAuth(prefs);
    await globalCacheAuth.getCacheInfoUser(prefs);
    if (globalCacheAuth.isLogged()) {
      await globalcontainer.read(pAccountProvider.notifier).getme();
    }
    await globalSearchHistoryCache.init();
    final hasNullPasscode = !(globalCacheAuth.user?.hasPascode ?? false);
    final hasInviteCode =
        !(globalCacheAuth.user?.isSkipInviteCodeBool ?? false);

    await delayFuture;

    /// Wait for fade-out to complete before navigating

    AppRoute.navigateWithFade(
        isFirstTimeOnApp ?? true
            ? AppRoute.routeTutorial
            : !globalCacheAuth.isLogged()
                ? AppRoute.routeHome
                : hasNullPasscode
                    ? hasInviteCode
                        ? AppRoute.routeMembershipCode
                        : AppRoute.routeMembershipSignIn
                    : AppRoute.routeHome,
        context,
        {});
  }

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
      visibleAppBar: false,
      canPop: false,
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Positioned.fill(
            child: Center(
              child: Transform.scale(
                scale: 1.27,
                child: Lottie.asset(
                  AppAssets.origin().lottieLoadingSplash.assetPath,
                  fit: BoxFit.cover,
                  repeat: false,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
