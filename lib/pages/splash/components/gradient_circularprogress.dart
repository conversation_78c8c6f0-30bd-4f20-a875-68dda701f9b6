import 'dart:math';
import 'package:flutter/material.dart';
import 'package:gradient_circular_progress_indicator/gradient_circular_progress_indicator.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

class GradientCircularProgress extends StatefulWidget {
  const GradientCircularProgress(
      {super.key, required this.duration, this.onCompleted});

  final Duration duration;
  final VoidCallback? onCompleted;

  @override
  State<GradientCircularProgress> createState() =>
      _GradientCircularProgressState();
}

class _GradientCircularProgressState extends State<GradientCircularProgress>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    )..repeat();
    _controller.addListener(() {
      if (_controller.isCompleted) {
        widget.onCompleted?.call();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) => Transform.rotate(
        angle: 1.5 * pi,
        child: GradientCircularProgressIndicator(
            progress: _controller.value,
            stroke: 1.8.H,
            size: 96.H,
            gradient: AppAssets.origin().circularGradient),
      ),
    );
  }
}
