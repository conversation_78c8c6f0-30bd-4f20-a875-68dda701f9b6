import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class PromotionalCodeEmptyView extends StatelessWidget {
  const PromotionalCodeEmptyView({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      AppAssets.origin().imEmptyView.widget(height: 192.H, width: 192.H),
      TripcText(
        context.strings.text_no_promotional_code,
        fontSize: 12,
        fontWeight: FontWeight.w400,
        textColor: AppAssets.origin().black,
      )
    ]);
  }
}
