import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';
import '../views/electronic_invoice.dart';

class InvoiceStatusViewV2 extends StatelessWidget {
  const InvoiceStatusViewV2({super.key, this.onTap, required this.status});
  final VoidCallback? onTap;
  final InvoiceStatus status;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppAssets.origin().cardColorTourV2,
          borderRadius: BorderRadius.circular(12.SP),
        ),
        padding: EdgeInsets.symmetric(vertical: 8.H, horizontal: 10.W),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                AppAssets.init.icBillCheck.widget(width: 20.H, color: AppAssets.init.lightBlueFD),
                <PERSON><PERSON>Box(width: 6.W),
                TripcText(
                  context.strings.request_for_electronic_invoice,
                  fontWeight: FontWeight.w700,
                  fontSize: 14,
                  textColor: AppAssets.init.textColorForNameTourV2,
                ),
                const Spacer(),
                Visibility(
                  visible: status == InvoiceStatus.notYetRequest,
                  child: AppAssets.origin().icParkOutlineRight.widget(
                    width: 16.H,
                    height: 16.H,
                    color: AppAssets.origin().black,
                  ),
                ),
              ],
            ),
            TripcText(
              '(${status.title(context)})',
              fontWeight: FontWeight.w400,
              fontSize: 14,
              padding: EdgeInsets.only(left: 26.W, top: 4.H),
              textColor: AppAssets.init.disableColorV2,
            ),
          ],
        ),
      ),
    );
  }
}
