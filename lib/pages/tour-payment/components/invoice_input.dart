import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';
import '../../../widgets/tripc_text_filed/tripc_text_filed.dart';

class InvoiceInput extends StatelessWidget {
  const InvoiceInput({
    super.key,
    this.controller,
    this.label,
    this.hintText,
    this.onChanged,
    this.errorText = '',
    this.textInputAction,
    this.readOnly
  });

  final TextEditingController? controller;
  final String? label;
  final String? hintText;
  final ValueChanged<String>? onChanged;
  final String errorText;
  final TextInputAction? textInputAction;
  final bool? readOnly;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            TripcText(
              label ?? '',
              fontSize: 14,
              fontWeight: FontWeight.w300,
              textColor: AppAssets.init.black,
            ),
            SizedBox(width: 12.W,),
            Expanded(
                child: TripcTextField(
              isVisibleBorder: false,
              hintText: hintText ?? '',
              fontWeight: FontWeight.w300,
              fontSize: 14,
              controller: controller,
              onChanged: onChanged,
              hintTextColor: AppAssets.init.greyTextColorC8,
              textAlign: TextAlign.right,
              textInputAction: textInputAction,
              contentPadding: EdgeInsets.zero,
              textSuffix: const SizedBox.shrink(),
              readOnly: readOnly ?? false
            )),
          ],
        ),
        if (errorText.isNotEmpty)
          TripcText(
           errorText,
            textAlign: TextAlign.right,
            fontSize: 10,
            fontWeight: FontWeight.w300,
            textColor: AppAssets.init.error,
          )
      ],
    );
  }
}
