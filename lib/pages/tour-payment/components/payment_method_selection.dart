import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/payment_method_enum.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_check_box/tripc_check_box.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class PaymentMethodSelection extends StatelessWidget {
  const PaymentMethodSelection(
      {super.key,
      this.method,
      this.padding,
      this.value,
      this.child,
      this.onTapCheck,
      this.title,
      this.textColor});
  final PaymentMethod? method;
  final EdgeInsetsGeometry? padding;
  final bool? value;
  final VoidCallback? onTapCheck;
  final Widget? child;
  final String? title;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TripcCheckbox(
              onTap: onTapCheck,
              value: value,
              size: 16,
              borderWidth: 2,
              borderColor: AppAssets.origin().lightGrayDD4),
          child ??
              TripcText(
                title ?? method?.title(context) ?? '',
                fontSize: 12,
                textAlign: TextAlign.start,
                fontWeight: FontWeight.w400,
                textColor: onTapCheck.isNotNull ?  AppAssets.origin().blackColor : AppAssets.origin().greyTextColorC8,
                padding: EdgeInsets.only(left: 10.W),
              )
        ],
      ),
    );
  }
}
