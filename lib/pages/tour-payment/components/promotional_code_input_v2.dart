import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class PromotionalCodeInputV2 extends StatelessWidget {
  const PromotionalCodeInputV2({super.key, required this.quantity, required this.onTap});
  final VoidCallback onTap;
  final int quantity;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.W),
        padding: EdgeInsets.all(10.W),
        decoration: BoxDecoration(
          color: AppAssets.origin().whiteBackgroundColor,
          borderRadius: BorderRadius.circular(12.SP),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 10.H,
          children: [
            Row(
              children: [
                AppAssets.origin()
                    .icVoucher
                    .widget(height: 14.H, width: 18.W, color: AppAssets.origin().colorTextOffer),
                TripcText(
                  context.strings.use_the_offer,
                  fontSize: 16,
                  textAlign: TextAlign.start,
                  fontWeight: FontWeight.w700,
                  padding: EdgeInsets.only(left: 5.W),
                  textColor: AppAssets.origin().colorTextOffer,
                ),
                const Spacer(),
                Row(
                  children: [
                    TripcText(
                      context.strings.text_see_more,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      padding: EdgeInsets.only(right: 5.W),
                      textColor: AppAssets.origin().black,
                    ),
                    AppAssets.origin().icParkOutlineRight.widget(
                      width: 16.H,
                      height: 16.H,
                      color: AppAssets.origin().black,
                    ),
                  ],
                ),
              ],
            ),
            TripcText(
              context.strings.you_having_x_discount_code_enjoy(quantity.toString()),
              fontSize: 14,
              textAlign: TextAlign.start,
              fontWeight: FontWeight.w400,
              padding: EdgeInsets.only(left: 23.W),
              textColor: AppAssets.origin().disableColorV2,
            ),
          ],
        ),
      ),
    );
  }
}
