import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';
import '../views/electronic_invoice.dart';

class InvoiceStatusView extends StatelessWidget {
  const InvoiceStatusView(
      {super.key, this.onTap, this.status = InvoiceStatus.request});
  final VoidCallback? onTap;
  final InvoiceStatus status;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 50.H,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5.H),
          border: Border.all(
            width: 1.H,
            color: AppAssets.init.lightBlueFD,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 18.W, right: 14.W),
          child: Row(
            children: [
              AppAssets.init.icBillCheck.widget(width: 20.H),
              SizedBox(width: 12.W),
              TripcText(
                context.strings.text_electronic_invoice,
                fontWeight: FontWeight.w400,
                fontSize: 14,
                textColor: AppAssets.init.lightBlueFD,
              ),
              const Spacer(),
              Visibility(
                visible: InvoiceStatus.request == status,
                replacement: TripcText(
                  status.title(context),
                  fontWeight: FontWeight.w300,
                  fontSize: 14,
                  textColor: AppAssets.init.lightBlueFD,
                ),
                child: Row(
                  children: [
                    TripcText(
                      status.title(context),
                      fontWeight: FontWeight.w300,
                      fontSize: 14,
                      textColor: AppAssets.init.lightBlueFD,
                    ),
                    SizedBox(width: 4.W),
                    SizedBox(
                      height: 20.H,
                      width: 20.H,
                      child: Center(
                        child: AppAssets.init.iconArrowRight.widget(
                          color: AppAssets.init.lightBlueFD,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
