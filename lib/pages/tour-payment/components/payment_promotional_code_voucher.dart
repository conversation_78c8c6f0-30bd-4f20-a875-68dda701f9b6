import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/promotional_code.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class PromotionalCodeVoucher extends StatelessWidget {
  const PromotionalCodeVoucher(
      {super.key,
      required this.promotionalCode,
      this.onTapDetail,
      this.onTapGet});
  final VoidCallback? onTapGet;
  final VoidCallback? onTapDetail;
  final PromotionalCode promotionalCode;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        CustomPaint(
            painter: VoucherPainter(),
            child: ClipPath(
              clipper: VoucherClipper(),
              child: Container(
                height: 143.H,
                decoration: BoxDecoration(
                  color: AppAssets.origin().whiteBackgroundColor,
                ),
              ),
            )),
        Positioned.fill(
          child: Container(
            padding: EdgeInsets.only(
                top: 12.H, left: 11.W, right: 7.W, bottom: 12.H),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TripcText(
                        '${context.strings.text_discount} ${promotionalCode.discountPercent.discountSymbol}',
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        textAlign: TextAlign.start,
                        textColor: AppAssets.origin().lightRedColor,
                      ),
                      TripcText(
                        '${context.strings.text_reduce} ${promotionalCode.discountPercent.discountSymbol}',
                        fontSize: 10,
                        fontWeight: FontWeight.w700,
                        textAlign: TextAlign.start,
                        textColor: AppAssets.origin().blackColor,
                        padding: EdgeInsets.symmetric(vertical: 4.H),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TripcText(
                              promotionalCode.note,
                              fontSize: 10,
                              fontWeight: FontWeight.w400,
                              textAlign: TextAlign.start,
                              maxLines: 1,
                              textColor: AppAssets.origin().blackColor,
                            ),
                            TripcText(
                              '${context.strings.text_save_up_to} ${promotionalCode.saving.d}',
                              fontSize: 10,
                              fontWeight: FontWeight.w400,
                              textAlign: TextAlign.start,
                              textColor: AppAssets.origin().blackColor,
                              maxLines: 1,
                              padding: EdgeInsets.symmetric(vertical: 4.H),
                            ),
                            TripcText(
                              '${context.strings.text_validity_period} ${promotionalCode.startAndEndTime}',
                              fontSize: 10,
                              fontWeight: FontWeight.w400,
                              textAlign: TextAlign.start,
                              maxLines: 2,
                              textColor: AppAssets.origin().blackColor,
                            ),
                          ],
                        ),
                      ),
                      TripcText(
                        onTap: onTapDetail,
                        context.strings.text_detail,
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                        textAlign: TextAlign.start,
                        textColor: AppAssets.origin().darkBlueColor,
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 33.W),
                FittedBox(
                    child: TripcButton(
                  onPressed: onTapGet,
                  height: 30,
                  width: 63,
                  title: context.strings.text_receive,
                  style: const AppButtonStyle(fontSize: 14, radius: 8),
                ))
              ],
            ),
          ),
        ),
        Positioned(
          right: 85.W,
          top: 14.H,
          bottom: 14.H,
          child: DottedLine(
            direction: Axis.vertical,
            alignment: WrapAlignment.center,
            lineThickness: 2.H,
            dashLength: 6,
            dashColor: AppAssets.origin().appBarUnderline,
            dashGapLength: 3,
            dashGapColor: Colors.transparent,
          ),
        ),
      ],
    );
  }
}

class VoucherClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    return getVoucherNotchPath(size);
  }

  @override
  bool shouldReclip(VoucherClipper oldClipper) => false;
}

class VoucherPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = AppAssets.origin().appBarUnderline
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.H;

    RRect roundedRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(1, 1, size.width - 2, size.height - 2),
      Radius.circular(16.SP),
    );

    canvas.drawRRect(roundedRect, paint);
    Path notchPath = getVoucherNotchPath(size);
    canvas.drawPath(notchPath, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

Path getVoucherNotchPath(Size size) {
  double notchWidth = 17.W;
  double notchHeight = 7.H;

  Path path = Path();
  path.moveTo(size.width * 0.75 - notchWidth / 2, 0);
  path.arcToPoint(
    Offset(size.width * 0.75 + notchWidth / 2, 0),
    radius: Radius.circular(notchHeight),
    clockwise: false,
  );

  path.moveTo(size.width * 0.75 + notchWidth / 2, size.height);
  path.arcToPoint(
    Offset(size.width * 0.75 - notchWidth / 2, size.height),
    radius: Radius.circular(notchHeight),
    clockwise: false,
  );

  return path;
}
