import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_box_input/tripc_box_input.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_filed.dart';

class PromotionalCodeInput extends StatelessWidget {
  const PromotionalCodeInput({super.key, required this.quantity, this.onTap});
  final VoidCallback? onTap;
  final int quantity;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcBoxInput(
          headerTitle: context.strings.text_promotion,
          titleBuilder: (headerTitle) => Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 8.W, right: 12.W),
                child: AppAssets.origin()
                    .icDiscount
                    .widget(height: 16.H, width: 20.W),
              ),
              TripcText(
                headerTitle,
                fontSize: 16,
                textAlign: TextAlign.start,
                fontWeight: FontWeight.w500,
                maxLines: 1,
              )
            ],
          ),
          gradient: AppAssets.origin().inputBoxPinkHeaderGradient,
          child: TripcTextField(
            onTap: () {},
            hintText: context.strings.text_enter_promotional_code,
            hintTextColor: AppAssets.origin().lightGrayDD4,
            suffix: TripcIconButton(
              onPressed: onTap,
              child: Container(
                height: 16.H,
                width: 16.H,
                padding: EdgeInsets.symmetric(vertical: 4.H),
                margin: EdgeInsets.only(right: 12.5.W),
                child: AppAssets.origin()
                    .icBlackRight
                    .widget(color: AppAssets.origin().lightGrayDD4),
              ),
            ),
            fontSize: 12,
            contentPadding:
                EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
          ),
        ),
        TripcRichText(
          text: '',
          padding: EdgeInsets.only(top: 10.H),
          children: [
            TextSpan(
              text: context.strings.text_you_are_having,
              style: AppAssets.origin().normalTextStyle.copyWith(
                  fontSize: 12.SP,
                  fontWeight: FontWeight.w300,
                  fontStyle: FontStyle.italic,
                  color: AppAssets.origin().secondDarkGreyTextColor),
            ),
            TextSpan(
              text: '0',
              style: AppAssets.origin().boldTextStyle.copyWith(
                  fontSize: 12.SP,
                  fontStyle: FontStyle.italic,
                  color: AppAssets.origin().secondDarkGreyTextColor),
            ),
            const TextSpan(text: ' '),
            TextSpan(
              text: context.strings.text_discount_code_enjoy,
              style: AppAssets.origin().normalTextStyle.copyWith(
                  fontSize: 12.SP,
                  fontWeight: FontWeight.w300,
                  fontStyle: FontStyle.italic,
                  color: AppAssets.origin().secondDarkGreyTextColor),
            ),
          ],
        )
      ],
    );
  }
}
