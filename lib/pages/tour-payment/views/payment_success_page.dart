import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/tripc_persistent_tab_type.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/pages/tabbar/app_tabbar.dart';
import 'package:tripc_app/pages/ticket_tour/providers/providers.dart';
import 'package:tripc_app/pages/tour-payment/providers/payment_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../models/app/booking_detail.dart';
import '../../../services/providers/providers.dart';
import '../../../widgets/app_loading.dart';
import '../../../widgets/commons/app_dialog/tripc_error_dialog.dart';

class TripcTourPaymenSuccess extends ConsumerWidget {
  const TripcTourPaymenSuccess({super.key, this.isPaymentSuccess = false});
  final bool isPaymentSuccess;

  String content(BuildContext context, WidgetRef ref) {
    final currentPayment = ref.read(pPaymentProvider).currentPayment;
    if (currentPayment?.service?.serviceType ==
        TripCServiceCategory.tourSightSeeing) {
      return context.strings.text_payment_successful;
    }
    return context.strings.text_wish_after_paid_combo;
  }

  Future<void> getIdByCode(BuildContext context, WidgetRef ref) async {
    final result = await ref.read(pPaymentProvider.notifier).getIdByCode();
    if (result == 0) {
      final errorMessage =
          ref.watch(pPaymentProvider.select((value) => value.errorMessage));
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorMessage));
    } else {
      _handleGoToOrderDetail(context, ref, result);
    }
  }

  void _handleGoToOrderDetail(
      BuildContext context, WidgetRef ref, int orderId) {
    ref.read(pTicketTourProvider.notifier).resetState();
    ref.read(pPaymentProvider.notifier).resetState();
    ref
        .read(pAppBottomNavProvider.notifier)
        .setTab(TripCPersistentTabType.mytrip);
    AppRoute.pushNamed(context,
        routeName: AppRoute.routeMyTripDetailedTourV2,
        arguments: BookingDetail(orderId: orderId));
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading =
        ref.watch(pPaymentProvider.select((value) => value.isLoading));
    final orderId =
        ref.watch(pPaymentProvider.select((value) => value.orderId));
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        visibleAppBar: false,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        body: Stack(
          children: [
            SafeArea(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 24.W).copyWith(top: 27.H),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    isPaymentSuccess
                        ? AppAssets.origin().icSuccess.widget(
                              height: 120.H,
                              width: 120.H,
                            )
                        : AppAssets.origin().icWarning.widget(
                              height: 120.H,
                              width: 120.H,
                            ),
                    _content(context, ref),
                    // SizedBox(height: 100.H),
                    const Spacer(),
                    if (!isPaymentSuccess)
                      TripcButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        height: 56,
                        title: context.strings.text_try_again,
                        style: AppButtonStyle(
                          textColor: AppAssets.origin().whiteSmokeColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      )
                    else
                      Column(
                        children: [
                          TripcButton(
                            onPressed: () {
                              if (orderId == 0) {
                                getIdByCode(context, ref);
                              } else {
                                _handleGoToOrderDetail(context, ref, orderId);
                              }
                            },
                            height: 56,
                            title: context.strings.text_view_booked_tours,
                            style: AppButtonStyle(
                              textColor: AppAssets.origin().whiteSmokeColor,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(height: 24.H),
                          TripcButton(
                            onPressed: () {
                              ref
                                  .read(pTicketTourProvider.notifier)
                                  .resetState();
                              ref.read(pPaymentProvider.notifier).resetState();
                              Navigator.popUntil(context,
                                  ModalRoute.withName(AppRoute.routeHome));
                            },
                            height: 56,
                            buttonType: ButtonType.outline,
                            style: AppButtonStyle(
                              textColor: AppAssets.origin().secondaryColor,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                            border: Border.all(
                                color: AppAssets.origin().secondaryColor,
                                width: 1.H),
                            title: context.strings.text_back_to_home,
                          ),
                        ],
                      )
                  ],
                ),
              ),
            ),
            AppLoading(isRequesting: isLoading),
          ],
        ));
  }

  Widget _content(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          isPaymentSuccess
              ? context.strings.text_payment_successful
              : context.strings.text_payment_fail,
          fontWeight: FontWeight.w500,
          fontSize: 24,
          textColor: AppAssets.origin().black,
          textAlign: TextAlign.start,
          padding: EdgeInsets.only(top: 27.H, bottom: 20.H),
        ),
        Visibility(
          visible: isPaymentSuccess,
          child: TripcText(
            context.strings.text_dont_forget_share_moment,
            fontWeight: FontWeight.w300,
            fontSize: 14,
            textColor: AppAssets.origin().black,
            textAlign: TextAlign.start,
            padding: EdgeInsets.only(
              top: 10.H,
            ),
          ),
        ),
        Visibility(
          visible: isPaymentSuccess,
          child: TripcText(
            context.strings.text_dont_forget_share_moment_2,
            fontWeight: FontWeight.w300,
            fontSize: 14,
            textColor: AppAssets.origin().black,
            textAlign: TextAlign.start,
            padding: EdgeInsets.only(
              top: 10.H,
            ),
          ),
        ),
        Visibility(
          visible: !isPaymentSuccess,
          child: TripcText(
            context.strings.text_please_try_again,
            fontWeight: FontWeight.w300,
            fontSize: 14,
            textColor: AppAssets.origin().black,
            textAlign: TextAlign.start,
            padding: EdgeInsets.only(
              top: 10.H,
            ),
          ),
        ),
        // TripcText(
        //   isPaymentSuccess
        //       ? content(context, ref)
        //       : context.strings.text_please_try_again,
        //   fontWeight: FontWeight.w400,
        //   fontSize: 14,
        //   textColor: AppAssets.origin().black,
        //   height: 1.2,
        //   textAlign: TextAlign.start,
        // ),
      ],
    );
  }
}
