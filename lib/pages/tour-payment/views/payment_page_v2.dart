import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/booking_detail.dart';
import 'package:tripc_app/models/app/payment_method_enum.dart';
import 'package:tripc_app/models/app/payment_request.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/app/tripc_persistent_tab_type.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/tabbar/app_tabbar.dart';
import 'package:tripc_app/pages/ticket_tour/components/header_tour_ticket_area.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/pages/tour-payment/components/invoice_status_view_v2.dart';
import 'package:tripc_app/pages/tour-payment/components/payment_method_selection.dart';
import 'package:tripc_app/pages/tour-payment/components/promotional_code_input_v2.dart';
import 'package:tripc_app/pages/tour-payment/providers/payment_provider.dart';
import 'package:tripc_app/pages/tour-payment/views/electronic_invoice.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/status_display_notifier.dart';
import 'package:tripc_app/utils/app_error_string.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog_v2.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/common_extension.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../services/app/app_constants.dart';
import '../../../utils/app_log.dart';

class TripcPaymentPageV2 extends ConsumerStatefulWidget {
  const TripcPaymentPageV2(
      {super.key, required this.tour, this.isFerryTour = false});

  final TourResponse tour;
  final bool isFerryTour;

  @override
  ConsumerState<TripcPaymentPageV2> createState() =>
      _TripcAddPassengerQuantityViewV2State();
}

class _TripcAddPassengerQuantityViewV2State
    extends ConsumerState<TripcPaymentPageV2> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        ref.listenManual(
            pPaymentProvider.select((state) => state.isPaymentSuccess),
            (_, state) {
          if (!state) {
            return;
          }
          _voidShowDialogSuccess(isPaymentSuccess: true);
        });
        ref.listenManual(
            pPaymentProvider.select((state) => state.isPaymentError),
            (_, state) {
          if (!state) {
            return;
          }
          _voidShowDialogSuccess(isPaymentSuccess: false);
        });
        ref.listenManual(pPaymentProvider.select((state) => state.errorMessage),
            (_, state) {
          if (state?.isEmpty ?? true) {
            return;
          }
          dialogHelpers.show(context,
              child: TripcErrorDialog(
                  errorText:
                      ErrorParser.getErrorMessage(state ?? '', context)));
        });
        ref.listenManual(
            pPaymentProvider.select((state) => state.payOSWebViewUrl),
            (_, url) {
          if (url != null) {
            AppRoute.pushNamed(context,
                routeName: AppRoute.routePayOSWebView, arguments: url);
          }
        });
      },
    );
  }

  bool isBeforeCurrentTime({required String current, required String time}) {
    return current.compareTo(time) >= 0;
  }

  void openDocxInWeb(String rawUrl) async {
    final path = '${AppConstants.googleDocsViewerBaseUrl}$rawUrl';
    final url = Uri.parse(path);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedTour =
        ref.watch(pTicketTourProvider.select((value) => value.selectedTour));
    final currentPayment =
        ref.watch(pPaymentProvider.select((value) => value.currentPayment));
    final totalPayment =
        ref.watch(pTicketTourProvider.select((value) => value.totalPayment));
    // final selectButtonEnable = ref
    //     .watch(pTicketTourProvider.select((value) => value.selectButtonEnable));
    final promotionalCodes =
        ref.watch(pPaymentProvider.select((value) => value.promotionalCodes));
    final invoiceRequest =
        ref.watch(pPaymentProvider.select((value) => value.invoiceRequest));
    final currentPaymentMethod = ref
        .watch(pPaymentProvider.select((value) => value.currentPaymentMethod));
    final tcentPoint = ref.watch(pAccountProvider
        .select((value) => value.user?.selectedMembership?.tcent ?? 0));
    final isLoading = ref.watch(
      pPaymentProvider.select((value) => value.isLoading),
    );
    return Stack(
      children: [
        TripcScaffold(
          onPressed: () => unfocusKeyboard(),
          onPopScope: () {
            ref.read(pTicketTourProvider.notifier).resetQuantity();
            ref.read(pPaymentProvider.notifier).selectPaymentMethod(null);
          },
          onLeadingPressed: () {
            ref.read(pTicketTourProvider.notifier).resetQuantity();
            ref.read(pPaymentProvider.notifier).selectPaymentMethod(null);
            Navigator.pop(context);
          },
          hasBackButton: true,
          titleAppBar: TripcText(
            context.strings.text_pay,
            fontWeight: FontWeight.w600,
            fontSize: 16,
            textColor: AppAssets.origin().black,
          ),
          appBarColor: AppAssets.origin().whiteBackgroundColor,
          bottom: PreferredSize(
              preferredSize: const Size(double.infinity, 16),
              child: Divider(
                height: 1,
                color: AppAssets.init.lightGray,
              )),
          backgroundColor: AppAssets.origin().bgDetailTourColorV2,
          body: Stack(
            children: [
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                bottom: 151.H,
                child: SingleChildScrollView(
                  child: Column(
                    spacing: 16.H,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (selectedTour != null)
                        HeaderTourTicketArea(tour: widget.tour),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 16.W),
                        decoration: BoxDecoration(
                          color: AppAssets.origin().whiteBackgroundColor,
                          borderRadius: BorderRadius.circular(12.SP),
                        ),
                        padding: EdgeInsets.symmetric(
                            horizontal: 9.W, vertical: 20.H),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 16.H,
                          children: [
                            _addPassengersArea(
                              context,
                              ref,
                              selectedTour: selectedTour,
                            ),
                            _informationContact(context,
                                isFerryTour: widget.isFerryTour),
                            _ticketType(context),
                            _optionalRequest(context)
                          ],
                        ),
                      ),
                      PromotionalCodeInputV2(
                        quantity: promotionalCodes.length,
                        onTap: () => AppRoute.pushNamed(context,
                            routeName: AppRoute.routeTripcPromotionalCode),
                      ),
                      _paymentMethodList(
                        context,
                        currentPaymentMethod,
                        selectedTour: selectedTour,
                        tcentPoint: tcentPoint,
                        invoiceRequest: invoiceRequest,
                        onTapCheck: (method) => ref
                            .read(pPaymentProvider.notifier)
                            .selectPaymentMethod(method),
                      ),
                      _policyNoticeText(),
                      SizedBox(
                        height: 15.H,
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      top: BorderSide(
                        color: Colors.grey.shade300,
                        width: 1.0,
                      ),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        offset: const Offset(0, -2),
                        blurRadius: 8.0,
                        spreadRadius: 1.0,
                      ),
                    ],
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 8.H)
                      .copyWith(bottom: 14.H),
                  child: _selectButton(
                    isButtonEnable: currentPaymentMethod.isNotNull,
                    context,
                    totalPay: totalPayment,
                    selectedTour: selectedTour,
                    onTapSelect: () {
                      logger.d(
                          "currentPaymentMethod = ${currentPaymentMethod?.value}");
                      ref
                          .read(pPaymentProvider.notifier)
                          .onPayment(selectedTour, context);
                      if (currentPayment?.passengerInfo?.contactResponse ==
                          null) {
                        ref.read(pPaymentProvider.notifier).addContact();
                      }
                      if (selectedTour?.isExportInvoice == true) {
                        ref.read(pPaymentProvider.notifier).resetStateInvoice();
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
        AppLoading(
          isRequesting: isLoading,
          // backgroundColor: Colors.transparent,
        ),
      ],
    );
  }

  Future<void> getIdByCode(BuildContext context, WidgetRef ref) async {
    final result = await ref.read(pPaymentProvider.notifier).getIdByCode();
    if (result == 0) {
      final errorMessage =
          ref.watch(pPaymentProvider.select((value) => value.errorMessage));
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorMessage));
    } else {
      _handleGoToOrderDetail(context, ref, result);
    }
  }

  void _handleGoToOrderDetail(
      BuildContext context, WidgetRef ref, int orderId) {
    ref.read(pTicketTourProvider.notifier).resetState();
    ref.read(pPaymentProvider.notifier).resetState();
    ref
        .read(pAppBottomNavProvider.notifier)
        .setTab(TripCPersistentTabType.mytrip);
    AppRoute.pushNamed(context,
        routeName: AppRoute.routeMyTripDetailedTourV2,
        arguments: BookingDetail(orderId: orderId));
  }

  Widget _addPassengersArea(
    BuildContext context,
    WidgetRef ref, {
    required TourResponse? selectedTour,
  }) {
    final subProducts = selectedTour?.subProducts ?? [];
    final totalStock = subProducts.fold<int>(0, (sum, e) => sum + e.stock);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          '${context.strings.quantity} ($totalStock)',
          fontSize: 16,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w600,
          padding: EdgeInsets.only(bottom: 15.H),
          textColor: AppAssets.origin().textColorForNameTourV2,
        ),
        Column(
          children: List.generate(
            subProducts.length,
            (index) {
              final subProduct = subProducts[index];
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: EdgeInsets.only(left: 10.W),
                      child: TripcText(
                        subProduct.audience?.name ??
                            context.strings.text_do_not_have,
                        fontSize: 14,
                        textAlign: TextAlign.start,
                        fontWeight: FontWeight.w500,
                        textColor: AppAssets.origin().black,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: TripcText(
                      '${subProduct.stock} ${context.strings.ticket}',
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w400,
                      textColor: AppAssets.origin().blackColor,
                    ),
                  )
                ],
              );
            },
          ),
        )
      ],
    );
  }

  Widget _informationContact(BuildContext context, {bool isFerryTour = false}) {
    String fullName = ref.watch(
      pTicketTourProvider.select(
        (value) => value.contactFullName,
      ),
    );
    String phoneNumber = ref.watch(
      pTicketTourProvider.select(
        (value) => value.contactPhoneNumber,
      ),
    );
    String email = ref.watch(
      pTicketTourProvider.select(
        (value) => value.contactEmail,
      ),
    );
    String numberPlate = ref.watch(
      pTicketTourProvider.select(
        (value) => value.numberPlatte,
      ),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 5.H,
      children: [
        TripcText(
          context.strings.text_contact_information.toSentenceCase(),
          fontSize: 16,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w600,
          padding: EdgeInsets.only(bottom: 10.H),
          textColor: AppAssets.origin().textColorForNameTourV2,
        ),
        _commonRowWidget(
            title:
                context.strings.text_first_name_and_last_name.toSentenceCase(),
            content: fullName),
        _commonRowWidget(
            title: context.strings.text_linked_phone, content: phoneNumber),
        _commonRowWidget(title: context.strings.text_email, content: email),
        if (isFerryTour)
          _commonRowWidget(
              title: context.strings.number_plate, content: numberPlate),
      ],
    );
  }

  Widget _ticketType(BuildContext context) {
    final selectedDate = ref.watch(
      pTicketTourProvider.select(
        (value) => value.selectedDate,
      ),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 5.H,
      children: [
        TripcText(
          context.strings.type_ticket,
          fontSize: 16,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w600,
          padding: EdgeInsets.only(bottom: 10.H),
          textColor: AppAssets.origin().textColorForNameTourV2,
        ),
        _commonRowWidget(
            title: context.strings.date,
            content: selectedDate.toVietnameseDateString()),
      ],
    );
  }

  Widget _optionalRequest(BuildContext context) {
    final specialRequest = ref.watch(
      pTicketTourProvider.select(
        (value) => value.passengerInfo?.specialRequest,
      ),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 5.H,
      children: [
        TripcText(
          context.strings.text_special_requests.toSentenceCase(),
          fontSize: 16,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w600,
          padding: EdgeInsets.only(bottom: 10.H),
          textColor: AppAssets.origin().textColorForNameTourV2,
        ),
        TripcText(
          specialRequest?.isNotEmpty == true
              ? specialRequest!
              : context.strings.text_do_not_have,
          fontSize: 14,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w400,
          padding: EdgeInsets.only(left: 10.W),
          textColor: AppAssets.origin().blackColor,
        ),
      ],
    );
  }

  Widget _commonRowWidget({required String title, required String content}) =>
      Row(
        children: [
          Expanded(
            flex: 2,
            child: TripcText(
              title,
              fontSize: 14,
              textAlign: TextAlign.start,
              fontWeight: FontWeight.w500,
              padding: EdgeInsets.only(left: 10.W),
              textColor: AppAssets.origin().blackColor,
            ),
          ),
          Expanded(
            flex: 3,
            child: TripcText(
              content,
              fontSize: 14,
              textAlign: TextAlign.start,
              fontWeight: FontWeight.w400,
              textColor: AppAssets.origin().blackColor,
            ),
          ),
        ],
      );

  Widget _paymentMethodList(
    BuildContext context,
    PaymentMethod? currentMethod, {
    Function(PaymentMethod)? onTapCheck,
    required int tcentPoint,
    required InvoiceRequest? invoiceRequest,
    required TourResponse? selectedTour,
  }) {
    // print("invoiceRequest: $invoiceRequest");
    // print("invoiceRequest2: ${invoiceRequest?.name?.isNotEmpty == true}");
    final bool disableTcent = tcentPoint < 10000;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.W),
      padding: EdgeInsets.all(10.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().whiteBackgroundColor,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TripcText(
            context.strings.text_payment_method,
            fontSize: 16,
            textAlign: TextAlign.start,
            fontWeight: FontWeight.w700,
            textColor: AppAssets.origin().textColorForNameTourV2,
            padding: EdgeInsets.only(bottom: 14.H),
          ),
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            PaymentMethodSelection(
              onTapCheck: selectedTour?.paymentOptions?.payLater != true
                  ? null
                  : () => onTapCheck?.call(PaymentMethod.paylate),
              method: PaymentMethod.paylate,
              value: PaymentMethod.paylate == currentMethod,
              padding: EdgeInsets.only(left: 8.W, bottom: 10.H),
            ),
            PaymentMethodSelection(
                onTapCheck:
                    disableTcent || selectedTour?.paymentOptions?.tcent != true
                        ? null
                        : () => onTapCheck?.call(PaymentMethod.tcent),
                method: PaymentMethod.tcent,
                value: PaymentMethod.tcent == currentMethod,
                padding: EdgeInsets.only(left: 8.W, bottom: 10.H),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TripcText(
                      PaymentMethod.tcent.title(context),
                      fontSize: 12,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w400,
                      textColor: disableTcent
                          ? AppAssets.origin().greyTextColorC8
                          : AppAssets.origin().blackColor,
                      padding: EdgeInsets.only(left: 10.W),
                    ),
                    TripcRichText(
                      text: '',
                      textAlign: TextAlign.start,
                      children: [
                        TextSpan(
                          text: ' (',
                          style: AppAssets.origin().normalTextStyle.copyWith(
                              fontSize: 12.SP,
                              color: disableTcent
                                  ? AppAssets.origin().greyTextColorC8
                                  : AppAssets.origin().blackColor),
                        ),
                        TextSpan(
                          text: context.strings.text_you_are_having,
                          style: AppAssets.origin().superBoldTextStyle.copyWith(
                              fontSize: 12.SP,
                              color: disableTcent
                                  ? AppAssets.origin().greyTextColorC8
                                  : AppAssets.origin().blackColor),
                        ),
                        TextSpan(
                          text: tcentPoint.tcent,
                          style: AppAssets.origin().boldTextStyle.copyWith(
                              fontSize: 12.SP,
                              color: disableTcent
                                  ? AppAssets.origin().greyTextColorC8
                                  : AppAssets.origin().tcentServiceItemColor),
                        ),
                        TextSpan(
                          text: ')',
                          style: AppAssets.origin().normalTextStyle.copyWith(
                              fontSize: 12.SP,
                              color: disableTcent
                                  ? AppAssets.origin().greyTextColorC8
                                  : AppAssets.origin().blackColor),
                        ),
                      ],
                    ),
                  ],
                )),
            //   PaymentMethodSelection(
            //       onTapCheck: !(selectedTour?.paymentOptions?.vnpay ?? false)
            //           ? null
            //           : () => onTapCheck?.call(PaymentMethod.vnpay),
            //       method: PaymentMethod.vnpay,
            //       value: PaymentMethod.vnpay == currentMethod,
            //       textColor: (selectedTour?.paymentOptions?.vnpay ?? false)
            //           ? AppAssets.origin().blackColor
            //           : AppAssets.origin().greyTextColorC8,
            //       padding: EdgeInsets.only(left: 8.W, bottom: 10.H)),
            PaymentMethodSelection(
                onTapCheck: selectedTour?.paymentOptions?.vnpay != true
                    ? null
                    : () => onTapCheck?.call(PaymentMethod.payos),
                method: PaymentMethod.payos,
                value: PaymentMethod.payos == currentMethod,
                textColor: AppAssets.origin().blackColor,
                padding: EdgeInsets.only(left: 8.W, bottom: 10.H)),
          ]),
          Visibility(
            visible: widget.tour.isExportInvoice ?? true,
            child: Padding(
              padding: EdgeInsets.only(top: 5.H),
              child: InvoiceStatusViewV2(
                status: (invoiceRequest?.name?.isNotEmpty == true)
                    ? InvoiceStatus.processing
                    : InvoiceStatus.notYetRequest,
                onTap: () => AppRoute.pushNamed(
                  context,
                  routeName: AppRoute.routeElectronicInvoiceV2,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _policyNoticeText() {
    double fontSize = 14.SP;
    const FontWeight fontWeight = FontWeight.w400;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.W),
      child: TripcRichText(
        text: '',
        textAlign: TextAlign.start,
        lineHeight: 1.4,
        children: [
          TextSpan(
            text: context.strings.text_by_clicking_pay_button,
            style: AppAssets.origin().normalTextStyle.copyWith(
                fontSize: fontSize,
                fontWeight: fontWeight,
                color: AppAssets.origin().blackColor),
          ),
          TextSpan(
              text: context.strings.text_terms_of_use,
              style: AppAssets.origin().normalTextStyle.copyWith(
                    fontWeight: fontWeight,
                    fontSize: fontSize,
                    color: AppAssets.origin().darkBlueColor,
                    decoration: TextDecoration.underline,
                  ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  openDocxInWeb(AppConstants.tripcTermAndPolicy);
                }),
          TextSpan(
            text: ' & ',
            style: AppAssets.origin().normalTextStyle.copyWith(
                fontWeight: fontWeight,
                fontSize: fontSize,
                color: AppAssets.origin().blackColor),
          ),
          TextSpan(
              text: context.strings.text_conditions_and_privacy,
              style: AppAssets.origin().normalTextStyle.copyWith(
                    fontWeight: fontWeight,
                    fontSize: fontSize,
                    color: AppAssets.origin().darkBlueColor,
                    decoration: TextDecoration.underline,
                  ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  openDocxInWeb(AppConstants.tripcTermAndPolicy);
                }),
          TextSpan(
            text: ' ${context.strings.text_of}',
            style: AppAssets.origin().normalTextStyle.copyWith(
                  fontWeight: fontWeight,
                  fontSize: fontSize,
                  color: AppAssets.origin().blackColor,
                ),
          ),
          TextSpan(
              text: ' TripC',
              style: AppAssets.origin().normalTextStyle.copyWith(
                    fontWeight: fontWeight,
                    fontSize: fontSize,
                    color: AppAssets.origin().darkBlueColor,
                  ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  AppRoute.pushNamed(context,
                      routeName: AppRoute.routeProfileIntroduceTripcAI);
                })
        ],
      ),
    );
  }

  Widget _selectButton(BuildContext context,
      {required bool isButtonEnable,
      TourResponse? selectedTour,
      VoidCallback? onTapSelect,
      required int totalPay}) {
    return Padding(
      padding: EdgeInsets.all(10.W),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                context.strings.ticket_price,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
              ),
              TripcText(
                totalPay.vnd,
                fontSize: 24,
                fontWeight: FontWeight.w700,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().priceServiceItemColor,
              ),
            ],
          ),
          SizedBox(
            height: 5.H,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                context.strings.surcharge_included,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().disableColorV2,
              ),
              if (globalReleaseStatusNotifier.isDisplayAll)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppAssets.origin().bgTcentServiceItemColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      AppAssets.origin()
                          .icTcentV2
                          .widget(height: 20.W, width: 20.W),
                      TripcText(
                        '${context.strings.text_receive_now} ${(totalPay.vndToTcent).tcent}',
                        ignorePointer: true,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        textColor: AppAssets.origin().tcentServiceItemColor,
                        padding: EdgeInsets.only(left: 4.W),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          SizedBox(
            height: 8.H,
          ),
          TripcButton(
            onPressed: onTapSelect,
            showSuggestLoginDialog: true,
            style: AppButtonStyle(
                backgroundColor: AppAssets.origin().primaryColorV2),
            isLogin: globalCacheAuth.isLogged(),
            textCase: TextCaseType.none,
            resultHandler: () {
              // ref
              //     .read(pTicketTourProvider.notifier)
              //     .getDetailedTour(tourId: widget.tourId);
            },
            isButtonDisabled: !isButtonEnable,
            height: 46,
            title: context.strings.text_pay,
          )
        ],
      ),
    );
  }

  _voidShowDialogSuccess({required bool isPaymentSuccess}) {
    final orderId = ref.watch(
      pPaymentProvider.select((value) => value.orderId),
    );
    dialogHelpers.show(
      context,
      child: TripcDialogV2(
        title: isPaymentSuccess
            ? context.strings.request_order_recorded
            : context.strings.text_failure_order,
        titleFontWeight: FontWeight.w500,
        icon: Container(
          height: 40.W,
          width: 40.W,
          decoration: BoxDecoration(
            color: AppAssets.origin().colorIconSuccess,
            borderRadius: BorderRadius.circular(50.SP),
          ),
          child: isPaymentSuccess
              ? Icon(
                  Icons.check_rounded,
                  color: AppAssets.origin().whiteBackgroundColor,
                )
              : Icon(
                  Icons.warning_amber_rounded,
                  color: AppAssets.origin().whiteBackgroundColor,
                ),
        ),
        titleFirstButton: isPaymentSuccess
            ? context.strings.view_order_history
            : context.strings.text_try_again,
        titleSecondButton: isPaymentSuccess
            ? context.strings.text_back_to_home.toSentenceCase()
            : null,
        onFirstButtonTap: () {
          if (!isPaymentSuccess) {
            Navigator.pop(context);
          } else {
            if (orderId == 0) {
              getIdByCode(context, ref);
            } else {
              _handleGoToOrderDetail(context, ref, orderId);
            }
          }
        },
        onSecondButtonTap: () {
          ref.read(pTicketTourProvider.notifier).resetState();
          ref.read(pPaymentProvider.notifier).resetState();
          Navigator.popUntil(
            context,
            ModalRoute.withName(AppRoute.routeHome),
          );
        },
        contentPadding: EdgeInsets.symmetric(horizontal: 12.W)
            .copyWith(top: 20.H, bottom: 20.H),
        child: TripcText(
          isPaymentSuccess
              ? context.strings.text_dont_forget_share_moment
              : context.strings.text_please_try_again,
          fontSize: 14,
          height: 1.42,
          fontWeight: FontWeight.w400,
          textColor: AppAssets.origin().black,
        ),
      ),
    );
  }
}
