import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/tour-payment/components/invoice_input.dart';
import 'package:tripc_app/pages/tour-payment/providers/payment_provider.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../../../models/remote/order_response/my_order_response.dart';
import '../../../services/app/app_assets.dart';
import '../../../services/providers/providers.dart';
import '../../../utils/app_extension.dart';
import '../../../widgets/commons/app_dialog/tripc_dialog.dart';
import '../../../widgets/commons/tripc_scafford/tripc_scafford.dart';
import '../components/payment_method_selection.dart';

enum InvoiceStatus {
  request,
  processing,
  resolve,
  notYetRequest;

  static InvoiceStatus fromByString(String i) {
    return InvoiceStatus.values.firstWhere((f) => f.toString() == i,
        orElse: () => InvoiceStatus.request);
  }

  String title(BuildContext context) {
    switch (this) {
      case InvoiceStatus.request:
        return context.strings.text_requested;
      case InvoiceStatus.processing:
        return context.strings.text_processing;
      case InvoiceStatus.resolve:
        return context.strings.text_processed;
      case InvoiceStatus.notYetRequest:
        return context.strings.text_request_now;
    }
  }
}

enum InvoiceType {
  individual,
  business;

  static InvoiceType fromByString(String i) {
    return InvoiceType.values.firstWhere((f) => f.toString() == i,
        orElse: () => InvoiceType.individual);
  }

  String title(BuildContext context) {
    switch (this) {
      case InvoiceType.individual:
        return context.strings.text_personal;
      case InvoiceType.business:
        return context.strings.text_company;
    }
  }

  String titleOptional(BuildContext context) => switch (this) {
        InvoiceType.individual => context.strings.invoices_for_individuals,
        InvoiceType.business => context.strings.invoices_for_businesses,
      };
}

class ElectronicInvoice extends ConsumerStatefulWidget {
  const ElectronicInvoice(
      {super.key, this.isDisableResetInvoice, this.invoice});
  final bool? isDisableResetInvoice;
  final InvoiceResponse? invoice;

  @override
  ConsumerState<ElectronicInvoice> createState() => _ElectronicInvoiceState();
}

class _ElectronicInvoiceState extends ConsumerState<ElectronicInvoice> {
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _taxCodeController = TextEditingController();
  final _emailController = TextEditingController();

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        final invoiceRequest =
            ref.watch(pPaymentProvider.select((value) => value.invoiceRequest));
        _nameController.text = invoiceRequest?.name ?? '';
        _addressController.text = invoiceRequest?.address ?? '';
        _taxCodeController.text = invoiceRequest?.taxCode ?? '';
        _emailController.text = invoiceRequest?.email ?? '';
      },
    );
    super.initState();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _taxCodeController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentInvoiceType =
        ref.watch(pPaymentProvider.select((value) => value.currentInvoice));
    final isButtonDisable =
        ref.watch(pPaymentProvider.select((value) => value.isButtonDisable));
    final invoiceResponseType =
        widget.invoice != null && widget.invoice?.type != null
            ? InvoiceType.fromByString(widget.invoice?.type ?? '')
            : null;
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        hasBackButton: true,
        needUnFocus: true,
        onPopScope: () {
          if (widget.isDisableResetInvoice == true) return;
          ref.read(pPaymentProvider.notifier).resetStateInvoice();
        },
        onLeadingPressed: () {
          ref.read(pPaymentProvider.notifier).resetStateInvoice();
          Navigator.pop(context);
        },
        titleAppBar: TripcText(
          context.strings.text_request_electronic_invoice,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          textColor: Colors.black,
        ),
        body: SafeArea(
          child: LayoutBuilder(builder: (context, constraints) {
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.W)
                            .copyWith(top: 24.H),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TripcText(
                              context.strings.text_invoice_type,
                              fontWeight: FontWeight.w300,
                              fontSize: 14,
                              textColor: Colors.black,
                            ),
                            PaymentMethodSelection(
                                title: InvoiceType.individual.title(context),
                                value: widget.invoice == null
                                    ? ref.watch(pPaymentProvider.select(
                                            (value) => value.currentInvoice)) ==
                                        InvoiceType.individual
                                    : invoiceResponseType ==
                                        InvoiceType.individual,
                                onTapCheck: () {
                                  ref
                                      .read(pPaymentProvider.notifier)
                                      .selectInvoiceType(
                                          InvoiceType.individual);
                                }),
                            PaymentMethodSelection(
                                title: InvoiceType.business.title(context),
                                value: widget.invoice != null
                                    ? ref.watch(pPaymentProvider.select(
                                            (value) => value.currentInvoice)) ==
                                        InvoiceType.business
                                    : invoiceResponseType ==
                                        InvoiceType.business,
                                onTapCheck: () {
                                  ref
                                      .read(pPaymentProvider.notifier)
                                      .selectInvoiceType(InvoiceType.business);
                                }),
                          ],
                        ),
                      ),
                      SizedBox(height: 12.H),
                      Container(
                        color: AppAssets.init.lightBlue,
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              vertical: 10.H, horizontal: 16.W),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppAssets.init.icNote.widget(
                                color: AppAssets.init.tabbarEnableColor,
                                width: 20.H,
                              ),
                              SizedBox(width: 8.W),
                              Expanded(
                                child: TripcText(
                                  context.strings.text_electronic_invoice_note,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w300,
                                  maxLines: null,
                                  textColor: AppAssets.init.blackColor,
                                  textAlign: TextAlign.left,
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 16.H, horizontal: 16.H),
                        child: Column(
                          children: [
                            InvoiceInput(
                              label:
                                  currentInvoiceType == InvoiceType.individual
                                      ? context.strings.text_card_name_label
                                      : context.strings.text_business_name,
                              hintText: widget.invoice == null
                                  ? currentInvoiceType == InvoiceType.individual
                                      ? context.strings.text_enter_your_name
                                      : context.strings.text_enter_business_name
                                  : widget.invoice?.name,
                              controller: _nameController,
                              onChanged:
                                  ref.read(pPaymentProvider.notifier).setName,
                              textInputAction: TextInputAction.next,
                              readOnly: widget.invoice != null,
                            ),
                            InvoiceInput(
                              label:
                                  currentInvoiceType == InvoiceType.individual
                                      ? context.strings.text_address
                                      : context.strings.text_business_address,
                              hintText: widget.invoice == null
                                  ? context.strings.text_enter_address
                                  : widget.invoice?.address,
                              controller: _addressController,
                              onChanged: ref
                                  .read(pPaymentProvider.notifier)
                                  .setAddress,
                              textInputAction: TextInputAction.next,
                              readOnly: widget.invoice != null,
                            ),
                            Visibility(
                              visible:
                                  currentInvoiceType == InvoiceType.business,
                              child: InvoiceInput(
                                label: context.strings.text_tax_code,
                                hintText: widget.invoice == null
                                    ? context.strings.text_enter_tax_code
                                    : widget.invoice?.taxCode,
                                controller: _taxCodeController,
                                onChanged: ref
                                    .read(pPaymentProvider.notifier)
                                    .setTaxCode,
                                textInputAction: TextInputAction.next,
                                readOnly: widget.invoice != null,
                              ),
                            ),
                            InvoiceInput(
                              label: context.strings.text_invoice_email,
                              hintText: widget.invoice == null
                                  ? context.strings.text_enter_email
                                  : widget.invoice?.receiverEmail,
                              controller: _emailController,
                              onChanged:
                                  ref.read(pPaymentProvider.notifier).setEmail,
                              textInputAction: TextInputAction.done,
                              readOnly: widget.invoice != null,
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 18.W),
                        child:
                            Divider(color: AppAssets.init.grayE9, height: 1.H),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            left: 16.W, top: 8.H, right: 14.W, bottom: 10.H),
                        child: TripcText(
                          context.strings.text_invoice_message,
                          textColor: AppAssets.init.secondDarkGreyTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 16.W),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TripcText(
                              context.strings.text_disclaimer,
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                              textColor: AppAssets.init.secondDarkGreyTextColor,
                              textAlign: TextAlign.left,
                            ),
                            SizedBox(height: 12.H),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TripcText('\u2022',
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    textColor: AppAssets.origin()
                                        .secondDarkGreyTextColor,
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 5.W)
                                            .copyWith(top: 3.H)),
                                Expanded(
                                  child: TripcText(
                                    context.strings.text_disclaimer_note_1,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w300,
                                    maxLines: null,
                                    textAlign: TextAlign.start,
                                    textColor: AppAssets.origin()
                                        .secondDarkGreyTextColor,
                                  ),
                                )
                              ],
                            ),
                            SizedBox(height: 8.H),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TripcText('\u2022',
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    textColor: AppAssets.origin()
                                        .secondDarkGreyTextColor,
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 5.W)
                                            .copyWith(top: 3.H)),
                                Expanded(
                                  child: TripcText(
                                    context.strings.text_disclaimer_note_2,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w300,
                                    textAlign: TextAlign.start,
                                    textColor: AppAssets.origin()
                                        .secondDarkGreyTextColor,
                                  ),
                                )
                              ],
                            ),
                            SizedBox(height: 8.H),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TripcText('\u2022',
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    textColor: AppAssets.origin()
                                        .secondDarkGreyTextColor,
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 5.W)
                                            .copyWith(top: 3.H)),
                                Expanded(
                                  child: TripcText(
                                    context.strings.text_disclaimer_note_3,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w300,
                                    textAlign: TextAlign.start,
                                    textColor: AppAssets.origin()
                                        .secondDarkGreyTextColor,
                                  ),
                                )
                              ],
                            ),
                            SizedBox(height: 8.H),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TripcText('\u2022',
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    textColor: AppAssets.origin()
                                        .secondDarkGreyTextColor,
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 5.W)
                                            .copyWith(top: 3.H)),
                                Expanded(
                                  child: TripcText(
                                    context.strings.text_disclaimer_note_4,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w300,
                                    textAlign: TextAlign.start,
                                    textColor: AppAssets.origin()
                                        .secondDarkGreyTextColor,
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                      const Spacer(),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.H)
                            .copyWith(top: 20, bottom: 30),
                        child: TripcButton(
                          height: 60.H,
                          isButtonDisabled:
                              widget.invoice == null ? !isButtonDisable : true,
                          title: context.strings.text_submit_request,
                          onPressed: () {
                            dialogHelpers.show(context,
                                child: TripcDialog(
                                  title: context.strings.text_invoice_success,
                                  titleFontWeight: FontWeight.w500,
                                  onTap: () {
                                    ref
                                        .read(pPaymentProvider.notifier)
                                        .updateInvoiceRequest();
                                    // ref.read(pPaymentProvider.notifier).resetStateInvoice();
                                    Navigator.popUntil(
                                        context,
                                        (route) =>
                                            route.settings.name ==
                                            AppRoute.routeTourPaymentV2);
                                  },
                                  icon: AppAssets.init.icSuccess.widget(
                                    height: 40.H,
                                    width: 40.H,
                                  ),
                                  titleButton: context.strings.text_confirm,
                                  contentPadding:
                                      EdgeInsets.symmetric(horizontal: 24.W)
                                          .copyWith(top: 24.H, bottom: 27.H),
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 16.H),
                                    child: TripcText(
                                      context
                                          .strings.text_invoice_message_popup,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w300,
                                      textColor: AppAssets.origin().black,
                                    ),
                                  ),
                                ));
                            // Navigator.pop(context);
                          },
                          style: AppButtonStyle(
                              radius: 12.H,
                              backgroundColor: AppAssets.init.lightBlueFD,
                              textColor: Colors.white,
                              fontSize: 15,
                              fontWeight: FontWeight.w500),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            );
          }),
        ));
  }
}
