import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tripc_app/models/app/booking_detail.dart';
import 'package:tripc_app/models/app/tripc_persistent_tab_type.dart';
import 'package:tripc_app/pages/tabbar/app_tabbar.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/pages/tour-payment/providers/payment_payos_provider.dart';
import 'package:tripc_app/pages/tour-payment/providers/payment_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_constants.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog_v2.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_error_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class PaymentPayOSWebview extends ConsumerStatefulWidget {
  const PaymentPayOSWebview({super.key, required this.paymentUrl});
  final String paymentUrl;

  @override
  ConsumerState<PaymentPayOSWebview> createState() =>
      _PaymentPayOSWebviewState();
}

class _PaymentPayOSWebviewState extends ConsumerState<PaymentPayOSWebview> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listenManual(
          pPayOSPaymentProvider.select((state) => state.isPaymentSuccess),
          (_, isPaymentSuccess) {
        if (isPaymentSuccess.isNotNull) {
          _voidShowDialogSuccessPayOs(isPaymentSuccess: isPaymentSuccess ?? false);
        }
      });

      ref.listenManual(
          pPayOSPaymentProvider.select((state) => state.isPaymentPending),
          (_, isPaymentPending) {
        if (isPaymentPending == true) {
          ref
              .read(pAppBottomNavProvider.notifier)
              .setTab(TripCPersistentTabType.mytrip);
          Navigator.popUntil(
              context, (route) => route.settings.name == AppRoute.routeHome);
        }
      });

      ref.listenManual(
          pPayOSPaymentProvider.select((state) => state.isPaymentCancelled),
          (_, isPaymentCancelled) {
        if (isPaymentCancelled == true) {
          ref.read(pPayOSPaymentProvider.notifier).resetState();
          Navigator.pop(context);
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        TripcScaffold(
          titleAppBar: TripcText(
            context.strings.text_payment_with_payos,
            fontSize: 24,
            fontWeight: FontWeight.w600,
            textColor: AppAssets.origin().whiteSmokeColor,
          ),
          leading: Padding(
            padding: EdgeInsets.only(top: 32.H),
            child: AppAssets.init.iconArrowleft.widget(
              color: AppAssets.origin().whiteSmokeColor,
            ),
          ),
          onPopScope: () =>
              ref.read(pPayOSPaymentProvider.notifier).resetState(),
          onLeadingPressed: () {
            ref.read(pPayOSPaymentProvider.notifier).resetState();
            Navigator.pop(context);
          },
          body: Stack(
            children: [
              InAppWebView(
                  initialUrlRequest: URLRequest(url: WebUri(widget.paymentUrl)),
                  initialSettings: InAppWebViewSettings(
                    javaScriptEnabled: true,
                    useOnDownloadStart: true,
                  ),
                  onLoadStart: (controller, url) {
                    setState(() {
                      _isLoading = true;
                    });
                  },
                  onLoadStop: (controller, url) {
                    setState(() {
                      _isLoading = false;
                    });
                  },
                  shouldOverrideUrlLoading: (controller, navigationAction) {
                    String url = navigationAction.request.url.toString();
                    if (url.startsWith(AppConstants.appUrlScheme)) {
                      ref
                          .read(pPayOSPaymentProvider.notifier)
                          .handleReturnUrl(Uri.parse(url));
                      return Future.value(NavigationActionPolicy.CANCEL);
                    }
                    return Future.value(NavigationActionPolicy.ALLOW);
                  },
                  onDownloadStartRequest:
                      (controller, DownloadStartRequest request) async {
                    final uri = request.url;

                    if (uri.scheme == AppConstants.appPayosDownloadQRScheme &&
                        uri.path
                            .startsWith(AppConstants.appPayosDownloadQRPath)) {
                      if (await Permission.storage.request().isGranted) {
                        final data = UriData.fromUri(uri);
                        final bytes = data.contentAsBytes();

                        final result = await ImageGallerySaver.saveImage(bytes,
                            quality: 100,
                            name:
                                "payos_qr_${DateTime.now().millisecondsSinceEpoch}");
                        if (result['isSuccess'] == true) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content: Text(
                              context.strings.text_save_qr_to_gallery,
                            )),
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content: Text(context
                                    .strings.text_error_something_wrong)),
                          );
                        }
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                              content: Text(context
                                  .strings.text_storage_permission_denied)),
                        );
                      }
                    }
                  }),
              Visibility(
                visible: _isLoading,
                child: Container(color: AppAssets.origin().whiteSmokeColor),
              )
            ],
          ),
        ),
        AppLoading(isRequesting: _isLoading)
      ],
    );
  }

  _voidShowDialogSuccessPayOs({required bool isPaymentSuccess}) {
    final orderId = ref.watch(
      pPaymentProvider.select((value) => value.orderId),
    );
    dialogHelpers.show(
      context,
      child: TripcDialogV2(
        title: isPaymentSuccess
            ? context.strings.text_success_payment
            : context.strings.text_payment_fail,
        titleFontWeight: FontWeight.w500,
        icon: Container(
          height: 40.W,
          width: 40.W,
          decoration: BoxDecoration(
            color: AppAssets.origin().colorIconSuccess,
            borderRadius: BorderRadius.circular(50.SP),
          ),
          child: isPaymentSuccess
              ? Icon(
            Icons.check_rounded,
            color: AppAssets.origin().whiteBackgroundColor,
          )
              : Icon(
            Icons.warning_amber_rounded,
            color: AppAssets.origin().whiteBackgroundColor,
          ),
        ),
        titleFirstButton: isPaymentSuccess
            ? context.strings.view_order_history
            : context.strings.text_try_again,
        titleSecondButton: isPaymentSuccess
            ? context.strings.text_back_to_home.toSentenceCase()
            : null,
        onFirstButtonTap: () {
          if (!isPaymentSuccess) {
            Navigator.pop(context);
          } else {
            if (orderId == 0) {
              getIdByCode(context, ref);
            } else {
              _handleGoToOrderDetail(context, ref, orderId);
            }
          }
        },
        onSecondButtonTap: () {
          ref.read(pTicketTourProvider.notifier).resetState();
          ref.read(pPaymentProvider.notifier).resetState();
          Navigator.popUntil(
            context,
            ModalRoute.withName(AppRoute.routeHome),
          );
        },
        contentPadding: EdgeInsets.symmetric(horizontal: 12.W)
            .copyWith(top: 20.H, bottom: 20.H),
        child: TripcText(
          isPaymentSuccess
              ? context.strings.text_dont_forget_share_moment
              : context.strings.text_please_try_again,
          fontSize: 14,
          height: 1.42,
          fontWeight: FontWeight.w400,
          textColor: AppAssets.origin().black,
        ),
      ),
    );
  }

  Future<void> getIdByCode(BuildContext context, WidgetRef ref) async {
    final result = await ref.read(pPaymentProvider.notifier).getIdByCode();
    if (result == 0) {
      final errorMessage =
      ref.watch(pPaymentProvider.select((value) => value.errorMessage));
      dialogHelpers.show(context,
          child: TripcErrorDialog(errorText: errorMessage));
    } else {
      _handleGoToOrderDetail(context, ref, result);
    }
  }

  void _handleGoToOrderDetail(
      BuildContext context, WidgetRef ref, int orderId) {
    ref.read(pTicketTourProvider.notifier).resetState();
    ref.read(pPaymentProvider.notifier).resetState();
    ref
        .read(pAppBottomNavProvider.notifier)
        .setTab(TripCPersistentTabType.mytrip);
    AppRoute.pushNamed(context,
        routeName: AppRoute.routeMyTripDetailedTourV2,
        arguments: BookingDetail(orderId: orderId));
  }
}
