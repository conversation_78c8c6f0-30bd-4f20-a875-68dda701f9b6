import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/promotional_code.dart';
import 'package:tripc_app/pages/tour-payment/components/payment_promotional_code_voucher.dart';
import 'package:tripc_app/pages/tour-payment/providers/payment_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_empty.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_filed.dart';

class TripcPromotionalCodePage extends ConsumerWidget {
  const TripcPromotionalCodePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final promotionalCodes =
        ref.watch(pPaymentProvider.select((value) => value.promotionalCodes));
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        resizeToAvoidBottomInset: false,
        hasBackButton: true,
        titleAppBar: TripcText(
          context.strings.text_select_promotional_code,
          fontWeight: FontWeight.w600,
          fontSize: 16,
          textColor: AppAssets.origin().black,
        ),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(8.H),
          child: Container(
            color: AppAssets.origin().appBarUnderline,
            height: 1.H,
          ),
        ),
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.W).copyWith(top: 26.H),
          child: Column(
            children: [
              SizedBox(
                height: 56.H,
                child: TripcTextField(
                  // height: 40.H,
                  hintText: context.strings.text_enter_promotional_code,
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                  contentPadding: EdgeInsets.symmetric(horizontal: 11.W),
                  suffix: TripcButton(
                    width: 91.W,
                    // height: 40.H,
                    titlePadding: EdgeInsets.zero,
                    title: context.strings.text_apply,
                    style: const AppButtonStyle(radius: 5),
                  ),
                ),
              ),
              AppEmpty(message: context.strings.text_promotional_empty_message)
              // promotionalCodes.isEmpty
              //     ? Padding(
              //         padding: EdgeInsets.only(top: 68.H),
              //         child: const PromotionalCodeEmptyView(),
              //       )
              //     : Expanded(
              //         child: _buildPromotionalCodeVouchers(promotionalCodes))
            ],
          ),
        ));
  }

  Widget _buildPromotionalCodeVouchers(List<PromotionalCode> promotionalCodes) {
    return ListView.separated(
        itemCount: promotionalCodes.length,
        padding: EdgeInsets.symmetric(vertical: 20.H),
        separatorBuilder: (context, _) => SizedBox(height: 20.H),
        itemBuilder: (context, index) => PromotionalCodeVoucher(
              onTapDetail: () {},
              onTapGet: () {},
              promotionalCode: promotionalCodes[index],
            ));
  }
}
