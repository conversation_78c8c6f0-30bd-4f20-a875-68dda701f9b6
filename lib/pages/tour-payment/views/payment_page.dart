import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/payment_method_enum.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/ticket_tour/components/tour_information_item.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/pages/tour-payment/components/payment_method_selection.dart';
import 'package:tripc_app/pages/tour-payment/components/promotional_code_input.dart';
import 'package:tripc_app/pages/tour-payment/providers/payment_provider.dart';
import 'package:tripc_app/pages/tour-payment/views/electronic_invoice.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/total_price/total_price_area.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../utils/app_error_string.dart';
import '../../../widgets/app_loading.dart';
import '../../../widgets/commons/app_dialog/tripc_error_dialog.dart';
import '../../../widgets/commons/tripc_button/tripc_button.dart';
import '../../ticket_tour/components/cancel_payment_bottom_sheet.dart';
import '../components/Invoice_status_view.dart';

class TripcPaymentPage extends ConsumerStatefulWidget {
  const TripcPaymentPage({super.key});

  @override
  ConsumerState<TripcPaymentPage> createState() => _TripcPaymentPageState();
}

class _TripcPaymentPageState extends ConsumerState<TripcPaymentPage> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        ref.listenManual(
            pPaymentProvider.select((state) => state.isPaymentSuccess),
            (_, state) {
          if (!state) {
            return;
          }
          AppRoute.pushNamed(context,
              routeName: AppRoute.routeTourPaymentSuccess, arguments: true);
        });
        ref.listenManual(
            pPaymentProvider.select((state) => state.isPaymentError),
            (_, state) {
          if (!state) {
            return;
          }
          AppRoute.pushNamed(context,
              routeName: AppRoute.routeTourPaymentSuccess, arguments: false);
        });
        ref.listenManual(pPaymentProvider.select((state) => state.errorMessage),
            (_, state) {
          if (state?.isEmpty ?? true) {
            return;
          }
          dialogHelpers.show(context,
              child: TripcErrorDialog(
                  errorText:
                      ErrorParser.getErrorMessage(state ?? '', context)));
        });
        ref.listenManual(
            pPaymentProvider.select((state) => state.payOSWebViewUrl),
            (_, url) {
          if (url != null) {
            AppRoute.pushNamed(context,
                routeName: AppRoute.routePayOSWebView, arguments: url);
          }
        });
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // final bookingCode = ref.watch(pPaymentProvider
    //         .select((value) => value.currentPayment?.bookingCode)) ??
    //     '';
    final selectedTour = ref.watch(
        pPaymentProvider.select((value) => value.currentPayment?.service));
    final selectedDate = ref.watch(pPaymentProvider
        .select((value) => value.currentPayment?.departureDate));
    final ticketQuantity = ref.watch(pTicketTourProvider
        .select((value) => value.ticketQuantityText(context)));
    final selectTour =
        ref.watch(pTicketTourProvider.select((value) => value.selectedTour));
    final totalPayment = ref.watch(pPaymentProvider
            .select((value) => value.currentPayment?.totalPrice)) ??
        0;
    final currentPaymentMethod = ref
        .watch(pPaymentProvider.select((value) => value.currentPaymentMethod));
    final tcentPoint = ref.watch(pAccountProvider
        .select((value) => value.user?.selectedMembership?.tcent ?? 0));
    final promotionalCodes =
        ref.watch(pPaymentProvider.select((value) => value.promotionalCodes));
    final isLoading =
        ref.watch(pPaymentProvider.select((value) => value.isLoading));
    final currentPayment =
        ref.watch(pPaymentProvider.select((value) => value.currentPayment));
    final invoiceRequest =
        ref.watch(pPaymentProvider.select((value) => value.invoiceRequest));
    return Stack(
      children: [
        TripcScaffold(
          onPopScope: () {
            ref.read(pPaymentProvider.notifier).resetState();
          },
          onPressed: () => unfocusKeyboard(),
          onLeadingPressed: () {
            ref.read(pPaymentProvider.notifier).resetState();
            Navigator.pop(context);
          },
          resizeToAvoidBottomInset: false,
          hasBackButton: true,
          // toolbarHeight: 62.H,
          titleAppBar: Padding(
            padding: EdgeInsets.only(top: 12.H),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                TripcText(
                  context.strings.text_pay,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  textColor: AppAssets.origin().black,
                  padding: EdgeInsets.only(bottom: 3.H),
                ),
                // TripcText(
                //   context.strings.text_booking_code(bookingCode),
                //   fontWeight: FontWeight.w300,
                //   fontSize: 14,
                //   textColor: AppAssets.origin().black,
                // ),
              ],
            ),
          ),
          backgroundColor: AppAssets.origin().whiteBackgroundColor,
          body: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 8.H)
                  .copyWith(bottom: context.mediaQuery.padding.bottom),
              child: Column(
                children: [
                  Column(
                    children: [
                      _tourInformation(context,
                          selectedTour: selectedTour,
                          startDate: selectedDate,
                          ticketQuantity: ticketQuantity),
                      SizedBox(height: 20.H),
                      PromotionalCodeInput(
                        quantity: promotionalCodes.length,
                        onTap: () => AppRoute.pushNamed(context,
                            routeName: AppRoute.routeTripcPromotionalCode),
                      ),
                      SizedBox(height: 16.H),
                      Visibility(
                        visible: selectTour?.isAllowedRefund ?? false,
                        child: TripcButton(
                          onPressed: () => bottomSheetHelpers.show(context,
                              backgroundColor: Colors.white,
                              child: const CancelPaymentBottomSheet()),
                          title: context.strings.text_cancel_free,
                          height: 50,
                          titlePadding: EdgeInsets.symmetric(horizontal: 16.W),
                          spacing: 12,
                          buttonType: ButtonType.outline,
                          titleAlignment: MainAxisAlignment.start,
                          style: AppButtonStyle(
                            borderColor: AppAssets.origin().lightBlueFD,
                            textStyle: AppAssets.origin()
                                .normalTextStyle
                                .copyWith(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w300,
                                    color: AppAssets.origin().lightBlueFD),
                          ),
                          prefixIcon: AppAssets.origin()
                              .icCancelDoc
                              .widget(height: 20.H, width: 20.H),
                        ),
                      ),
                      Visibility(
                          visible: selectTour?.isExportInvoice ?? true,
                          child: Padding(
                              padding: EdgeInsets.only(top: 12.H),
                              child: InvoiceStatusView(
                                status:
                                    (invoiceRequest?.name?.isNotEmpty ?? false)
                                        ? InvoiceStatus.processing
                                        : InvoiceStatus.request,
                                onTap: () => AppRoute.pushNamed(context,
                                    routeName: AppRoute.routeElectronicInvoice,
                                    arguments: true),
                              ))),
                      _paymentMethodList(context, currentPaymentMethod,
                          selectedTour: selectedTour,
                          tcentPoint: tcentPoint,
                          onTapCheck: (method) => ref
                              .read(pPaymentProvider.notifier)
                              .selectPaymentMethod(method)),
                    ],
                  ),
                  SizedBox(height: 26.H),
                  TotalPriceArea(
                      isReceiveBottom: false,
                      onTap: () {
                        ref
                            .read(pPaymentProvider.notifier)
                            .onPayment(selectTour, context);
                        if (currentPayment?.passengerInfo?.contactResponse ==
                            null) {
                          ref.read(pPaymentProvider.notifier).addContact();
                        }
                        if (selectTour?.isExportInvoice == true) {
                          ref
                              .read(pPaymentProvider.notifier)
                              .resetStateInvoice();
                        }
                      },
                      titleButton: context.strings.text_pay,
                      total: totalPayment)
                ],
              )),
        ),
        AppLoading(
          isRequesting: isLoading,
          // backgroundColor: Colors.transparent,
        ),
      ],
    );
  }

  Widget _tourInformation(BuildContext context,
      {required TourResponse? selectedTour,
      required DateTime? startDate,
      required String ticketQuantity}) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          TripcText(
            selectedTour?.name ?? '',
            fontSize: 16,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
          ),
          TourInformationItem(
            title: '${context.strings.text_departure_date}:',
            value: startDate.formatddMMYYY,
            fontSize: 14,
            fontWeight: FontWeight.w300,
            padding: EdgeInsets.symmetric(vertical: 12.H),
          ),
          TripcText(
            ticketQuantity,
            fontSize: 14,
            textAlign: TextAlign.start,
            fontWeight: FontWeight.w300,
            textColor: AppAssets.origin().blackColor,
          ),
        ],
      ),
    );
  }

  Widget _paymentMethodList(BuildContext context, PaymentMethod? currentMethod,
      {Function(PaymentMethod)? onTapCheck,
      required int tcentPoint,
      TourResponse? selectedTour}) {
    double fontSize = 12.SP;
    const FontWeight fontWeight = FontWeight.w300;
    final bool disableTcent = tcentPoint < 10000;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_payment_method,
          fontSize: 14,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w500,
          textColor: AppAssets.origin().blackColor,
          padding: EdgeInsets.only(top: 18.H, bottom: 8.H),
        ),
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          PaymentMethodSelection(
              onTapCheck: () => onTapCheck?.call(PaymentMethod.paylate),
              method: PaymentMethod.paylate,
              value: PaymentMethod.paylate == currentMethod,
              padding: EdgeInsets.only(left: 8.W, bottom: 10.H)),
          PaymentMethodSelection(
              onTapCheck: disableTcent
                  ? null
                  : () => onTapCheck?.call(PaymentMethod.tcent),
              method: PaymentMethod.tcent,
              value: PaymentMethod.tcent == currentMethod,
              padding: EdgeInsets.only(left: 8.W, bottom: 10.H),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TripcText(
                    PaymentMethod.tcent.title(context),
                    fontSize: 12,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w400,
                    textColor: disableTcent
                        ? AppAssets.origin().greyTextColorC8
                        : AppAssets.origin().blackColor,
                    padding: EdgeInsets.only(left: 10.W),
                  ),
                  TripcRichText(
                    text: '',
                    textAlign: TextAlign.start,
                    children: [
                      TextSpan(
                        text: ' (',
                        style: AppAssets.origin().normalTextStyle.copyWith(
                            fontSize: 12.SP,
                            color: disableTcent
                                ? AppAssets.origin().greyTextColorC8
                                : AppAssets.origin().blackColor),
                      ),
                      TextSpan(
                        text: context.strings.text_you_are_having,
                        style: AppAssets.origin().superBoldTextStyle.copyWith(
                            fontSize: 12.SP,
                            color: disableTcent
                                ? AppAssets.origin().greyTextColorC8
                                : AppAssets.origin().blackColor),
                      ),
                      TextSpan(
                        text: tcentPoint.tcent,
                        style: AppAssets.origin().boldTextStyle.copyWith(
                            fontSize: 12.SP,
                            color: disableTcent
                                ? AppAssets.origin().greyTextColorC8
                                : AppAssets.origin().secondDarkYellow),
                      ),
                      TextSpan(
                        text: ')',
                        style: AppAssets.origin().normalTextStyle.copyWith(
                            fontSize: 12.SP,
                            color: disableTcent
                                ? AppAssets.origin().greyTextColorC8
                                : AppAssets.origin().blackColor),
                      ),
                    ],
                  ),
                ],
              )),
          //   PaymentMethodSelection(
          //       onTapCheck: !(selectedTour?.paymentOptions?.vnpay ?? false)
          //           ? null
          //           : () => onTapCheck?.call(PaymentMethod.vnpay),
          //       method: PaymentMethod.vnpay,
          //       value: PaymentMethod.vnpay == currentMethod,
          //       textColor: (selectedTour?.paymentOptions?.vnpay ?? false)
          //           ? AppAssets.origin().blackColor
          //           : AppAssets.origin().greyTextColorC8,
          //       padding: EdgeInsets.only(left: 8.W, bottom: 10.H)),
          PaymentMethodSelection(
              onTapCheck: () => onTapCheck?.call(PaymentMethod.payos),
              method: PaymentMethod.payos,
              value: PaymentMethod.payos == currentMethod,
              textColor: AppAssets.origin().blackColor,
              padding: EdgeInsets.only(left: 8.W, bottom: 10.H)),
        ]),
        TripcRichText(
          text: '',
          padding: EdgeInsets.only(top: 8.H),
          textAlign: TextAlign.start,
          lineHeight: 1.4,
          children: [
            TextSpan(
              text: context.strings.text_by_clicking_pay_button,
              style: AppAssets.origin().normalTextStyle.copyWith(
                  fontSize: fontSize,
                  fontWeight: fontWeight,
                  color: AppAssets.origin().secondDarkGreyTextColor),
            ),
            TextSpan(
                text: context.strings.text_terms_of_use,
                style: AppAssets.origin().normalTextStyle.copyWith(
                    fontWeight: fontWeight,
                    fontSize: fontSize,
                    color: AppAssets.origin().darkBlueColor),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    AppRoute.pushNamed(context,
                        routeName: AppRoute.routeProfileTermAndCondition);
                  }),
            TextSpan(
              text: ' & ',
              style: AppAssets.origin().normalTextStyle.copyWith(
                  fontWeight: fontWeight,
                  fontSize: fontSize,
                  color: AppAssets.origin().secondDarkGreyTextColor),
            ),
            TextSpan(
                text: context.strings.text_conditions_and_privacy,
                style: AppAssets.origin().normalTextStyle.copyWith(
                    fontWeight: fontWeight,
                    fontSize: fontSize,
                    color: AppAssets.origin().darkBlueColor),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    AppRoute.pushNamed(context,
                        routeName: AppRoute.routeProfilePrivacy);
                  }),
            TextSpan(
              text: context.strings.text_of_tripc,
              style: AppAssets.origin().normalTextStyle.copyWith(
                  fontWeight: fontWeight,
                  fontSize: fontSize,
                  color: AppAssets.origin().secondDarkGreyTextColor),
            )
          ],
        ),
      ],
    );
  }
}
