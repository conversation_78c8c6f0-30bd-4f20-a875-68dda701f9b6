import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/payos_enum.dart';

class PayOSPaymentScreenModel {
  PayOSPaymentScreenModel(
      {this.isPaymentSuccess, this.isPaymentPending, this.isPaymentCancelled});

  final bool? isPaymentSuccess;
  final bool? isPaymentPending;
  final bool? isPaymentCancelled;

  static PayOSPaymentScreenModel getDefault() {
    return PayOSPaymentScreenModel();
  }

  PayOSPaymentScreenModel copyWith(
      {bool? isPaymentSuccess,
      bool? isPaymentPending,
      bool? isPaymentCancelled}) {
    return PayOSPaymentScreenModel(
        isPaymentSuccess: isPaymentSuccess,
        isPaymentPending: isPaymentPending,
        isPaymentCancelled: isPaymentCancelled);
  }
}

class PayOSPaymentScreenProvider
    extends StateNotifier<PayOSPaymentScreenModel> {
  PayOSPaymentScreenProvider(super._state);

  void handleReturnUrl(Uri url) {
    final paymentStatus =
        getPayOSPaymentStatus(url.queryParameters['status'] ?? '');
    final cancelStatus =
        getPayOSCancleStatus(url.queryParameters['cancel'] ?? '');

    if (paymentStatus == PayOSPaymentStatus.unknown ||
        cancelStatus == PayOSCancleStatus.unknown) {
      state = state.copyWith(isPaymentSuccess: false);
      return;
    }
    if (cancelStatus == PayOSCancleStatus.cancelled) {
      if (paymentStatus == PayOSPaymentStatus.cancelled) {
        state = state.copyWith(isPaymentCancelled: true);
        return;
      } else {
        state = state.copyWith(isPaymentSuccess: false);
        return;
      }
    }
    if (cancelStatus == PayOSCancleStatus.notYetCancelled) {
      if (paymentStatus == PayOSPaymentStatus.paid) {
        state = state.copyWith(isPaymentSuccess: true);

        return;
      } else {
        state = state.copyWith(isPaymentPending: true);
        return;
      }
    }
  }

  void resetState() {
    state = state.copyWith(
        isPaymentSuccess: null,
        isPaymentPending: null,
        isPaymentCancelled: null);
  }
}

final pPayOSPaymentProvider =
    StateNotifierProvider<PayOSPaymentScreenProvider, PayOSPaymentScreenModel>(
        (ref) =>
            PayOSPaymentScreenProvider(PayOSPaymentScreenModel.getDefault()));
