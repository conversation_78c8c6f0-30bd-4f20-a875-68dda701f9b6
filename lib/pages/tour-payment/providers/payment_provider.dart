import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/payment.dart';
import 'package:tripc_app/models/app/payment_method_enum.dart';
import 'package:tripc_app/models/app/payment_request.dart';
import 'package:tripc_app/models/app/promotional_code.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/tour-payment/views/electronic_invoice.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:vnpay_flutter/vnpay_flutter.dart';

import '../../../models/app/contact_request.dart';
import '../../../models/remote/common_error.dart';
import '../../../services/apis/auth/api_user_login.dart';
import '../../../services/apis/order/api_order.dart';

class PaymentScreenModel {
  PaymentScreenModel(
      {this.currentPayment,
      this.promotionalCodes = const [],
      this.currentPaymentMethod,
      this.errorMessage,
      this.isLoading = false,
      this.isPaymentSuccess = false,
      this.isPaymentError = false,
      this.currentInvoice = InvoiceType.individual,
      this.name = '',
      this.address = '',
      this.taxCode = '',
      this.email = '',
      this.phoneNo = '',
      this.orderId = 0,
      this.txnRef,
      this.invoiceRequest,
      this.payOSWebViewUrl});

  final Payment? currentPayment;
  final List<PromotionalCode> promotionalCodes;
  final PaymentMethod? currentPaymentMethod;
  final String? errorMessage;
  final bool isLoading;
  final bool isPaymentSuccess;
  final bool isPaymentError;
  final InvoiceType currentInvoice;
  final String name;
  final String address;
  final String taxCode;
  final String phoneNo;
  final String email;
  final int orderId;
  final String? txnRef;
  final InvoiceRequest? invoiceRequest;
  final String? payOSWebViewUrl;

  static PaymentScreenModel getDefault() {
    return PaymentScreenModel(
        //   promotionalCodes: [
        //   PromotionalCode(
        //     discountPercent: 5,
        //     code: 'PROMO-1234',
        //     saving: 70000,
        //     startTime: DateTime(2025, 1, 20, 17),
        //     endTime: DateTime(2025, 1, 24, 23, 59),
        //     note: 'Chỉ dành cho Tour đầu tiên của bạn tại TripC',
        //   ),
        // ]
        );
  }

  PaymentScreenModel copyWith(
      {Payment? currentPayment,
      List<PromotionalCode>? promotionalCodes,
      PaymentMethod? currentPaymentMethod,
      String? errorMessage,
      bool? isLoading,
      bool? isPaymentSuccess,
      bool? isPaymentError,
      InvoiceType? currentInvoice,
      String? name,
      String? address,
      String? taxCode,
      String? email,
      int? orderId,
      String? txnRef,
      String? phoneNo,
      InvoiceRequest? invoiceRequest,
      String? payOSWebViewUrl}) {
    return PaymentScreenModel(
        currentPayment: currentPayment ?? this.currentPayment,
        promotionalCodes: promotionalCodes ?? this.promotionalCodes,
        currentPaymentMethod: currentPaymentMethod ?? this.currentPaymentMethod,
        errorMessage: errorMessage ?? this.errorMessage,
        isLoading: isLoading ?? this.isLoading,
        isPaymentSuccess: isPaymentSuccess ?? this.isPaymentSuccess,
        isPaymentError: isPaymentError ?? this.isPaymentError,
        currentInvoice: currentInvoice ?? this.currentInvoice,
        name: name ?? this.name,
        address: address ?? this.address,
        taxCode: taxCode ?? this.taxCode,
        email: email ?? this.email,
        orderId: orderId ?? this.orderId,
        txnRef: txnRef ?? this.txnRef,
        phoneNo: phoneNo ?? this.phoneNo,
        invoiceRequest: invoiceRequest ?? this.invoiceRequest,
        payOSWebViewUrl: payOSWebViewUrl);
  }

  PaymentScreenModel copyWithPaymentMethod({
    PaymentMethod? paymentMethod,
  }) {
    return PaymentScreenModel(
        currentPaymentMethod: paymentMethod,
        currentPayment: currentPayment,
        promotionalCodes: promotionalCodes,
        errorMessage: errorMessage,
        isLoading: isLoading,
        isPaymentSuccess: isPaymentSuccess,
        isPaymentError: isPaymentError,
        currentInvoice: currentInvoice,
        name: name,
        address: address,
        taxCode: taxCode,
        email: email,
        orderId: orderId,
        txnRef: txnRef,
        phoneNo: phoneNo,
        invoiceRequest: invoiceRequest,
        payOSWebViewUrl: payOSWebViewUrl);
  }

  bool get isButtonDisable {
    if (currentInvoice == InvoiceType.business) {
      return email.isNotEmpty &&
          name.isNotEmpty &&
          address.isNotEmpty &&
          taxCode.isNotEmpty;
    } else {
      return email.isNotEmpty && name.isNotEmpty && address.isNotEmpty;
    }
  }

  String ticketQuantityText(BuildContext context) {
    return '${currentPayment?.aldultQuantity} ${context.strings.text_aldult}, ${{
      currentPayment?.childQuantity
    }} ${context.strings.text_children}';
  }
}

class PaymentScreenProvider extends StateNotifier<PaymentScreenModel> {
  PaymentScreenProvider(super._state);
  final ApiOrder _apiOrder = ApiOrder();
  final ApiUser _api = ApiUser();

  void setLoading(bool value) {
    state = state.copyWith(
      isLoading: value,
    );
  }

  void selectInvoiceType(InvoiceType type) {
    state = state.copyWith(
      currentInvoice: type,
    );
  }

  void createPayment(Payment payment) {
    state = state.copyWith(currentPayment: payment);
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void setEmail(String value) {
    state = state.copyWith(
      email: value,
    );
  }

  void setName(String value) {
    state = state.copyWith(
      name: value,
    );
  }

  void setPhoneNo(String value) {
    state = state.copyWith(
      phoneNo: value,
    );
  }

  void setTaxCode(String value) {
    state = state.copyWith(
      taxCode: value,
    );
  }

  void setAddress(String value) {
    state = state.copyWith(
      address: value,
    );
  }

  void setTxnRef(String? txnRef) {
    state = state.copyWith(txnRef: txnRef);
  }

  void resetStateInvoice() {
    state = state.copyWith(
      email: '',
      name: '',
      address: '',
      taxCode: '',
    );
  }

  void selectPaymentMethod(PaymentMethod? paymentMethod) {
    state = state.copyWithPaymentMethod(
      paymentMethod: paymentMethod,
    );
  }

  void updateInvoiceRequest() {
    state = state.copyWith(
        invoiceRequest: InvoiceRequest(
      type: state.currentInvoice.name,
      address: state.address,
      name: state.name,
      phoneNo: state.phoneNo,
      taxCode:
          state.currentInvoice == InvoiceType.individual ? '' : state.taxCode,
      email: state.email,
    ));
  }

  void onPayment(TourResponse? selectTour, BuildContext context) {
    setLoading(true);
    state = state.copyWith(errorMessage: '');
    final passengers =
        state.currentPayment?.passengerInfo?.passengers.toList() ?? [];
    final subProducts =
        state.currentPayment?.service?.subProducts?.toList() ?? [];
    final List<OrderItem> orders = subProducts.map(
      (e) {
        return OrderItem(
          subProductId: e.id,
          customer: passengers
              .where(
            (element) => element.subProductId == e.id,
          )
              .map(
            (e) {
              return Customer(
                email: e.email,
                fullname: e.name,
                phoneNumber: e.phone,
                guardianName: e.guardianName,
                type: e.type,
              );
            },
          ).toList(),
          quantity: e.stock,
        );
      },
    ).toList();
    final List<AttributeRequest> attributeRequest = [];
    selectTour?.attributes?.forEach(
      (element) {
        element.values?.forEach(
          (e) {
            if (e == state.currentPayment?.selectHotTime ||
                e == state.currentPayment?.selectSeat) {
              attributeRequest.add(
                  AttributeRequest(id: element.attributeId, valueId: e.id));
            }
          },
        );
      },
    );
    final shuttleService = ShuttleService(
      contact: state.currentPayment?.passengerInfo?.shuttleInfo?.contact,
      expectedSchedule:
          state.currentPayment?.passengerInfo?.shuttleInfo?.expected,
      pickupLocation: state.currentPayment?.passengerInfo?.shuttleInfo?.hotel,
      pickupTime: state.currentPayment?.passengerInfo?.shuttleInfo?.time,
    );
    final paymentRequest = PaymentRequest(
      productId: selectTour?.id,
      description: state.currentPayment?.noteText(context),
      shuttleService: shuttleService,
      departureTime:
          state.currentPayment?.departureDate.toUtc().toIso8601String(),
      order: orders,
      invoice: state.invoiceRequest,
      attributes: attributeRequest,
      // BE open when no time sended
      // time: state.currentPayment?.selectedTime,
      time: state.currentPayment?.selectedTime ?? "00:00",
      date: state.currentPayment?.selectedDate?.formatyyyyMMdd(),
    );
    switch (state.currentPaymentMethod) {
      case PaymentMethod.paylate:
        createOrderTourPayLater(paymentRequest);
      case PaymentMethod.vnpay:
        createOrderTourWithVNPay(paymentRequest);
      case PaymentMethod.tcent:
        createOrderTourWithTcent(paymentRequest);
      case PaymentMethod.payos:
        createOrderTourWithPayOS(paymentRequest);
      default:
        break;
    }
  }

  Future<void> createOrderTourWithTcent(PaymentRequest request) async {
    state = state.copyWith(isPaymentSuccess: false, isPaymentError: false);
    try {
      final result =
          await _apiOrder.createOrderTourWithTcent(request: request).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      setLoading(false);
      state =
          state.copyWith(isPaymentSuccess: true, orderId: result.data?.orderId);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> createOrderTourWithVNPay(PaymentRequest request) async {
    state = state.copyWith(isPaymentSuccess: false, isPaymentError: false);
    try {
      final result =
          await _apiOrder.createOrderTourWithVNPay(request: request).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      openWebViewVNPay(result.data ?? '');
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> createOrderTourWithPayOS(PaymentRequest request) async {
    state = state.copyWith(
        isPaymentSuccess: false,
        isPaymentError: false,
        payOSWebViewUrl: null,
        orderId: 0);
    try {
      final result =
          await _apiOrder.createOrderTourWithPayOS(request: request).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      state = state.copyWith(
          payOSWebViewUrl: result.data?.paymentUrl,
          orderId: result.data?.orderId,
          isLoading: false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> openWebViewVNPay(String url) async {
    state = state.copyWith(isPaymentSuccess: false, isPaymentError: false);
    await VNPAYFlutter.instance.show(
      paymentUrl: url,
      onPaymentSuccess: (params) {
        setLoading(false);
        state = state.copyWith(isPaymentSuccess: true);
        setTxnRef(params["vnp_TxnRef"]);
      },
      onPaymentError: (params) {
        state = state.copyWith(isPaymentError: true);
        if (!state.isLoading) {
          paymentFailure(params);
        }
      },
    );
  }

  Future<void> paymentFailure(Map<String, dynamic> request) async {
    setErrorMessage('');
    setLoading(true);
    try {
      await _apiOrder.paymentFailure(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> createOrderTourPayLater(PaymentRequest request) async {
    state = state.copyWith(isPaymentSuccess: false, isPaymentError: false);
    try {
      final result =
          await _apiOrder.createOrderTourPayLater(request: request).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      setLoading(false);
      state =
          state.copyWith(isPaymentSuccess: true, orderId: result.data?.orderId);
    } catch (exceptionMessage) {
      setLoading(false);
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
    }
  }

  Future<int> getIdByCode({String orderCode = ''}) async {
    setLoading(true);
    state = state.copyWith(
      orderId: 0,
    );
    try {
      final result = await _apiOrder
          .getIdByCode(txnRef: orderCode.isEmpty ? state.txnRef : orderCode)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      return result.data?.orderId ?? 0;
    } catch (exceptionMessage) {
      setLoading(false);
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      return 0;
    }
  }

  Future<bool> addContact() async {
    try {
      final result = await _api
          .addContact(
              request: ContactRequest(
                  fullname: state.currentPayment?.passengerInfo?.name ?? '',
                  email: state.currentPayment?.passengerInfo?.email ?? '',
                  phone:
                      (state.currentPayment?.passengerInfo?.phoneNumber ?? '')
                          .addLeadingZero(),
                  isDefault: 1))
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );

      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      return false;
    }
  }

  void resetState() {
    state = PaymentScreenModel.getDefault();
  }
}

final pPaymentProvider =
    StateNotifierProvider<PaymentScreenProvider, PaymentScreenModel>(
        (ref) => PaymentScreenProvider(PaymentScreenModel.getDefault()));
