import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_circle_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class GetStartedButton extends StatelessWidget {
  const GetStartedButton({
    super.key,
    this.onPressed,
  });
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(bottom: 91.H),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(100.SP),
              child: Blur(
                blur: 16,
                colorOpacity: 0.15,
                child: Container(
                  decoration: BoxDecoration(
                      color: AppAssets.origin().slideButtonColor,
                      borderRadius: BorderRadius.circular(100.SP),
                      gradient: AppAssets.origin().slideButtonGradient),
                  height: 71.H,
                  width: 344.W,
                ),
              ),
            ),
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.SP),
                  border: Border.all(
                      color: AppAssets.origin().whiteColor20, width: 1.2.H),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TripcText(
                      context.strings.text_get_started,
                      fontSize: 18.SP,
                      textColor: AppAssets.origin().whiteBackgroundColor,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(left: 32.W),
                    ),
                    TripcCircleButton(
                      onPressed: onPressed,
                      margin: EdgeInsets.only(right: 5.W),
                      icon: AppAssets.origin()
                          .iconDoubleArrowRight
                          .widget(height: 12.H, width: 12.H),
                    )
                  ],
                ),
              ),
            )
          ],
        ));
  }
}
