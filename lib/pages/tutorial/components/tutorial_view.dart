import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/tutorial_model.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TutorialView extends StatelessWidget {
  const TutorialView({
    super.key,
    required this.tutorialModel,
  });
  final TutorialModel tutorialModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 32.W),
      decoration: BoxDecoration(
          image: DecorationImage(
              image: AssetImage(tutorialModel.image), fit: BoxFit.cover)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TripcText(
            tutorialModel.title,
            fontSize: 34,
            fontWeight: FontWeight.w600,
            textColor: AppAssets.origin().whiteBackgroundColor,
          ),
          TripcText(
            tutorialModel.content,
            fontSize: 14,
            height: 1.5,
            fontWeight: FontWeight.w500,
            textColor: AppAssets.origin().whiteBackgroundColor,
            padding: EdgeInsets.only(top: 100.H),
          ),
          SizedBox(
            height: 214.H,
          )
        ],
      ),
    );
  }
}
