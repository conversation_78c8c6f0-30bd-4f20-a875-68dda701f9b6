import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_circle_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../tutorial.dart';

class TutorialScreen extends ConsumerStatefulWidget {
  const TutorialScreen({super.key});

  @override
  ConsumerState<TutorialScreen> createState() => _TutorialScreenState();
}

class _TutorialScreenState extends ConsumerState<TutorialScreen> {
  final _pageController = PageController(initialPage: 0);
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(pTutorialScreenProvider.notifier).resetState(context);
    });
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  void _skip() {
    ref.read(pTutorialScreenProvider.notifier).resetState(context);
    Navigator.pushReplacementNamed(context, AppRoute.routeHome);
  }

  @override
  Widget build(BuildContext context) {
    final tutorials =
        ref.watch(pTutorialScreenProvider.select((state) => state.tutorials));
    final activeIndex = ref
        .watch(pTutorialScreenProvider.select((state) => state.currentIndex));
    return TripcScaffold(
      visibleAppBar: false,
      backgroundColor: Colors.transparent,
      canPop: false,
      body: Stack(
        children: [
          PageView.builder(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              onPageChanged: (index) => ref
                  .read(pTutorialScreenProvider.notifier)
                  .changeCurrentIndex(index),
              itemCount: tutorials.length,
              itemBuilder: (context, index) {
                return TutorialView(
                  tutorialModel: tutorials[index],
                );
              }),
          Visibility(
            visible: activeIndex != 2,
            child: Positioned(
              right: 28.W,
              top: context.mediaQuery.viewPadding.top + 12.H,
              child: TripcText(
                  onTap: _skip,
                  context.strings.text_skip,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  textColor: activeIndex == 0
                      ? AppAssets.origin().blackColor
                      : AppAssets.origin().whiteBackgroundColor),
            ),
          ),
          Visibility(
            visible: activeIndex != 2,
            replacement: Align(
                alignment: Alignment.bottomCenter,
                child: GetStartedButton(onPressed: _skip)),
            child: Positioned(
              left: 0,
              right: 0,
              bottom: 52.H,
              top: context.mediaQuery.viewPadding.top + 12.H,
              child: _buildNextButton(indicatorLength: tutorials.length),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextButton({required int indicatorLength}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TripcCircleButton(
          onPressed: () => _pageController.nextPage(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut),
          icon: AppAssets.origin()
              .iconArrowRight
              .widget(height: 12.H, width: 6.W),
        ),
        SizedBox(
          height: 41.H,
        ),
        SmoothPageIndicator(
            controller: _pageController,
            count: indicatorLength,
            effect: CustomizableEffect(
              dotDecoration: DotDecoration(
                width: 8.H,
                height: 8.H,
                color: AppAssets.origin().whiteSmokeColor,
                borderRadius: BorderRadius.circular(8.SP),
              ),
              spacing: 8.W,
              activeDotDecoration: DotDecoration(
                width: 24.H,
                height: 8.H,
                borderRadius: BorderRadius.circular(8.SP),
                color: AppAssets.origin().secondaryColor,
              ),
            )),
      ],
    );
  }
}
