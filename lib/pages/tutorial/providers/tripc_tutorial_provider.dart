import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/tutorial_model.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

class TutorialScreenModel {
  List<TutorialModel> tutorials;
  int currentIndex;
  TutorialScreenModel({this.tutorials = const [], this.currentIndex = 0});
  static TutorialScreenModel getDefault(BuildContext context) {
    return TutorialScreenModel(tutorials: [
      TutorialModel(
        title: context.strings.text_explore_viet_nam,
        image: AppAssets.origin().imTutorialBG1New.assetPath,
        content: context.strings.text_explore_viet_nam_content,
      ),
      TutorialModel(
          title: context.strings.text_choose_destination,
          image: AppAssets.origin().imTutorialBG2New.assetPath,
          content: context.strings.text_choose_destination_content),
      TutorialModel(
        title: context.strings.text_enjoy_your_trip,
        image: AppAssets.origin().imTutorialBG3New.assetPath,
        content: context.strings.text_choose_destination_content,
      ),
    ]);
  }

  TutorialScreenModel copyWith(
      {List<TutorialModel>? tutorials, int? currentIndex}) {
    return TutorialScreenModel(
        tutorials: tutorials ?? this.tutorials,
        currentIndex: currentIndex ?? this.currentIndex);
  }
}

class TutorialScreenProvider extends StateNotifier<TutorialScreenModel> {
  TutorialScreenProvider(super._state);

  void changeCurrentIndex(int index) {
    state = state.copyWith(currentIndex: index);
  }

  void resetState(BuildContext context) {
    state = TutorialScreenModel.getDefault(context);
  }
}

final pTutorialScreenProvider =
    StateNotifierProvider<TutorialScreenProvider, TutorialScreenModel>(
        (ref) => TutorialScreenProvider(TutorialScreenModel()));
