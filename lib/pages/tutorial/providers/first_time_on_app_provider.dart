import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FirstTimeOnAppProvider extends ChangeNotifier {
  final String _keyToCache = 'checkFirstTime';
  final String _keyFirstTimeLogin = 'checkFirstTimeLogin';
  SharedPreferences? _prefs;

  Future<void> saveState({required bool value}) async {
    _prefs = await SharedPreferences.getInstance();
    _prefs?.setBool(
      _keyToCache,
      value,
    );
  }

  Future<bool?> getState(SharedPreferences? prefs) async {
    _prefs = prefs ?? await SharedPreferences.getInstance();
    final result = _prefs?.getBool(
      _keyToCache,
    );
    return result;
  }

  Future<void> saveStateLogin({required bool value}) async {
    _prefs = await SharedPreferences.getInstance();
    _prefs?.setBool(
      _keyFirstTimeLogin,
      value,
    );
  }

  Future<bool?> getStateLogin(SharedPreferences? prefs) async {
    _prefs = prefs ?? await SharedPreferences.getInstance();
    final result = _prefs?.getBool(
      _keyFirstTimeLogin,
    );
    return result;
  }
}

final pFirstTimeOnAppProvider = ChangeNotifierProvider<FirstTimeOnAppProvider>(
    (ref) => FirstTimeOnAppProvider());
