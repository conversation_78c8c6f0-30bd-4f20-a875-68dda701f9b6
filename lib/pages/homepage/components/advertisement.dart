import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/advertisement_model.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class Advertisement extends StatelessWidget {
  const Advertisement({super.key, required this.advertisement});
  final AdvertisementModel advertisement;

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 169.H,
        width: 345.W,
        padding: EdgeInsets.symmetric(horizontal: 15.W),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.SP),
          image: DecorationImage(
            image: AssetImage(AppAssets.origin().imPlane.assetPath),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  children: [
                    TripcText(
                      advertisement.discount.discountSymbol,
                      ignorePointer: true,
                      fontSize: 37,
                      fontWeight: FontWeight.w500,
                      textColor: AppAssets.origin().whiteBackgroundColor,
                      padding: EdgeInsets.only(right: 4.W),
                    ),
                    TripcRichText(text: '', children: [
                      TextSpan(
                        text: context.strings.text_extra,
                        style: AppAssets.origin().mediumTextStyle.copyWith(
                            fontSize: 24.SP,
                            color: AppAssets.origin().whiteBackgroundColor),
                      ),
                      TextSpan(
                        text: '\n${context.strings.text_discount_upper}',
                        style: AppAssets.origin().mediumTextStyle.copyWith(
                            fontSize: 14.SP,
                            color: AppAssets.origin().whiteBackgroundColor),
                      ),
                    ])
                  ],
                ),
                Container(
                  margin: EdgeInsets.only(top: 6.H, bottom: 15.H),
                  padding:
                      EdgeInsets.symmetric(vertical: 4.H, horizontal: 15.W),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100.SP),
                    color: AppAssets.origin().secondDarkYellow,
                  ),
                  child: TripcText(
                    ignorePointer: true,
                    advertisement.advertisement,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    textColor: AppAssets.origin().blackColor,
                  ),
                ),
                TripcButton(
                  onPressed: () {},
                  height: 36.H,
                  width: 113.W,
                  title: context.strings.text_get_now,
                  style: const AppButtonStyle(
                    radius: 100,
                  ),
                  border: Border.all(
                      width: 0.8.H, color: AppAssets.origin().whiteSmokeColor),
                  titleBuilder: (title) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TripcText(
                          title,
                          fontSize: 12,
                          ignorePointer: true,
                          fontWeight: FontWeight.w500,
                          textColor: AppAssets.origin().whiteBackgroundColor,
                          padding: EdgeInsets.only(right: 9.W),
                        ),
                        AppAssets.origin()
                            .iconDoubleArrowRight
                            .widget(height: 9.H, width: 9.H)
                      ],
                    );
                  },
                )
              ],
            )
          ],
        ));
  }
}
