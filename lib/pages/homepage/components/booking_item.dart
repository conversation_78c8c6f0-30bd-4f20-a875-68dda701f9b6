import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/supplier_response.dart';
import 'package:tripc_app/pages/homepage/components/rating_widget_food.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_constants.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

class BookingItem extends StatelessWidget {
  const BookingItem({
    super.key,
    required this.service,
    required this.category,
    this.onTap,
    this.isExpandView = false,
    this.onTapLike,
  });

  final VoidCallback? onTap;
  final bool isExpandView;
  final TripCServiceCategory category;
  final Supplier service;
  final VoidCallback? onTapLike;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: service.isPublic ?? true,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: isExpandView ? double.infinity : 176.W,
          decoration: BoxDecoration(
            color: AppAssets.origin().whiteBackgroundColor,
            borderRadius: BorderRadius.circular(12.SP),
            border: Border.all(
              color: AppAssets.init.buttonStrokeColor,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _image(context),
              Padding(
                padding: EdgeInsets.symmetric(vertical: 8.H, horizontal: 5.W),
                child: _infoArea(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _infoArea(BuildContext context) {
    return Column(
      spacing: 4.H,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          service.name,
          fontSize: 14,
          ignorePointer: true,
          fontWeight: FontWeight.w700,
          textAlign: TextAlign.start,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          padding: EdgeInsets.only(bottom: 4.H),
          textColor: AppAssets.origin().titleServiceItemColor,
        ),
        Stack(
          clipBehavior: Clip.none,
          children: [
            Row(
              spacing: 10.W,
              children: [
                Expanded(
                  flex: 3,
                  child: RatingWidgetFood(
                    model: service,
                    iconSize: Size(
                      isExpandView ? 20.W : 14.W,
                      isExpandView ? 20.W : 14.W,
                    ),
                    fontSize: isExpandView ? 14 : 12,
                  ),
                ),
                const Expanded(child: SizedBox.shrink())
              ],
            ),
            // service.sale == null || service.sale == 0
            //     ? const SizedBox()
            //     : Positioned(
            //         top: -3.H,
            //         right: -18.W,
            //         child: Column(
            //           crossAxisAlignment: CrossAxisAlignment.end,
            //           children: [
            //             Visibility(
            //               visible: service.sale.isNotNull,
            //               child: RedSaleTagV2(
            //                 text:
            //                     "${context.strings.text_sale} ${service.sale ?? 0}%",
            //                 isExpandView: isExpandView,
            //               ),
            //             ),
            //             Visibility(
            //               visible: service.tickerCategory.isNotNull,
            //               child: Padding(
            //                 padding:
            //                     EdgeInsets.only(top: isExpandView ? 8.H : 3.H),
            //                 child: RedSaleTagV2(
            //                   text: service.tickerCategory ?? '',
            //                   isExpandView: isExpandView,
            //                 ),
            //               ),
            //             ),
            //           ],
            //         ),
            //       ),
          ],
        ),
        _rowInformationCategory(category.name(context)),
        if (service.fullAddress != null)
          _rowInformationLocation(service.fullAddress ?? ''),
      ],
    );
  }

  Widget _rowInformationCategory(String info) {
    return Row(
      spacing: 4.W,
      children: [
        _getIconType(),
        TripcText(
          info,
          fontSize: 12,
          ignorePointer: true,
          fontWeight: FontWeight.w400,
          textAlign: TextAlign.start,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textColor: AppAssets.origin().neutralN7,
        ),
      ],
    );
  }

  Widget _rowInformationLocation(String info) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 4.W,
      children: [
        AppAssets.origin().iconLocation.widget(
            height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
        Flexible(
          child: TripcText(
            info,
            fontSize: 12,
            ignorePointer: true,
            fontWeight: FontWeight.w400,
            textAlign: TextAlign.start,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            textColor: AppAssets.origin().neutralN7,
          ),
        ),
      ],
    );
  }

  Widget _getIconType() {
    final Map<String, Widget> iconMap = {
      AppConstants.slugFood: AppAssets.origin().iconFoodType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
      AppConstants.slugEntertainment: AppAssets.origin().iconKaraokeType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
      AppConstants.slugHealthAndBeauty: AppAssets.origin().iconSpaType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
    };

    return iconMap[category.serviceTypeSlug] ?? Container();
  }

  Widget _image(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.SP),
            topRight: Radius.circular(12.SP),
          ),
          child: BaseCachedNetworkImage(
            imageUrl: service.logoUrl ?? '',
            height: isExpandView ? 200.H : 126.H,
            width: double.infinity,
            placeholder: (context, _) => Container(
              color: AppAssets.origin().lightGrayDD4,
            ),
            errorWidget: (context, error, stackTrace) =>
                AppAssets.origin().icErrorImg.widget(
                      height: isExpandView ? 126.H : 126.H,
                      color: context.appCustomPallet.buttonBG,
                    ),
            fit: BoxFit.cover,
          ),
        ),
        //TODO: saved icon
        // Positioned(
        //   top: 8.H,
        //   right: 8.W,
        //   child: GestureDetector(
        //     onTap: () => onTapLike?.call(),
        //     child: (service.isLiked ?? false)
        //         ? AppAssets.init.icHeartFill.widget(width: 16.H)
        //         : AppAssets.init.icHeart.widget(width: 16.H),
        //   ),
        // ),
      ],
    );
  }
}
