import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/supplier_response.dart';
import 'package:tripc_app/pages/homepage/components/booking_item.dart';
import 'package:tripc_app/pages/homepage/components/booking_item_row.dart';
import 'package:tripc_app/pages/profile/components/tripc_not_yet_login_dialog.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/tour_shimmer_loading/tour_shimmer_loading.dart';

class ListToursGridViewBooking extends ConsumerWidget {
  const ListToursGridViewBooking({
    super.key,
    required this.services,
    this.listScrollController,
    this.gridViewScrollController,
    required this.category,
    this.isGridView = true,
    this.isDisableScroll = false,
    this.isLoading = false,
    required this.onTapSaved,
  });

  final List<Supplier> services;
  final bool isGridView;
  final bool isLoading;
  final ScrollController? listScrollController;
  final ScrollController? gridViewScrollController;
  final ValueChanged<int>? onTapSaved;
  final bool isDisableScroll;
  final TripCServiceCategory category;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 400),
      switchInCurve: Curves.easeInOut,
      switchOutCurve: Curves.easeInOut,
      transitionBuilder: (child, animation) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
      layoutBuilder: (currentChild, previousChildren) {
        return Stack(
          children: <Widget>[
            ...previousChildren,
            if (currentChild != null) currentChild,
          ],
        );
      },
      child: isGridView
          ? Visibility(
              visible: !isLoading,
              replacement: const ListTourGridShimmerLoading(),
              child: GridView.builder(
                controller: gridViewScrollController,
                itemCount: services.length,
                shrinkWrap: isDisableScroll,
                physics: isDisableScroll
                    ? const NeverScrollableScrollPhysics()
                    : null,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    childAspectRatio: 170.W / 259.H,
                    crossAxisCount: 2,
                    mainAxisSpacing: 16.H,
                    crossAxisSpacing: 16.W),
                itemBuilder: (context, index) => BookingItem(
                  category: category,
                  onTap: () {
                    AppRoute.pushNamed(
                      context,
                      routeName: AppRoute.routeRestaurantDetailView,
                      arguments: {
                        'id': services[index].id,
                        'category': category,
                      },
                    );
                  },
                  service: services[index],
                  onTapLike: () {
                    if (globalCacheAuth.isLogged()) {
                      onTapSaved?.call(services[index].id ?? 0);
                    } else {
                      dialogHelpers.show(context,
                          child: const NotYetLoginDialog());
                    }
                  },
                ),
              ),
            )
          : ListView.separated(
              controller: listScrollController,
              itemCount: services.length,
              shrinkWrap: isDisableScroll,
              physics:
                  isDisableScroll ? const NeverScrollableScrollPhysics() : null,
              separatorBuilder: (context, index) => SizedBox(
                height: 16.H,
              ),
              itemBuilder: (context, index) => BookingItemRow(
                onTap: () {
                  AppRoute.pushNamed(
                    context,
                    routeName: AppRoute.routeRestaurantDetailView,
                    arguments: {
                      'id': services[index].id,
                      'category': category,
                    },
                  );
                },
                onTapLike: () {
                  if (globalCacheAuth.isLogged()) {
                    onTapSaved?.call(services[index].id ?? 0);
                  } else {
                    dialogHelpers.show(context,
                        child: const NotYetLoginDialog());
                  }
                },
                category: category,
                service: services[index],
              ),
            ),
    );
  }
}
