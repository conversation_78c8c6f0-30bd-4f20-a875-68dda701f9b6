import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/supplier_response.dart';
import 'package:tripc_app/pages/homepage/components/rating_widget_food.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_constants.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

class BookingItemRow extends StatelessWidget {
  const BookingItemRow({
    super.key,
    required this.service,
    required this.category,
    this.onTap,
    this.onTapLike,
  });

  final VoidCallback? onTap;
  final TripCServiceCategory category;
  final Supplier service;
  final VoidCallback? onTapLike;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: service.isPublic ?? true,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: AppAssets.origin().whiteBackgroundColor,
            borderRadius: BorderRadius.circular(12.SP),
            border: Border.all(
              color: AppAssets.init.buttonStrokeColor,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12.SP),
                        bottomLeft: Radius.circular(12.SP),
                      ),
                      child: BaseCachedNetworkImage(
                        imageUrl: service.logoUrl ?? '',
                        height: 140.H,
                        placeholder: (context, _) => Container(
                          color: AppAssets.origin().lightGrayDD4,
                        ),
                        errorWidget: (context, error, stackTrace) =>
                            AppAssets.origin().icErrorImg.widget(
                                  color: context.appCustomPallet.buttonBG,
                                ),
                        fit: BoxFit.fitHeight,
                      ),
                    ),
                    // TODO: fill this service.like == true
                    //                           ? AppAssets.init.icHeartFill.widget(width: 16.H)
                    //                           : AppAssets.init.icHeart.widget(width: 16.H),
                    // Positioned(
                    //   top: 8.H,
                    //   right: 8.W,
                    //   child: GestureDetector(
                    //     onTap: onTapLike,
                    //     child: AppAssets.init.icHeart.widget(width: 16.H),
                    //   ),
                    // ),
                  ],
                ),
              ),
              Expanded(
                  flex: 2,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 18.W),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 4.H,
                      children: [
                        TripcText(
                          service.name,
                          fontSize: 14,
                          ignorePointer: true,
                          fontWeight: FontWeight.w700,
                          textAlign: TextAlign.start,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          padding: EdgeInsets.only(bottom: 4.H),
                          textColor: AppAssets.origin().titleServiceItemColor,
                        ),
                        RatingWidgetFood(
                          model: service,
                          iconSize: Size(
                            14.W,
                            14.W,
                          ),
                          fontSize: 12,
                        ),
                        _rowInformationCategory(service.productTypes ?? ''),
                        if (service.fullAddress != null)
                          _rowInformationLocation(service.fullAddress ?? ''),
                      ],
                    ),
                  ))
            ],
          ),
        ),
      ),
    );
  }

  Widget _rowInformationCategory(String info) {
    return Row(
      spacing: 4.W,
      children: [
        _getIconType(),
        TripcText(
          info,
          fontSize: 12,
          ignorePointer: true,
          fontWeight: FontWeight.w400,
          textAlign: TextAlign.start,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textColor: AppAssets.origin().neutralN7,
        ),
      ],
    );
  }

  Widget _rowInformationLocation(String info) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 4.W,
      children: [
        AppAssets.origin().iconLocation.widget(
            height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
        Flexible(
          child: TripcText(
            info,
            fontSize: 12,
            ignorePointer: true,
            fontWeight: FontWeight.w400,
            textAlign: TextAlign.start,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            textColor: AppAssets.origin().neutralN7,
          ),
        ),
      ],
    );
  }

  Widget _getIconType() {
    final Map<String, Widget> iconMap = {
      AppConstants.slugFood: AppAssets.origin().iconFoodType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
      AppConstants.slugEntertainment: AppAssets.origin().iconKaraokeType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
      AppConstants.slugHealthAndBeauty: AppAssets.origin().iconSpaType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
    };

    return iconMap[category.serviceTypeSlug] ?? Container();
  }
}
