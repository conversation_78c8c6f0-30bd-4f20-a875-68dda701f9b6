import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class RedSaleTagV2 extends StatelessWidget {
  const RedSaleTagV2({super.key, required this.text, this.isExpandView = false});
  final String text;
  final bool isExpandView;

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          height: 22.H,
          padding: EdgeInsets.symmetric(horizontal: 2.W, vertical: 4.H),
          decoration: BoxDecoration(
              color: AppAssets.origin().lightRedColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.SP),
                bottomRight: Radius.zero,
                topRight: Radius.circular(8.SP),
                bottomLeft: Radius.circular(8.SP),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.25),
                  blurRadius: 2,
                  offset: const Offset(0, 2),
                )
              ]),
          child: TripcText(
            text,
            textColor: AppAssets.origin().whiteBackgroundColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        Positioned(
          bottom: -12.H,
          right: 0,
          child: Transform.flip(
            flipX: true,
            child: CustomPaint(
              size: Size(12.W, 12.H),
              painter: TrianglePainter(),
            ),
          ),
        ),
      ],
    );
  }
}

class TrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint()
      ..color = AppAssets.origin().darkBrownRedColor
      ..style = PaintingStyle.fill;

    var path = Path();
    path.lineTo(size.width, size.height);
    path.lineTo(size.width, 0);
    path.moveTo(0, size.height);

    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
