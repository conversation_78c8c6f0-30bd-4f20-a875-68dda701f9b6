import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class RedSaleTagV2 extends StatelessWidget {
  const RedSaleTagV2({super.key, required this.text, this.isExpandView = false});
  final String text;
  final bool isExpandView;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 22.H,
          padding: EdgeInsets.symmetric(horizontal: 5.W, vertical: 4.H),
          decoration: BoxDecoration(
              color: AppAssets.origin().lightRedColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.SP),
                bottomRight: Radius.circular(10.SP),
                topRight: Radius.circular(10.SP),
                bottomLeft: Radius.zero,
              ),),
          child: TripcText(
            text,
            textColor: AppAssets.origin().whiteBackgroundColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        Transform.flip(
          child: CustomPaint(
            size: Size(6.W, 6.H),
            painter: TrianglePainter(),
          ),
        ),
      ],
    );
  }
}

class TrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint()
      ..color = AppAssets.origin().darkBrownRedColor
      ..style = PaintingStyle.fill;

    var path = Path();
    path.lineTo(size.width, size.height);
    path.lineTo(size.width, 0);
    path.moveTo(0, size.height);

    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
