import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/status_display_notifier.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class ServiceCategoryArea extends StatefulWidget {
  const ServiceCategoryArea({super.key});

  @override
  State<ServiceCategoryArea> createState() => _ServiceCategoryAreaState();
}

class _ServiceCategoryAreaState extends State<ServiceCategoryArea> {
  final _pageController = PageController(viewportFraction: 0.931);
  int _currentPage = 0;
  //TODO: set _isBigSizeForThreeItems = false for the bottom row of categories become small size
  bool _isBigSizeForThreeItems = false;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isNotDisplayAll = !globalReleaseStatusNotifier.isDisplayAll;

    const hideWhenDisplayAll = {
      TripCServiceCategory.event,
      TripCServiceCategory.stay,
      TripCServiceCategory.golfAndSport,
      TripCServiceCategory.shopping,
    };

    final baseFirstRow = <TripCServiceCategory>[
      TripCServiceCategory.food,
      TripCServiceCategory.entertainment,
      TripCServiceCategory.healthAndBeauty,
      TripCServiceCategory.event,
    ];

    final firstCategories = baseFirstRow.where((cat) {
      if (isNotDisplayAll) {
        return !hideWhenDisplayAll.contains(cat);
      }
      return true;
    }).toList();

    final allBottom = List.generate(
      TripCServiceCategory.values.length - 4,
      (i) => TripCServiceCategory.getByValue(i + 5),
    );

    // TODO: remove this when actually delete old category
    allBottom.removeLast();
    allBottom.removeLast();

    final bottomCategories = allBottom.where((cat) {
      if (isNotDisplayAll) {
        return !hideWhenDisplayAll.contains(cat);
      }
      return true;
    }).toList();

    const itemsPerPage = 4;
    final pageCount = (bottomCategories.length / itemsPerPage).ceil();

    return Container(
        margin: EdgeInsets.symmetric(horizontal: 14.W, vertical: 15.H),
        padding: EdgeInsets.symmetric(vertical: 7.H, horizontal: 12.W),
        decoration: BoxDecoration(
          color: AppAssets.origin().whiteBackgroundColor,
          borderRadius: BorderRadius.circular(12.SP),
          border: Border.all(
            color: AppAssets.origin().buttonStrokeColor.withValues(alpha: 0.5),
          ),
        ),
        child: Column(
          spacing: _isBigSizeForThreeItems ? 0 : 8,
          children: [
            //first row
            Row(
              mainAxisAlignment: _isBigSizeForThreeItems
                  ? MainAxisAlignment.spaceEvenly
                  : MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: firstCategories.map((cat) {
                return SizedBox(
                  width: 82.W,
                  height: 77.H,
                  child: ServiceCategory(
                    isFirstRow: true,
                    category: cat,
                  ),
                );
              }).toList(),
            ),
            //second row
            SizedBox(
              height: _isBigSizeForThreeItems ? 77.H : 60.H,
              child: Visibility(
                visible: bottomCategories.length > 4,
                replacement: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: bottomCategories
                      .map((cat) => SizedBox(
                            width: _isBigSizeForThreeItems ? 82.W : 72.W,
                            height: _isBigSizeForThreeItems ? 77.H : 56.H,
                            child: ServiceCategory(
                                category: cat,
                                isBigSizeForThreeItems:
                                    _isBigSizeForThreeItems),
                          ))
                      .toList(),
                ),
                child: PageView.builder(
                  controller: _pageController,
                  padEnds: false,
                  itemCount: pageCount,
                  onPageChanged: (p) => setState(() => _currentPage = p),
                  itemBuilder: (context, pageIndex) {
                    final start = pageIndex * itemsPerPage;
                    final slice = bottomCategories
                        .skip(start)
                        .take(itemsPerPage)
                        .toList();

                    final tiles = <Widget>[];
                    for (var cat in slice) {
                      tiles.add(
                        SizedBox(
                            width: 72.W,
                            height: 56.H,
                            child: ServiceCategory(category: cat)),
                      );
                    }
                    while (tiles.length < itemsPerPage) {
                      tiles.add(SizedBox(width: 72.W));
                    }

                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: tiles,
                    );
                  },
                ),
              ),
            ),
            //pageview indicator
            Visibility(
              visible: bottomCategories.length > 4,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: 8,
                children: List.generate(pageCount, (i) {
                  final isActive = i == _currentPage;
                  return Container(
                    width: 8.W,
                    height: 8.W,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isActive
                          ? AppAssets.origin().darkBlueColor
                          : AppAssets.origin().disableDot,
                    ),
                  );
                }),
              ),
            ),
          ],
        ));
  }
}

class ServiceCategory extends StatelessWidget {
  const ServiceCategory(
      {super.key,
      required this.category,
      this.hasNoti = false,
      this.isFirstRow = false,
      this.isBigSizeForThreeItems = false});

  final TripCServiceCategory category;
  final bool hasNoti;
  final bool isFirstRow;
  final bool isBigSizeForThreeItems;

  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
      onPressed: () => category.onTap(context),
      child: Column(
        children: [
          Stack(
            children: [
              category.icon.widget(
                height:
                    isFirstRow ? 32.W : (isBigSizeForThreeItems ? 32.W : 24.W),
                width:
                    isFirstRow ? 32.W : (isBigSizeForThreeItems ? 32.W : 24.W),
              ),
              Visibility(
                visible: hasNoti,
                child: Positioned(
                  right: 0,
                  top: 2.H,
                  child: Container(
                    height: 6.H,
                    width: 6.H,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppAssets.origin().redDotColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
          TripcText(
            ignorePointer: true,
            category.name(context),
            fontSize: isFirstRow ? 12 : (isBigSizeForThreeItems ? 12 : 10),
            height: 1.2,
            fontWeight: FontWeight.w500,
            overflow: TextOverflow.fade,
            textColor: AppAssets.origin().blackColor,
            padding: EdgeInsets.only(top: 6.H),
            maxLines: 2,
          )
        ],
      ),
    );
  }
}
