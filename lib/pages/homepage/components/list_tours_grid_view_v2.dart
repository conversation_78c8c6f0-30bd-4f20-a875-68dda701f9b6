import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/homepage/components/service_item_v2.dart';
import 'package:tripc_app/pages/profile/components/tripc_not_yet_login_dialog.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider_v2.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/tour_shimmer_loading/tour_shimmer_loading.dart';

class ListToursGridViewV2 extends ConsumerWidget {
  const ListToursGridViewV2(
      {super.key,
      required this.services,
      required this.fromListType,
      this.listScrollController,
      this.gridViewScrollController,
      this.isGridView = true,
      this.isLoading = false,
      this.onSaved,
      this.isFerryTour = false,
      });
  final List<TourResponse> services;
  final bool isGridView;
  final bool isLoading;
  final ScrollController? listScrollController;
  final ScrollController? gridViewScrollController;
  final ValueChanged<int>? onSaved;
  final bool isFerryTour;
  final ListType fromListType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final padding = EdgeInsets.only(
        top: 28.H, bottom: context.spacingBottom, left: 16.W, right: 16.W);
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 400),
      switchInCurve: Curves.easeInOut,
      switchOutCurve: Curves.easeInOut,
      transitionBuilder: (child, animation) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
      layoutBuilder: (currentChild, previousChildren) {
        return Stack(
          children: <Widget>[
            ...previousChildren,
            if (currentChild != null) currentChild,
          ],
        );
      },
      child: isGridView
          ? Visibility(
              visible: !isLoading,
              replacement: const ListTourGridShimmerLoading(),
              child: GridView.builder(
                controller: gridViewScrollController,
                itemCount: services.length,
                padding: padding,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    childAspectRatio: 185.W / 263.H,
                    crossAxisCount: 2,
                    mainAxisSpacing: 16.H,
                    crossAxisSpacing: 16.W,
                ),
                itemBuilder: (context, index) => ServiceItemV2(
                  onTap: () {
                    AppRoute.pushNamed(
                      context,
                      routeName: AppRoute.routeTourDetailViewV2,
                      arguments: {
                        'id': services[index].id,
                        'isFerryTour': isFerryTour,
                        'listType': fromListType
                      },
                    );
                  },
                  service: services[index],
                  onTapLike: () {
                    if (globalCacheAuth.isLogged()) {
                      onSaved?.call(services[index].id ?? 0);
                    } else {
                      dialogHelpers.show(context, child: const NotYetLoginDialog());
                    }
                  },
                ),
              ),
            )
          : ListView.separated(
              controller: listScrollController,
              itemCount: services.length,
              padding: padding,
              separatorBuilder: (context, index) => SizedBox(
                height: 16.H,
              ),
              itemBuilder: (context, index) => ServiceItemV2(
                onTap: () {
                  AppRoute.pushNamed(
                    context,
                    routeName: AppRoute.routeTourDetailViewV2,
                    arguments: {
                      'id': services[index].id,
                      'isFerryTour': isFerryTour,
                      'listType': fromListType
                    },
                  );
                },
                onTapLike: () {
                  onSaved?.call(services[index].id ?? 0);
                },
                isExpandView: true,
                service: services[index],
              ),
            ),
    );
  }
}
