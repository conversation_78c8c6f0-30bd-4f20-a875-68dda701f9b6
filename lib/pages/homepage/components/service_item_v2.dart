import 'package:flutter/material.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/homepage/components/rating_widget.dart';
import 'package:tripc_app/pages/homepage/components/red_tag_v2.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

import '../../../status_display_notifier.dart';

class ServiceItemV2 extends StatelessWidget {
  const ServiceItemV2({
    super.key,
    required this.service,
    this.onTap,
    this.isExpandView = false,
    this.onTapLike,
  });

  final VoidCallback? onTap;
  final bool isExpandView;
  final TourResponse service;
  final VoidCallback? onTapLike;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: isExpandView ? double.infinity : 176.W,
        decoration: BoxDecoration(
          color: AppAssets.origin().whiteBackgroundColor,
          borderRadius: BorderRadius.circular(12.SP),
          border: Border.all(
            color: AppAssets.init.buttonStrokeColor,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.SP),
                    topRight: Radius.circular(12.SP),
                  ),
                  child: Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      BaseCachedNetworkImage(
                        imageUrl: service.thumbnail ?? '',
                        height: isExpandView ? 200.H : 126.H,
                        width: double.infinity,
                        placeholder: (context, _) => Container(
                          color: AppAssets.origin().lightGrayDD4,
                        ),
                        errorWidget: (context, error, stackTrace) =>
                            AppAssets.origin().icErrorImg.widget(
                                  height: isExpandView ? 126.H : 126.H,
                                  color: context.appCustomPallet.buttonBG,
                                ),
                        fit: BoxFit.cover,
                      ),
                      Container(
                        height: 18.H,
                        decoration: BoxDecoration(
                          gradient: AppAssets.origin().itemImageCard,
                        ),
                      )
                    ],
                  ),
                ),
                Positioned(
                  bottom: 2.H,
                  left: 8.W,
                  child: Row(
                    children: [
                      AppAssets.origin().icFillLocation.widget(
                          height: isExpandView ? 12.H : 10.H,
                          width: isExpandView ? 12.H : 10.H,
                          color: AppAssets.origin().whiteBackgroundColor),
                      TripcText(
                        service.address ?? '',
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        textColor: AppAssets.origin().whiteBackgroundColor,
                        maxLines: 1,
                        height: 1.4,
                        overflow: TextOverflow.ellipsis,
                        padding: EdgeInsets.only(left: 4.W),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  bottom: 2.H,
                  right: 8.W,
                  child: Row(
                    children: [
                      AppAssets.origin().icGroup.widget(
                          height: isExpandView ? 12.H : 10.H,
                          width: isExpandView ? 12.H : 10.H,
                          color: AppAssets.origin().whiteBackgroundColor),
                      TripcText(
                        context.strings.text_flexible_ticket,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        textColor: AppAssets.origin().whiteBackgroundColor,
                        maxLines: 1,
                        height: 1.4,
                        overflow: TextOverflow.ellipsis,
                        padding: EdgeInsets.only(left: 4.W),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: 8.H,
                  right: 8.W,
                  child: GestureDetector(
                    onTap: onTapLike,
                    child: Container(
                      width: 20.W,
                      height: 20.W,
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppAssets.origin().whiteBackgroundColor),
                      child: Center(
                        child: service.like == true
                            ? AppAssets.init.icHeartFill
                                .widget(width: 10.W, height: 10.W)
                            : AppAssets.init.icHeart.widget(
                                width: 10.W,
                                height: 10.W,
                                color: AppAssets.origin().grey53),
                      ),
                    ),
                  ),
                ),
                service.sale == null || service.sale == 0
                    ? const SizedBox.shrink()
                    : Positioned(
                        top: 8.H,
                        left: -6.W,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Visibility(
                              visible: service.sale.isNotNull,
                              child: RedSaleTagV2(
                                text:
                                    "${context.strings.text_sale} ${service.sale ?? 0}%",
                                isExpandView: isExpandView,
                              ),
                            ),
                            Visibility(
                              visible: service.tickerCategory.isNotNull,
                              child: Padding(
                                padding: EdgeInsets.only(
                                    top: isExpandView ? 8.H : 3.H),
                                child: RedSaleTagV2(
                                  text: service.tickerCategory ?? '',
                                  isExpandView: isExpandView,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
              ],
            ),
            Padding(
              padding: EdgeInsets.only(top: 8.H, left: 5.W, right: 5.W),
              child: Column(
                spacing: 4.H,
                children: [
                  _infoArea(context),
                  _priceArea(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _infoArea(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          service.name,
          fontSize: 14,
          ignorePointer: true,
          fontWeight: FontWeight.w700,
          textAlign: TextAlign.start,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          padding: EdgeInsets.only(bottom: 4.H),
          textColor: AppAssets.origin().titleServiceItemColor,
        ),
        Stack(
          clipBehavior: Clip.none,
          children: [
            Row(
              spacing: 10.W,
              children: [
                Expanded(
                  flex: 3,
                  child: RatingWidget(
                    model: service,
                    iconSize: Size(
                      isExpandView ? 20.W : 14.W,
                      isExpandView ? 20.W : 14.W,
                    ),
                    fontSize: isExpandView ? 14 : 12,
                  ),
                ),
                const Expanded(child: SizedBox.shrink())
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _priceArea(BuildContext context) {
    return Column(
      crossAxisAlignment:
          isExpandView ? CrossAxisAlignment.start : CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      spacing: 4.H,
      children: [
        Row(
          mainAxisAlignment: isExpandView
              ? MainAxisAlignment.start
              : MainAxisAlignment.spaceBetween,
          children: [
            TripcText(
              ignorePointer: true,
              service.salePrice > 0 ? service.salePrice.vndong : '',
              fontSize: 14,
              fontWeight: FontWeight.w700,
              textColor: AppAssets.origin().priceServiceItemColor,
            ),
            isExpandView
                ? const SizedBox(
                    width: 8,
                  )
                : const SizedBox(),
            Visibility(
              visible: service.salePrice < (service.totalPrice ?? 0),
              child: TripcText(
                ignorePointer: true,
                // service.salePrice > 0 ? service.salePrice.vndong : '',
                (service.totalPrice ?? 0).vndLower,
                fontSize: 10,
                fontWeight: FontWeight.w400,
                textColor: AppAssets.origin().priceSaleServiceItemColor,
                decoration: TextDecoration.lineThrough,
              ),
            ),
          ],
        ),
        Visibility(
          visible: globalReleaseStatusNotifier.isDisplayAll,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 4.W, vertical: 3.H),
            margin: EdgeInsets.only(bottom: 5.H),
            decoration: BoxDecoration(
              color: AppAssets.origin().bgTcentServiceItemColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                AppAssets.origin().icTcentV2.widget(height: 14.W, width: 14.W),
                TripcText(
                  '${context.strings.text_receive_now} ${(service.returnTcent ?? 0).tcent}',
                  ignorePointer: true,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  textColor: AppAssets.origin().tcentServiceItemColor,
                  padding: EdgeInsets.only(left: 4.W),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
