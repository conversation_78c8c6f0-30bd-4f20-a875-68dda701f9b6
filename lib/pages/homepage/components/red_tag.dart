import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class RedSaleTag extends StatelessWidget {
  const RedSaleTag({super.key, required this.text});
  final String text;
  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 9.W, vertical: 4.H),
          decoration: BoxDecoration(
              color: AppAssets.origin().lightRedColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(3.SP),
                bottomRight: Radius.zero,
                topRight: Radius.circular(3.SP),
                bottomLeft: Radius.circular(3.SP),
              )),
          child: TripcText(
            text,
            textColor: AppAssets.origin().whiteBackgroundColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        Positioned(
          bottom: -12.H,
          right: 0,
          child: Transform.flip(
            flipX: true,
            child: CustomPaint(
              size: Size(12.W, 12.H),
              painter: TrianglePainter(),
            ),
          ),
        ),
      ],
    );
  }
}

class TrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint()
      ..color = AppAssets.origin().darkBrownRedColor
      ..style = PaintingStyle.fill;

    var path = Path();
    path.lineTo(size.width, size.height);
    path.lineTo(size.width, 0);
    path.moveTo(0, size.height);

    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
