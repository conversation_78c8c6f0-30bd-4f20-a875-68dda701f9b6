import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/homepage/components/rating_widget.dart';
import 'package:tripc_app/pages/homepage/components/red_tag.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/status_display_notifier.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

class ServiceItem extends StatelessWidget {
  const ServiceItem(
      {super.key,
      required this.service,
      this.onTap,
      this.isExpandView = false,
      this.onTapLike});
  final VoidCallback? onTap;
  final bool isExpandView;
  final TourResponse service;
  final VoidCallback? onTapLike;

  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
      onPressed: onTap,
      child: Container(
          width: 184.W,
          decoration: BoxDecoration(
              color: AppAssets.origin().whiteBackgroundColor,
              borderRadius: BorderRadius.circular(12.SP),
              boxShadow: [
                AppAssets.origin()
                    .itemShadow
                    .copyWith(color: AppAssets.origin().black10,
                )
              ]),
          child: Column(children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.SP),
                      topRight: Radius.circular(12.SP),
                    ),
                    child: BaseCachedNetworkImage(
                      imageUrl: service.thumbnail ?? '',
                      height: isExpandView ? 141.H : 117.H,
                      width: double.infinity,
                      placeholder: (context, _) => Container(
                        color: AppAssets.origin().lightGrayDD4,
                      ),
                      errorWidget: (context, error, stackTrace) =>
                          AppAssets.origin().icErrorImg.widget(
                                height: isExpandView ? 141.H : 117.H,
                                color: context.appCustomPallet.buttonBG,
                              ),
                      fit: BoxFit.cover,
                    )),
                Positioned(
                  bottom: 9.H,
                  left: 10.W,
                  child: Row(
                    children: [
                      AppAssets.origin().icFillLocation.widget(
                          height: 16.H,
                          width: 16.H,
                          color: AppAssets.origin().whiteBackgroundColor),
                      TripcText(
                        service.address ?? '',
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        textColor: AppAssets.origin().whiteBackgroundColor,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        padding: EdgeInsets.only(left: 6.W),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: 8.H,
                  right: 8.W,
                  child: GestureDetector(
                    onTap: onTapLike,
                    child: service.like == true
                        ? AppAssets.init.icHeartFill.widget(width: 16.H)
                        : AppAssets.init.icHeart.widget(width: 16.H),
                  ),
                ),
                Positioned(
                    right: -11.W,
                    bottom: 12.H,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Visibility(
                          // visible: service.sale.isNotNull,
                          visible: false,
                          child: RedSaleTag(
                            text:
                                "${context.strings.text_sale} ${service.sale ?? 0}%",
                          ),
                        ),
                        Visibility(
                          visible: service.tickerCategory.isNotNull,
                          child: Padding(
                            padding:
                                EdgeInsets.only(top: isExpandView ? 13.H : 3.H),
                            child:
                                RedSaleTag(text: service.tickerCategory ?? ''),
                          ),
                        ),
                      ],
                    ))
              ],
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                    top: 6.H,
                    bottom: isExpandView ? 16.H : 8.H,
                    left: 8.W,
                    right: 12.W),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [_infoArea(context), _priceArea(context)],
                ),
              ),
            )
          ])),
    );
  }

  Widget _infoArea(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          service.name,
          fontSize: 14,
          ignorePointer: true,
          fontWeight: FontWeight.w500,
          textCase: TextCaseType.title,
          textAlign: TextAlign.start,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          padding: EdgeInsets.only(bottom: 8.H),
        ),
        RatingWidget(model: service, iconSize: Size(12.W, 12.H), fontSize: 12),
      ],
    );
  }

  Widget _priceArea(BuildContext context) {
    return Column(
      crossAxisAlignment:
          isExpandView ? CrossAxisAlignment.start : CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Visibility(
          // visible:
          //     !isExpandView && service.salePrice < (service.totalPrice ?? 0),
          visible: false,
          child: TripcText(
            ignorePointer: true,
            // service.salePrice > 0 ? service.salePrice.vndong : '',
            (service.totalPrice ?? 0).vndong,
            fontSize: 12,
            fontWeight: FontWeight.w300,
            textColor: AppAssets.origin().greyTextColorC8,
            decoration: TextDecoration.lineThrough,
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(vertical: 2.H),
          child: Row(
            mainAxisAlignment:
                isExpandView ? MainAxisAlignment.start : MainAxisAlignment.end,
            children: [
              TripcText(
                ignorePointer: true,
                // (service.totalPrice ?? 0).vndong,
                // service.salePrice > 0 ? service.salePrice.vndong : '',
                (service.totalPrice ?? 0).vndong,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                textColor: AppAssets.origin().black,
              ),
              Visibility(
                visible: isExpandView &&
                    service.salePrice < (service.totalPrice ?? 0),
                child: TripcText(
                  ignorePointer: true,
                  (service.totalPrice ?? 0).vndong,
                  // service.salePrice > 0 ? service.salePrice.vndong : '',
                  fontSize: 12,
                  fontWeight: FontWeight.w300,
                  textColor: AppAssets.origin().greyTextColorC8,
                  decoration: TextDecoration.lineThrough,
                  padding: EdgeInsets.only(left: 16.W),
                ),
              ),
            ],
          ),
        ),
        if (globalReleaseStatusNotifier.isDisplayAll) Row(
          mainAxisAlignment:
              isExpandView ? MainAxisAlignment.start : MainAxisAlignment.end,
          children: [
            AppAssets.origin().icTcent.widget(height: 16.H, width: 16.H),
            TripcText(
              '${context.strings.text_receive_now} ${(service.returnTcent ?? 0).tcent}',
              ignorePointer: true,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              textColor: AppAssets.origin().darkYellow,
              padding: EdgeInsets.only(left: 8.W),
            ),
          ],
        )
      ],
    );
  }
}
