import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/homepage/components/service_item.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/tour_shimmer_loading/tour_shimmer_loading.dart';

class ListToursGridView extends ConsumerWidget {
  const ListToursGridView(
      {super.key,
      required this.services,
      this.listScrollController,
      this.gridViewScrollController,
      this.isGridView = true,
      this.isLoading = false,
      this.onSaved});
  final List<TourResponse> services;
  final bool isGridView;
  final bool isLoading;
  final ScrollController? listScrollController;
  final ScrollController? gridViewScrollController;
  final ValueChanged<int>? onSaved;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final padding = EdgeInsets.only(
        top: 28.H, bottom: context.spacingBottom, left: 16.W, right: 16.W);
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 400),
      switchInCurve: Curves.easeInOut,
      switchOutCurve: Curves.easeInOut,
      transitionBuilder: (child, animation) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
      layoutBuilder: (currentChild, previousChildren) {
        return Stack(
          children: <Widget>[
            ...previousChildren,
            if (currentChild != null) currentChild,
          ],
        );
      },
      child: isGridView
          ? Visibility(
              visible: !isLoading,
              replacement: const ListTourGridShimmerLoading(),
              child: GridView.builder(
                controller: gridViewScrollController,
                itemCount: services.length,
                padding: padding,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    childAspectRatio: 185.W / 259.H,
                    crossAxisCount: 2,
                    mainAxisSpacing: 16.H,
                    crossAxisSpacing: 16.W),
                itemBuilder: (context, index) => ServiceItem(
                  onTap: () {
                    AppRoute.pushNamed(
                      context,
                      routeName: AppRoute.routeTourDetailView,
                      arguments: services[index].id,
                    );
                  },
                  service: services[index],
                  onTapLike: () {
                    onSaved?.call(services[index].id ?? 0);
                  },
                ),
              ),
            )
          : ListView.separated(
              controller: listScrollController,
              itemCount: services.length,
              padding: padding,
              separatorBuilder: (context, index) => SizedBox(
                height: 16.H,
              ),
              itemBuilder: (context, index) => ServiceItem(
                onTap: () {
                  AppRoute.pushNamed(
                    context,
                    routeName: AppRoute.routeTourDetailView,
                    arguments: services[index].id,
                  );
                },
                onTapLike: () {
                  onSaved?.call(services[index].id ?? 0);
                },
                isExpandView: true,
                service: services[index],
              ),
            ),
    );
  }
}
