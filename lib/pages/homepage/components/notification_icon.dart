import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';

class NotificationIcon extends StatelessWidget {
  const NotificationIcon(
      {super.key,
      this.iconSize = 24,
      this.onTap,
      required this.icon,
      this.color,
      this.backgroundColor,
      this.hasDot = false,
      this.resultHandler});
  final AppAssetBuilder icon;
  final double iconSize;
  final VoidCallback? onTap;
  final bool hasDot;
  final Color? color;
  final Color? backgroundColor;
  final VoidCallback? resultHandler;

  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
      onPressed: onTap,
      isLogin: globalCacheAuth.isLogged(),
      resultHandler: resultHandler,
      showSuggestLoginDialog: true,
      child: Container(
          height: 38.H,
          width: 38.H,
          decoration: BoxDecoration(
              color: backgroundColor ?? AppAssets.origin().whiteBackgroundColor,
              shape: BoxShape.circle),
          child: Center(
              child: Stack(
            children: [
              icon.widget(height: iconSize.H, width: iconSize.H, color: color),
              Visibility(
                visible: hasDot,
                child: Positioned(
                  right: 3.H,
                  top: 3.H,
                  child: Container(
                    height: 6.H,
                    width: 6.H,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppAssets.origin().redDotColor,
                    ),
                  ),
                ),
              )
            ],
          ))),
    );
  }
}
