import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/advertisement_model.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/remote/api_tour_response/api_tour_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/service_type_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/apis/tours/api_tours.dart';

import '../../../models/app/service_type_enum/service_type_enum.dart';

class HomePageScreenModel {
  HomePageScreenModel({
    required this.advertisements,
    this.bestSeller = const [],
    this.buyMoreEarnMore = const [],
    this.discounts = const [],
    this.dealsAroundHere = const [],
    this.viewMoreTours = const [],
    this.page = 1,
    this.pageSize = 20,
    this.errorMessage,
    this.isLoading = false,
    this.isLoadingLoadMore = false,
    this.canLoadmore = true,
    this.listServiceType = const [],
  });
  final List<AdvertisementModel> advertisements;
  static HomePageScreenModel getDefault() {
    return HomePageScreenModel(
      advertisements: List.generate(
          4,
          (index) => AdvertisementModel(
              discount: 10, advertisement: 'ON FLIGHT TICKETS')),
    );
  }

  final List<TourResponse> dealsAroundHere;
  final List<TourResponse> discounts;
  final List<TourResponse> bestSeller;
  final List<TourResponse> buyMoreEarnMore;
  final List<TourResponse> viewMoreTours;
  final List<ServiceType> listServiceType;
  final String? errorMessage;
  final int page;
  final int pageSize;
  final bool isLoading;
  final bool canLoadmore;
  final bool isLoadingLoadMore;

  HomePageScreenModel copyWith(
      {List<AdvertisementModel>? advertisements,
      List<TourResponse>? bestSeller,
      List<TourResponse>? buyMoreEarnMore,
      List<TourResponse>? discounts,
      String? errorMessage,
      List<TourResponse>? dealsAroundHere,
      List<TourResponse>? viewMoreTours,
      List<ServiceType>? listServiceType,
      bool? isLoading,
      bool? isLoadingLoadMore,
      bool? canLoadmore,
      int? page,
      int? pageSize}) {
    return HomePageScreenModel(
        advertisements: advertisements ?? this.advertisements,
        bestSeller: bestSeller ?? this.bestSeller,
        buyMoreEarnMore: buyMoreEarnMore ?? this.buyMoreEarnMore,
        discounts: discounts ?? this.discounts,
        dealsAroundHere: dealsAroundHere ?? this.dealsAroundHere,
        errorMessage: errorMessage ?? this.errorMessage,
        viewMoreTours: viewMoreTours ?? this.viewMoreTours,
        page: page ?? this.page,
        isLoadingLoadMore: isLoadingLoadMore ?? this.isLoadingLoadMore,
        canLoadmore: canLoadmore ?? this.canLoadmore,
        pageSize: pageSize ?? this.pageSize,
        isLoading: isLoading ?? this.isLoading,
        listServiceType: listServiceType ?? this.listServiceType);
  }

  HomePageScreenModel copyWithUpdateService(
    int id,
    bool like,
  ) {
    final updatedDealsAroundHere = dealsAroundHere.map((tour) {
      if (tour.id == id) {
        return tour.copyWith(like: like);
      }
      return tour;
    }).toList();
    return copyWith(dealsAroundHere: updatedDealsAroundHere);
  }
}

class HomepageScreenProvider extends StateNotifier<HomePageScreenModel> {
  HomepageScreenProvider(super._state);
  final ApiTours _api = ApiTours();

  void resetState() {
    state = HomePageScreenModel.getDefault();
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void setLoading(bool value) {
    state = state.copyWith(
      isLoading: value,
    );
  }

  void forceLoadingLoadMore(bool value) {
    state = state.copyWith(isLoadingLoadMore: value);
  }

  void appendListViewMoreTour(ListTourResponse result) {
    if (state.viewMoreTours.isEmpty) {
      state = state.copyWith(viewMoreTours: result.data);
      state = state.copyWith(
          canLoadmore: state.viewMoreTours.length < (result.total ?? 0));
      return;
    }
    state =
        state.copyWith(viewMoreTours: [...state.viewMoreTours, ...result.data]);
    state = state.copyWith(
        canLoadmore: state.viewMoreTours.length < (result.total ?? 0));
    forceLoadingLoadMore(false);
  }

  void updateTourResponse(int tourId, bool isSave) {
    final cacheList = state.dealsAroundHere.toList();
    final index = cacheList.indexWhere((element) => element.id == tourId);
    if (index == -1) {
      return;
    }
    cacheList[index] = cacheList[index].copyWith(like: isSave);
    state = state.copyWith(
      dealsAroundHere: cacheList,
    );
  }

  void setPageGetData({required int page, required int pageSize}) {
    state = state.copyWith(page: page, pageSize: pageSize);
  }

  Future<void> getTourDealsAroundHere({bool viewMore = false}) async {
    final request = ListDataRequest(page: state.page, pageSize: state.pageSize);
    setLoading(true);
    try {
      final categoryBySlug = await _api
          .getServiceCategoryBySlug(
              slug: ParentServiceTypeEnum.tourTraiNghiem.slug)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (categoryBySlug.status == true) {
        final result = await _api
            .getTourDealsAroundHere(
                request: request, categoryId: categoryBySlug.data?.id)
            .timeout(
              const Duration(
                seconds: 30,
              ),
            );
        if (viewMore) {
          appendListViewMoreTour(result);
        } else {
          state = state.copyWith(dealsAroundHere: result.data);
        }
      }

      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<void> getServiceTypesList() async {
    setLoading(true);
    try {
      final result = await _api.getServiceTypesList().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.status) {
        state = state.copyWith(listServiceType: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  Future<bool> savedTour(int id) async {
    setLoading(true);
    try {
      final result = await _api.savedTour(tourId: id).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      return result.status;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  void updatedDealsAroundHere(int id, bool isLike) {
    state = state.copyWithUpdateService(id, isLike);
  }
}

final pHomepageScreenProvider =
    StateNotifierProvider<HomepageScreenProvider, HomePageScreenModel>(
        (ref) => HomepageScreenProvider(HomePageScreenModel.getDefault()));
