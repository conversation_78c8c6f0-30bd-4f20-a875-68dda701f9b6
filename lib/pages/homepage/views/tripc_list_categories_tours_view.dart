import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/pages/homepage/components/list_tours_grid_view_v2.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider_v2.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_enum.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcListCategoriesToursView extends ConsumerStatefulWidget {
  const TripcListCategoriesToursView(
      {super.key, required this.id, required this.name, this.ferryType = FerryType.catBaTuanChau, required this.fromListType});

  final int id;
  final String name;
  final FerryType? ferryType;
  final ListType fromListType;
  @override
  ConsumerState<TripcListCategoriesToursView> createState() =>
      _TripcListCategoriesToursViewState();
}

class _TripcListCategoriesToursViewState
    extends ConsumerState<TripcListCategoriesToursView> {
  bool _isGridView = true;
  final _scrollController = ScrollController();
  final _gridViewScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _gridViewScrollController.addListener(() {
      if (_isGridView) {
        _loadMore();
      }
    });
    _scrollController.addListener(() {
      if (!_isGridView) {
        _loadMore();
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(pTicketTourProvider.notifier)
          .viewMoreCategoriesTours(id: widget.id, ferryType: widget.ferryType);
    });
  }

  void changeTourView(bool value) {
    setState(() {
      _isGridView = value;
    });
  }

  void _loadMore() {
    final ScrollController controller =
        _isGridView ? _gridViewScrollController : _scrollController;
    if (controller.position.pixels >=
        controller.position.maxScrollExtent - 100) {
      ref.read(pTicketTourProvider.notifier).loadMoreCategories(id: widget.id, ferryType: widget.ferryType);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _gridViewScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final viewMoreTours =
        ref.watch(pTicketTourProvider.select((value) => value.viewMoreTours));
    final isLoading =
        ref.watch(pTicketTourProvider.select((value) => value.isLoading));
    final loadingLoadmore = ref
        .watch(pTicketTourProvider.select((value) => value.isLoadingLoadMore));

    return Stack(children: [
      TripcScaffold(
        onPopScope: () {
          ref.read(pAccountProvider.notifier).resetLinkEmailPage();
        },
        onLeadingPressed: () {
          ref.read(pAccountProvider.notifier).resetLinkEmailPage();
          Navigator.pop(context);
        },
        hasBackButton: true,
        needUnFocus: true,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        toolbarHeight: 46.H,
        leadingPadding: EdgeInsets.only(top: 10.H),
        leadingWidth: 37,
        titleAppBar: TripcText(
          widget.name,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          textColor: AppAssets.origin().black,
          padding: EdgeInsets.only(top: 12.H),
        ),
        bottom: null,
        actions: [
          Visibility(
            visible: !isLoading,
            child: Padding(
              padding: EdgeInsets.only(right: 20.W, top: 12.H),
              child: _isGridView
                  ? TripcIconButton(
                      onPressed: () {
                        changeTourView(false);
                      },
                      child: AppAssets.origin()
                          .icEqual
                          .widget(height: 20.H, width: 20.H),
                    )
                  : TripcIconButton(
                      onPressed: () {
                        changeTourView(true);
                      },
                      child: AppAssets.origin()
                          .icDashBoard
                          .widget(height: 16.H, width: 16.H),
                    ),
            ),
          )
        ],
        body: RefreshIndicator(
          onRefresh: () async {
            ref
                .read(pTicketTourProvider.notifier)
                .viewMoreCategoriesTours(id: widget.id);
          },
          child: ListToursGridViewV2(
            listScrollController: _scrollController,
            gridViewScrollController: _gridViewScrollController,
            services: viewMoreTours,
            isGridView: _isGridView,
            isLoading: isLoading,
            onSaved: (value) {
              ref.read(pTicketTourProvider.notifier).listSavedTour(value);
            },
            fromListType: widget.fromListType,
            isFerryTour: widget.ferryType != null,
          ),
        ),
      ),
      AppLoading(isRequesting: loadingLoadmore),
    ]);
  }
}
