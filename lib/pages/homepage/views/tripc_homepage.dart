import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tripc_app/models/app/advertisement_model.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/app/tour_type_enum.dart';
import 'package:tripc_app/models/app/tripc_tier.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/pages/homepage/components/service_item_v2.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/tour_shimmer_loading/tour_shimmer_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_search/tripc_search.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/commons/view_all_row/view_all_row_v2.dart';
import '../../../generated/l10n.dart';
import '../../../status_display_notifier.dart';
import '../../../widgets/commons/app_dialog/tripc_dialog.dart';
import '../../notifications/providers/tripc_notification_provider.dart';
import '../../profile/components/tripc_not_yet_login_dialog.dart';
import '../../tutorial/providers/first_time_on_app_provider.dart';
import '../components/components.dart';
import '../providers/providers.dart';

class TripcHomepage extends ConsumerStatefulWidget {
  const TripcHomepage({super.key});

  @override
  ConsumerState<TripcHomepage> createState() => _TripcHomepageState();
}

class _TripcHomepageState extends ConsumerState<TripcHomepage> {
  final TextEditingController _controller = TextEditingController();
  bool loadingData = false;

  @override
  void initState() {
    super.initState();
    globalCacheAuth.saveCurrentScreen(AppRoute.I.currentRoute);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      forceLoading(true);
      await ref.read(pHomepageScreenProvider.notifier).getServiceTypesList();
      ref
          .read(pHomepageScreenProvider.notifier)
          .setPageGetData(page: 1, pageSize: 4);
      await ref.read(pHomepageScreenProvider.notifier).getTourDealsAroundHere();
      if (globalCacheAuth.isLogged()) {
        await ref.read(pNotificationProvider.notifier).getNotification();
      }

      forceLoading(false);

      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final isFirstTimeOnApp =
          await globalcontainer.read(pFirstTimeOnAppProvider).getState(prefs);
      if (isFirstTimeOnApp ?? true) {
        ref.read(pFirstTimeOnAppProvider.notifier).saveState(value: false);
        dialogHelpers.show(context,
            child: TripcDialog(
              onTap: () => Navigator.of(context, rootNavigator: true).pop(),
              isDisplayIcon: false,
              title: S.current.ministry_of_industry_notice,
              titleFontWeight: FontWeight.w400,
              titleTextCase: TextCaseType.upper,
              titleFontSize: 14,
              titleButton: 'OK',
              contentPadding:
                  EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
            ));
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void forceLoading(bool value) {
    if (mounted) {
      setState(() {
        loadingData = value;
      });
    }
  }

  Future<void> onTapLikeService(
      int id, bool isLike, BuildContext contextcontext) async {
    final result =
        await ref.read(pHomepageScreenProvider.notifier).savedTour(id);

    if (result) {
      forceLoading(true);
      ref
          .read(pHomepageScreenProvider.notifier)
          .updatedDealsAroundHere(id, !isLike);
      forceLoading(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final advertisements = ref
        .watch(pHomepageScreenProvider.select((value) => value.advertisements));
    final dealsAroundHere = ref.watch(
        pHomepageScreenProvider.select((value) => value.dealsAroundHere));

    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        visibleAppBar: false,
        backgroundColor: AppAssets.origin().whiteSmokeColor,
        body: Stack(
          children: [
            _gradientBehind(),
            SafeArea(
              child: Column(
                children: [
                  _header(),
                  _searchArea(
                    onTap: () => AppRoute.pushNamed(context,
                        routeName: AppRoute.routeSearchPage),
                  ),
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: () async {
                        forceLoading(true);
                        ref
                            .read(pHomepageScreenProvider.notifier)
                            .setPageGetData(page: 1, pageSize: 4);
                        await ref
                            .read(pHomepageScreenProvider.notifier)
                            .getTourDealsAroundHere();
                        forceLoading(false);
                      },
                      child: ListView(
                        children: [
                          globalCacheAuth.isLogged()
                              ? _tierAndTcentInfo(context)
                              : SizedBox(
                                  height: 15.H,
                                ),
                          const ServiceCategoryArea(),
                          _advertisementList(advertisements),
                          ViewAllRowV2(
                            buttonText: context.strings.text_see_more,
                            title: TourType.dealsAroundHere.title(context),
                            padding: EdgeInsets.symmetric(
                                vertical: 16.H, horizontal: 16.W),
                            seeAllColor: AppAssets.origin().neutralN7,
                            // Dark overlay
                            fontStyle: FontStyle.normal,
                            titleFontWeight: FontWeight.w700,
                            titleSize: 16,
                            onTapViewAll: () => AppRoute.pushNamed(
                              context,
                              routeName: AppRoute.routeListToursView,
                              arguments: {'category': TourType.dealsAroundHere},
                            ),
                          ),
                          _serviceItemList(dealsAroundHere),
                          SizedBox(
                            height: 23.H,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }

  Widget _gradientBehind() {
    return SizedBox(
      height: 291.H,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          AppAssets.origin()
              .imSeaBackGround
              .widget(height: 291.H, width: double.infinity, fit: BoxFit.fitWidth),
          Align(
            alignment: Alignment.topCenter,
            child: Container(
              height: 291.H,
              decoration:
                  BoxDecoration(gradient: AppAssets.origin().homePageGradient),
            ),
          )
        ],
      ),
    );
  }

  Widget _searchArea({VoidCallback? onTap}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 5.H),
      child: TripcSearch(
        onTap: onTap,
        readOnly: true,
        borderColor: AppAssets.origin().borderGlassTextField,
        radius: 12.SP,
        hintTextColor: AppAssets.origin().grayE5,
        prefixIconColor: AppAssets.origin().grayE5,
        fillColor: AppAssets.origin().whiteBackgroundColor.withOpacity(0.2),
        keyboardType: TextInputType.text,
        controller: _controller,
        hintText: context.strings.text_danang_2d_1n,
      ),
    );
  }

  // Widget _advertisementList(List<AdvertisementModel> advertisements) {
  //   return SizedBox(
  //       height: 169.H,
  //       child: ListView.separated(
  //           clipBehavior: Clip.none,
  //           padding: EdgeInsets.symmetric(horizontal: 16.W),
  //           itemCount: advertisements.length,
  //           scrollDirection: Axis.horizontal,
  //           separatorBuilder: (context, _) => SizedBox(width: 16.W),
  //           itemBuilder: (context, index) {
  //             return Advertisement(
  //               advertisement: advertisements[index],
  //             );
  //           }));
  // }

  Widget _advertisementList(List<AdvertisementModel> advertisements) {
    return SizedBox(
        height: 169.H,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.W),
          child: Container(
              clipBehavior: Clip.none,
              height: 169.H,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15.SP),
                image: DecorationImage(
                  image: AssetImage(AppAssets.origin().imHomeBanner.assetPath),
                  fit: BoxFit.cover,
                ),
              )),
        ));
  }

  Widget _serviceItemList(List<TourResponse> services) {
    return SizedBox(
        height: globalReleaseStatusNotifier.isDisplayAll ? 246.H : 220.H,
        child: Visibility(
          visible: !loadingData,
          replacement: const ListTourShimmerLoading(),
          child: ListView.separated(
              clipBehavior: Clip.none,
              padding: EdgeInsets.symmetric(horizontal: 16.W),
              itemCount: services.length,
              scrollDirection: Axis.horizontal,
              separatorBuilder: (context, _) => SizedBox(width: 18.W),
              itemBuilder: (context, index) {
                return ServiceItemV2(
                  onTap: () {
                    AppRoute.pushNamed(
                      context,
                      routeName: AppRoute.routeTourDetailView,
                      arguments: services[index].id,
                    );
                  },
                  service: services[index],
                  onTapLike: () {
                    if (globalCacheAuth.isLogged()) {
                      onTapLikeService(services[index].id ?? 0,
                          services[index].like ?? false, context);
                    } else {
                      dialogHelpers.show(context, child: NotYetLoginDialog(
                        resultHandler: () async {
                          ref
                              .read(pHomepageScreenProvider.notifier)
                              .setPageGetData(page: 1, pageSize: 4);
                          await ref
                              .read(pHomepageScreenProvider.notifier)
                              .getTourDealsAroundHere();
                          await ref
                              .read(pNotificationProvider.notifier)
                              .getNotification();
                        },
                      ));
                    }
                  },
                );
              }),
        ));
  }

  Widget _header() {
    bool? hasUnreadNotifications;
    if (globalCacheAuth.isLogged()) {
      hasUnreadNotifications = ref
          .watch(pNotificationProvider)
          .notifications
          .any((notification) => notification.read == 0);
    }

    return Padding(
      padding: EdgeInsets.only(
        left: 24.W,
        right: 16.W,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TripcText('',
              // context.strings.text_wanna_go_danang,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              textColor: AppAssets.origin().whiteBackgroundColor),
          Row(
            children: [
              // NotificationIcon(
              //   onTap: () => AppRoute.pushNamed(context,
              //       routeName: AppRoute.routeChatCommingSoon),
              //   icon: AppAssets.origin().icChatNoti,
              //   hasDot: true,
              // ),
              // SizedBox(width: 12.W),
              NotificationIcon(
                  onTap: !globalCacheAuth.isLogged()
                      ? () => dialogHelpers.show(context,
                          child: const NotYetLoginDialog())
                      : () => AppRoute.pushNamed(context,
                          routeName: AppRoute.routeNotificationPage,
                          withNavBar: true),
                  resultHandler: () async {
                    ref
                        .read(pHomepageScreenProvider.notifier)
                        .setPageGetData(page: 1, pageSize: 4);
                    await ref
                        .read(pHomepageScreenProvider.notifier)
                        .getTourDealsAroundHere();
                    if (globalCacheAuth.isLogged()) {
                      await ref
                          .read(pNotificationProvider.notifier)
                          .getNotification();
                    }
                  },
                  icon: globalCacheAuth.isLogged()
                      ? hasUnreadNotifications == true
                          ? AppAssets.origin().icNotification
                          : AppAssets.origin().icReadNotification
                      : AppAssets.origin().icReadNotification),
            ],
          )
        ],
      ),
    );
  }

  Widget _tierAndTcentInfo(BuildContext context) {
    final userInfo = ref.watch(pAccountProvider.select((value) => value.user));
    final currentTier =
        userInfo?.selectedMembership?.tierType ?? TripcTierType.bronze;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.W, vertical: 5.H),
      decoration: BoxDecoration(
        color: AppAssets.origin().tierAndInfoCardBg,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15.W, vertical: 9.H),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Column(
                  spacing: 5.W,
                  children: [
                    Row(
                      children: [
                        currentTier.icon().widget(height: 24.W, width: 24.W),
                        SizedBox(
                          width: 7.W,
                        ),
                        TripcText(
                          currentTier.text(context),
                          fontSize: 14,
                          textColor: currentTier.textColor(),
                          fontWeight: FontWeight.w700,
                        ),
                      ],
                    ),
                    TripcText(
                      userInfo?.getMemberShipID() ?? '-',
                      fontSize: 14,
                      textColor: AppAssets.origin().whiteBackgroundColor,
                      fontWeight: FontWeight.w700,
                    ),
                  ],
                )
              ],
            ),
            Visibility(
              visible: globalReleaseStatusNotifier.isDisplayAll,
              child: Column(
                children: [
                  Row(
                    children: [
                      AppAssets.origin()
                          .icTcentV2
                          .widget(height: 24.H, width: 24.H),
                      SizedBox(
                        width: 8.W,
                      ),
                      ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Color(0xFFFFE9B2),
                            Color(0xFFFFC738),
                          ],
                        ).createShader(
                            Rect.fromLTWH(0, 0, bounds.width, bounds.height)),
                        blendMode: BlendMode.srcIn,
                        child: Text(
                          context.strings.text_tcent,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                            color: AppAssets.origin().whiteBackgroundColor,
                          ),
                        ),
                      )
                    ],
                  ),
                  TripcText(
                    (userInfo?.selectedMembership?.tcent ?? 0).separator,
                    fontSize: 14,
                    textColor: AppAssets.origin().whiteBackgroundColor,
                    fontWeight: FontWeight.w700,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
