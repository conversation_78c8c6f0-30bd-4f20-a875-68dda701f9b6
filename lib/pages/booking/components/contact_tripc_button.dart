import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../services/providers/providers.dart';
import '../../../widgets/commons/app_dialog/tripc_dialog.dart';
import '../../../widgets/commons/tripc_button/tripc_button.dart';
import '../../../widgets/commons/tripc_rich_text/tripc_rich_text.dart';

class ContactTripcButton extends StatelessWidget {
  const ContactTripcButton({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                color: Colors.grey.shade300,
                width: 1.0,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                offset: const Offset(0, -2),
                blurRadius: 8.0,
                spreadRadius: 1.0,
              ),
            ],
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 16.H),
          child: _contactTripcButton(context)),
    );
  }

  Widget _contactTripcButton(BuildContext context) {
    return TripcButton(
        onPressed: () {
          dialogHelpers.show(context,
              child: TripcDialog(
                title: context.strings.text_contact_tripc,
                onTap: () {
                  Navigator.pop(context);
                },
                icon: AppAssets.init.imageContactTripC.widget(
                  height: 40.H,
                  width: 40.H,
                ),
                titleButton: context.strings.text_close,
                contentPadding: EdgeInsets.symmetric(horizontal: 24.W)
                    .copyWith(top: 24.H, bottom: 27.H),
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.H),
                  child: TripcRichText(
                    text: '',
                    children: [
                      TextSpan(
                          text: context.strings.text_contact_message_1,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w300,
                            color: Colors.black,
                          )),
                      TextSpan(
                          text: context.strings.text_contact_tripc_email,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w300,
                            color: AppAssets.init.darkBlue5FF,
                          )),
                      TextSpan(
                          text: context.strings.text_contact_message_2,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w300,
                            color: Colors.black,
                          ))
                    ],
                  ),
                ),
              ));
        },
        height: 56,
        style: const AppButtonStyle(fontWeight: FontWeight.w700, fontSize: 16),
        textCase: TextCaseType.none,
        title: context.strings.text_contact_tripc);
  }
}
