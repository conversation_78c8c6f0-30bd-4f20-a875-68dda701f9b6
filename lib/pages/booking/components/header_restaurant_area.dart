import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/detail_supplier_response.dart';
import 'package:tripc_app/pages/booking/components/rating_widget_detail_restaurant.dart';
import 'package:tripc_app/pages/ticket_tour/components/address_link_widget.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_constants.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../generated/l10n.dart';
import '../../../models/remote/booking_response/supplier_responses/work_time_response.dart';
part 'working_time_dialog.dart';

class HeaderRestaurantDetailArea extends StatelessWidget {
  final DetailSupplier supplier;

  final TripCServiceCategory category;

  const HeaderRestaurantDetailArea({
    super.key,
    required this.supplier,
    required this.category,
  });

  void showWorkTimeDialog(BuildContext context) {
    final data = supplier.workingTime;
    if (data == null) return;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.SP),
        ),
        backgroundColor: Colors.white,
        insetPadding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 16.H),
        child: WorkingTimeDialogContent(data: data),
      ),
    );
  }

  String getCurrentDayWorkingTimeString(WorkingTimeResponse? workingTime) {
    if (workingTime == null) return S.current.close;

    for (final entry in workingTime.orderedDayEntries) {
      if (entry.value.any((s) => s.isCurrent == true)) {
        return entry.value
            .map((s) => '${s.startTime} - ${s.endTime}')
            .join(' | ');
      }
    }
    return S.current.close;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.W).copyWith(bottom: 0),
      padding: EdgeInsets.all(10.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().whiteBackgroundColor,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            clipBehavior: Clip.none,
            children: [
              TripcText(
                supplier.name,
                fontSize: 20,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().textColorForNameTourV2,
              ),
            ],
          ),
          RatingWidgetRestaurantDetail(
            model: supplier,
            iconSize: Size(12.W, 12.W),
            fontSize: 12.SP,
            addOnTextFontSize: 12.SP,
            padding: EdgeInsets.symmetric(vertical: 8.H),
            onReviewTap: () => AppRoute.pushNamed(
              context,
              routeName: AppRoute.routeRating,
            ),
          ),
          _rowInformationCategory(supplier.productTypes ?? ''),
          SizedBox(height: 8.H),
          Row(
            spacing: 4.W,
            children: [
              AppAssets.origin().iconClock.widget(
                  height: 16.W,
                  width: 16.W,
                  color: AppAssets.origin().darkBlueColor),
              TripcText(
                getCurrentDayWorkingTimeString(supplier.workingTime),
                fontSize: 12,
                ignorePointer: false,
                fontWeight: FontWeight.w400,
                textAlign: TextAlign.start,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textColor: AppAssets.origin().neutralN7,
                decoration: TextDecoration.underline,
                onTap: () => showWorkTimeDialog(context),
              ),
            ],
          ),
          SizedBox(height: 8.H),
          AddressLink(
              address: supplier.fullAddress ?? '',
              textDecoration: TextDecoration.none,
              textColor: AppAssets.origin().neutralN7,
              spacing: 4),
        ],
      ),
    );
  }

  Widget _rowInformationCategory(String info) {
    return Row(
      spacing: 4.W,
      children: [
        _getIconType(),
        TripcText(
          info,
          fontSize: 12,
          ignorePointer: true,
          fontWeight: FontWeight.w400,
          textAlign: TextAlign.start,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textColor: AppAssets.origin().neutralN7,
        ),
      ],
    );
  }

  Widget _getIconType() {
    final Map<String, Widget> iconMap = {
      AppConstants.slugFood: AppAssets.origin().iconFoodType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
      AppConstants.slugEntertainment: AppAssets.origin().iconKaraokeType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
      AppConstants.slugHealthAndBeauty: AppAssets.origin().iconSpaType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
    };

    return iconMap[category.serviceTypeSlug] ?? Container();
  }
}
