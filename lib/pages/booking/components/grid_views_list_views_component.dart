// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:tripc_app/pages/homepage/components/list_tours_grid_view_booking.dart';
// import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider_v2.dart';
// import 'package:tripc_app/utils/app_extension.dart';
//
// class GridViewsListViewsComponent extends StatelessWidget {
//   const GridViewsListViewsComponent({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       spacing: 4.H,
//       children: [
//         RefreshIndicator(
//           onRefresh: () async {
//             ref.read(pBookingSearchProvider.notifier).getRestaurantsByTypeId(
//               currentServiceType.id ?? 0,
//               ListDataRequest(page: 1, pageSize: 20),
//             );
//           },
//           child: ListToursGridViewBooking(
//               services: listTourTabs[currentServiceType.id] ?? [],
//               fromListType: ListType.all,
//               listScrollController: _scrollController,
//               gridViewScrollController: _gridViewScrollController,
//               category: widget.category,
//               isDisableScroll: true,
//               isGridView: viewModesListView[currentServiceType.id] ?? false),
//         ),
//         SizedBox(height: 20.H,)
//       ],
//     );
//   }
// }
