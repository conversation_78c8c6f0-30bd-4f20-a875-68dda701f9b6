import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class BookingDialog extends StatelessWidget {
  const BookingDialog(
      {super.key,
      required this.title,
      this.child,
      this.dialogRadius = 12,
      this.insetPadding,
      this.contentPadding,
      this.type = TripcDialogType.warning,
      this.icon,
      this.titleFirstButton,
      this.titleSecondButton,
      this.onFirstButtonTap,
      this.onSecondButtonTap,
      this.titleFontWeight});
  final Widget? child;
  final EdgeInsets? insetPadding;
  final double dialogRadius;
  final String title;
  final VoidCallback? onFirstButtonTap;
  final VoidCallback? onSecondButtonTap;
  final EdgeInsetsGeometry? contentPadding;
  final TripcDialogType type;
  final Widget? icon;
  final String? titleFirstButton;
  final String? titleSecondButton;
  final FontWeight? titleFontWeight;

  @override
  Widget build(BuildContext context) {
    return Dialog(
        insetPadding: insetPadding ??
            EdgeInsets.only(
                left: 16.W,
                right: 16.W,
                bottom: context.mediaQuery.size.height / 2 - 350.H),
        elevation: 0,
        backgroundColor: AppAssets.init.whiteBackgroundColor,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(dialogRadius.SP)),
        child: Padding(
          padding: contentPadding ?? EdgeInsets.zero,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              icon ??
                  (type == TripcDialogType.warning
                          ? AppAssets.origin().icWarning
                          : AppAssets.origin().icSuccess)
                      .widget(height: 40.H, width: 40.H),
              TripcText(
                title,
                fontSize: 16,
                fontWeight: titleFontWeight ?? FontWeight.w600,
                textColor: AppAssets.origin().black,
                padding: EdgeInsets.only(top: 9.H),
              ),
              child ??
                  SizedBox(
                    height: 20.H,
                  ),
              Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(right: 16.W),
                      child: TripcButton(
                        onPressed: () {
                          onFirstButtonTap.isNotNull
                              ? onFirstButtonTap!.call()
                              : Navigator.of(context).pop();
                        },
                        title: titleFirstButton ?? context.strings.text_confirm,
                        height: 48,
                      ),
                    ),
                  ),
                  Expanded(
                    child: TripcButton(
                      onPressed: () {
                        onSecondButtonTap.isNotNull
                            ? onSecondButtonTap!.call()
                            : Navigator.of(context).pop();
                      },
                      buttonType: ButtonType.outline,
                      style: AppButtonStyle(
                        borderColor: AppAssets.origin().primaryColorV2,
                        textColor: AppAssets.origin().primaryColorV2,
                      ),
                      title: titleSecondButton ?? context.strings.text_cancel,
                      height: 48,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ));
  }
}
