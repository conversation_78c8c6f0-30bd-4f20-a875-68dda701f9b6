import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/detail_supplier_response.dart';
import 'package:tripc_app/pages/ticket_tour/components/expanded_view_v2.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:markdown/markdown.dart' as md;

class ProductDetailView extends ConsumerStatefulWidget {
  const ProductDetailView(this.category,
      {super.key, required this.restaurantDetail});

  final DetailSupplier? restaurantDetail;
  final TripCServiceCategory category;

  @override
  ConsumerState<ProductDetailView> createState() =>
      _TourTicketDetailViewState();
}

class _TourTicketDetailViewState extends ConsumerState<ProductDetailView> {
  @override
  Widget build(BuildContext context) {
    return widget.restaurantDetail != null
        ? Column(children: [
      SizedBox(
        height: 16.H,
      ),
      _buildIntroduction(context, context.strings.text_detailed_information,
          widget.restaurantDetail?.intro ?? ''),
      SizedBox(
        height: 16.H,
      ),
      _buildIntroduction(context, context.strings.terms_and_conditions,
          widget.restaurantDetail?.policy ?? ''),
    ])
        : const SizedBox.shrink();
  }

  Widget _buildIntroduction(
      BuildContext context, String title, String content) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().whiteBackgroundColor,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      child: ExpandedViewV2(
        title: title,
        content: Markdown(
          padding: EdgeInsets.symmetric(horizontal: 5.W, vertical: 7.H)
              .copyWith(top: 0),
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          data: content,
          extensionSet: md.ExtensionSet(
            md.ExtensionSet.gitHubFlavored.blockSyntaxes,
            <md.InlineSyntax>[
              md.EmojiSyntax(),
              ...md.ExtensionSet.gitHubFlavored.inlineSyntaxes,
            ],
          ),
        ),
      ),
    );
  }
}
