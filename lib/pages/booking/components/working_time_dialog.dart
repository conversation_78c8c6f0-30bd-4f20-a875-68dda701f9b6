part of 'header_restaurant_area.dart';

class WorkingTimeDialogContent extends StatelessWidget {
  final WorkingTimeResponse data;

  const WorkingTimeDialogContent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    const vietnameseDays = {
      'monday': 'Thứ 2',
      'tuesday': 'Thứ 3',
      'wednesday': 'Thứ 4',
      'thursday': 'Thứ 5',
      'friday': 'Thứ 6',
      'saturday': 'Thứ 7',
      'sunday': 'Chủ nhật',
    };

    final dayEntries = data.orderedDayEntries;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.H, horizontal: 12.W),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TripcText(
            context.strings.work_time,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            textAlign: TextAlign.center,
            textColor: AppAssets.origin().blue1E40,
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: 18.H),
          Sized<PERSON>ox(
            height: 255.H,
            child: Scrollbar(
              thumbVisibility: true,
              child: ListView.separated(
                itemCount: dayEntries.length,
                separatorBuilder: (_, __) => SizedBox(height: 8.H),
                itemBuilder: (context, index) {
                  final entry = dayEntries[index];
                  final dayLabel = vietnameseDays[entry.key]!;
                  final slots = entry.value;
                  final labelColor = slots.any((s) => s.isCurrent == true)
                      ? AppAssets.origin().blue0365
                      : AppAssets.origin().blackColor;

                  return Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.H),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TripcText(
                          dayLabel,
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          height: 2,
                          textColor: labelColor,
                        ),
                        Visibility(
                          visible: slots.isNotEmpty,
                          replacement: TripcText(
                            context.strings.close,
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            height: 1.5,
                            textColor:
                                AppAssets.origin().textColorGlassTextField,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: slots.map((slot) {
                              final time =
                                  '${slot.startTime} - ${slot.endTime}';
                              return TripcText(
                                time,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                height: 1.5,
                                textColor: (slot.isCurrent ?? false)
                                    ? AppAssets.origin().blue0365
                                    : AppAssets.origin().blackColor,
                              );
                            }).toList(),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
          SizedBox(height: context.spacingBottom),
        ],
      ),
    );
  }
}
