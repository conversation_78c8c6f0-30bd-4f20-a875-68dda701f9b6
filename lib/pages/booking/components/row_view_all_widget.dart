import 'package:flutter/cupertino.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/supplier_response.dart';
import 'package:tripc_app/pages/homepage/components/booking_item.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/tour_shimmer_loading/tour_shimmer_loading.dart';
import 'package:tripc_app/widgets/commons/view_all_row/view_all_row_v2.dart';

class RowViewAllListWidget extends StatelessWidget {
  const RowViewAllListWidget(
      {super.key,
      required this.title,
      required this.services,
      required this.typeProduct,
      required this.category,
      this.onTapViewAll,
      required this.isLoading,
      this.listViewPadding = EdgeInsets.zero});

  final String title;
  final List<Supplier> services;
  final TripCServiceCategory category;
  final String typeProduct;
  final Function()? onTapViewAll;
  final bool isLoading;
  final EdgeInsets listViewPadding;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ViewAllRowV2(
          title: title,
          onTapViewAll: onTapViewAll,
          buttonText: context.strings.text_see_more,
          fontStyle: FontStyle.normal,
        ),
        SizedBox(
          height: 260.H,
          child: Visibility(
            visible: !isLoading,
            replacement: const ListTourShimmerLoading(),
            child: ListView.separated(
              clipBehavior: Clip.none,
              shrinkWrap: true,
              padding: listViewPadding,
              itemCount: services.length,
              scrollDirection: Axis.horizontal,
              separatorBuilder: (context, _) => SizedBox(width: 18.W),
              itemBuilder: (context, index) {
                return BookingItem(
                  service: services[index],
                  category: category,
                  onTap: () {
                    AppRoute.pushNamed(
                      context,
                      routeName: AppRoute.routeRestaurantDetailView,
                      arguments: {
                        'id': services[index].id,
                        'category': category,
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
