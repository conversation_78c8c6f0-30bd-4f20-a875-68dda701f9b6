import 'package:flutter/cupertino.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/product_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';
import 'package:html2md/html2md.dart' as html2md;

class FoodItem extends StatelessWidget {
  final ProductResponse item;
  final VoidCallback? onTap;
  const FoodItem({super.key, required this.item, this.onTap});

  @override
  Widget build(BuildContext context) {
    // String descriptionText = html2md.convert(item.description ?? '');
    return GestureDetector(
      onTap: onTap?.call,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.SP),
        child: Container(
          color: AppAssets.origin().whiteBackgroundColor,
          child: Row(children: [
            Expanded(
              child: BaseCachedNetworkImage(
                imageUrl: item.thumbnail ?? '',
                height: 94.H,
                placeholder: (context, _) => Container(
                  color: AppAssets.origin().lightGrayDD4,
                ),
                errorWidget: (context, error, stackTrace) =>
                    AppAssets.origin().icErrorImg.widget(
                      color: context.appCustomPallet.buttonBG,
                    ),
                fit: BoxFit.fitHeight,
              ),),
            Expanded(
                flex: 2,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.W, vertical: 10.H),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 4.H,
                    children: [
                      TripcText(
                        item.name,
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        height: 1.3,
                        textAlign: TextAlign.start,
                        textColor: AppAssets.origin().colorTextFoodName,
                      ),
                      TripcText(
                        item.note,
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                        textAlign: TextAlign.start,
                        textColor: AppAssets.origin().neutralN7,
                      ),
                      TripcText(
                        item.price?.vnd ?? 0.vnd,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        textAlign: TextAlign.start,
                        textColor: AppAssets.origin().neutralN7,
                      ),
                    ],
                  ),
                )
            ),
          ],),
        ),
    ),
    );
  }
}
