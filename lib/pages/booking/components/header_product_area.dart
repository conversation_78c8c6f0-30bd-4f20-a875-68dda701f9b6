import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/detail_supplier_response.dart';
import 'package:tripc_app/pages/booking/components/rating_widget_detail_restaurant.dart';
import 'package:tripc_app/pages/ticket_tour/components/address_link_widget.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_constants.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class HeaderProductDetailArea extends StatelessWidget {
  final DetailSupplier resDetail;

  final TripCServiceCategory category;

  const HeaderProductDetailArea(
      {super.key, required this.resDetail, required this.category});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.W).copyWith(bottom: 0),
      padding: EdgeInsets.all(10.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().whiteBackgroundColor,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            clipBehavior: Clip.none,
            children: [
              TripcText(
                resDetail.name,
                fontSize: 20,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().colorTextFoodName,
              ),
            ],
          ),
          SizedBox(height: 4.H),
          TripcRichText(
            text: '',
            textAlign: TextAlign.start,
            children: [
              TextSpan(
                text: '${context.strings.serving_at} ',
                style: AppAssets.origin().normalTextStyle.copyWith(
                    fontSize: 12.SP,
                    fontWeight: FontWeight.w400,
                    color: AppAssets.origin().neutralN7,
                ),
              ),
              TextSpan(
                  text: 'Provider',
                  style: AppAssets.origin().normalTextStyle.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 12.SP,
                    color: AppAssets.origin().primaryColorV2,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      // TODO: Mock up onTap for provider
                    }),
            ],
          ),
          SizedBox(height: 8.H),
          Row(
            spacing: 4.W,
            children: [
              AppAssets.origin().iconServing.widget(
                  height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
              TripcText(
                'Chỉ phục vụ vào buổi tối',
                fontSize: 12,
                ignorePointer: true,
                fontWeight: FontWeight.w400,
                textAlign: TextAlign.start,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textColor: AppAssets.origin().neutralN7,
              ),
            ],
          ),
          SizedBox(height: 8.H),
          AddressLink(
            address: resDetail.fullAddress ?? '',
            textDecoration: TextDecoration.none,
            textColor: AppAssets.origin().neutralN7,
            spacing: 4
          ),
        ],
      ),
    );
  }

  Widget _rowInformationCategory(String info) {
    return Row(
      spacing: 4.W,
      children: [
        _getIconType(),
        TripcText(
          info,
          fontSize: 12,
          ignorePointer: true,
          fontWeight: FontWeight.w400,
          textAlign: TextAlign.start,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textColor: AppAssets.origin().neutralN7,
        ),
      ],
    );
  }

  Widget _getIconType() {
    final Map<String, Widget> iconMap = {
      AppConstants.slugFood: AppAssets.origin().iconFoodType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
      AppConstants.slugEntertainment: AppAssets.origin().iconKaraokeType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
      AppConstants.slugHealthAndBeauty: AppAssets.origin().iconSpaType.widget(
          height: 16.W, width: 16.W, color: AppAssets.origin().darkBlueColor),
    };

    return iconMap[category.serviceTypeSlug] ?? Container();
  }
}
