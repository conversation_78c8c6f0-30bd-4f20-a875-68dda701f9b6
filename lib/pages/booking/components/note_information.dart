import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:html2md/html2md.dart' as html2md;
import 'package:markdown/markdown.dart' as md;
import 'package:tripc_app/utils/app_extension.dart';

import '../../../services/app/app_assets.dart';
import '../../../widgets/commons/tripc_button/tripc_icon_button.dart';
import '../../../widgets/commons/tripc_text/tripc_text.dart';
import '../../my_trip/components/order_detail_bottom_sheet_v2.dart';

class NoteInformationWidget extends StatelessWidget {
  const NoteInformationWidget({super.key, required this.htmlText});
  final String htmlText;

  @override
  Widget build(BuildContext context) {
    final markdown = html2md.convert(htmlText);
    TextSpan textSpan = TextSpan(
      text: markdown,
      style: TextStyle(
        fontSize: 14.SP,
        fontWeight: FontWeight.w700,
      ),
    );
    TextPainter textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.start,
      textDirection: TextDirection.ltr,
      maxLines: 3,
    );

    textPainter.layout(maxWidth: MediaQuery.of(context).size.width - 32.W);
    bool isLongText = textPainter.didExceedMaxLines;
    String truncatedText =
        isLongText ? '${markdown.split('\n').take(3).join('\n')}...' : markdown;

    return Visibility(
      visible: htmlText.isNotEmpty,
      child: Container(
        width: double.infinity,
        margin: EdgeInsets.symmetric(horizontal: 16.W),
        decoration: BoxDecoration(
          color: AppAssets.origin().yellow7E5,
          borderRadius: BorderRadius.circular(12.SP),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 9.W,
          vertical: 20.H,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TripcText(
                  context.strings.text_information_to_note,
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().blackColor,
                ),
                Visibility(
                  visible: isLongText,
                  child: TripcIconButton(
                    onPressed: () => showDialog(
                        context: context,
                        builder: (context) => Dialog(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12.SP),
                              ),
                              backgroundColor: Colors.white,
                              insetPadding: EdgeInsets.symmetric(
                                  horizontal: 16.W, vertical: 16.H),
                              child: OrderDetailBottomSheetV2(
                                content: htmlText,
                                title: context.strings.text_information_to_note,
                              ),
                            )),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TripcText(
                          context.strings.text_detail,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          textColor: AppAssets.origin().yellowEAA,
                          ignorePointer: true,
                        ),
                        SizedBox(
                          width: 4.W,
                        ),
                        AppAssets.origin().icParkOutlineRight.widget(
                              width: 16.H,
                              height: 16.H,
                              color: AppAssets.origin().yellowEAA,
                            ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.H),
            Markdown(
              padding: EdgeInsets.symmetric(horizontal: 5.W, vertical: 7.H)
                  .copyWith(top: 0),
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              data: truncatedText,
              extensionSet: md.ExtensionSet(
                md.ExtensionSet.gitHubFlavored.blockSyntaxes,
                <md.InlineSyntax>[
                  md.EmojiSyntax(),
                  ...md.ExtensionSet.gitHubFlavored.inlineSyntaxes,
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}