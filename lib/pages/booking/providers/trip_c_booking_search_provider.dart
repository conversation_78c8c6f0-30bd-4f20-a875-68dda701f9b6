import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/app/passenger_information.dart';
import 'package:tripc_app/models/remote/api_tour_response/service_type_response.dart';
import 'package:tripc_app/models/remote/api_tour_response/tour_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/supplier_response.dart';
import 'package:tripc_app/models/remote/search_response/keyword_response.dart';
import 'package:tripc_app/services/apis/tours/api_tours.dart';

class BookingStateModel {
  PassengerInfo? passengerInfo;
  DateTime? selectedDate;
  AttributeValue? selectedSeatType;
  final String keyWord;
  final List<ServiceType> listServiceTypeTabs;
  final Map<int, List<Supplier>> listTourTabs;
  final List<Supplier> searchList;
  final Map<int, bool> viewModesListView;
  final bool isLoading;
  final ServiceType serviceType;
  List<KeywordResponse>? suggestTypingSearch;
  final bool isSearching;

  BookingStateModel({
    this.passengerInfo,
    this.selectedDate,
    this.selectedSeatType,
    this.keyWord = '',
    this.listServiceTypeTabs = const [],
    this.listTourTabs = const {},
    this.viewModesListView = const {},
    this.isLoading = false,
    this.suggestTypingSearch,
    this.searchList = const [],
    this.isSearching = false,
    this.serviceType = const ServiceType(name: 'Tất cả'),
  });

  static BookingStateModel getDefault() {
    return BookingStateModel();
  }

  BookingStateModel copyWith({
    PassengerInfo? passengerInfo,
    DateTime? selectedDate,
    AttributeValue? selectedSeatType,
    String? keyWord,
    bool? isSearching,
    List<ServiceType>? listServiceTypeTabs,
    List<TourResponse>? myRecentlyTour,
    Map<int, List<Supplier>>? listTourTabs,
    Map<int, bool>? viewModesListView,
    bool? isLoading,
    List<KeywordResponse>? suggestTypingSearch,
    ServiceType? serviceType,
    List<Supplier>? searchList,
  }) {
    return BookingStateModel(
        passengerInfo: passengerInfo ?? this.passengerInfo,
        selectedDate: selectedDate ?? this.selectedDate,
        selectedSeatType: selectedSeatType ?? this.selectedSeatType,
        keyWord: keyWord ?? this.keyWord,
        listServiceTypeTabs: listServiceTypeTabs ?? this.listServiceTypeTabs,
        listTourTabs: listTourTabs ?? this.listTourTabs,
        viewModesListView: viewModesListView ?? this.viewModesListView,
        isLoading: isLoading ?? this.isLoading,
        suggestTypingSearch: suggestTypingSearch ?? this.suggestTypingSearch,
        serviceType: serviceType ?? this.serviceType,
        searchList: searchList ?? this.searchList,
        isSearching: isSearching ?? this.isSearching);
  }
}

class BookingProvider extends StateNotifier<BookingStateModel> {
  BookingProvider(super._state);

  final ApiTours _api = ApiTours();

  void setLoading(bool value) {
    state = state.copyWith(
      isLoading: value,
    );
  }

  void initializeViewModesListView(List<ServiceType> serviceTypes) {
    final initialViewModes = <int, bool>{};
    for (var serviceType in serviceTypes) {
      initialViewModes[serviceType.id ?? 0] = false;
    }

    state = state.copyWith(viewModesListView: initialViewModes);
  }

  void updateViewModeById(int id, bool value) {
    final updatedViewModes = Map<int, bool>.from(state.viewModesListView);
    updatedViewModes[id] = value;
    state = state.copyWith(viewModesListView: updatedViewModes);
  }

  Future<void> getServiceTypesCategory(String slug) async {
    setLoading(true);
    try {
      final result = await _api.getServiceTypeByParentSlug(slug: slug).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.status) {
        state =
            state.copyWith(listServiceTypeTabs: result.data?.children ?? []);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      setLoading(false);
    }
  }

  void onChangeSearchMode(String value) {
    print(value != '');
    state = state.copyWith(isSearching: value != '', keyWord: value);
  }

  Future<void> getProductTypes(String slug) async {
    setLoading(true);
    try {
      final result = await _api.getProductTypes(supplierTypeSlug: slug).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.status) {
        state = state.copyWith(listServiceTypeTabs: result.data);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      setLoading(false);
    }
  }

  Future<void> getRestaurantsByTypeId(
      int categoryId, ListDataRequest request) async {
    setLoading(true);
    try {
      final result = await _api
          .getBookingSuppliersByTypeId(
              request: request, productTypeId: categoryId)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );

      if (result.status) {
        final updatedTourTabs =
            Map<int, List<Supplier>>.from(state.listTourTabs);
        updatedTourTabs[categoryId] = result.data ?? [];

        state = state.copyWith(listTourTabs: updatedTourTabs);
      }

      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {}
      setLoading(false);
    }
  }

  Future<void> searchRestaurant(ListDataRequest request, String slug) async {
    setLoading(true);
    request = request.copyWith(keyWord: state.keyWord);
    try {
      final result =
          await _api.getRestaurantsBySlug(request: request, slug: slug).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      if (result.status) {
        state = state.copyWith(searchList: result.data ?? []);
      }

      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {}
      setLoading(false);
    }
  }

  Future<void> getMyRecentlyTour() async {
    setLoading(true);
    try {
      final result = await _api
          .getRecentlyViewed(request: ListDataRequest(page: 1, pageSize: 4))
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.status) {
        state = state.copyWith(myRecentlyTour: result.data);
      }

      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {}
      setLoading(false);
    }
  }

  void onChangeServiceType(ServiceType newType) {
    if (state.serviceType != newType) {
      state = state.copyWith(serviceType: newType);
    }
  }

  void clearCacheData() {
    state = state.copyWith(
      listServiceTypeTabs: const [],
      listTourTabs: const {},
      viewModesListView: const {},
      serviceType: const ServiceType(name: 'Tất cả'),
    );
  }
}

final pBookingSearchProvider =
    StateNotifierProvider<BookingProvider, BookingStateModel>(
  (ref) => BookingProvider(
    BookingStateModel.getDefault(),
  ),
);
