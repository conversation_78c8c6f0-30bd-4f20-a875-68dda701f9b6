import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/booking_request/booking_request.dart';
import 'package:tripc_app/services/apis/booking/api_booking.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../generated/l10n.dart';
import '../../../models/app/attribute_enum.dart';
import '../../../models/app/contact_request.dart';
import '../../../models/app/payment_request.dart';
import '../../../models/remote/api_tour_response/sub_product_response.dart';
import '../../../models/remote/booking_response/service_responses/attribute_response.dart';
import '../../../models/remote/booking_response/service_responses/detail_booking_service_response.dart';
import '../../../models/remote/common_error.dart';
import '../../../services/apis/contact/api_contact.dart';
import '../../../utils/app_log.dart';
import '../../../utils/app_validation.dart';

class AddPassengerQuantityCuisineModel {
  final DetailBookingService? detailCuisine;

  //cache selected datetime
  DateTime? selectedDate;
  String? selectedTime;

  //cache contact information
  final String phoneContact;
  final String phoneContactError;
  final String nameContact;
  final String nameContactError;
  final String emailContact;
  final String emailContactError;
  final String specialRequest;

  final bool isLoading;
  final String? error;

  AddPassengerQuantityCuisineModel(
      {this.detailCuisine,
      this.selectedDate,
      this.selectedTime,
      this.phoneContact = '',
      this.nameContact = '',
      this.emailContact = '',
      this.phoneContactError = '',
      this.nameContactError = '',
      this.emailContactError = '',
      this.specialRequest = '',
      this.isLoading = false,
      this.error});

  static AddPassengerQuantityCuisineModel getDefault() {
    return AddPassengerQuantityCuisineModel();
  }

  AddPassengerQuantityCuisineModel copyWith(
          {DetailBookingService? detailCuisine,
          DateTime? selectedDate,
          String? selectedTime,
          String? phoneContact,
          String? nameContact,
          String? emailContact,
          String? phoneContactError,
          String? nameContactError,
          String? emailContactError,
          String? specialRequest,
          bool? isLoading,
          String? error}) =>
      AddPassengerQuantityCuisineModel(
          detailCuisine: detailCuisine ?? this.detailCuisine,
          selectedDate: selectedDate ?? this.selectedDate,
          selectedTime: selectedTime ?? this.selectedTime,
          phoneContact: phoneContact ?? this.phoneContact,
          nameContact: nameContact ?? this.nameContact,
          emailContact: emailContact ?? this.emailContact,
          phoneContactError: phoneContactError ?? this.phoneContactError,
          nameContactError: nameContactError ?? this.nameContactError,
          emailContactError: emailContactError ?? this.emailContactError,
          specialRequest: specialRequest ?? this.specialRequest,
          isLoading: isLoading ?? this.isLoading,
          error: error);

  bool get isButtonEnable =>
      selectedDate.isNotNull &&
      selectedTime.isNotNull &&
      nameContact.isNotEmpty &&
      nameContactError.isEmpty &&
      phoneContact.isNotEmpty &&
      phoneContactError.isEmpty &&
      emailContact.isNotEmpty &&
      emailContactError.isEmpty &&
      (detailCuisine?.subProducts?.any((subProduct) => subProduct.stock != 0) ??
          false);

  BookingRequest get bookingRequest => BookingRequest(
      productId: detailCuisine?.id ?? 0,
      description: specialRequest.isNotEmpty
          ? specialRequest
          : S.current.text_do_not_have,
      order: detailCuisine?.subProducts
              ?.where((subProduct) => subProduct.stock != 0)
              .map((subProduct) => OrderRequest(
                    subProductId: subProduct.id,
                    quantity: subProduct.stock,
                  ))
              .toList() ??
          [],
      attributes: detailCuisine?.attributes
              ?.firstWhereOrNull(
                  (a) => a.type == AttributeEnum.time) //only get attribute_time
              ?.values
              .whereType<AttributeTime>()
              .map((e) =>
                  AttributeRequest(id: AttributeEnum.time.id, valueId: e.id))
              .take(1)
              .toList() ??
          [],
      date: selectedDate?.formatyyyyMMdd(),
      time: selectedTime);
}

class AddPassengerQuantityCuisineProvider
    extends StateNotifier<AddPassengerQuantityCuisineModel> {
  AddPassengerQuantityCuisineProvider(super._state);
  final ApiBooking _api = ApiBooking();
  final ApiContact _apiContact = ApiContact();

  //////////////////get
  Future<void> init(int? seatProductId) async {
    setLoading(true);
    try {
      await Future.wait([
        getDetailCuisine(seatProductId),
        getContact(),
      ]);
    } catch (e) {
      logger.e('Init error: $e');
    } finally {
      setLoading(false);
    }
  }

  Future<void> getDetailCuisine(int? seatProductId) async {
    try {
      final result =
          await _api.getDetailCuisine(id: seatProductId ?? 0).timeout(
                const Duration(
                  seconds: 30,
                ),
              );
      if (result.status ?? false) {
        state = state.copyWith(detailCuisine: result.data);
      }
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
    }
  }

  Future<void> getContact() async {
    setLoading(true);
    try {
      final result = await _apiContact.getMyContact().timeout(
            const Duration(
              seconds: 30,
            ),
          );
      final contact =
          result.data.firstWhereOrNull((element) => element.isDefault == 1);
      state = state.copyWith(
        nameContact: contact?.fullname ?? '',
        emailContact: contact?.email ?? '',
        phoneContact: contact?.phone ?? '',
      );
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
    }
  }

  ///////////////////set
  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setErrorMessage(String? value) {
    state = state.copyWith(error: value);
  }

  void selectDate({required DateTime? date}) {
    if (date == null) return;
    state = state.copyWith(selectedDate: date);
  }

  void selectTime({required String timeFrame}) {
    state = state.copyWith(selectedTime: timeFrame);
  }

  void setFullName(BuildContext context, String value) {
    state = state.copyWith(
      nameContact: value,
      nameContactError: ValidationAccount.fullNameValidation(context, value),
    );
  }

  void setPhoneNumber(String value, BuildContext context) {
    state = state.copyWith(
      phoneContact: value,
      phoneContactError:
          ValidationAccount.phoneNumberWithZeroValidation(context, value),
    );
  }

  void setEmail(BuildContext context, {required String value}) {
    state = state.copyWith(
        emailContact: value,
        emailContactError: ValidationAccount.emailValidation(context, value));
  }

  void setSpecialRequest(BuildContext context, {required String value}) {
    state = state.copyWith(specialRequest: value);
  }

  //////////

  void onTapMinusQtt(SubProductResponse subProduct) {
    DetailBookingService? cacheCuisine = state.detailCuisine;

    final cacheSubProducts = state.detailCuisine?.subProducts?.toList() ?? [];
    final idx = cacheSubProducts.indexWhere((element) => element == subProduct);
    cacheSubProducts[idx] = cacheSubProducts[idx].copyWith(
      stock: subProduct.stock - 1,
    );

    cacheCuisine = cacheCuisine?.copyWith(
      subProducts: cacheSubProducts,
    );
    state = state.copyWith(
      detailCuisine: cacheCuisine,
    );
  }

  void onTapAddQtt(SubProductResponse subProduct) {
    DetailBookingService? cacheCuisine = state.detailCuisine;

    final cacheSubProducts = state.detailCuisine?.subProducts?.toList() ?? [];
    final idx = cacheSubProducts.indexWhere((element) => element == subProduct);
    cacheSubProducts[idx] = cacheSubProducts[idx].copyWith(
      stock: subProduct.stock + 1,
    );

    cacheCuisine = cacheCuisine?.copyWith(
      subProducts: cacheSubProducts,
    );
    state = state.copyWith(
      detailCuisine: cacheCuisine,
    );
  }

  Future<bool> addContact() async {
    try {
      setLoading(true);
      final result = await _apiContact
          .addContact(
              request: ContactRequest(
                  fullname: state.nameContact,
                  email: state.emailContact,
                  phone: state.phoneContact,
                  isDefault: 1))
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      return result;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
      return false;
    }
  }

  void resetState() {
    state = AddPassengerQuantityCuisineModel.getDefault();
  }
}

final pAddPassengerQuantityProvider = StateNotifierProvider<
        AddPassengerQuantityCuisineProvider, AddPassengerQuantityCuisineModel>(
    (ref) => AddPassengerQuantityCuisineProvider(
        AddPassengerQuantityCuisineModel.getDefault()));
