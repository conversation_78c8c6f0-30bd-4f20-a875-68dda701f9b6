import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/detail_supplier_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/apis/tours/api_tours.dart';
import 'package:tripc_app/utils/app_extension.dart';

enum BookingStep { viewDetail, viewAllProduct }

class BookingDetailStateModel {
  DetailSupplier? restaurantDetail;
  final bool isLoading;
  String? errorMessage;
  BookingStep? step;

  BookingDetailStateModel(
      {this.errorMessage,
      this.isLoading = false,
      this.restaurantDetail,
      this.step});

  BookingDetailStateModel copyWith(
      {DetailSupplier? restaurantDetail,
      bool? isLoading,
      String? errorMessage,
      BookingStep? step}) {
    return BookingDetailStateModel(
        restaurantDetail: restaurantDetail ?? this.restaurantDetail,
        isLoading: isLoading ?? this.isLoading,
        errorMessage: errorMessage ?? this.errorMessage,
        step: step ?? this.step);
  }

  static BookingDetailStateModel getDefault() => BookingDetailStateModel();
}

class BookingDetailProvider extends StateNotifier<BookingDetailStateModel> {
  BookingDetailProvider(super._state);

  final ApiTours _api = ApiTours();

  void setLoading(bool value) {
    state = state.copyWith(
      isLoading: value,
    );
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void updateStep(BookingStep newStep) {
    state = state.copyWith(
      step: newStep,
    );
  }

  Future<void> getDetailRestaurant({required int? restaurantId}) async {
    setLoading(true);
    try {
      final result = await _api
          .getDetailBookingSupplier(restaurantId: restaurantId ?? 0)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.data.isNotNull) {}
      setLoading(false);
      state = state.copyWith(restaurantDetail: result.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  void resetDetailedPage() {
    state = state.copyWith(
        step: BookingStep.viewDetail,
        errorMessage: null,
        restaurantDetail: null);
  }
}

final pBookingDetailProvider =
    StateNotifierProvider<BookingDetailProvider, BookingDetailStateModel>(
  (ref) => BookingDetailProvider(
    BookingDetailStateModel.getDefault(),
  ),
);
