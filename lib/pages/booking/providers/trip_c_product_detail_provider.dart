import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/detail_supplier_response.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/services/apis/tours/api_tours.dart';

class ProductDetailStateModel {
  DetailSupplier? restaurantDetail;
  final bool isLoading;
  String? errorMessage;

  ProductDetailStateModel({
    this.errorMessage,
    this.isLoading = false,
    this.restaurantDetail,
  });

  ProductDetailStateModel copyWith({
    DetailSupplier? restaurantDetail,
    bool? isLoading,
    String? errorMessage,
  }) {
    return ProductDetailStateModel(
      restaurantDetail: restaurantDetail ?? this.restaurantDetail,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  static ProductDetailStateModel getDefault() => ProductDetailStateModel();
}

class ProductDetailProvider extends StateNotifier<ProductDetailStateModel> {
  ProductDetailProvider(super._state);

  final ApiTours _api = ApiTours();

  void setLoading(bool value) {
    state = state.copyWith(
      isLoading: value,
    );
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  Future<void> getDetailRestaurant({required int? restaurantId}) async {
    setLoading(true);
    try {
      final result = await _api
          .getDetailBookingSupplier(restaurantId: restaurantId ?? 0)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      setLoading(false);
      state = state.copyWith(restaurantDetail: result.data);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  void resetDetailedPage() {
    state = state.copyWith(errorMessage: null, restaurantDetail: null);
  }
}

final pProductDetailProvider =
    StateNotifierProvider<ProductDetailProvider, ProductDetailStateModel>(
  (ref) => ProductDetailProvider(
    ProductDetailStateModel.getDefault(),
  ),
);
