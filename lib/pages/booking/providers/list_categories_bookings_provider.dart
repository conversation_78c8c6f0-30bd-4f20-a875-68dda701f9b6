import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/remote/common_error.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/list_supplier_response.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/supplier_response.dart';
import 'package:tripc_app/services/apis/tours/api_tours.dart';
import 'package:tripc_app/utils/app_enum.dart';

class ListCategoriesBookingModel {
  ListCategoriesBookingModel({
    this.errorMessage,
    this.viewMoreRestaurant = const [],
    this.canLoadMore = false,
    this.isLoadingLoadMore = false,
    this.isLoading = false,
    this.page = 1,
    this.pageSize = 20,
  });
  final bool isLoading;
  final List<Supplier> viewMoreRestaurant;
  final bool canLoadMore;
  final bool isLoadingLoadMore;
  final int page;
  final int pageSize;
  final String? errorMessage;

  static ListCategoriesBookingModel getDefault() {
    return ListCategoriesBookingModel(
        page: 1,
        pageSize: 20,
        isLoading: false,
        isLoadingLoadMore: false,
        errorMessage: null,
        viewMoreRestaurant: []);
  }

  ListCategoriesBookingModel copyWith({
    bool? isLoading,
    bool? isLoadingLoadMore,
    bool? canLoadMore,
    List<Supplier>? viewMoreRestaurant,
    int? page,
    int? pageSize,
    String? errorMessage,
  }) {
    return ListCategoriesBookingModel(
      viewMoreRestaurant: viewMoreRestaurant ?? this.viewMoreRestaurant,
      isLoading: isLoading ?? this.isLoading,
      canLoadMore: canLoadMore ?? this.canLoadMore,
      isLoadingLoadMore: isLoadingLoadMore ?? this.isLoadingLoadMore,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class TicketTourScreenProvider
    extends StateNotifier<ListCategoriesBookingModel> {
  TicketTourScreenProvider(super._state);
  final ApiTours _api = ApiTours();

  void setLoading(bool value) {
    state = state.copyWith(
      isLoading: value,
    );
  }

  void setErrorMessage(String? error) {
    state = state.copyWith(
      errorMessage: error,
    );
  }

  void forceLoadingLoadMore(bool value) {
    state = state.copyWith(isLoadingLoadMore: value);
  }

  void setPageGetData({required int page, int pageSize = 20, String? keyword}) {
    state = state.copyWith(page: page, pageSize: pageSize);
  }

  void viewMoreCategoriesTours(
      {required int id, FerryType? ferryType = FerryType.catBaTuanChau}) {
    state = state.copyWith(viewMoreRestaurant: []);
    getToursByServiceType(id, viewMore: true);
  }

  Future<void> getToursByServiceType(int id,
      {bool viewMore = false, bool useLoading = true}) async {
    setLoading(useLoading);
    final request = ListDataRequest(page: state.page, pageSize: state.pageSize);
    try {
      final result = await _api
          .getBookingSuppliersByTypeId(request: request, productTypeId: id)
          .timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (viewMore) {
        appendListViewMoreTour(result);
      }
      setLoading(false);
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
      setLoading(false);
    }
  }

  void appendListViewMoreTour(ListSupplierResponse result) {
    if (state.viewMoreRestaurant.isEmpty) {
      state = state.copyWith(
        viewMoreRestaurant: result.data,
      );
      state = state.copyWith(
          canLoadMore: state.viewMoreRestaurant.length < (result.total ?? 0));
      return;
    }
    state = state.copyWith(viewMoreRestaurant: [
      ...state.viewMoreRestaurant,
      ...result.data ?? []
    ]);
    state = state.copyWith(
        canLoadMore: state.viewMoreRestaurant.length < (result.total ?? 0));
    forceLoadingLoadMore(false);
  }

  void loadMoreCategories(
      {required int id, FerryType? ferryType = FerryType.catBaTuanChau}) {
    if (!state.canLoadMore) return;
    forceLoadingLoadMore(true);
    setPageGetData(page: state.page + 1, pageSize: 20);
    getToursByServiceType(id, viewMore: true, useLoading: false);
  }

  void resetState() {
    state = ListCategoriesBookingModel.getDefault();
  }

  void resetListTour() {
    state = state.copyWith(
      viewMoreRestaurant: [],
    );
  }
}

final pListCategoriesBookings =
    StateNotifierProvider<TicketTourScreenProvider, ListCategoriesBookingModel>(
        (ref) =>
            TicketTourScreenProvider(ListCategoriesBookingModel.getDefault()));
