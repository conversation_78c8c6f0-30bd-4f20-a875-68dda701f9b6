import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/apis/booking/api_booking.dart';
import 'package:tripc_app/utils/app_extension.dart';

import '../../../models/app/booking_request/booking_request.dart';
import '../../../models/app/payment_request.dart';
import '../../../models/remote/common_error.dart';
import '../../tour-payment/views/electronic_invoice.dart';

class BookingModel {
  final InvoiceType invoiceType;
  final String? nameInvoice;
  final String? addressInvoice;
  final String? taxCodeInvoice;
  final String? phoneInvoice;
  final String? emailInvoice;

  final bool isLoading;
  final String? error;

  final bool? isBookingStatus;

  final int? orderId;

  BookingModel(
      {this.invoiceType = InvoiceType.individual,
      this.nameInvoice,
      this.addressInvoice,
      this.taxCodeInvoice,
      this.phoneInvoice,
      this.emailInvoice,
      this.isLoading = false,
      this.error,
      this.isBookingStatus,
      this.orderId});

  static BookingModel getDefault() {
    return BookingModel();
  }

  BookingModel copyWith(
          {InvoiceType? invoiceType,
          String? nameInvoice,
          String? addressInvoice,
          String? taxCodeInvoice,
          String? phoneInvoice,
          String? emailInvoice,
          bool? isLoading,
          String? error,
          bool? isBookingStatus,
          int? orderId}) =>
      BookingModel(
          invoiceType: invoiceType ?? this.invoiceType,
          nameInvoice: nameInvoice ?? this.nameInvoice,
          addressInvoice: addressInvoice ?? this.addressInvoice,
          taxCodeInvoice: taxCodeInvoice ?? this.taxCodeInvoice,
          phoneInvoice: phoneInvoice ?? this.phoneInvoice,
          emailInvoice: emailInvoice ?? this.emailInvoice,
          isLoading: isLoading ?? this.isLoading,
          error: error,
          isBookingStatus: isBookingStatus,
          orderId: orderId ?? this.orderId);
}

class BookingProvider extends StateNotifier<BookingModel> {
  BookingProvider(super._state);
  final ApiBooking _api = ApiBooking();

  void resetState() {
    state = BookingModel.getDefault();
  }

  ///////////////////set
  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setErrorMessage(String? value) {
    state = state.copyWith(error: value);
  }

  void setInvoice(
      {InvoiceType? invoiceType,
      String? name,
      String? phone,
      String? email,
      String? address,
      String? taxCode}) {
    state = state.copyWith(
        invoiceType: invoiceType,
        nameInvoice: name,
        phoneInvoice: phone,
        emailInvoice: email,
        addressInvoice: address,
        taxCodeInvoice: taxCode);
  }

  void setBookingStatus(bool? value) {
    state = state.copyWith(isBookingStatus: value);
  }

  void setOrderId(int? id) {
    state = state.copyWith(orderId: id);
  }

  ////////call api
  Future<void> onPayment({required BookingRequest bookingRequest}) async {
    setLoading(true);
    try {
      final request = bookingRequest.copyWith(
        invoice: state.nameInvoice.isNotNull
            ? InvoiceRequest(
                type: state.invoiceType.name,
                name: state.nameInvoice,
                email: state.emailInvoice,
                phoneNo: state.invoiceType == InvoiceType.individual
                    ? state.phoneInvoice
                    : null,
                address: state.invoiceType == InvoiceType.business
                    ? state.addressInvoice
                    : null,
                taxCode: state.invoiceType == InvoiceType.business
                    ? state.taxCodeInvoice
                    : null,
              )
            : null,
      );
      final result = await _api.createBooking(request: request).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      if (result.status) {
        setOrderId(result.data?.orderId);
      }
      setBookingStatus(result.status);
      return;
    } catch (exceptionMessage) {
      if (exceptionMessage is CommonErrorResponse) {
        setErrorMessage(exceptionMessage.error);
      }
    }
    setLoading(false);
    setBookingStatus(false);
  }
}

final pBookingProvider = StateNotifierProvider<BookingProvider, BookingModel>(
    (ref) => BookingProvider(BookingModel.getDefault()));
