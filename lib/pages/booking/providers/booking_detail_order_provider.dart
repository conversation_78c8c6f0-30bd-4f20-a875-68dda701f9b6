import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../models/remote/booking_response/order_responses/booking_order_response.dart';
import '../../../services/apis/booking/api_booking.dart';
import '../../../utils/app_log.dart';

class BookingDetailOrderProviderModel {
  BookingDetailOrderProviderModel({
    this.isLoading = false,
    this.orderDetail,
  });

  final bool isLoading;
  final BookingOrder? orderDetail;

  static BookingDetailOrderProviderModel getDefault() {
    return BookingDetailOrderProviderModel();
  }

  BookingDetailOrderProviderModel copyWith({
    bool? isLoading,
    BookingOrder? orderDetail,
  }) {
    return BookingDetailOrderProviderModel(
      isLoading: isLoading ?? this.isLoading,
      orderDetail: orderDetail ?? this.orderDetail,
    );
  }
}

class BookingDetailOrderScreenProvider
    extends StateNotifier<BookingDetailOrderProviderModel> {
  BookingDetailOrderScreenProvider(super._state);

  final ApiBooking _api = ApiBooking();

  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  Future<void> getOrderDetail(int orderId) async {
    setLoading(true);
    try {
      final result = await _api.getDetailBookingOrder(orderId: orderId).timeout(
            const Duration(
              seconds: 30,
            ),
          );
      state = state.copyWith(orderDetail: result.data);
    } catch (exceptionMessage) {
      logger.e(exceptionMessage);
    }
    setLoading(false);
  }

  void resetState() {
    state = BookingDetailOrderProviderModel.getDefault();
  }
}

final pBookingDetailOrderProvider = StateNotifierProvider<
        BookingDetailOrderScreenProvider, BookingDetailOrderProviderModel>(
    (ref) => BookingDetailOrderScreenProvider(
        BookingDetailOrderProviderModel.getDefault()));
