import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/my_trip/components/include_bottom_sheet_v2.dart';
import 'package:tripc_app/pages/my_trip/components/order_detail_bottom_sheet_v2.dart';
import 'package:tripc_app/pages/my_trip/providers/my_trip_provider.dart';
import 'package:tripc_app/pages/tour-payment/components/invoice_status_view_v2.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/common_extension.dart';

import '../../../models/remote/booking_response/order_responses/booking_order_response.dart';
import '../../../widgets/app_loading.dart';
import '../../../widgets/commons/content_shadow_view.dart';
import '../../../widgets/commons/information_row.dart';
import '../../tour-payment/views/electronic_invoice.dart';
import '../providers/booking_detail_order_provider.dart';
import '../components/contact_tripc_button.dart';

class BookingDetailOrderPage extends ConsumerStatefulWidget {
  const BookingDetailOrderPage({super.key, required this.orderId});

  final int? orderId;

  @override
  ConsumerState<BookingDetailOrderPage> createState() =>
      _ReservationDetailPageState();
}

class _ReservationDetailPageState
    extends ConsumerState<BookingDetailOrderPage> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        ref
            .read(pBookingDetailOrderProvider.notifier)
            .getOrderDetail(widget.orderId ?? 0);
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final orderDetail = ref.watch(
        pBookingDetailOrderProvider.select((value) => value.orderDetail));
    final qty = ref.watch(pPaidTourProvider.select(
      (value) => value.qty,
    ));

    final isLoading = ref.watch(pPaidTourProvider.select(
      (value) => value.isLoading,
    ));

    return Stack(
      children: [
        TripcScaffold(
            onPressed: () => unfocusKeyboard(),
            resizeToAvoidBottomInset: false,
            onLeadingPressed: () {
              Navigator.popUntil(
                  context, ModalRoute.withName(AppRoute.routeHome));
            },
            hasBackButton: true,
            hasAppbarBottomLine: false,
            backgroundColor: AppAssets.origin().grayF4F6,
            appBarColor: AppAssets.origin().grayF4F6,
            titleAppBar: TripcText(
              context.strings.text_reservation_details,
              fontWeight: FontWeight.w600,
              fontSize: 16,
              textColor: AppAssets.origin().black,
            ),
            body: Stack(
              children: [
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 90.H,
                  child: RefreshIndicator(
                    onRefresh: () async {
                      ref
                          .read(pPaidTourProvider.notifier)
                          .getOrderDetail(widget.orderId ?? 0);
                    },
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(
                              horizontal: 16.W, vertical: 12.H)
                          .copyWith(bottom: context.mediaQuery.padding.bottom),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ContentShadowView(
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 16.H, horizontal: 16.W),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                spacing: 24.H,
                                children: [
                                  _generalInfo(
                                    context,
                                    orderDetail: orderDetail,
                                  ),
                                  _serviceInfo(context,
                                      orderDetail: orderDetail, qty: qty),
                                  _passengerInfo(context,
                                      orderDetail: orderDetail),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      TripcText(
                                        context.strings.text_special_requests
                                            .toSentenceCase(),
                                        fontSize: 16,
                                        fontWeight: FontWeight.w700,
                                        textAlign: TextAlign.start,
                                        textColor: AppAssets.origin().blue1E40,
                                      ),
                                      TripcText(
                                        orderDetail?.description ?? '',
                                        fontSize: 14,
                                        textAlign: TextAlign.start,
                                        fontWeight: FontWeight.w400,
                                        textColor:
                                            AppAssets.origin().blackColor,
                                        padding: EdgeInsets.only(
                                            left: 8.W, top: 8.H),
                                      ),
                                    ],
                                  ),
                                  TripcRichText(
                                    text: '',
                                    textAlign: TextAlign.start,
                                    children: [
                                      TextSpan(
                                          text:
                                              '${context.strings.text_note_2} ',
                                          style: TextStyle(
                                              fontSize: 13,
                                              fontWeight: FontWeight.w400,
                                              color: AppAssets.origin()
                                                  .redDotColor,
                                              fontStyle: FontStyle.italic)),
                                      TextSpan(
                                          text: context.strings
                                              .text_contact_tripc_to_change_your_information,
                                          style: TextStyle(
                                              fontSize: 13,
                                              fontWeight: FontWeight.w400,
                                              color:
                                                  AppAssets.origin().blackColor,
                                              fontStyle: FontStyle.italic)),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: 12.H),
                          _sectionInfo(context, orderDetail),
                          SizedBox(height: 24.H),
                          _electronicInvoiceInfo(context,
                              orderDetail: orderDetail),
                        ],
                      ),
                    ),
                  ),
                ),
                const Positioned(
                    left: 0, right: 0, bottom: 0, child: ContactTripcButton()),
              ],
            )),
        Visibility(
            visible: isLoading, child: AppLoading(isRequesting: isLoading))
      ],
    );
  }

  Widget _electronicInvoiceInfo(BuildContext context,
      {BookingOrder? orderDetail}) {
    final invoice = orderDetail?.invoice;
    return Visibility(
      visible: orderDetail?.invoice.isNotNull ?? false,
      child: Padding(
        padding: EdgeInsets.only(bottom: 24.H),
        child: InvoiceStatusViewV2(
            status: invoice?.status ?? InvoiceStatus.request,
            onTap: invoice == null
                ? null
                : () => AppRoute.pushNamed(context,
                    routeName: AppRoute.routeElectronicInvoice,
                    arguments: [false, invoice])),
      ),
    );
  }

  Widget _generalInfo(BuildContext context, {BookingOrder? orderDetail}) {
    final double titleWidth = 110.W;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_general_information,
          fontWeight: FontWeight.w700,
          fontSize: 16,
          textColor: AppAssets.origin().blue1E40,
          padding: EdgeInsets.only(bottom: 8.H),
        ),
        InformationRow(
          title: '${context.strings.order_code}:',
          content: orderDetail?.orderCode ?? '',
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
          titleWidth: titleWidth,
        ),
        InformationRow(
          title: context.strings.text_booking_date_time,
          content: orderDetail?.createdAt?.formatddMMYYY() ?? '',
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
          titleWidth: titleWidth,
        ),
        InformationRow(
          title: '${context.strings.text_status}:',
          content: context.strings.text_reservation_success,
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
      ],
    );
  }

  Widget _serviceInfo(BuildContext context,
      {BookingOrder? orderDetail, int? qty}) {
    final double titleWidth = 110.W;
    final bookingService = orderDetail?.order?.first.bookingService;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_service_information,
          fontWeight: FontWeight.w700,
          fontSize: 16,
          textColor: AppAssets.origin().blue1E40,
          padding: EdgeInsets.only(
            bottom: 8.H,
          ),
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: TripcText(
                context.strings.text_service_name,
                fontSize: 14,
                height: 2,
                fontWeight: FontWeight.w500,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().blackColor,
              ),
            ),
            Expanded(
              child: TripcRichText(
                  text: '',
                  textAlign: TextAlign.start,
                  lineHeight: 2,
                  children: [
                    TextSpan(
                      text: context.strings.text_reservate_at,
                      style: AppAssets.origin().mediumTextStyle.copyWith(
                          fontSize: 14.SP,
                          fontWeight: FontWeight.w400,
                          color: AppAssets.origin().blackColor),
                    ),
                    TextSpan(
                      text: bookingService?.supplier?.name ?? '',
                      style: AppAssets.origin().mediumTextStyle.copyWith(
                          fontWeight: FontWeight.w500,
                          fontSize: 14.SP,
                          color: AppAssets.origin().blackColor),
                    ),
                  ]),
            ),
          ],
        ),
        // InformationRow(
        //   title: context.strings.text_service_name,
        //   content: bookingService?.supplier?.name ?? '',
        //   titleWidth: titleWidth,
        //   contentFontWeight: FontWeight.w400,
        //   contentFontSize: 14,
        // ),
        InformationRow(
          title: context.strings.text_quantity,
          content: (orderDetail?.order?.first.quantity?.toString() ?? '')
              .padLeft(2, '0'),
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
        InformationRow(
          title: context.strings.text_note,
          content: orderDetail?.description ?? '',
          titleWidth: titleWidth,
          isHtmlContent: true,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
        InformationRow(
          title: '${context.strings.time}:',
          content: orderDetail?.departureTime
                  ?.toString()
                  .toVietnameseDateTimeString() ??
              '',
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        )
      ],
    );
  }

  Widget _passengerInfo(BuildContext context, {BookingOrder? orderDetail}) {
    final double titleWidth = 110.W;
    final contactPerson = orderDetail?.contact;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(
            bottom: 8.H,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                context.strings.text_contact_information,
                fontWeight: FontWeight.w700,
                fontSize: 16,
                textColor: AppAssets.origin().blue1E40,
                textCase: TextCaseType.normal,
              ),
              Visibility(
                visible: (orderDetail?.combinedTicketUrl?.isNotNull ?? false),
                child: Row(
                  children: [
                    TripcText(
                      context.strings.text_qr_code_reservation,
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                      textColor: AppAssets.origin().primaryColorV2,
                      onTap: () {
                        AppRoute.pushNamed(context,
                            routeName: AppRoute.routeBookingTicket,
                            arguments: orderDetail);
                      },
                    ),
                    AppAssets.origin().icParkOutlineRight.widget(
                          width: 16.H,
                          height: 16.H,
                          color: AppAssets.origin().primaryColorV2,
                        ),
                  ],
                ),
              ),
            ],
          ),
        ),
        InformationRow(
          title: context.strings.text_fullname,
          content: contactPerson?.fullname ?? '',
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
        InformationRow(
          title: '${context.strings.text_linked_phone}:',
          content: contactPerson?.phone ?? '',
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
        InformationRow(
          title: '${context.strings.text_email}:',
          content: contactPerson?.email ?? '',
          titleWidth: titleWidth,
          contentFontWeight: FontWeight.w400,
          contentFontSize: 14,
        ),
      ],
    );
  }

  Widget _sectionItem(BuildContext context,
      {String? title, VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        height: 28.H,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TripcText(
              title,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              textColor: AppAssets.init.blackColor,
            ),
            AppAssets.init.icArrowRightGray.widget(width: 20.H)
          ],
        ),
      ),
    );
  }

  Widget _sectionInfo(BuildContext context, BookingOrder? orderDetail) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.H),
        boxShadow: [
          BoxShadow(
            color: AppAssets.origin().blackColor.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 15.W),
        child: Column(
          children: [
            _sectionItem(
              context,
              title: context.strings.text_includes,
              onTap: () {
                showDialog(
                    context: context,
                    builder: (context) => Dialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.SP),
                          ),
                          backgroundColor: Colors.white,
                          insetPadding: EdgeInsets.symmetric(
                              horizontal: 16.W, vertical: 16.H),
                          child: IncludeBottomSheetV2(
                            content: orderDetail?.generalTerms,
                            title: context.strings.text_includes,
                          ),
                        ));
              },
            ),
            SizedBox(height: 12.H),
            _sectionItem(
              context,
              title: context.strings.text_how_to_use,
              onTap: () {
                showDialog(
                    context: context,
                    builder: (context) => Dialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.SP),
                          ),
                          backgroundColor: Colors.white,
                          insetPadding: EdgeInsets.symmetric(
                              horizontal: 16.W, vertical: 16.H),
                          child: OrderDetailBottomSheetV2(
                            content: orderDetail?.usageInstructions,
                            title: context.strings.text_how_to_use,
                          ),
                        ));
              },
            ),
            SizedBox(height: 12.H),
            _sectionItem(
              context,
              title: context.strings.text_before_placing,
              onTap: () {
                showDialog(
                    context: context,
                    builder: (context) => Dialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.SP),
                          ),
                          backgroundColor: Colors.white,
                          insetPadding: EdgeInsets.symmetric(
                              horizontal: 16.W, vertical: 16.H),
                          child: OrderDetailBottomSheetV2(
                            content: orderDetail?.condition,
                            title: context.strings.text_before_placing,
                          ),
                        ));
              },
            ),
          ],
        ),
      ),
    );
  }
}
