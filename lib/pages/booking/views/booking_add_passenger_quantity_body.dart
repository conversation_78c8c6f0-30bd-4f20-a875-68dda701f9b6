import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:tripc_app/models/remote/booking_response/service_responses/attribute_response.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/models/remote/api_tour_response/applicable_time_response.dart';
import 'package:tripc_app/models/remote/booking_response/service_responses/detail_booking_service_response.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_option_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../widgets/commons/content_shadow_view.dart';
import '../../../widgets/commons/tripc_button/tripc_math_button.dart';
import '../../../widgets/commons/tripc_button/tripc_math_button_v2.dart';
import '../../../widgets/commons/tripc_button/tripc_option_button_v2.dart';
import '../../../widgets/tripc_text_filed/tripc_text_filed.dart';
import '../../ticket_tour/components/calendar_bottom_sheet.dart';
import '../providers/add_passenger_quantity_provider.dart';
import '../components/note_information.dart';

class BookingAddPassengerQuantityBody extends ConsumerStatefulWidget {
  const BookingAddPassengerQuantityBody({
    super.key,
    required this.detailCuisine,
    required this.nameController,
    required this.emailController,
    required this.phoneController,
    required this.noteController,
  });

  final DetailBookingService? detailCuisine;
  final TextEditingController nameController;
  final TextEditingController emailController;
  final TextEditingController phoneController;
  final TextEditingController noteController;

  @override
  ConsumerState<BookingAddPassengerQuantityBody> createState() =>
      _AddPassengerQuantityCuisineState();
}

class _AddPassengerQuantityCuisineState
    extends ConsumerState<BookingAddPassengerQuantityBody> {
  late final ScrollController pickedDateScrollController;

  @override
  void initState() {
    super.initState();
    pickedDateScrollController = ScrollController();
  }

  void scrollToSelectedDate(
      DateTime? selectedDate, ApplicableTimeResponse? availableDates) {
    if (selectedDate == null || availableDates == null) return;

    final index = availableDates.listApplicableTime
        .indexWhere((date) => DateUtils.isSameDay(date, selectedDate));

    if (index != -1) {
      final itemWidth = 95.W;
      final separatorWidth = 10.W;
      final scrollPosition = index * (itemWidth + separatorWidth);

      final maxScroll = pickedDateScrollController.position.maxScrollExtent;
      final finalScrollPosition = scrollPosition.clamp(0.0, maxScroll);

      Future.delayed(const Duration(milliseconds: 100), () {
        if (pickedDateScrollController.hasClients) {
          pickedDateScrollController.animateTo(
            finalScrollPosition,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  void onShowCalendarBottomSheet(
      ApplicableTimeResponse? availableDates, DateTime? selectedDate) {
    bottomSheetHelpers.show(
      context,
      child: Builder(
        builder: (dialogContext) {
          return TripcCalendarBottomSheet(
            onSelectDate: (date) {
              Navigator.pop(dialogContext);

              if (availableDates == null) {
                return;
              }

              final pickedDate = date.atDate();
              final endDate = availableDates.endDateTime.atDate();
              final startDate = availableDates.startDateTime.atDate();

              if (pickedDate.isBefore(endDate) &&
                      pickedDate.isAfter(startDate) ||
                  pickedDate.isAtSameMomentAs(startDate) ||
                  pickedDate.isAtSameMomentAs(endDate)) {
                ref
                    .read(pAddPassengerQuantityProvider.notifier)
                    .selectDate(date: date);
              }
            },
            selectedDay: selectedDate ?? DateTime.now(),
            startDate: availableDates?.startDateTime,
            endDate: availableDates?.endDateTime,
          );
        },
      ),
    );
  }

  bool isBeforeCurrentTime({required String current, required String time}) {
    return current.compareTo(time) >= 0;
  }

  @override
  Widget build(BuildContext context) {
    final detailCuisine = widget.detailCuisine;

    final selectedDate = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.selectedDate));
    final selectedTime = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.selectedTime));

    return Column(children: [
      Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 16.H),
        child: ContentShadowView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.H, vertical: 10.W),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TripcText(
                  context.strings.booking_information,
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  textAlign: TextAlign.start,
                  textColor: AppAssets.origin().blue001,
                ),
                _pickDateArea(
                  context,
                  selectedDate: selectedDate,
                  availableDates: detailCuisine?.applicableTime,
                  onPickDate: (value) => ref
                      .read(pAddPassengerQuantityProvider.notifier)
                      .selectDate(date: value),
                  onTapCalendar: () => onShowCalendarBottomSheet(
                      detailCuisine?.applicableTime, selectedDate),
                ),
                _pickTimeArea(
                  context,
                  selectedTimeFrame: selectedTime ?? '',
                  timeFrame: detailCuisine?.attributes?.getTimeSlots() ?? [],
                  onTap: (value) => ref
                      .read(pAddPassengerQuantityProvider.notifier)
                      .selectTime(timeFrame: value),
                ),
                _addPassengerArea(context, ref, cuisine: detailCuisine),
                _contactInformation(context, ref)
              ],
            ),
          ),
        ),
      ),
      NoteInformationWidget(
        htmlText: detailCuisine?.supplier?.note ?? '',
      )
    ]);
  }

  Widget _pickDateArea(BuildContext context,
      {required DateTime? selectedDate,
      required ApplicableTimeResponse? availableDates,
      Function(DateTime?)? onPickDate,
      VoidCallback? onTapCalendar}) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      scrollToSelectedDate(selectedDate, availableDates);
    });
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.reservation_date,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          textAlign: TextAlign.start,
          textColor: AppAssets.origin().textColorForNameTourV2,
          padding: EdgeInsets.only(bottom: 8.H, top: 16.H),
        ),
        SizedBox(
          height: 36.H,
          child: Row(
            children: [
              SizedBox(width: 8.W),
              Expanded(
                child: ListView.separated(
                    controller: pickedDateScrollController,
                    itemCount: availableDates?.listApplicableTime.length ?? 0,
                    scrollDirection: Axis.horizontal,
                    separatorBuilder: (context, _) => SizedBox(width: 8.W),
                    itemBuilder: (context, index) {
                      final date = availableDates!.listApplicableTime[index];
                      return TripcOptionButtonV2(
                        onPressed: () {
                          onPickDate?.call(date);
                        },
                        isActive: DateUtils.isSameDay(date, selectedDate),
                        value: date.dateTimeVi,
                      );
                    }),
              ),
              SizedBox(width: 10.W),
              TripcIconButton(
                onPressed: onTapCalendar,
                child: AppAssets.origin()
                    .icCalendar
                    .widget(height: 20.W, width: 20.W),
              )
            ],
          ),
        )
      ],
    );
  }

  Widget _pickTimeArea(BuildContext context,
      {required List<String> timeFrame,
      required String selectedTimeFrame,
      Function(String)? onTap}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.reservation_time,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          textAlign: TextAlign.start,
          textColor: AppAssets.origin().textColorForNameTourV2,
          padding: EdgeInsets.only(bottom: 8.H, top: 16.H),
        ),
        SizedBox(
          height: 40.H,
          child: ListView.separated(
              itemCount: timeFrame.length,
              padding: EdgeInsets.only(right: 24.W),
              scrollDirection: Axis.horizontal,
              separatorBuilder: (context, _) => SizedBox(
                    width: 10.W,
                  ),
              itemBuilder: (context, index) {
                return TripcOptionButton(
                    onPressed: () {
                      onTap?.call(timeFrame[index]);
                    },
                    isActive: selectedTimeFrame == timeFrame[index],
                    value: timeFrame[index]);
              }),
        ),
      ],
    );
  }

  Widget _addPassengerArea(
    BuildContext context,
    WidgetRef ref, {
    required DetailBookingService? cuisine,
  }) {
    final filteredSubProducts =
        cuisine?.subProducts?.where((e) => e.audience?.isDefault == 0).toList();

    final int totalStock =
        filteredSubProducts?.fold(0, (sum, e) => (sum ?? 0) + e.stock) ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.guest_quantity,
          fontSize: 14,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w500,
          padding: EdgeInsets.only(bottom: 8.H, top: 16.H),
          textColor: AppAssets.origin().textColorForNameTourV2,
        ),
        Column(
          spacing: 8.H,
          children: cuisine?.subProducts
                  ?.where((e) => e.audience?.isDefault == 0)
                  .toList()
                  .mapIndexed((idx, e) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      child: Padding(
                        padding: EdgeInsets.only(left: 10.W),
                        child: TripcText(
                          e.audience?.name ??
                              context.strings.text_passenger_title,
                          fontSize: 14,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.start,
                          fontWeight: FontWeight.w500,
                          textColor: AppAssets.origin().black,
                        ),
                      ),
                    ),
                    Row(
                      spacing: 8.W,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        _addAndMinusArea(
                          result: e.stock,
                          onTapMinus: () => ref
                              .read(pAddPassengerQuantityProvider.notifier)
                              .onTapMinusQtt(e),
                          onTapPlus: () => ref
                              .read(pAddPassengerQuantityProvider.notifier)
                              .onTapAddQtt(
                                e,
                              ),
                          capacity: cuisine.capacity ?? 0,
                          currentTotalStock: totalStock,
                        )
                      ],
                    )
                  ],
                );
              }).toList() ??
              [],
        ),
      ],
    );
  }

  Widget _contactInformation(BuildContext context, WidgetRef ref) {
    final contactErrorPhoneNumber = ref.watch(
      pAddPassengerQuantityProvider.select((value) => value.phoneContactError),
    );
    final contactErrorEmail = ref.watch(
      pAddPassengerQuantityProvider.select(
        (value) => value.emailContactError,
      ),
    );
    final fullNameErrorText = ref.watch(
      pAddPassengerQuantityProvider.select(
        (value) => value.nameContactError,
      ),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TripcText(
          context.strings.text_contact_information.toSentenceCase(),
          fontSize: 16,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w700,
          padding: EdgeInsets.symmetric(vertical: 16.H),
          textColor: AppAssets.origin().blue001,
        ),
        Padding(
          padding: EdgeInsets.only(left: 8.W),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  TripcText(
                    context.strings.text_first_name_and_last_name
                        .toSentenceCase(),
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(bottom: 3.H),
                    textColor: AppAssets.origin().blue191,
                  ),
                  TripcText(
                    '*',
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(left: 3.H),
                    textColor: AppAssets.origin().redDotColor,
                  ),
                ],
              ),
              TripcTextField(
                controller: widget.nameController,
                hintText: context.strings.text_full_name,
                fontSize: 14,
                errorText: fullNameErrorText,
                scrollPadding: EdgeInsets.zero,
                onChanged: (value) => ref
                    .read(pAddPassengerQuantityProvider.notifier)
                    .setFullName(context, value),
                textInputAction: TextInputAction.next,
                hintTextColor: AppAssets.origin().gray8D,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 11.H, horizontal: 16.W),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 8.W, top: 16.H),
          child: Column(
            children: [
              Row(
                children: [
                  TripcText(
                    context.strings.text_linked_phone,
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(bottom: 3.H),
                    textColor: AppAssets.origin().blue191,
                  ),
                  TripcText(
                    '*',
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(left: 3.H),
                    textColor: AppAssets.origin().redDotColor,
                  ),
                ],
              ),
              TripcTextField(
                errorText: contactErrorPhoneNumber,
                controller: widget.phoneController,
                hintText: context.strings.text_mobile_phone,
                hintTextColor: AppAssets.origin().gray8D,
                keyboardType: TextInputType.number,
                fontSize: 14,
                maxLength: 10,
                onChanged: (value) {
                  ref
                      .read(pAddPassengerQuantityProvider.notifier)
                      .setPhoneNumber(value, context);
                },
                textInputAction: TextInputAction.done,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 11.H, horizontal: 16.W),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 8.W, top: 16.H),
          child: Column(
            children: [
              Row(
                children: [
                  TripcText(
                    context.strings.text_email,
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(bottom: 3.H),
                    textColor: AppAssets.origin().blue191,
                  ),
                  TripcText(
                    '*',
                    fontSize: 14,
                    textAlign: TextAlign.start,
                    fontWeight: FontWeight.w500,
                    padding: EdgeInsets.only(left: 3.H),
                    textColor: AppAssets.origin().redDotColor,
                  ),
                ],
              ),
              TripcTextField(
                controller: widget.emailController,
                onChanged: (value) => ref
                    .read(pAddPassengerQuantityProvider.notifier)
                    .setEmail(context, value: value),
                hintText: context.strings.text_email,
                errorText: contactErrorEmail,
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.emailAddress,
                hintTextColor: AppAssets.origin().gray8D,
                fontSize: 14,
                contentPadding:
                    EdgeInsets.symmetric(vertical: 11.H, horizontal: 16.W),
              ),
            ],
          ),
        ),
        //note
        TripcText(
          context.strings.text_special_requests.toSentenceCase(),
          fontSize: 16,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w600,
          padding: EdgeInsets.only(bottom: 8.H, top: 16.H),
          textColor: AppAssets.origin().blue001,
        ),
        Container(
          height: 120.H,
          padding: EdgeInsets.only(left: 10.W),
          margin: EdgeInsets.only(bottom: 2.H),
          child: TripcTextField(
            controller: widget.noteController,
            onChanged: (value) => ref
                .read(pAddPassengerQuantityProvider.notifier)
                .setSpecialRequest(context, value: value),
            hintText: context.strings.enter_special_request,
            hintTextColor: AppAssets.origin().gray8D,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            expands: true,
            maxLines: null,
            textAlignVertical: TextAlignVertical.top,
            contentPadding:
                EdgeInsets.symmetric(vertical: 8.H, horizontal: 16.W),
          ),
        ),
      ],
    );
  }

  Widget _addAndMinusArea({
    required int result,
    required VoidCallback onTapMinus,
    required VoidCallback onTapPlus,
    required int capacity,
    required int currentTotalStock,
  }) {
    return Row(
      children: [
        TripcMathButtonV2(
          isActive: result > 0,
          onPressed: onTapMinus,
          symbol: MathSymbol.minus,
        ),
        TripcText(
          result.toString(),
          fontSize: 14,
          fontWeight: FontWeight.w400,
          textColor: AppAssets.origin().blackColor,
          padding: EdgeInsets.symmetric(horizontal: 8.W),
        ),
        TripcMathButtonV2(
          isActive: currentTotalStock < capacity,
          onPressed: onTapPlus,
          symbol: MathSymbol.add,
        ),
      ],
    );
  }
}
