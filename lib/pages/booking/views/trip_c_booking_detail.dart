import 'package:flutter/material.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/detail_supplier_response.dart';
import 'package:tripc_app/pages/booking/components/food_item.dart';
import 'package:tripc_app/pages/booking/components/header_restaurant_area.dart';
import 'package:tripc_app/pages/booking/components/restaurant_detail_view.dart';
import 'package:tripc_app/pages/booking/providers/trip_c_booking_detail_provider.dart';
import 'package:tripc_app/pages/ticket_tour/components/tour_saved_view.dart';
import 'package:tripc_app/pages/ticket_tour/components/tripc_image_slide.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_portal/tripc_portal.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../../../models/app/tour_type_enum.dart';
import '../../../widgets/app_show_api_error_dialog.dart';

class TripCBookingDetail extends ConsumerStatefulWidget {
  const TripCBookingDetail(this.category,
      {super.key, required this.restaurantId});

  final int restaurantId;
  final TripCServiceCategory category;

  @override
  ConsumerState<TripCBookingDetail> createState() =>
      _TripcTourDetailViewState();
}

class _TripcTourDetailViewState extends ConsumerState<TripCBookingDetail> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ref.read(pBookingDetailProvider.notifier).resetDetailedPage();
      ref
          .read(pBookingDetailProvider.notifier)
          .updateStep(BookingStep.viewDetail);
      ref
          .read(pBookingDetailProvider.notifier)
          .getDetailRestaurant(restaurantId: widget.restaurantId);
      ref.listenManual(
          pBookingDetailProvider.select((value) => value.errorMessage),
          (_, error) {
        if (error == null || error.isEmpty) {
          return;
        }
        dialogHelpers.show(context, child: ErrorDialog(text: error));
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final restaurantDetail = ref.watch(
        pBookingDetailProvider.select((value) => value.restaurantDetail));

    final bookingStep =
        ref.watch(pBookingDetailProvider.select((value) => value.step));

    return TripcScaffold(
      onPressed: () => unfocusKeyboard(),
      onPopScope: () =>
          ref.read(pBookingDetailProvider.notifier).resetDetailedPage(),
      extendBodyBehindAppBar: true,
      hasBackButton: true,
      visibleAppBar: true,
      appBarColor: Colors.transparent,
      hasAppbarBottomLine: false,
      backgroundColor: AppAssets.origin().bgDetailTourColorV2,
      leading: AppAssets.init.iconArrowleft.widget(
        color: AppAssets.origin().whiteBackgroundColor,
      ),
      onLeadingPressed: () {
        if (bookingStep == BookingStep.viewDetail) {
          ref.read(pBookingDetailProvider.notifier).resetDetailedPage();
          Navigator.pop(context);
          return;
        }
        ref
            .read(pBookingDetailProvider.notifier)
            .updateStep(BookingStep.viewDetail);
      },
      titleAppBar: TripcText(
        bookingStep == BookingStep.viewAllProduct
            ? context.strings.our_products
            : '',
        fontSize: 16,
        fontWeight: FontWeight.w600,
        textAlign: TextAlign.start,
        textColor: AppAssets.origin().whiteBackgroundColor,
      ),
      actions: [
        // SizedBox(width: 16.W),
        // TripCPortal(
        //     aligned: const Aligned(
        //         follower: Alignment.topRight,
        //         target: Alignment.bottomRight,
        //         offset: Offset(0, 5)),
        //     isBlur: false,
        //     // TODO: fill this isSave
        //     //                 ? AppAssets.init.icHeartFill.widget(width: 24.H)
        //     //                 : AppAssets.init.icHeart.widget(width: 24.H),
        //     icon: bookingStep == BookingStep.viewDetail
        //         ? AppAssets.init.icHeart.widget(width: 24.H)
        //         : null,
        //     onClose: () {
        //       // ref.read(pBookingDetailProvider.notifier).onCloseSavedPopup();
        //     },
        //     onPressed: () async {
        //       // if (globalCacheAuth.isLogged()) {
        //       //   final result =
        //       //       await ref.read(pBookingDetailProvider.notifier).savedTour();
        //       //   if (result) {
        //       //     // ref
        //       //     //     .read(pHomepageScreenProvider.notifier)
        //       //     //     .updatedDealsAroundHere(widget.tourId, !isSave);
        //       //     // ref
        //       //     //     .read(pBookingDetailProvider.notifier)
        //       //     //     .updateListTour(widget.tourId, !isSave, widget.fromListType);
        //       //   }
        //       // } else {
        //       //   dialogHelpers.show(context, child: NotYetLoginDialog(
        //       //     resultHandler: () {
        //       //       ref
        //       //           .read(pBookingDetailProvider.notifier)
        //       //           .getDetailedTour(tourId: widget.tourId);
        //       //     },
        //       //   ));
        //       // }
        //     },
        //     follower: TourSavedView(
        //       onPressed: () {
        //         // ref.read(pBookingDetailProvider.notifier).onCloseSavedPopup();
        //         AppRoute.navigateToRoute(
        //           context,
        //           routeName: AppRoute.routeListToursView,
        //           arguments: {'category': TourType.tourSaved},
        //           resultHandler: (value) {
        //             // ref
        //             //     .read(pBookingDetailProvider.notifier)
        //             //     .getDetailedTour(tourId: widget.tourId);
        //           },
        //         );
        //       },
        //     )),
        // SizedBox(
        //   width: 26.W,
        // )
      ],
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            bottom: (restaurantDetail?.bookingSevices?.isNotEmpty ?? false)
                ? 133.H
                : 0,
            child: SingleChildScrollView(
              padding: EdgeInsets.zero,
              physics: const ClampingScrollPhysics(),
              child: restaurantDetail == null
                  ? Container()
                  : Column(
                      children: [
                        TripcImageSlide(
                          height: 223.H,
                          images: [restaurantDetail.logoUrl ?? ''],
                          applyGradient: true,
                        ),
                        HeaderRestaurantDetailArea(
                          supplier: restaurantDetail,
                          category: widget.category,
                        ),
                        if (bookingStep == BookingStep.viewDetail)
                          RestaurantDetailView(
                            widget.category,
                            restaurantDetail: restaurantDetail,
                          ),
                        if (bookingStep == BookingStep.viewDetail)
                          SizedBox(
                            height: 16.H,
                          ),
                        SizedBox(
                          height: 16.H,
                        ),
                        if (bookingStep == BookingStep.viewDetail)
                          _buildListProduct(restaurantDetail),
                        if (bookingStep == BookingStep.viewAllProduct)
                          _buildListProductByCategory(restaurantDetail),
                      ],
                    ),
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Visibility(
              visible: restaurantDetail?.bookingSevices?.isNotEmpty ?? false,
              child: _selectButton(
                context,
                onTapSelect: () => AppRoute.pushNamed(
                  context,
                  routeName: AppRoute.routeBookingAddPassengerQuantity,
                  arguments: restaurantDetail?.bookingSevices?.first.id ?? -1,
                ),
                minTicketPrice: ref.watch(pTicketTourProvider).minTicketPrice,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListProductByCategory(DetailSupplier restaurantDetail) {
    if (restaurantDetail.productGroups?.isEmpty == true) return Container();

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.W),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (var productGroup in restaurantDetail.productGroups ?? [])
            Padding(
              padding: EdgeInsets.only(bottom: 16.H),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TripcText(
                    productGroup.name ?? '',
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    textAlign: TextAlign.start,
                    textColor: AppAssets.origin().colorTextFoodName,
                  ),
                  SizedBox(height: 20.H),
                  if (productGroup.products != null &&
                      productGroup.products!.isNotEmpty)
                    ListView.separated(
                      itemCount: productGroup.products?.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      separatorBuilder: (BuildContext context, int index) =>
                          SizedBox(
                        height: 15.H,
                      ),
                      itemBuilder: (context, index) {
                        var foodItem = productGroup.products?[index];
                        if (foodItem == null) return Container();
                        return FoodItem(
                          item: foodItem,
                          // TODO: Mockup onTap food item to detail product item
                          // onTap: () => AppRoute.pushNamed(
                          //   context,
                          //   routeName: AppRoute.routeProductDetailView,
                          //   arguments: {
                          //     'id': foodItem.id,
                          //     'category': widget.category,
                          //   },
                          // ),
                        );
                      },
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildListProduct(DetailSupplier restaurantDetail) {
    if (restaurantDetail.productGroups?.isEmpty == true) return Container();
    final listProductOverview = restaurantDetail.productGroups?.first;
    final listFoodOverview = listProductOverview?.products?.take(1).toList();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.W),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        spacing: 15.H,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TripcText(
                context.strings.our_products,
                fontSize: 16,
                fontWeight: FontWeight.w700,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().colorTextFoodName,
              ),
              GestureDetector(
                onTap: () {
                  ref
                      .read(pBookingDetailProvider.notifier)
                      .updateStep(BookingStep.viewAllProduct);
                },
                child: Padding(
                    padding: EdgeInsets.only(right: 8.W),
                    child: Icon(
                      Icons.arrow_forward,
                      size: 16.W,
                      color: AppAssets.origin().colorIconViewAllProducts,
                    )),
              )
            ],
          ),
          ListView.builder(
              itemCount: listFoodOverview?.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                var item = listFoodOverview?[index];
                if (item == null) return Container();
                return FoodItem(
                  item: item,
                  // TODO: Mockup onTap food item to detail product item
                  // onTap: () => AppRoute.pushNamed(
                  //   context,
                  //   routeName: AppRoute.routeProductDetailView,
                  //   arguments: {
                  //     'id': item.id,
                  //     'category': widget.category,
                  //   },
                  // ),
                );
              }),
          SizedBox(
            height: 1.H,
          ),
        ],
      ),
    );
  }

  Widget _selectButton(BuildContext context,
      {VoidCallback? onTapSelect, required int minTicketPrice}) {
    return Container(
      height: 133.H,
      padding: EdgeInsets.symmetric(horizontal: 10.W),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade300,
            width: 1.0,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            offset: const Offset(0, -2),
            blurRadius: 8.0,
            spreadRadius: 1.0,
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            spacing: 8.W,
            children: [
              AppAssets.origin().iconBooking.widget(height: 24.W, width: 24.W),
              TripcText(
                context.strings.booking,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                height: 1.6,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().colorTextBooking,
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TripcText(
                context.strings.no_deposit_required_when_booking,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                height: 1.4,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().grey53,
              ),
            ],
          ),
          SizedBox(
            height: 8.H,
          ),
          TripcButton(
            onPressed: onTapSelect,
            showSuggestLoginDialog: true,
            textCase: TextCaseType.none,
            style: AppButtonStyle(
                backgroundColor: AppAssets.origin().primaryColorV2),
            isLogin: globalCacheAuth.isLogged(),
            resultHandler: () {
              // ref
              //     .read(pTicketTourProvider.notifier)
              //     .getDetailedTour(tourId: widget.tourId);
            },
            // isButtonDisabled: !isButtonEnable,
            height: 46,
            title: context.strings.text_book_now.toSentenceCase(),
          ),
          SizedBox(
            height: 16.H,
          ),
        ],
      ),
    );
  }
}
