import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:tripc_app/widgets/commons/content_shadow_view.dart';
import 'package:tripc_app/widgets/commons/information_row.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_cached_network_image.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/common_extension.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

import '../../../models/remote/booking_response/order_responses/booking_order_response.dart';
import '../../../models/remote/order_response/my_order_response.dart';
import '../../../services/app/app_assets.dart';
import '../components/contact_tripc_button.dart';

class BookingTicketPage extends ConsumerStatefulWidget {
  const BookingTicketPage({super.key, this.orderDetail});

  final BookingOrder? orderDetail;

  @override
  ConsumerState<BookingTicketPage> createState() => _TicketInfoViewState();
}

class _TicketInfoViewState extends ConsumerState<BookingTicketPage> {
  @override
  Widget build(BuildContext context) {
    final customerTicket =
        widget.orderDetail?.order?.first.bookingService?.customerTickets?.first;
    final double titleWidth = 80.W;

    return TripcScaffold(
        needUnFocus: true,
        hasBackButton: true,
        backgroundColor: AppAssets.origin().grayF4F6,
        appBarColor: AppAssets.origin().grayF4F6,
        titleAppBar: TripcText(
          context.strings.text_qr_reservation,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          textColor: Colors.black,
        ),
        body: Stack(
          children: [
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 90.H,
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 16.H),
                child: ContentShadowView(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.W, vertical: 16.H),
                    child: Column(
                      children: [
                        //ticket
                        _buildQRTicket(context, customerTicket: customerTicket),
                        //contact information
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            InformationRow(
                              title: context.strings.text_fullname,
                              content: customerTicket?.fullname ?? '',
                              titleWidth: titleWidth,
                              contentFontWeight: FontWeight.w400,
                              contentFontSize: 14,
                            ),
                            InformationRow(
                              title: '${context.strings.text_linked_phone}:',
                              content: customerTicket?.phoneNumber ?? '',
                              titleWidth: titleWidth,
                              contentFontWeight: FontWeight.w400,
                              contentFontSize: 14,
                            ),
                            InformationRow(
                              title: '${context.strings.text_email}:',
                              content: customerTicket?.email ?? '',
                              titleWidth: titleWidth,
                              contentFontWeight: FontWeight.w400,
                              contentFontSize: 14,
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: TripcText(
                                    '${context.strings.text_service}:',
                                    fontSize: 14,
                                    height: 2,
                                    fontWeight: FontWeight.w500,
                                    textAlign: TextAlign.start,
                                    textColor: AppAssets.origin().blackColor,
                                  ),
                                ),
                                Expanded(
                                  child: TripcRichText(
                                      text: '',
                                      textAlign: TextAlign.start,
                                      children: [
                                        TextSpan(
                                          text:
                                              context.strings.text_reservate_at,
                                          style: AppAssets.origin()
                                              .mediumTextStyle
                                              .copyWith(
                                                  fontSize: 14.SP,
                                                  fontWeight: FontWeight.w400,
                                                  color: AppAssets.origin()
                                                      .blackColor),
                                        ),
                                        TextSpan(
                                          text: widget
                                                  .orderDetail
                                                  ?.order
                                                  ?.first
                                                  .bookingService
                                                  ?.supplier
                                                  ?.name ??
                                              '',
                                          style: AppAssets.origin()
                                              .mediumTextStyle
                                              .copyWith(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 14.SP,
                                                  color: AppAssets.origin()
                                                      .blackColor),
                                        ),
                                      ]),
                                ),
                              ],
                            ),
                            InformationRow(
                              title: context.strings.text_time,
                              content: widget.orderDetail?.departureTime
                                      .toString()
                                      .toVietnameseDateTimeString() ??
                                  '',
                              titleWidth: titleWidth,
                              contentFontWeight: FontWeight.w400,
                              contentFontSize: 14,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            //contact tripc button
            const Positioned(
                left: 0, right: 0, bottom: 0, child: ContactTripcButton()),
          ],
        ));
  }

  Widget _buildQRTicket(BuildContext context,
      {CustomerTicket? customerTicket}) {
    return Column(
      children: [
        TripcText(
          context.strings.text_your_reservation_code,
          fontSize: 16,
          fontWeight: FontWeight.w700,
          textColor: AppAssets.init.blue191,
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8.H),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TripcRichText(text: '', children: [
              TextSpan(
                text: context.strings.text_ticket_id,
                style: AppAssets.origin().mediumTextStyle.copyWith(
                    fontSize: 14.SP,
                    fontWeight: FontWeight.w500,
                    color: AppAssets.origin().blackColor),
              ),
              TextSpan(
                text: customerTicket?.ticketId,
                style: AppAssets.origin().mediumTextStyle.copyWith(
                    fontWeight: FontWeight.w500,
                    fontSize: 14.SP,
                    color: AppAssets.origin().blackColor),
              ),
            ]),
            SizedBox(width: 10.W),
            GestureDetector(
              onTap: () {
                Clipboard.setData(
                    ClipboardData(text: customerTicket?.ticketId ?? ''));
                Fluttertoast.showToast(
                  msg: context.strings.text_copied,
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.BOTTOM,
                  backgroundColor: Colors.black54,
                  textColor: Colors.white,
                  fontSize: 14.0,
                );
              },
              child: AppAssets.init.icCopy.widget(width: 16.H),
            )
          ],
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.W, vertical: 30.H),
          child: BaseCachedNetworkImage(
            imageUrl: customerTicket?.ticketQrCode ?? '',
            height: 210.H,
            width: 210.W,
            placeholder: (context, _) => Container(
              color: AppAssets.origin().lightGrayDD4,
            ),
            errorWidget: (context, error, stackTrace) =>
                AppAssets.origin().icErrorImg.widget(
                      height: 210.H,
                      width: 210.W,
                      color: context.appCustomPallet.buttonBG,
                    ),
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(height: 8.H),
      ],
    );
  }
}
