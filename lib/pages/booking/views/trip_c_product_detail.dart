import 'package:flutter/material.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/app/tripc_service_category.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/detail_supplier_response.dart';
import 'package:tripc_app/pages/booking/components/food_item.dart';
import 'package:tripc_app/pages/booking/components/header_product_area.dart';
import 'package:tripc_app/pages/booking/components/product_detail_view.dart';
import 'package:tripc_app/pages/booking/providers/trip_c_product_detail_provider.dart';
import 'package:tripc_app/pages/ticket_tour/components/tour_saved_view.dart';
import 'package:tripc_app/pages/ticket_tour/components/tripc_image_slide.dart';
import 'package:tripc_app/pages/ticket_tour/providers/ticket_tour_provider.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_portal/tripc_portal.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../../../models/app/tour_type_enum.dart';
import '../../../widgets/app_show_api_error_dialog.dart';

class TripCProductDetail extends ConsumerStatefulWidget {
  const TripCProductDetail(this.category,
      {super.key, required this.productId});

  final int productId;
  final TripCServiceCategory category;

  @override
  ConsumerState<TripCProductDetail> createState() =>
      _TripcTourDetailViewState();
}

class _TripcTourDetailViewState extends ConsumerState<TripCProductDetail> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ref.read(pProductDetailProvider.notifier).resetDetailedPage();
      ref
          .read(pProductDetailProvider.notifier)
          .getDetailRestaurant(restaurantId: widget.productId);
      ref.listenManual(
          pProductDetailProvider.select((value) => value.errorMessage),
              (_, error) {
            if (error == null || error.isEmpty) {
              return;
            }
            dialogHelpers.show(context, child: ErrorDialog(text: error));
          });
    });
  }

  @override
  Widget build(BuildContext context) {
    final restaurantDetail = ref.watch(
        pProductDetailProvider.select((value) => value.restaurantDetail));

    return TripcScaffold(
      onPressed: () => unfocusKeyboard(),
      onPopScope: () =>
          ref.read(pProductDetailProvider.notifier).resetDetailedPage(),
      extendBodyBehindAppBar: true,
      hasBackButton: true,
      visibleAppBar: true,
      appBarColor: Colors.transparent,
      hasAppbarBottomLine: false,
      backgroundColor: AppAssets.origin().bgDetailTourColorV2,
      leading: AppAssets.init.iconArrowleft.widget(
        color: AppAssets.origin().whiteBackgroundColor,
      ),
      onLeadingPressed: () {
        ref.read(pProductDetailProvider.notifier).resetDetailedPage();
        Navigator.pop(context);
      },
      actions: [
        SizedBox(width: 16.W),
        TripCPortal(
            aligned: const Aligned(
                follower: Alignment.topRight,
                target: Alignment.bottomRight,
                offset: Offset(0, 5)),
            isBlur: false,
            // TODO: fill this isSave
            //                 ? AppAssets.init.icHeartFill.widget(width: 24.H)
            //                 : AppAssets.init.icHeart.widget(width: 24.H),
            icon: AppAssets.init.icHeart.widget(width: 24.H),
            onClose: () {
              // ref.read(pProductDetailProvider.notifier).onCloseSavedPopup();
            },
            onPressed: () async {
              // if (globalCacheAuth.isLogged()) {
              //   final result =
              //       await ref.read(pProductDetailProvider.notifier).savedTour();
              //   if (result) {
              //     // ref
              //     //     .read(pHomepageScreenProvider.notifier)
              //     //     .updatedDealsAroundHere(widget.tourId, !isSave);
              //     // ref
              //     //     .read(pProductDetailProvider.notifier)
              //     //     .updateListTour(widget.tourId, !isSave, widget.fromListType);
              //   }
              // } else {
              //   dialogHelpers.show(context, child: NotYetLoginDialog(
              //     resultHandler: () {
              //       ref
              //           .read(pProductDetailProvider.notifier)
              //           .getDetailedTour(tourId: widget.tourId);
              //     },
              //   ));
              // }
            },
            follower: TourSavedView(
              onPressed: () {
                // ref.read(pProductDetailProvider.notifier).onCloseSavedPopup();
                AppRoute.navigateToRoute(
                  context,
                  routeName: AppRoute.routeListToursView,
                  arguments: {'category': TourType.tourSaved},
                  resultHandler: (value) {
                    // ref
                    //     .read(pProductDetailProvider.notifier)
                    //     .getDetailedTour(tourId: widget.tourId);
                  },
                );
              },
            )),
        SizedBox(
          width: 26.W,
        )
      ],
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            bottom: 133.H,
            child: SingleChildScrollView(
              padding: EdgeInsets.zero,
              physics: const ClampingScrollPhysics(),
              child: restaurantDetail == null
                  ? Container()
                  : Column(
                children: [
                  TripcImageSlide(
                    height: 223.H,
                    images: [restaurantDetail.logoUrl ?? ''],
                    applyGradient: true,
                  ),
                  HeaderProductDetailArea(
                    resDetail: restaurantDetail,
                    category: widget.category,
                  ),
                  ProductDetailView(
                    widget.category,
                    restaurantDetail: restaurantDetail,
                  ),
                  SizedBox(
                    height: 16.H,
                  ),
                  _buildListProduct(restaurantDetail),
                ],
              ),
            ),
          ),
          if (restaurantDetail != null && restaurantDetail.bookingSevices?.isNotEmpty == true)
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: _selectButton(
                context,
                onTapSelect: () => AppRoute.pushNamed(
                  context,
                  routeName: AppRoute.routeBookingAddPassengerQuantity,
                  arguments: restaurantDetail.bookingSevices?.first.id ?? -1,
                ),
                minTicketPrice: ref.watch(pTicketTourProvider).minTicketPrice,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildListProduct(DetailSupplier restaurantDetail) {
    if (restaurantDetail.productGroups?.isEmpty == true) return Container();
    final listProductOverview = restaurantDetail.productGroups?.first;
    final listFoodOverview = listProductOverview?.products?.take(1).toList();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.W),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 15.H,
        children: [
          TripcText(
            context.strings.other_products,
            fontSize: 16,
            fontWeight: FontWeight.w700,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().colorTextFoodName,
          ),
          ListView.builder(
              itemCount: listFoodOverview?.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                var item = listFoodOverview?[index];
                if (item == null) return Container();
                return FoodItem(
                  item: item,
                );
              }),
          SizedBox(
            height: 1.H,
          ),
        ],
      ),
    );
  }

  Widget _selectButton(BuildContext context,
      {VoidCallback? onTapSelect, required int minTicketPrice}) {
    return Container(
      height: 133.H,
      padding: EdgeInsets.symmetric(horizontal: 10.W),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade300,
            width: 1.0,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            offset: const Offset(0, -2),
            blurRadius: 8.0,
            spreadRadius: 1.0,
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            spacing: 8.W,
            children: [
              AppAssets.origin().iconBooking.widget(height: 24.W, width: 24.W),
              TripcText(
                context.strings.booking,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                height: 1.6,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().colorTextBooking,
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TripcText(
                context.strings.no_deposit_required_when_booking,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                height: 1.4,
                textAlign: TextAlign.start,
                textColor: AppAssets.origin().grey53,
              ),
            ],
          ),
          SizedBox(
            height: 8.H,
          ),
          TripcButton(
            onPressed: onTapSelect,
            showSuggestLoginDialog: true,
            textCase: TextCaseType.none,
            style: AppButtonStyle(
                backgroundColor: AppAssets.origin().primaryColorV2),
            isLogin: globalCacheAuth.isLogged(),
            resultHandler: () {
              // ref
              //     .read(pTicketTourProvider.notifier)
              //     .getDetailedTour(tourId: widget.tourId);
            },
            // isButtonDisabled: !isButtonEnable,
            height: 46,
            title: context.strings.text_book_now.toSentenceCase(),
          ),
          SizedBox(
            height: 16.H,
          ),
        ],
      ),
    );
  }
}
