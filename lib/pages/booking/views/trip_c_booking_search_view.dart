import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/list_data_request.dart';
import 'package:tripc_app/models/remote/api_tour_response/service_type_response.dart';
import 'package:tripc_app/models/remote/booking_response/supplier_responses/supplier_response.dart';
import 'package:tripc_app/pages/booking/components/row_view_all_widget.dart';
import 'package:tripc_app/pages/booking/providers/trip_c_booking_search_provider.dart';
import 'package:tripc_app/pages/homepage/components/list_tours_grid_view_booking.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_search_scafford_v2.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import '../../../models/app/tripc_service_category.dart';
import '../../../utils/app_extension.dart';

class TripCBookingSearchView extends ConsumerStatefulWidget {
  const TripCBookingSearchView({super.key, required this.category});

  final TripCServiceCategory category;

  @override
  ConsumerState<TripCBookingSearchView> createState() =>
      _TripCBookingSearchViewViewState();
}

class _TripCBookingSearchViewViewState
    extends ConsumerState<TripCBookingSearchView> {
  final TextEditingController _controller = TextEditingController();

  final _scrollController = ScrollController();
  final _gridViewScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      ref.read(pBookingSearchProvider.notifier).clearCacheData();
      await ref
          .read(pBookingSearchProvider.notifier)
          .getProductTypes(widget.category.serviceTypeSlug);

      final tabsFromProvider = ref.watch(
        pBookingSearchProvider.select((s) => s.listServiceTypeTabs),
      );

      for (var tab in tabsFromProvider) {
        ref.read(pBookingSearchProvider.notifier).getRestaurantsByTypeId(
              tab.id ?? 0,
              ListDataRequest(page: 1, pageSize: 20),
            );
      }

      ref
          .read(pBookingSearchProvider.notifier)
          .initializeViewModesListView(tabsFromProvider);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final keyWord =
        ref.watch(pBookingSearchProvider.select((value) => value.keyWord));

    return TripcSearchScaffoldV2(
      controller: _controller,
      hintText: widget.category.name(context),
      keyword: keyWord,
      titleAppBar: widget.category.name(context),
      backgroundColor: AppAssets.origin().grayF4F6,
      fillTfColor: AppAssets.origin().whiteBackgroundColor,
      onChanged: (value) async {
        debounceHelpers.action(
          callBack: () async {
            ref.read(pBookingSearchProvider.notifier).onChangeSearchMode(value);

            ref.read(pBookingSearchProvider.notifier).searchRestaurant(
                ListDataRequest(page: 1, pageSize: 20, keyWord: value),
                widget.category.serviceTypeSlug);
          },
        );
      },
      onTapSearch: () => unfocusKeyboard(),
      body: _getBody(),
    );
  }

  Widget _getBody() {
    final searchList =
        ref.watch(pBookingSearchProvider.select((value) => value.searchList));

    final isSearching =
        ref.watch(pBookingSearchProvider.select((value) => value.isSearching));

    if (isSearching) {
      if (searchList.isEmpty) {
        return Column(
          children: [
            Expanded(child: _searchNotFoundWidget()),
          ],
        );
      } else {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 20.H),
          child: _searchView(),
        );
      }
    }
    return _allListViews();
  }

  Widget _allListViews() {
    final tabsFromProvider = ref.watch(
      pBookingSearchProvider.select((s) => s.listServiceTypeTabs),
    );
    final types = [
      ServiceType(name: context.strings.text_all),
      ...tabsFromProvider
    ];

    final selectedServiceType =
        ref.watch(pBookingSearchProvider.select((value) => value.serviceType));

    final isLoading =
        ref.watch(pBookingSearchProvider.select((value) => value.isLoading));

    final listTourTabs =
        ref.watch(pBookingSearchProvider.select((value) => value.listTourTabs));

    final viewModesListView = ref.watch(
        pBookingSearchProvider.select((value) => value.viewModesListView));

    bool isAll = selectedServiceType.id == null;

    return SingleChildScrollView(
      child: Column(
        spacing: 25.H,
        children: [
          SizedBox(
            height: 40.H,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: 16.W),
              itemCount: types.length,
              separatorBuilder: (_, __) => SizedBox(width: 24.W),
              itemBuilder: (context, index) {
                final t = types[index];
                final isSel = types[index].id == selectedServiceType.id;
                return GestureDetector(
                  onTap: () => _onSelectType(t),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 9.W),
                    margin: EdgeInsets.symmetric(vertical: 7.H),
                    decoration: BoxDecoration(
                        border: Border(
                      bottom: BorderSide(
                        color: isSel
                            ? AppAssets.origin().primaryColorV2
                            : Colors.transparent,
                        width: 1.0,
                      ),
                    )),
                    child: Text(
                      t.name,
                      style: TextStyle(
                        fontSize: 16.SP,
                        fontWeight: FontWeight.w500,
                        color: isSel
                            ? AppAssets.origin().primaryColorV2
                            : AppAssets.origin().disableColorV2,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.W),
            child: widget.category.banner,
          ),
          // SizedBox(
          //   height: 230,
          //   child: ListView.separated(
          //     padding: EdgeInsets.symmetric(horizontal: 16.W),
          //     scrollDirection: Axis.horizontal,
          //     itemCount: 1,
          //     separatorBuilder: (_, __) => SizedBox(width: 16.W),
          //     itemBuilder: (context, index) {
          //       return AppAssets.origin().imBookingCuisine.widget();
          //     },
          //   ),
          // ),
          isAll
              ? _listViewAll(tabsFromProvider, listTourTabs, isLoading)
              : _listServiceTabs(listTourTabs, selectedServiceType, isLoading,
                  viewModesListView),
        ],
      ),
    );
  }

  Widget _listViewAll(List<ServiceType> tabs,
      Map<int, List<Supplier>> listTourTabs, bool isLoading) {
    return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: tabs.length,
        itemBuilder: (context, index) {
          final tabFromProvider = tabs[index];
          return RowViewAllListWidget(
            listViewPadding: EdgeInsets.symmetric(horizontal: 16.W),
            title: tabFromProvider.name,
            services: listTourTabs[tabFromProvider.id]?.take(4).toList() ?? [],
            isLoading: isLoading,
            category: widget.category,
            onTapViewAll: () => AppRoute.pushNamed(
              context,
              routeName: AppRoute.routeListBookingView,
              arguments: {
                'id': tabFromProvider.id,
                'name': tabFromProvider.name,
                'category': widget.category
              },
            ),
            typeProduct: tabFromProvider.name,
          );
        });
  }

  Widget _listServiceTabs(
      Map<int, List<Supplier>> listTourTabs,
      ServiceType currentServiceType,
      bool isLoading,
      Map<int, bool> viewModesListView) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.W),
      child: Column(
        spacing: 4.H,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              TripcText(
                context.strings.sort,
                fontSize: 16,
                fontWeight: FontWeight.w700,
                height: 1.25,
                textColor: AppAssets.origin().black,
              ),
              Visibility(
                visible: !isLoading,
                child: viewModesListView[currentServiceType.id] ?? false
                    ? TripcIconButton(
                        onPressed: () {
                          ref
                              .read(pBookingSearchProvider.notifier)
                              .updateViewModeById(
                                  currentServiceType.id ?? 0, false);
                        },
                        child: AppAssets.origin().iconGridView.widget(
                            height: 20.H,
                            width: 20.H,
                            color: AppAssets.origin().disableColorV2),
                      )
                    : TripcIconButton(
                        onPressed: () {
                          ref
                              .read(pBookingSearchProvider.notifier)
                              .updateViewModeById(
                                  currentServiceType.id ?? 0, true);
                        },
                        child: AppAssets.origin().iconListView.widget(
                            height: 20.H,
                            width: 20.H,
                            color: AppAssets.origin().disableColorV2),
                      ),
              )
            ],
          ),
          RefreshIndicator(
            onRefresh: () async {
              ref.read(pBookingSearchProvider.notifier).getRestaurantsByTypeId(
                    currentServiceType.id ?? 0,
                    ListDataRequest(page: 1, pageSize: 20),
                  );
            },
            child: ListToursGridViewBooking(
                services: listTourTabs[currentServiceType.id] ?? [],
                listScrollController: _scrollController,
                gridViewScrollController: _gridViewScrollController,
                category: widget.category,
                isDisableScroll: true,
                isGridView: viewModesListView[currentServiceType.id] ?? false,
                onTapSaved: (value) {
                  //TODO: saved supplier
                }),
          ),
          SizedBox(
            height: 20.H,
          )
        ],
      ),
    );
  }

  Widget _searchView() {
    final searchList =
        ref.watch(pBookingSearchProvider.select((value) => value.searchList));

    final isLoading =
        ref.watch(pBookingSearchProvider.select((value) => value.isLoading));

    return ListToursGridViewBooking(
        services: searchList,
        category: widget.category,
        isLoading: isLoading,
        onTapSaved: (value) {
          //TODO: saved supplier
        });
  }

  void _onSelectType(ServiceType t) {
    final notifier = ref.read(pBookingSearchProvider.notifier);
    notifier.onChangeServiceType(t);
  }

  Widget _searchNotFoundWidget() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AppAssets.origin().imEmptyView.widget(),
          TripcText(context.strings.text_search_not_found)
        ],
      ),
    );
  }
}
