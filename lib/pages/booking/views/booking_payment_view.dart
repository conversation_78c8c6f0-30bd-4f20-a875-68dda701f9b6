import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/booking_response/service_responses/detail_booking_service_response.dart';
import 'package:tripc_app/pages/booking/components/rating_widget_detail_restaurant.dart';
import 'package:tripc_app/pages/ticket_tour/components/address_link_widget.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';
import '../../../models/app/tripc_persistent_tab_type.dart';
import '../../../models/remote/booking_response/supplier_responses/detail_supplier_response.dart';
import '../../../services/app/app_route.dart';
import '../../../services/providers/providers.dart';
import '../../../widgets/commons/base_cached_network_image.dart';
import '../../../widgets/commons/tripc_button/tripc_button.dart';
import '../../tabbar/app_tabbar.dart';
import '../providers/add_passenger_quantity_provider.dart';
import '../providers/booking_provider.dart';
import '../components/booking_dialog.dart';
import 'booking_payment_body.dart';

class TripcBookingPaymentView extends ConsumerStatefulWidget {
  const TripcBookingPaymentView({
    super.key,
    required this.supplier,
  });

  final DetailSupplier? supplier;

  @override
  ConsumerState<TripcBookingPaymentView> createState() =>
      _TripcAddPassengerQuantityCuisineState();
}

class _TripcAddPassengerQuantityCuisineState
    extends ConsumerState<TripcBookingPaymentView> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        ref.listenManual(
            pBookingProvider.select((state) => state.isBookingStatus),
            (_, state) {
          if (state.isNotNull) {
            _voidShowDialogSuccess(isPaymentSuccess: state!);
            ref.read(pBookingProvider.notifier).setBookingStatus(null);
          }
        });
      },
    );
  }

  _voidShowDialogSuccess({required bool isPaymentSuccess}) {
    final orderId = ref.watch(
      pBookingProvider.select((value) => value.orderId),
    );
    dialogHelpers.show(
      context,
      child: BookingDialog(
        title: isPaymentSuccess
            ? context.strings.booking_success
            : context.strings.booking_failure,
        titleFontWeight: FontWeight.w500,
        icon: Container(
          height: 40.H,
          width: 40.W,
          decoration: BoxDecoration(
            color: AppAssets.origin().colorIconSuccess,
            borderRadius: BorderRadius.circular(50.SP),
          ),
          child: isPaymentSuccess
              ? Icon(
                  Icons.check_rounded,
                  color: AppAssets.origin().whiteBackgroundColor,
                )
              : Icon(
                  Icons.warning_amber_rounded,
                  color: AppAssets.origin().whiteBackgroundColor,
                ),
        ),
        titleFirstButton: isPaymentSuccess
            ? context.strings.detail_booking
            : context.strings.text_try_again,
        titleSecondButton:
            isPaymentSuccess ? context.strings.go_back_to_homepage : null,
        onFirstButtonTap: () {
          if (!isPaymentSuccess) {
            Navigator.pop(context);
          } else {
            AppRoute.pushNamed(context,
                routeName: AppRoute.routeBookingDetailOrder,
                arguments: orderId);

            ref.read(pBookingProvider.notifier).resetState();
            ref.read(pAddPassengerQuantityProvider.notifier).resetState();
            ref
                .read(pAppBottomNavProvider.notifier)
                .setTab(TripCPersistentTabType.mytrip);
          }
        },
        onSecondButtonTap: () {
          if (!isPaymentSuccess) {
            Navigator.pop(context);
          } else {
            ref.read(pBookingProvider.notifier).resetState();
            ref.read(pAddPassengerQuantityProvider.notifier).resetState();
            ref
                .read(pAppBottomNavProvider.notifier)
                .setTab(TripCPersistentTabType.home);
            Navigator.popUntil(
              context,
              ModalRoute.withName(AppRoute.routeHome),
            );
          }
        },
        contentPadding: EdgeInsets.symmetric(horizontal: 24.W)
            .copyWith(top: 24.H, bottom: 27.H),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 16.H),
          child: TripcText(
            isPaymentSuccess
                ? context.strings.booking_success_dialog_content
                : context.strings.text_please_try_again,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            textColor: AppAssets.origin().black,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final cuisine = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.detailCuisine));

    final isLoading =
        ref.watch(pBookingProvider.select((value) => value.isLoading));

    return Stack(
      children: [
        TripcScaffold(
          onPressed: () => unfocusKeyboard(),
          onPopScope: () => ref.read(pBookingProvider.notifier).resetState(),
          onLeadingPressed: () {
            ref.read(pBookingProvider.notifier).resetState();
            Navigator.pop(context);
          },
          visibleAppBar: false,
          extendBodyBehindAppBar: false,
          leadingColor: AppAssets.origin().neutralColor,
          backgroundColor: AppAssets.origin().bgDetailTourColorV2,
          hasAppbarBottomLine: false,
          body: Stack(
            children: [
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                bottom: 80.H,
                child: SingleChildScrollView(
                  padding: EdgeInsets.zero,
                  physics: const ClampingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _suplierImage(cuisine),
                      const BookingPaymentBody(),
                    ],
                  ),
                ),
              ),
              //payment button
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        top: BorderSide(
                          color: Colors.grey.shade300,
                          width: 1.0,
                        ),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          offset: const Offset(0, -2),
                          blurRadius: 8.0,
                          spreadRadius: 1.0,
                        ),
                      ],
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: 16.W, vertical: 16.H).copyWith(bottom: 25.H),
                    child: TripcButton(
                      onPressed: () async {
                        final result = await ref
                            .read(pAddPassengerQuantityProvider.notifier)
                            .addContact();
                        if (result) {
                          ref.read(pBookingProvider.notifier).onPayment(
                                bookingRequest: ref.read(
                                    pAddPassengerQuantityProvider.select(
                                        (value) => value.bookingRequest)),
                              );
                        } else {
                          ref
                              .read(pBookingProvider.notifier)
                              .setBookingStatus(false);
                        }
                      },
                      style: AppButtonStyle(
                          backgroundColor: AppAssets.origin().primaryColorV2),
                      height: 46,
                      title: context.strings.booking,
                    )),
              ),
            ],
          ),
        ),
        AppLoading(isRequesting: isLoading)
      ],
    );
  }

  Widget _suplierImage(DetailBookingService? cuisine) {
    return SizedBox(
      height: 247.H,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          BaseCachedNetworkImage(
            imageUrl: widget.supplier?.logoUrl ?? '',
            height: 247.H,
            width: double.infinity,
            placeholder: (context, _) => Container(
              color: AppAssets.origin().lightGrayDD4,
            ),
            errorWidget: (context, error, stackTrace) =>
                AppAssets.origin().icErrorImg.widget(
                      height: 247.H,
                      color: context.appCustomPallet.buttonBG,
                    ),
            fit: BoxFit.cover,
          ),
          Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: double.infinity,
                height: 247.H,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppAssets.origin().gray4D.withValues(alpha: 0.1),
                      AppAssets.origin().black.withValues(alpha: 0.91),
                    ],
                    stops: const [0, 1],
                  ),
                ),
              )),
          Positioned(
            bottom: 16.H,
            left: 24.W,
            right: 24.W,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TripcText(
                  cuisine?.supplier?.name,
                  textAlign: TextAlign.start,
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  textColor: AppAssets.origin().neutralColor,
                ),
                if (cuisine?.supplier != null)
                  RatingWidgetRestaurantDetail(
                      model: cuisine!.supplier!,
                      iconSize: Size(16.W, 16.W),
                      textColor: AppAssets.origin().whiteBackgroundColor,
                      fontSize: 12.SP,
                      addOnTextFontSize: 10.SP
                  ),
                SizedBox(height: 8.H),
                if (cuisine?.supplier != null)
                  AddressLink(
                      address: cuisine?.supplier?.fullAddress ?? '',
                      textDecoration: TextDecoration.none,
                      textColor: AppAssets.origin().whiteBackgroundColor,
                      spacing: 4
                  ),
              ],
            ),
          ),
          Positioned(
            top: 0,
            right: 0,
            left: 0,
            child: SafeArea(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TripcText(
                    context.strings.booking_information,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    textColor: AppAssets.origin().neutralColor,
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: 3,
            left: 20,
            child: SafeArea(
              child: GestureDetector(
                onTap: () {
                  ref.read(pBookingProvider.notifier).resetState();
                  Navigator.pop(context);
                },
                child: AppAssets.init.iconArrowleft.widget(
                  color: AppAssets.origin().whiteBackgroundColor,
                ),
              ),
            ),),
        ],
      ),
    );
  }
}
