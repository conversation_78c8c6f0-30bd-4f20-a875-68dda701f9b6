import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/ticket_tour/components/order_info.dart';
import 'package:tripc_app/pages/tour-payment/views/electronic_invoice.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_filed.dart';
import '../../../services/app/app_assets.dart';
import '../../../services/app/app_route.dart';
import '../../../services/providers/providers.dart';
import '../../../utils/app_extension.dart';
import '../../../widgets/commons/app_dialog/tripc_dialog.dart';
import '../../../widgets/commons/tripc_scafford/tripc_scafford.dart';
import '../../tour-payment/components/payment_method_selection.dart';
import '../providers/booking_provider.dart';

class BookingElectricInvoiceView extends ConsumerStatefulWidget {
  const BookingElectricInvoiceView({
    super.key,
  });

  @override
  ConsumerState<BookingElectricInvoiceView> createState() =>
      _ElectronicInvoiceState();
}

class _ElectronicInvoiceState
    extends ConsumerState<BookingElectricInvoiceView> {
  late TextEditingController _nameController;
  late TextEditingController _addressController;
  late TextEditingController _phoneNoController;
  late TextEditingController _taxCodeController;
  late TextEditingController _emailController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _addressController = TextEditingController();
    _phoneNoController = TextEditingController();
    _taxCodeController = TextEditingController();
    _emailController = TextEditingController();

    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        _nameController.text =
            ref.watch(pBookingProvider.select((value) => value.nameInvoice)) ??
                '';
        _addressController.text = ref.watch(
                pBookingProvider.select((value) => value.addressInvoice)) ??
            '';
        _taxCodeController.text = ref.watch(
                pBookingProvider.select((value) => value.taxCodeInvoice)) ??
            '';
        _emailController.text =
            ref.watch(pBookingProvider.select((value) => value.emailInvoice)) ??
                '';
        _phoneNoController.text =
            ref.watch(pBookingProvider.select((value) => value.phoneInvoice)) ??
                '';
      },
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _taxCodeController.dispose();
    _emailController.dispose();
    _phoneNoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final invoiceType =
        ref.watch(pBookingProvider.select((value) => value.invoiceType));

    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        appBarColor: AppAssets.origin().whiteBackgroundColor,
        backgroundColor: AppAssets.origin().bgDetailTourColorV2,
        hasBackButton: true,
        needUnFocus: true,
        onLeadingPressed: () {
          ref.read(pBookingProvider.notifier).resetState();
          Navigator.pop(context);
        },
        titleAppBar: TripcText(
          context.strings.issue_electronic_invoice,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          textColor: Colors.black,
        ),
        body: SafeArea(
          child: LayoutBuilder(builder: (context, constraints) {
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.W, vertical: 16.H),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            PaymentMethodSelection(
                                title: InvoiceType.individual
                                    .titleOptional(context),
                                value: invoiceType == InvoiceType.individual,
                                onTapCheck: () => ref
                                    .read(pBookingProvider.notifier)
                                    .setInvoice(
                                        invoiceType: InvoiceType.individual)),
                            PaymentMethodSelection(
                                title:
                                    InvoiceType.business.titleOptional(context),
                                value: invoiceType == InvoiceType.business,
                                onTapCheck: () => ref
                                    .read(pBookingProvider.notifier)
                                    .setInvoice(
                                        invoiceType: InvoiceType.business))
                          ],
                        ),
                      ),
                      OrderInfo(
                        title: context.strings.text_note,
                        contents: [
                          context.strings.text_electronic_invoice_note,
                          context.strings.text_invoice_message,
                        ],
                        backgroundColor: AppAssets.origin().yellow7E5,
                      ),
                      invoiceType == InvoiceType.individual
                          ? _contactInformationPerson(context, ref)
                          : _contactInformationBusiness(context, ref),
                      OrderInfo(
                        title: context.strings.text_disclaimer,
                        contents: [
                          context.strings.text_disclaimer_note_1,
                          context.strings.text_disclaimer_note_2,
                          context.strings.text_disclaimer_note_3,
                          context.strings.text_disclaimer_note_4,
                        ],
                        backgroundColor: AppAssets.origin().yellow7E5,
                      ),
                      const Spacer(),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.H)
                            .copyWith(top: 20, bottom: 30),
                        child: TripcButton(
                          height: 48.H,
                          textCase: TextCaseType.none,
                          isButtonDisabled: _nameController.text.isEmpty ||
                                  _emailController.text.isEmpty ||
                                  invoiceType == InvoiceType.individual
                              ? _phoneNoController.text.isEmpty
                              : _addressController.text.isEmpty ||
                                  _taxCodeController.text.isEmpty,
                          title: context.strings.text_submit_request,
                          onPressed: () {
                            dialogHelpers.show(context,
                                child: TripcDialog(
                                  title:
                                      '${context.strings.text_invoice_success.toSentenceCase()}!',
                                  titleFontWeight: FontWeight.w500,
                                  onTap: () => Navigator.popUntil(
                                      context,
                                      (route) =>
                                          route.settings.name ==
                                          AppRoute.routeBookingPayment),
                                  icon: Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                        color:
                                            AppAssets.origin().colorIconSuccess,
                                        shape: BoxShape.circle),
                                    child: Icon(
                                      Icons.check_rounded,
                                      color: AppAssets.origin()
                                          .whiteBackgroundColor,
                                    ),
                                  ),
                                  titleButton: context.strings.text_agree
                                      .toSentenceCase(),
                                  contentPadding:
                                      EdgeInsets.symmetric(horizontal: 24.W)
                                          .copyWith(top: 24.H, bottom: 27.H),
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 16.H),
                                    child: TripcText(
                                      context
                                          .strings.text_invoice_message_popup,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w300,
                                      textColor: AppAssets.origin().black,
                                    ),
                                  ),
                                ));
                            // Navigator.pop(context);
                          },
                          style: AppButtonStyle(
                              radius: 12.H,
                              backgroundColor: AppAssets.init.lightBlueFD,
                              textColor: Colors.white,
                              fontSize: 15,
                              fontWeight: FontWeight.w500),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            );
          }),
        ));
  }

  Widget _contactInformationPerson(BuildContext context, WidgetRef ref) {
    // final contactErrorPhoneNumber = ref.watch(
    //     pTicketTourProvider.select((value) => value.contactErrorPhoneNumber));
    // final contactErrorEmail = ref.watch(pTicketTourProvider.select(
    //   (value) => value.contactErrorEmail,
    // ));
    return Container(
      margin: EdgeInsets.all(16.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().whiteBackgroundColor,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      padding: EdgeInsets.symmetric(horizontal: 9.W, vertical: 20.H),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 11.H,
        children: [
          TripcText(
            context.strings.text_contact_information.toSentenceCase(),
            fontSize: 16,
            textAlign: TextAlign.start,
            fontWeight: FontWeight.w600,
            padding: EdgeInsets.only(bottom: 4.H),
            textColor: AppAssets.origin().textColorForNameTourV2,
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.W),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    TripcText(
                      context.strings.text_first_name_and_last_name
                          .toSentenceCase(),
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(bottom: 3.H),
                      textColor: AppAssets.origin().black,
                    ),
                    TripcText(
                      '*',
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(left: 3.H),
                      textColor: AppAssets.origin().redDotColor,
                    ),
                  ],
                ),
                TripcTextField(
                  controller: _nameController,
                  hintText: context.strings.text_full_name,
                  fontSize: 12,
                  scrollPadding: EdgeInsets.zero,
                  onChanged: (value) => ref
                      .read(pBookingProvider.notifier)
                      .setInvoice(name: value),
                  textInputAction: TextInputAction.next,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.W),
            child: Column(
              children: [
                Row(
                  children: [
                    TripcText(
                      context.strings.text_linked_phone,
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(bottom: 3.H),
                      textColor: AppAssets.origin().black,
                    ),
                    TripcText(
                      '*',
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(left: 3.H),
                      textColor: AppAssets.origin().redDotColor,
                    ),
                  ],
                ),
                TripcTextField(
                  // errorText: contactErrorPhoneNumber.isEmpty
                  //     ? null
                  //     : contactErrorPhoneNumber,
                  controller: _phoneNoController,
                  hintText: context.strings.text_mobile_phone,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  keyboardType: TextInputType.number,
                  fontSize: 12,
                  maxLength: 10,
                  onChanged: (value) => ref
                      .read(pBookingProvider.notifier)
                      .setInvoice(phone: value),
                  textInputAction: TextInputAction.done,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.W),
            child: Column(
              children: [
                Row(
                  children: [
                    TripcText(
                      context.strings.text_email,
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(bottom: 3.H),
                      textColor: AppAssets.origin().black,
                    ),
                    TripcText(
                      '*',
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(left: 3.H),
                      textColor: AppAssets.origin().redDotColor,
                    ),
                  ],
                ),
                TripcTextField(
                  controller: _emailController,
                  onChanged: (value) => ref
                      .read(pBookingProvider.notifier)
                      .setInvoice(email: value),
                  hintText: context.strings.text_email,
                  // errorText: contactErrorEmail.isEmpty ? null : contactErrorEmail,
                  textInputAction: TextInputAction.done,
                  keyboardType: TextInputType.emailAddress,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  fontSize: 12,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _contactInformationBusiness(BuildContext context, WidgetRef ref) {
    // final contactErrorPhoneNumber = ref.watch(
    //     pTicketTourProvider.select((value) => value.contactErrorPhoneNumber));
    // final contactErrorEmail = ref.watch(pTicketTourProvider.select(
    //   (value) => value.contactErrorEmail,
    // ));
    return Container(
      margin: EdgeInsets.all(16.W),
      decoration: BoxDecoration(
        color: AppAssets.origin().whiteBackgroundColor,
        borderRadius: BorderRadius.circular(12.SP),
      ),
      padding: EdgeInsets.symmetric(horizontal: 9.W, vertical: 20.H),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 11.H,
        children: [
          TripcText(
            context.strings.business_information,
            fontSize: 16,
            textAlign: TextAlign.start,
            fontWeight: FontWeight.w600,
            padding: EdgeInsets.only(bottom: 4.H),
            textColor: AppAssets.origin().textColorForNameTourV2,
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.W),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    TripcText(
                      context.strings.text_business_name.toSentenceCase(),
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(bottom: 3.H),
                      textColor: AppAssets.origin().black,
                    ),
                    TripcText(
                      '*',
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(left: 3.H),
                      textColor: AppAssets.origin().redDotColor,
                    ),
                  ],
                ),
                TripcTextField(
                  controller: _nameController,
                  hintText: context.strings.text_enter_business_name,
                  fontSize: 12,
                  scrollPadding: EdgeInsets.zero,
                  onChanged: (value) => ref
                      .read(pBookingProvider.notifier)
                      .setInvoice(name: value),
                  textInputAction: TextInputAction.next,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.W),
            child: Column(
              children: [
                Row(
                  children: [
                    TripcText(
                      context.strings.text_business_address,
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(bottom: 3.H),
                      textColor: AppAssets.origin().black,
                    ),
                    TripcText(
                      '*',
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(left: 3.H),
                      textColor: AppAssets.origin().redDotColor,
                    ),
                  ],
                ),
                TripcTextField(
                  controller: _addressController,
                  hintText: context.strings.enter_your_business_address,
                  fontSize: 12,
                  scrollPadding: EdgeInsets.zero,
                  onChanged: (value) => ref
                      .read(pBookingProvider.notifier)
                      .setInvoice(address: value),
                  textInputAction: TextInputAction.next,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.W),
            child: Column(
              children: [
                Row(
                  children: [
                    TripcText(
                      context.strings.text_tax_code,
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(bottom: 3.H),
                      textColor: AppAssets.origin().black,
                    ),
                    TripcText(
                      '*',
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(left: 3.H),
                      textColor: AppAssets.origin().redDotColor,
                    ),
                  ],
                ),
                TripcTextField(
                  controller: _taxCodeController,
                  hintText: context.strings.enter_your_business_tax_code,
                  fontSize: 12,
                  scrollPadding: EdgeInsets.zero,
                  onChanged: (value) => ref
                      .read(pBookingProvider.notifier)
                      .setInvoice(taxCode: value),
                  textInputAction: TextInputAction.next,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.W),
            child: Column(
              children: [
                Row(
                  children: [
                    TripcText(
                      context.strings.text_invoice_email,
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(bottom: 3.H),
                      textColor: AppAssets.origin().black,
                    ),
                    TripcText(
                      '*',
                      fontSize: 14,
                      textAlign: TextAlign.start,
                      fontWeight: FontWeight.w500,
                      padding: EdgeInsets.only(left: 3.H),
                      textColor: AppAssets.origin().redDotColor,
                    ),
                  ],
                ),
                TripcTextField(
                  controller: _emailController,
                  onChanged: (value) => ref
                      .read(pBookingProvider.notifier)
                      .setInvoice(email: value),
                  hintText: context.strings.enter_your_business_email,
                  textInputAction: TextInputAction.done,
                  keyboardType: TextInputType.emailAddress,
                  hintTextColor: AppAssets.origin().lightGrayDD4,
                  fontSize: 12,
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 13.H, horizontal: 12.5.W),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
