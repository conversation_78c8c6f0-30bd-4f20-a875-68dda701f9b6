import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/common_extension.dart';

import '../../../models/remote/api_tour_response/sub_product_response.dart';
import '../../../services/app/app_route.dart';
import '../../../widgets/commons/content_shadow_view.dart';
import '../../tour-payment/components/invoice_status_view_v2.dart';
import '../../tour-payment/views/electronic_invoice.dart';
import '../providers/add_passenger_quantity_provider.dart';
import '../providers/booking_provider.dart';
import '../components/note_information.dart';

class BookingPaymentBody extends ConsumerStatefulWidget {
  const BookingPaymentBody({
    super.key,
  });

  @override
  ConsumerState<BookingPaymentBody> createState() =>
      _CuisineBookingPaymentState();
}

class _CuisineBookingPaymentState extends ConsumerState<BookingPaymentBody> {
  @override
  Widget build(BuildContext context) {
    final cuisine = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.detailCuisine));

    final selectedDate = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.selectedDate));
    final selectedTime = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.selectedTime));

    final subProducts = cuisine?.subProducts;

    final name = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.nameContact));
    final phone = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.phoneContact));
    final email = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.emailContact));

    final specialRequest = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.specialRequest));

    final noteInformation = cuisine?.supplier?.note;

    final nameInvoice =
        ref.watch(pBookingProvider.select((value) => value.nameInvoice));

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.W, vertical: 16.H),
        child: ContentShadowView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.H, vertical: 16.W),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _bookingInformation(
                    selectedDate: selectedDate,
                    selectedTime: selectedTime,
                    subProducts: subProducts),
                _contactInformation(name: name, phone: phone, email: email),
                _specialRequest(specialRequest: specialRequest),
              ],
            ),
          ),
        ),
      ),
      Padding(
        padding: EdgeInsets.only(bottom: 16.H),
        child: NoteInformationWidget(
          htmlText: noteInformation ?? '',
        ),
      ),
      Visibility(
        visible: true,
        //  cuisine?.isExportInvoice ?? false,
        child: Padding(
          padding: EdgeInsets.only(left: 16.H, right: 16.H, bottom: 16.H),
          child: InvoiceStatusViewV2(
            status: (nameInvoice?.isNotEmpty == true)
                ? InvoiceStatus.processing
                : InvoiceStatus.notYetRequest,
            onTap: () => AppRoute.pushNamed(
              context,
              routeName: AppRoute.routeBookingElectricInvoice,
            ),
          ),
        ),
      ),
    ]);
  }

  Widget _bookingInformation(
      {required DateTime? selectedDate,
      required String? selectedTime,
      required List<SubProductResponse>? subProducts}) {
    final totalStock = subProducts?.fold<int>(0, (sum, e) => sum + e.stock);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 16.H,
      children: [
        TripcText(
          context.strings.booking_information,
          fontSize: 16,
          fontWeight: FontWeight.w700,
          textAlign: TextAlign.start,
          textColor: AppAssets.origin().blue191,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 8.H,
          children: [
            TripcText(
              context.strings.coming_time,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              textAlign: TextAlign.start,
              textColor: AppAssets.origin().blue001,
            ),
            _commonRowWidget(
                title: context.strings.date,
                content: selectedDate?.toVietnameseDateString()),
            _commonRowWidget(
                title: context.strings.text_time, content: selectedTime)
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 8.H,
          children: [
            TripcText(
              context.strings.guest_quantity_with_count(totalStock ?? 0),
              fontSize: 14,
              fontWeight: FontWeight.w500,
              textAlign: TextAlign.start,
              textColor: AppAssets.origin().blue001,
            ),
            Column(
              spacing: 8.H,
              children: subProducts
                      ?.where((e) => e.audience?.isDefault == 0)
                      .toList()
                      .mapIndexed((idx, e) {
                    return _commonRowWidget(
                        title: e.audience?.name ?? '',
                        content: '${e.stock} ${context.strings.ticket}');
                  }).toList() ??
                  [],
            )
          ],
        ),
      ],
    );
  }

  Widget _contactInformation(
      {required String? name, required String? phone, required String? email}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 16.H,
      children: [
        TripcText(
          context.strings.text_contact_information.toSentenceCase(),
          fontSize: 16,
          fontWeight: FontWeight.w700,
          textAlign: TextAlign.start,
          textColor: AppAssets.origin().blue191,
          padding: EdgeInsets.only(top: 16.H),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 8.H,
          children: [
            _commonRowWidget(
                title: context.strings.text_first_name_and_last_name
                    .toSentenceCase(),
                content: name),
            _commonRowWidget(
                title: context.strings.text_linked_phone, content: phone),
            _commonRowWidget(title: context.strings.text_email, content: email),
          ],
        )
      ],
    );
  }

  Widget _specialRequest({required String? specialRequest}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 16.H,
      children: [
        TripcText(
          context.strings.text_special_requests.toSentenceCase(),
          fontSize: 16,
          fontWeight: FontWeight.w700,
          textAlign: TextAlign.start,
          textColor: AppAssets.origin().blue191,
          padding: EdgeInsets.only(top: 16.H),
        ),
        TripcText(
          specialRequest,
          fontSize: 14,
          textAlign: TextAlign.start,
          fontWeight: FontWeight.w400,
          textColor: AppAssets.origin().blackColor,
          padding: EdgeInsets.only(left: 8.W),
        ),
      ],
    );
  }

  Widget _commonRowWidget({required String title, required String? content}) =>
      Row(
        children: [
          Expanded(
            flex: 2,
            child: TripcText(
              title,
              fontSize: 14,
              textAlign: TextAlign.start,
              fontWeight: FontWeight.w500,
              padding: EdgeInsets.only(left: 10.W),
              textColor: AppAssets.origin().blackColor,
            ),
          ),
          Expanded(
            flex: 3,
            child: TripcText(
              content,
              fontSize: 14,
              textAlign: TextAlign.start,
              fontWeight: FontWeight.w400,
              textColor: AppAssets.origin().blackColor,
            ),
          ),
        ],
      );
}
