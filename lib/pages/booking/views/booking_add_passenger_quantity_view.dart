import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/remote/booking_response/service_responses/detail_booking_service_response.dart';
import 'package:tripc_app/pages/ticket_tour/components/address_link_widget.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/app/app_route.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_loading.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';
import '../../../widgets/commons/base_cached_network_image.dart';
import '../../../widgets/commons/tripc_button/tripc_button.dart';
import '../components/rating_widget_detail_restaurant.dart';
import '../providers/add_passenger_quantity_provider.dart';
import 'booking_add_passenger_quantity_body.dart';

class TripcBookingAddPassengerQuantityView extends ConsumerStatefulWidget {
  const TripcBookingAddPassengerQuantityView({
    super.key,
    required this.seatProductId,
  });

  final int? seatProductId;

  @override
  ConsumerState<TripcBookingAddPassengerQuantityView> createState() =>
      _TripcAddPassengerQuantityCuisineState();
}

class _TripcAddPassengerQuantityCuisineState
    extends ConsumerState<TripcBookingAddPassengerQuantityView> {

  late final TextEditingController _fullNameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _noteController;

  @override
  void initState() {
    super.initState();

    _fullNameController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();
    _noteController = TextEditingController();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await ref
          .read(pAddPassengerQuantityProvider.notifier)
          .init(widget.seatProductId ?? 0);

      _fullNameController.text = ref.read(
          pAddPassengerQuantityProvider.select((value) => value.nameContact));
      _emailController.text = ref.read(
          pAddPassengerQuantityProvider.select((value) => value.emailContact));
      _phoneController.text = ref.read(
          pAddPassengerQuantityProvider.select((value) => value.phoneContact));
    });
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bookingService = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.detailCuisine));

    final isButtonEnable = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.isButtonEnable));

    final isLoading = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.isLoading));

    final specialRequest = ref.watch(
        pAddPassengerQuantityProvider.select((value) => value.specialRequest));

    return Stack(
      children: [
        TripcScaffold(
          onPressed: () => unfocusKeyboard(),
          onPopScope: () =>
              ref.read(pAddPassengerQuantityProvider.notifier).resetState(),
          onLeadingPressed: () {
            ref.read(pAddPassengerQuantityProvider.notifier).resetState();
            Navigator.pop(context);
          },
          visibleAppBar: false,
          extendBodyBehindAppBar: false,
          leadingColor: AppAssets.origin().neutralColor,
          backgroundColor: AppAssets.origin().bgDetailTourColorV2,
          hasAppbarBottomLine: false,
          body: Stack(
            children: [
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                bottom: 80.H,
                child: SingleChildScrollView(
                  padding: EdgeInsets.zero,
                  physics: const ClampingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _supplierImage(bookingService),
                      BookingAddPassengerQuantityBody(
                        detailCuisine: bookingService,
                        nameController: _fullNameController,
                        emailController: _emailController,
                        phoneController: _phoneController,
                        noteController: _noteController,
                      ),
                    ],
                  ),
                ),
              ),
              //continue button
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      top: BorderSide(
                        color: Colors.grey.shade300,
                        width: 1.0,
                      ),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        offset: const Offset(0, -2),
                        blurRadius: 8.0,
                        spreadRadius: 1.0,
                      ),
                    ],
                  ),
                  padding: EdgeInsets.symmetric(
                      horizontal: 16.W, vertical: 16.H).copyWith(bottom: 25.H),
                  child: TripcButton(
                    onPressed: () {
                      unfocusKeyboard();
                      if (specialRequest.isEmpty) {
                        ref
                            .read(pAddPassengerQuantityProvider.notifier)
                            .setSpecialRequest(context,
                            value: '${context.strings.text_do_not_have}.');
                      }
                      AppRoute.pushNamed(context,
                          routeName: AppRoute.routeBookingPayment,
                          arguments: bookingService?.supplier);
                    },
                    showSuggestLoginDialog: true,
                    style: AppButtonStyle(
                        backgroundColor: AppAssets.origin().primaryColorV2),
                    isButtonDisabled: !isButtonEnable,
                    height: 46,
                    title: context.strings.text_continue,
                  ),
                ),
              ),
            ],
          ),
        ),
        Visibility(
            visible: isLoading, child: AppLoading(isRequesting: isLoading))
      ],
    );
  }

  Widget _supplierImage(DetailBookingService? cuisine) {
    return SizedBox(
      height: 247.H,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          BaseCachedNetworkImage(
            imageUrl: cuisine?.supplier?.logoUrl ?? '',
            height: 247.H,
            width: double.infinity,
            placeholder: (context, _) => Container(
              color: AppAssets.origin().lightGrayDD4,
            ),
            errorWidget: (context, error, stackTrace) =>
                AppAssets.origin().icErrorImg.widget(
                      height: 247.H,
                      color: context.appCustomPallet.buttonBG,
                    ),
            fit: BoxFit.cover,
          ),
          Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: double.infinity,
                height: 247.H,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppAssets.origin().gray4D.withValues(alpha: 0.1),
                      AppAssets.origin().black.withValues(alpha: 0.91),
                    ],
                    stops: const [0, 1],
                  ),
                ),
              )),
          Positioned(
              bottom: 16.H,
              left: 24.W,
              right: 24.W,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TripcText(
                    cuisine?.supplier?.name,
                    textAlign: TextAlign.start,
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    textColor: AppAssets.origin().neutralColor,
                  ),
                  if (cuisine?.supplier != null)
                    RatingWidgetRestaurantDetail(
                      model: cuisine!.supplier!,
                      iconSize: Size(16.W, 16.W),
                      textColor: AppAssets.origin().whiteBackgroundColor,
                      fontSize: 12.SP,
                      addOnTextFontSize: 10.SP
                    ),
                  SizedBox(height: 8.H),
                  if (cuisine?.supplier != null)
                  AddressLink(
                      address: cuisine?.supplier?.fullAddress ?? '',
                      textDecoration: TextDecoration.none,
                      textColor: AppAssets.origin().whiteBackgroundColor,
                      spacing: 4
                  ),
                ],
              ),
          ),
          Positioned(
            top: 0,
            right: 0,
            left: 0,
            child: SafeArea(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TripcText(
                    context.strings.booking_information,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    textColor: AppAssets.origin().neutralColor,
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: 3,
              left: 20,
              child: SafeArea(
            child: GestureDetector(
              onTap: () {
                ref.read(pAddPassengerQuantityProvider.notifier).resetState();
                Navigator.pop(context);
              },
              child: AppAssets.init.iconArrowleft.widget(
                color: AppAssets.origin().whiteBackgroundColor,
              ),
            ),
          ),),
        ],
      ),
    );
  }
}
