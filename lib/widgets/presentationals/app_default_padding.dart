import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';
import '/widgets/widget_const.dart';

class AppDefaultPaddingVertical extends StatelessWidget {
  const AppDefaultPaddingVertical({super.key, this.multiply});

  final double? multiply;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: WidgetConst.basePadding.H * (multiply ?? 1.0),
    );
  }
}

class AppDefaultPaddingHorizontal extends StatelessWidget {
  const AppDefaultPaddingHorizontal({super.key, this.multiply});

  final double? multiply;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: (WidgetConst.basePadding * (multiply ?? 1.0)).W,
    );
  }
}

class AppDefaultChildHorizontalPadding extends StatelessWidget {
  const AppDefaultChildHorizontalPadding({
    super.key,
    required this.child,
    this.multiply,
  });
  final Widget child;
  final double? multiply;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: (WidgetConst.buttonLoginPadding * (multiply ?? 1.0)).W,
      ),
      child: child,
    );
  }
}

class AppDefaultChildVerticalPadding extends StatelessWidget {
  const AppDefaultChildVerticalPadding({
    super.key,
    required this.child,
    this.multiply,
  });

  final Widget child;
  final double? multiply;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: WidgetConst.basePadding.H * (multiply ?? 1.0),
      ),
      child: child,
    );
  }
}
