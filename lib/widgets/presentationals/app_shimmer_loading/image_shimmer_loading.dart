import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/base_shimmer_loading.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

class ImageShimmerLoading extends StatelessWidget {
  const ImageShimmerLoading({
    super.key,
    this.height = 80,
    this.color,
    this.fit = BoxFit.contain,
  });

  final double height;
  final Color? color;
  final BoxFit fit;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: color,
      child: Center(
        child: BaseShimmerLoading(
          highlightColor: Colors.white,
          child: AppAssets.init.icMyTrip.widget(
            height: height.H,
            width: height.H,
            fit: fit,
            color: context.appCustomPallet.bf000?.withAlpha(80),
          ),
        ),
      ),
    );
  }
}
