import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_general_button.dart';

class GeneralDialog extends StatelessWidget {
  const GeneralDialog(
      {super.key,
      this.title,
      this.child,
      this.onTapConfirm,
      this.maxHeight,
      this.titlePadding,
      this.closeButtonPadding,
      this.bottomButton,
      this.dialogRadius,
      this.onTapClose,
      this.showTitle = true,
      this.isShowCloseButton = true,
      this.insetPadding,
      this.text});

  final String? title;
  final bool showTitle;
  final VoidCallback? onTapClose;
  final Widget? child;
  final Widget? bottomButton;
  final VoidCallback? onTapConfirm;
  final double? maxHeight;
  final Widget? text;
  final bool isShowCloseButton;
  final double? dialogRadius;
  final EdgeInsetsGeometry? titlePadding;
  final EdgeInsets? insetPadding;
  final EdgeInsetsGeometry? closeButtonPadding;
  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: insetPadding ?? EdgeInsets.only(left: 33.W, right: 27.W),
      elevation: 0,
      backgroundColor: AppAssets.init.whiteBackgroundColor,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(dialogRadius ?? 5.SP)),
      child: Column(mainAxisSize: MainAxisSize.min, children: [
        Visibility(
          visible: showTitle,
          child: Row(
            children: [
              Expanded(
                child: Padding(
                  padding:
                      titlePadding ?? EdgeInsets.only(top: 20.H, left: 15.W),
                  child: Column(
                    children: [
                      Align(
                        alignment: Alignment.topLeft,
                        child: text ??
                            Text(title ?? '',
                                style:
                                    AppAssets.init.superBoldTextStyle.copyWith(
                                  fontSize: 16.SP,
                                  color: Colors.black,
                                )),
                      ),
                    ],
                  ),
                ),
              ),
              Visibility(
                visible: isShowCloseButton,
                child: Padding(
                  padding: closeButtonPadding ??
                      EdgeInsets.only(top: 10.H, right: 10.W),
                  child: SizedBox(
                    height: 40.H,
                    width: 40.H,
                    child: InkWell(
                      onTap: onTapClose ?? () => Navigator.pop(context),
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      child: AppAssets.init.close.widget(
                        width: 20.W,
                        height: 20.H,
                        color: Colors.black.withOpacity(1),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Column(
          children: [
            Visibility(
              visible: child != null,
              child: ConstrainedBox(
                constraints: BoxConstraints(maxHeight: maxHeight ?? 300.H),
                child: SingleChildScrollView(child: child),
              ),
            ),
            bottomButton ??
                Padding(
                  padding:
                      EdgeInsets.only(left: 15.W, right: 15.W, bottom: 17.H),
                  child: Row(
                    children: [
                      Expanded(
                        child: GeneralButton(
                            onTap: () => Navigator.pop(context),
                            title: context.strings.text_cancel,
                            height: 40.H,
                            radius: 60.SP,
                            textStyle: AppAssets.init.normalTextStyle.copyWith(
                                fontSize: 12.SP,
                                color: Colors.black.withOpacity(0.3)),
                            boderColor: Colors.black.withOpacity(0.3),
                            buttonType: ButtonType.outline,
                            isDisabled: false),
                      ),
                      SizedBox(
                        width: 10.W,
                      ),
                      Expanded(
                        child: GeneralButton(
                            onTap: onTapConfirm,
                            title: context.strings.text_confirm,
                            height: 40.H,
                            radius: 60.SP,
                            textStyle: AppAssets.init.normalTextStyle.copyWith(
                                fontSize: 12.SP,
                                color: AppAssets.origin().whiteBackgroundColor),
                            buttonType: ButtonType.normal,
                            isDisabled: false),
                      ),
                    ],
                  ),
                ),
          ],
        ),
      ]),
    );
  }
}
