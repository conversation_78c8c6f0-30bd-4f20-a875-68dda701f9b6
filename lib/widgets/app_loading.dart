import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

class AppLoading extends ConsumerWidget {
  const AppLoading({super.key, required this.isRequesting, this.backgroundColor});
  final bool isRequesting;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Positioned.fill(
      child: IgnorePointer(
        ignoring: !isRequesting,
        child: AnimatedOpacity(
          opacity: isRequesting ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: Container(
            color: backgroundColor ?? Colors.black54,
            child: Center(
                child: AppAssets.origin().lottieLoadingTripCNew.widget(
                      height: 100.H,
                      width: 200.W,
                      repeat: true,
                    )),
          ),
        ),
      ),
    );
  }
}
