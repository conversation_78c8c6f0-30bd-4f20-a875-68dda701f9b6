import 'package:flutter/material.dart';

// ignore: must_be_immutable
class CustomAppBar extends StatelessWidget {
  CustomAppBar(
      {super.key,
      this.iconLeading,
      required this.backGroundColor,
      this.iconAction,
      this.title,
      this.onTapLeading,
      this.onTapActions,
      this.padding,
      this.trailing,
      this.leadingChild,
      this.leadingWidth});
  final Widget? iconLeading;
  final Widget? leadingChild;
  final Color backGroundColor;
  final Widget? iconAction;
  final Widget? title;
  final Widget? trailing;
  VoidCallback? onTapLeading;
  VoidCallback? onTapActions;
  EdgeInsetsGeometry? padding;
  double? leadingWidth;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      color: backGroundColor,
      child: AppBar(
        title: title,
        centerTitle: true,
        leadingWidth: leadingWidth,
        backgroundColor: backGroundColor,
        scrolledUnderElevation: 0.0,
        leading: iconLeading == null
            ? leadingChild ?? const SizedBox()
            : InkWell(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: onTapLeading,
                child: iconLeading,
              ),
        actions: [
          iconAction == null
              ? trailing ?? const SizedBox.shrink()
              : InkWell(
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  onTap: onTapActions,
                  child: iconAction,
                ),
        ],
      ),
    );
  }
}
