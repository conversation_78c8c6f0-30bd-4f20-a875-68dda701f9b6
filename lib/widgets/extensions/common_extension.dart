import 'package:intl/intl.dart';

extension StringCasingExtension on String {
  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
  String toTitleCase() => replaceAll(RegExp(' +'), ' ')
      .split(' ')
      .map((str) => str.toCapitalized())
      .join(' ');
}

/// Extension on nullable DateTime to produce strings like
extension VietnameseDateFormatting on DateTime? {
  String toVietnameseDateString() {
    final date = this;
    if (date == null) return '';

    const weekdayNames = {
      DateTime.monday   : 'Thứ 2',
      DateTime.tuesday  : 'Thứ 3',
      DateTime.wednesday: 'Thứ 4',
      DateTime.thursday : 'Thứ 5',
      DateTime.friday   : 'Thứ 6',
      DateTime.saturday : 'Thứ 7',
      DateTime.sunday   : 'Ch<PERSON> nhật',
    };

    final weekdayString = weekdayNames[date.weekday]!;
    final formattedDate  = DateFormat('dd/MM/yyyy').format(date);

    return '$weekdayString, $formattedDate';
  }
}

extension VietnameseDateTimeFormatting on String {
  String toVietnameseDateTimeString() {
    DateTime utcTime = DateTime.parse(this);
    // DateTime localTime = utcTime.toLocal();

    const weekdayNames = {
      DateTime.monday: 'Thứ 2',
      DateTime.tuesday: 'Thứ 3',
      DateTime.wednesday: 'Thứ 4',
      DateTime.thursday: 'Thứ 5',
      DateTime.friday: 'Thứ 6',
      DateTime.saturday: 'Thứ 7',
      DateTime.sunday: 'Chủ nhật',
    };

    String weekday = weekdayNames[utcTime.weekday] ?? '';
    String datePart = DateFormat('dd/MM/yyyy').format(utcTime);
    String timePart = DateFormat('HH:mm').format(utcTime);

    return '$weekday, $datePart - $timePart';
  }
}
