import 'package:flutter/material.dart';

enum AppChatThemeName {
  dark,
  light,
}

extension AppChatThemeNameX on AppChatThemeName {
  AppCustomColor get getThemeByName {
    switch (this) {
      case AppChatThemeName.light:
        return customThemes[2];
      default:
        return customThemes[1];
    }
  }

  Brightness get keyboardAppearanceByChatTheme {
    switch (this) {
      case AppChatThemeName.light:
        return Brightness.light;
      default:
        return Brightness.dark;
    }
  }
}

@immutable
class AppCustomColor extends ThemeExtension<AppCustomColor> {
  const AppCustomColor({
    required this.secondaryColor,
    required this.bf000,
    required this.gradientPink,
    required this.gradientBGChat,
    required this.gold,
    required this.greenText,
    required this.goldtexture,
    required this.silverTexture,
    required this.blackTexture,
    required this.buttonBG,
    required this.title,
    required this.subText,
    required this.navBG,
    required this.bgApp,
    required this.bgForm,
    required this.darkNavBg,
    required this.error,
  });

  final Color? secondaryColor;
  final Color? bf000;
  final Color? gradientPink;
  final Color? gradientBGChat;
  final Color? gold;
  final Color? greenText;
  final Color? goldtexture;
  final Color? silverTexture;
  final Color? blackTexture;
  final Color? buttonBG;
  final Color? title;
  final Color? subText;
  final Color? navBG;
  final Color? bgApp;
  final Color? bgForm;
  final Color? darkNavBg;
  final Color? error;

  @override
  AppCustomColor copyWith({
    Color? secondaryColor,
    Color? bf000,
    Color? gradientPink,
    Color? gradientBGChat,
    Color? gold,
    Color? greenText,
    Color? goldtexture,
    Color? buttonBG,
    Color? tittle,
    Color? subText,
    Color? darkNavBg,
    Color? navBG,
    Color? bgForm,
    Color? bgApp,
    Color? error,
  }) {
    return AppCustomColor(
      secondaryColor: secondaryColor ?? this.secondaryColor,
      bf000: bf000 ?? this.bf000,
      gradientPink: gradientPink ?? gradientPink,
      gradientBGChat: gradientBGChat ?? gradientBGChat,
      gold: gold ?? gold,
      greenText: greenText ?? greenText,
      goldtexture: goldtexture ?? goldtexture,
      silverTexture: silverTexture ?? silverTexture,
      blackTexture: blackTexture ?? blackTexture,
      buttonBG: buttonBG ?? buttonBG,
      title: tittle ?? tittle,
      subText: subText ?? subText,
      darkNavBg: darkNavBg ?? darkNavBg,
      navBG: navBG ?? navBG,
      bgApp: bgApp ?? bgApp,
      bgForm: bgForm ?? bgForm,
      error: error ?? error,
    );
  }

  @override
  AppCustomColor lerp(AppCustomColor? other, double t) {
    if (other is! AppCustomColor) {
      return this;
    }
    return AppCustomColor(
      secondaryColor: Color.lerp(secondaryColor, other.secondaryColor, t),
      bf000: Color.lerp(bf000, other.bf000, t),
      gradientPink: Color.lerp(gradientPink, other.gradientPink, t),
      gradientBGChat: Color.lerp(gradientBGChat, other.gradientBGChat, t),
      gold: Color.lerp(gold, other.gold, t),
      greenText: Color.lerp(greenText, other.greenText, t),
      goldtexture: Color.lerp(goldtexture, other.goldtexture, t),
      silverTexture: Color.lerp(silverTexture, other.silverTexture, t),
      blackTexture: Color.lerp(blackTexture, other.blackTexture, t),
      buttonBG: Color.lerp(blackTexture, other.buttonBG, t),
      title: Color.lerp(blackTexture, other.title, t),
      subText: Color.lerp(subText, other.title, t),
      darkNavBg: Color.lerp(darkNavBg, other.darkNavBg, t),
      navBG: Color.lerp(navBG, other.navBG, t),
      bgApp: Color.lerp(bgApp, other.bgApp, t),
      bgForm: Color.lerp(bgForm, other.bgForm, t),
      error: Color.lerp(error, other.error, t),
    );
  }

  // Optional
  @override
  String toString() => '';
}

// the designer must provide us the defination of colors....
List<AppCustomColor> customThemes = [
  const AppCustomColor(
    secondaryColor: Color(0xFF0C641E),
    bf000: Color(0xFF1E88E5),
    gradientPink: Color(0xFF1E88E5),
    gradientBGChat: Color(0xFF1E88E5),
    gold: Color(0xFFFFAD00),
    greenText: Color(0xFF35C75A),
    goldtexture: Color(0xFF1E88E5),
    silverTexture: Color(0xFF1E88E5),
    blackTexture: Color(0xFF000000),
    buttonBG: Color(0xFF23252B),
    title: Color(0xFFFFFFFF),
    subText: Color(0xFF848D97),
    navBG: Color(0xFF191A1E),
    bgApp: Color(0xFF0D0D10),
    bgForm: Colors.black26,
    darkNavBg: Color(0xFF15161A),
    error: Color(0xFFE93131),
  ), // Default
  const AppCustomColor(
    secondaryColor: Color(0xFF0C641E),
    bf000: Color(0xFF1E88E5),
    gradientPink: Color(0xFF1E88E5),
    gradientBGChat: Color(0xFF1E88E5),
    gold: Color(0xFFFFAD00),
    greenText: Color(0xFF35C75A),
    goldtexture: Color(0xFF1E88E5),
    silverTexture: Color(0xFF1E88E5),
    blackTexture: Color(0xFF000000),
    buttonBG: Color(0xFF1C1E23),
    title: Color(0xFFFFFFFF),
    subText: Color(0xFF848D97),
    navBG: Color(0xFF15161A),
    bgApp: Color(0xFF0D0D10),
    bgForm: Color(0xFFB5B5B5),
    darkNavBg: Color(0xFF15161A),
    error: Color(0xFFE93131),
  ), // Dark
  const AppCustomColor(
    secondaryColor: Color(0xFF0C641E),
    bf000: Color(0xFF1E88E5),
    gradientPink: Color(0xFF1E88E5),
    gradientBGChat: Color(0xFF1E88E5),
    gold: Color(0xFFFFAD00),
    greenText: Color(0xFF35C75A),
    goldtexture: Color(0xFF1E88E5),
    silverTexture: Color(0xFF1E88E5),
    blackTexture: Color(0xFF000000),
    buttonBG: Color(0xFFEDEDF0),
    title: Color(0xFF181923),
    subText: Color(0xFF687078),
    navBG: Color(0xFFFFFFFF),
    bgApp: Color(0xFFF7F7FA),
    bgForm: Colors.black26,
    darkNavBg: Color(0xFF191A1E),
    error: Color(0x00e93131),
  ), // Light
];
