import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

// ignore: must_be_immutable
enum ButtonType { normal, outline }

// ignore: must_be_immutable
class GeneralButton extends StatelessWidget {
  GeneralButton(
      {super.key,
      this.title,
      this.onTap,
      this.radius,
      required this.buttonType,
      required this.isDisabled,
      this.height,
      this.customTitle,
      this.textStyleNormal,
      this.textStyleOutline,
      this.width,
      this.textStyle,
      this.disabledTextStyle,
      this.boderColor,
      this.isLoading = false,
      this.buttonColor});
  String? title;
  Widget? customTitle;
  double? height;
  double? width;
  ButtonType buttonType;
  VoidCallback? onTap;
  double? radius;
  bool isDisabled;
  Color? buttonColor;
  TextStyle? textStyle;
  TextStyle? disabledTextStyle;
  TextStyle? textStyleNormal;
  TextStyle? textStyleOutline;
  Color? boderColor;
  bool isLoading;

  TextStyle _renderTextStyle() {
    if (buttonType == ButtonType.outline) {
      return textStyleOutline ??
          AppAssets.init.boldTextStyle.copyWith(
            fontSize: 16.SP,
            color: AppAssets.init.greenColor,
          );
    } else {
      return textStyleNormal ??
          AppAssets.init.boldTextStyle.copyWith(
            fontSize: 16.SP,
            color: Colors.white,
          );
    }
  }

  Color _renderColor() {
    if (buttonType == ButtonType.outline) {
      return Colors.transparent;
    } else {
      return buttonColor ?? AppAssets.init.greenColor;
    }
  }

  Color _renderColorBorder() {
    if (buttonType == ButtonType.outline) {
      return AppAssets.init.greenColor;
    } else {
      return Colors.transparent;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: height ?? 60.H,
        width: width ?? double.infinity,
        decoration: BoxDecoration(
          color: isDisabled
              ? buttonType == ButtonType.outline
                  ? Colors.transparent
                  : AppAssets.origin().disableButtonColor
              : _renderColor(),
          borderRadius: BorderRadius.circular(radius ?? 60.SP),
          border: Border.all(
              color: isDisabled
                  ? buttonType == ButtonType.outline
                      ? AppAssets.origin().grayTextColor
                      : Colors.transparent
                  : boderColor ?? _renderColorBorder(),
              width: 1.H),
        ),
        child: InkWell(
          onTap: onTap,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          child: Center(
            child: title.isNotNull
                ? Visibility(
                    visible: !isLoading,
                    replacement: LoadingAnimationWidget.fourRotatingDots(
                      color: AppAssets.origin().whiteBackgroundColor,
                      size: 20.H,
                    ),
                    child: Text(
                      title ?? '',
                      textAlign: TextAlign.center,
                      style: isDisabled
                          ? disabledTextStyle ??
                              AppAssets.init.superBoldTextStyle.copyWith(
                                fontSize: 16.SP,
                                color: AppAssets.origin().mainBlackColor,
                              )
                          : textStyle ?? _renderTextStyle(),
                    ),
                  )
                : Visibility(
                    visible: !isLoading,
                    replacement: LoadingAnimationWidget.fourRotatingDots(
                      color: AppAssets.origin().whiteBackgroundColor,
                      size: 20.H,
                    ),
                    child: customTitle ?? const SizedBox.shrink()),
          ),
        ));
  }
}
