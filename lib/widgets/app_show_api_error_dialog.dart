import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/app_general_button.dart';
import 'package:tripc_app/widgets/app_general_dialog.dart';

class ErrorDialog extends ConsumerWidget {
  const ErrorDialog(
      {super.key, required this.text, this.title, this.textStyle});
  final String text;
  final String? title;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GeneralDialog(
        title: title ?? 'Error!!!',
        titlePadding: EdgeInsets.only(left: 16.W, top: 10.H),
        closeButtonPadding: EdgeInsets.only(right: 8.W, top: 8.H),
        dialogRadius: 10.H,
        onTapClose: () {
          Navigator.pop(context);
        },
        bottomButton: Padding(
          padding: EdgeInsets.only(left: 13.W, right: 13.W, bottom: 16.H),
          child: GeneralButton(
            height: 45.H,
            title: context.strings.text_close,
            buttonType: ButtonType.normal,
            buttonColor: AppAssets.init.mainBlackColor,
            isDisabled: false,
            onTap: () async {
              Navigator.pop(context);
            },
          ),
        ),
        child: Padding(
            padding: EdgeInsets.only(bottom: 30.H, left: 13.W, right: 13.W),
            child: Text(
              text.isEmpty ? context.strings.text_error_something_wrong : text,
              textAlign: TextAlign.center,
              style: textStyle ??
                  AppAssets.init.normalTextStyle
                      .copyWith(color: AppAssets.init.redColor),
            )));
  }
}
