import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

class TripcSwitch extends StatefulWidget {
  const TripcSwitch({super.key, this.onToogle, this.value});
  final Function(bool)? onToogle;
  final bool? value;

  @override
  _TripcSwitchState createState() => _TripcSwitchState();
}

class _TripcSwitchState extends State<TripcSwitch>
    with SingleTickerProviderStateMixin {
  bool isSwitched = false;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  void _toggleSwitch() {
    if (widget.value.isNull) {
      setState(() {
        if (isSwitched) {
          _animationController.reverse();
        } else {
          _animationController.forward();
        }
        isSwitched = !isSwitched;
      });
    }
    widget.onToogle?.call(widget.value ?? isSwitched);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleSwitch,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background (Switch Track)
          widget.value ?? isSwitched
              ? AppAssets.origin()
                  .icSwitchActive
                  .widget(width: 60, height: 30) // Active thumb SVG
              : AppAssets.origin()
                  .icSwitchInactive
                  .widget(width: 60, height: 30),

          // Thumb Animation
          // AnimatedBuilder(
          //   animation: _animation,
          //   builder: (context, child) {
          //     return Positioned(
          //         left: _animation.value, // Moves thumb across the track
          //         child: isSwitched
          //             ? AppAssets.origin()
          //                 .icSwitchActive
          //                 .widget(width: 60, height: 30) // Active thumb SVG
          //             : AppAssets.origin()
          //                 .icSwitchInactive
          //                 .widget(width: 60, height: 30));
          //   },
          // ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}
