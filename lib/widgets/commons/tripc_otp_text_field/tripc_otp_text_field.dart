import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:pin_input_text_field/pin_input_text_field.dart';

class TripcOtpTextField extends StatelessWidget {
  const TripcOtpTextField(
      {super.key,
      required this.controller,
      this.onChanged,
      this.decoration,
      this.isError = false,
      this.focusNode,
      this.autoFocus = false,
      this.enable = true});
  final TextEditingController controller;
  final Function(String)? onChanged;
  final PinDecoration? decoration;
  final bool autoFocus;
  final bool isError;
  final FocusNode? focusNode;
  final bool enable;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60.H,
      decoration: BoxDecoration(
        boxShadow: [AppAssets.origin().itemShadow],
      ),
      child: PinInputTextFormField(
          enabled: enable,
          autoFocus: autoFocus,
          focusNode: focusNode,
          pinLength: 6,
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: decoration ??
              BoxLooseDecoration(
                strokeWidth: 1.H,
                bgColorBuilder: PinListenColorBuilder(
                    AppAssets.init.whiteBackgroundColor,
                    AppAssets.init.whiteBackgroundColor),
                strokeColorBuilder: PinListenColorBuilder(
                    isError
                        ? AppAssets.init.redColor
                        : AppAssets.init.secondaryColor,
                    AppAssets.init.secondDarkGreyTextColor),
                radius: Radius.circular(12.SP),
                gapSpace: 9.W,
                textStyle: AppAssets.init.normalTextStyle,
                obscureStyle: ObscureStyle(
                  isTextObscure: false,
                  obscureText: '*',
                ),
              ),
          cursor: Cursor(
              enabled: true,
              color: AppAssets.init.blackTextColor,
              width: 1.W,
              height: 17.H),
          onChanged: onChanged,
          tapRegionCallback: (region) {
            unfocusKeyboard();
          }),
    );
  }
}
