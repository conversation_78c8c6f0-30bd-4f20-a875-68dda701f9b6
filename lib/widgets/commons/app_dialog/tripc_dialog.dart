import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../../models/app/text_case_type.dart';

enum TripcDialogType { warning, success }

class TripcDialog extends StatelessWidget {
  const TripcDialog(
      {super.key,
      required this.title,
      this.child,
      this.dialogRadius = 12,
      this.insetPadding,
      this.contentPadding,
      this.type = TripcDialogType.warning,
      this.icon,
      this.titleButton,
      this.onTap,
      this.titleFontWeight,
      this.isDisplayIcon = true,
      this.titleTextCase = TextCaseType.none,
      this.titleFontSize});
  final Widget? child;
  final EdgeInsets? insetPadding;
  final double dialogRadius;
  final String title;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? contentPadding;
  final TripcDialogType type;
  final Widget? icon;
  final String? titleButton;
  final FontWeight? titleFontWeight;
  final bool isDisplayIcon;
  final TextCaseType titleTextCase;
  final double? titleFontSize;

  @override
  Widget build(BuildContext context) {
    return Dialog(
        insetPadding: insetPadding ??
            EdgeInsets.only(
                left: 16.W,
                right: 16.W,
                bottom: context.mediaQuery.size.height / 2 - 350.H),
        elevation: 0,
        backgroundColor: AppAssets.init.whiteBackgroundColor,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(dialogRadius.SP)),
        child: Padding(
          padding: contentPadding ?? EdgeInsets.zero,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              isDisplayIcon
                  ? icon ??
                      (type == TripcDialogType.warning
                              ? AppAssets.origin().icWarning
                              : AppAssets.origin().icSuccess)
                          .widget(height: 40.H, width: 40.H)
                  : const SizedBox.shrink(),
              TripcText(
                title,
                fontSize: titleFontSize ?? 16,
                fontWeight: titleFontWeight ?? FontWeight.w600,
                textColor: AppAssets.origin().black,
                padding: EdgeInsets.only(top: 9.H),
              ),
              child ??
                  SizedBox(
                    height: 20.H,
                  ),
              TripcButton(
                onPressed: () {
                  if (onTap != null) {
                    onTap!.call();
                  } else {
                    Navigator.of(context).pop();
                  }
                },
                title: titleButton ?? context.strings.text_confirm,
                textCase: titleTextCase,
                height: 56,
              )
            ],
          ),
        ));
  }
}
