import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_dialog/tripc_dialog.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcErrorDialog extends StatelessWidget {
  const TripcErrorDialog(
      {super.key,
      required this.errorText,
      this.title,
      this.contentPadding,
      this.textAlign,
      this.fontWeight,
      this.textColor,
      this.child,
      this.padding});
  final String? errorText;
  final String? title;
  final TextAlign? textAlign;
  final FontWeight? fontWeight;
  final Color? textColor;
  final EdgeInsets? padding;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return TripcDialog(
        onTap: () => Navigator.pop(context),
        title: title ?? context.strings.text_error,
        contentPadding: contentPadding ??
            EdgeInsets.symmetric(vertical: 12.H, horizontal: 24.W),
        child: child ??
            TripcText(
              errorText ?? context.strings.text_error_something_wrong,
              textAlign: textAlign ?? TextAlign.center,
              fontSize: 14,
              height: 1.2,
              fontWeight: fontWeight ?? FontWeight.w400,
              textColor: textColor ?? AppAssets.origin().redColor,
              padding: padding ?? EdgeInsets.symmetric(vertical: 30.H),
            ));
  }
}
