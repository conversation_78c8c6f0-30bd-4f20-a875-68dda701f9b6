import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/pages/profile/components/tripc_not_yet_login_dialog.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/constants/widget_const.dart';

enum ButtonType { normal, outline }

class AppButtonStyle {
  const AppButtonStyle(
      {this.backgroundColor,
      this.borderColor,
      this.textColor,
      this.leadingIcon,
      this.textStyle,
      this.fontWeight,
      this.radius,
      this.gradient,
      this.marginBottom = WidgetConst.zero,
      this.borderWidth = WidgetConst.defaultBorderWidth,
      this.fontSize,
      this.textCase});

  final Color? backgroundColor;
  final Color? borderColor;
  final Color? textColor;
  final TextStyle? textStyle;
  final FontWeight? fontWeight;
  final Widget? leadingIcon;
  final double? radius;
  final LinearGradient? gradient;
  final double marginBottom;
  final double borderWidth;
  final double? fontSize;
  final TextCaseType? textCase;
}

final class TripcButton extends StatelessWidget {
  const TripcButton(
      {super.key,
      this.onPressed,
      this.style = const AppButtonStyle(),
      this.titleBuilder,
      this.title = '',
      this.prefixIcon,
      this.isLoading,
      this.loading,
      this.isButtonDisabled = false,
      this.buttonType = ButtonType.normal,
      this.margin = EdgeInsets.zero,
      this.height,
      this.width,
      this.border,
      this.note,
      this.showSuggestLoginDialog = false,
      this.textCase = TextCaseType.title,
      this.isLogin,
      this.titleAlignment = MainAxisAlignment.center,
      this.titlePadding = EdgeInsets.zero,
      this.spacing = 22,
      this.resultHandler});

  final AppButtonStyle style;
  final VoidCallback? onPressed;
  final String title;
  final Widget Function(String title)? titleBuilder;
  final Widget? prefixIcon;
  final bool? isLoading;
  final Widget? loading;
  final bool isButtonDisabled;
  final double spacing;
  final ButtonType buttonType;
  final EdgeInsetsGeometry margin;
  final double? height;
  final double? width;
  final BoxBorder? border;
  final Widget? note;
  final bool showSuggestLoginDialog;
  final TextCaseType textCase;
  final bool? isLogin;
  final MainAxisAlignment titleAlignment;
  final EdgeInsetsGeometry titlePadding;
  final VoidCallback? resultHandler;

  bool get isOutLineButton => buttonType == ButtonType.outline;

  Widget _buildTitle(BuildContext context, Widget? prefixIcon) {
    return Column(
      children: [
        Container(
          height: height?.H ?? WidgetConst.defaultHeightButton.H,
          width: width?.W,
          margin: margin,
          decoration: BoxDecoration(
            color: isButtonDisabled
                ? AppAssets.origin().grayColor
                : isOutLineButton
                    ? style.backgroundColor ?? Colors.transparent
                    : style.backgroundColor ??
                        AppAssets.origin().secondaryColor,
            gradient: style.gradient,
            borderRadius: BorderRadius.circular(
              style.radius ?? WidgetConst.defaultCornerRadiusButton,
            ),
            border: border ??
                Border.all(
                    width: isOutLineButton
                        ? style.borderWidth.H
                        : WidgetConst.zero,
                    color: style.borderColor ??
                        (isOutLineButton
                            ? AppAssets.origin().buttonStrokeColor
                            : Colors.transparent)),
          ),
          child: Visibility(
            visible: isLoading ?? false,
            replacement: titleBuilder?.call(title) ??
                Padding(
                    padding: titlePadding,
                    child: Row(
                      mainAxisAlignment: titleAlignment,
                      children: [
                        prefixIcon ?? const SizedBox.shrink(),
                        SizedBox(
                          width: prefixIcon == null ? 0 : spacing.W,
                        ),
                        Flexible(
                            child: Text(
                          title.getTextByCasing(style.textCase ?? textCase),
                          textAlign: TextAlign.center,
                          style: style.textStyle ??
                              AppAssets.init.mediumTextStyle.copyWith(
                                fontSize: style.fontSize?.SP ??
                                    WidgetConst.mediumFontSize.SP,
                                fontWeight: style.fontWeight ?? FontWeight.w500,
                                color: style.textColor ??
                                    (isOutLineButton
                                        ? AppAssets.origin().blackColor
                                        : AppAssets.origin().whiteSmokeColor),
                              ),
                        )),
                      ],
                    )),
            child: Center(child: loading),
          ),
        ),
        Visibility(
            visible: note.isNotNull, child: note ?? const SizedBox.shrink())
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      minSize: 0,
      padding: EdgeInsets.zero,
      onPressed: isButtonDisabled
          ? null
          : () {
              HapticFeedback.selectionClick();
              if (showSuggestLoginDialog && isLogin != null && !isLogin!) {
                dialogHelpers.show(context, child: NotYetLoginDialog(resultHandler: resultHandler));
                return;
              }
              onPressed?.call();
            },
      child: _buildTitle(context, prefixIcon),
    );
  }
}
