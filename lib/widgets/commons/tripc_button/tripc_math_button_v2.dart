import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_math_button.dart';

class TripcMathButtonV2 extends StatelessWidget {
  const TripcMathButtonV2(
      {super.key,
      required this.symbol,
      required this.onPressed,
      this.activeColor,
      this.inActiveColor,
      this.isActive = true});
  final MathSymbol symbol;
  final bool isActive;
  final VoidCallback onPressed;
  final Color? activeColor;
  final Color? inActiveColor;

  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
        onPressed: isActive ? onPressed : null,
        child: isActive ? _buildActiveIcon : _buildInActiveIcon);
  }

  Widget get _buildActiveIcon {
    final size = 20.W;
    switch (symbol) {
      case MathSymbol.add:
        return AppAssets.origin()
            .icAdd
            .widget(height: size, width: size, color: activeColor);
      case MathSymbol.minus:
        return AppAssets.origin()
            .icMinus
            .widget(height: size, width: size, color: activeColor);
    }
  }

  Widget get _buildInActiveIcon {
    final size = 20.W;
    switch (symbol) {
      case MathSymbol.add:
        return AppAssets.origin().icAdd.widget(
            height: size,
            width: size,
            color: inActiveColor ?? AppAssets.origin().greyTextColorC8);
      case MathSymbol.minus:
        return AppAssets.origin().icMinus.widget(
            height: size,
            width: size,
            color: inActiveColor ?? AppAssets.origin().greyTextColorC8);
    }
  }
}
