import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';

final class TripcOptionButton extends StatelessWidget {
  const TripcOptionButton(
      {super.key,
      required this.value,
      this.child,
      this.onPressed,
      this.height = 40,
      required this.isActive,
      this.isDisable = false});

  final Widget? child;
  final VoidCallback? onPressed;
  final double? height;
  final bool isActive;
  final String value;
  final bool isDisable;

  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
        onPressed: onPressed,
        child: Container(
            height: height?.H,
            padding: EdgeInsets.symmetric(horizontal: 13.W),
            decoration: BoxDecoration(
                color: isActive
                    ? AppAssets.origin().primaryColor
                    : AppAssets.origin().whiteSmokeColor,
                border: Border.all(
                    width: 0.5.H,
                    color: isActive
                        ? AppAssets.origin().secondaryColor
                        : Colors.transparent),
                borderRadius: BorderRadius.circular(4.SP)),
            child: Center(
              child: Text(
                value,
                style: AppAssets.origin().mediumTextStyle.copyWith(
                      fontSize: 12.SP,
                      fontWeight: FontWeight.w400,
                      color: isDisable
                          ? AppAssets.origin().greyTextColorC8
                          : isActive
                              ? AppAssets.origin().darkBlueColor
                              : AppAssets.origin().black,
                    ),
              ),
            )));
  }
}
