import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';

final class TripcOptionButtonV2 extends StatelessWidget {
  const TripcOptionButtonV2(
      {super.key,
      required this.value,
      this.child,
      this.onPressed,
      this.height = 36,
      required this.isActive,
      this.isDisable = false,
      });

  final Widget? child;
  final VoidCallback? onPressed;
  final double? height;
  final bool isActive;
  final String value;
  final bool isDisable;

  @override
  Widget build(BuildContext context) {
    return TripcIconButton(
        onPressed: onPressed,
        child: Container(
            height: height?.H,
            padding: EdgeInsets.all(8.W,),
            decoration: BoxDecoration(
                color: isActive
                    ? AppAssets.origin().primaryColorV2
                    : AppAssets.origin().whiteSmokeColor,
                borderRadius: BorderRadius.circular(8.SP)),
            child: Text(
              value,
              style: AppAssets.origin().mediumTextStyle.copyWith(
                    fontSize: 14.SP,
                    fontWeight: isActive ? FontWeight.w700 : FontWeight.w400,
                    color: isDisable
                        ? AppAssets.origin().greyTextColorC8
                        : isActive
                            ? AppAssets.origin().whiteBackgroundColor
                            : AppAssets.origin().black,
                  ),
            )));
  }
}
