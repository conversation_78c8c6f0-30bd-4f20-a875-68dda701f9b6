import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tripc_app/pages/profile/components/tripc_not_yet_login_dialog.dart';
import 'package:tripc_app/services/providers/providers.dart';

final class TripcIconButton extends StatelessWidget {
  const TripcIconButton({
    super.key,
    this.child,
    this.onPressed,
    this.showSuggestLoginDialog = false,
    this.isLogin,
    this.isEnableTapIgnoreUnLogin = false,
    this.resultHandler,
  });

  final Widget? child;
  final VoidCallback? onPressed;
  final bool showSuggestLoginDialog;
  final bool? isLogin;
  final bool isEnableTapIgnoreUnLogin;
  final VoidCallback? resultHandler;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          if (showSuggestLoginDialog &&
              isLogin != null &&
              !isLogin! &&
              !isEnableTapIgnoreUnLogin) {
            dialogHelpers.show(context,
                child: NotYetLoginDialog(resultHandler: resultHandler));
            return;
          }
          onPressed?.call();
        },
        child: child);
  }
}
