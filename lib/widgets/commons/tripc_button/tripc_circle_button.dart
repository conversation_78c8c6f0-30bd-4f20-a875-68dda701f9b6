import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

final class TripcCircleButton extends StatelessWidget {
  const TripcCircleButton(
      {super.key,
      this.icon,
      this.onPressed,
      this.size = 60,
      this.color,
      this.padding,
      this.margin});

  final Widget? icon;
  final VoidCallback? onPressed;
  final double size;
  final Color? color;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onPressed,
        child: Container(
          width: size.H,
          height: size.H,
          padding: padding,
          margin: margin,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color ?? AppAssets.origin().secondaryColor,
          ),
          child: Center(child: icon),
        ));
  }
}
