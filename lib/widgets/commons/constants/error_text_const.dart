import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

abstract interface class ErrorMessage {
  static String emailInvalid(BuildContext context) =>
      context.strings.text_email_invalid;
  static String passInvalid(BuildContext context) =>
      context.strings.text_password_invalid;
  static String plsTypePhoneNumber(BuildContext context) =>
      context.strings.text_pls_type_phone_number;
  static String typeValidPhoneNumber(BuildContext context) =>
      context.strings.text_pls_type_valid_phone_number;
  static String plsTypeValidPhoneNumber10(BuildContext context) =>
    context.strings.text_error_phone_10;
  static String sameOldNumber(BuildContext context) =>
      context.strings.text_not_same_old_phone_number;
  static String wrongOtp(BuildContext context) =>
      context.strings.text_wrong_otp;
  static String sameOldEmail(BuildContext context) =>
      context.strings.text_same_old_email;
  static String plsTypeFullName(BuildContext context) =>
      context.strings.text_pls_enter_full_name;
  static String fullNameInvalid(BuildContext context) =>
      context.strings.text_full_name_invalid;
  static String plsTypeInvalidEmail(BuildContext context) =>
      context.strings.text_pls_enter_valid_email;
  static String plsTypeVerifiedEmail(BuildContext context) =>
      context.strings.text_pls_enter_verified_email;
  static String emailAlreadyInUse(BuildContext context) =>
      context.strings.text_email_already_in_use;
  static String passwordValidation(BuildContext context) =>
      context.strings.text_password_validation;
  static String fullNameOver50(BuildContext context) =>
      context.strings.text_full_name_over_50;
  static String fullNameWithoutNumber(BuildContext context) =>
      context.strings.text_full_name_without_num;
  static String fullNameWithoutSpecialSign(BuildContext context) =>
      context.strings.text_full_name_without_special_sign;
  static String fullNameWithoutSpace(BuildContext context) =>
      context.strings.text_full_name_without_space;
  static String passwordMustBe8Characters(BuildContext context) =>
      context.strings.text_password_must_be_at_last_8_characters;
  static String passwordMustBe1Upper(BuildContext context) =>
      context.strings.text_password_uppercase_valid;
  static String passwordMustBe1Lower(BuildContext context) =>
      context.strings.text_password_lowercase_valid;
  static String passwordMustBe1Number(BuildContext context) =>
      context.strings.text_password_one_number_valid;
  static String passwordMustBeWithoutSpace(BuildContext context) =>
      context.strings.text_password_without_space;
  static String passwordMustBe1Special(BuildContext context) =>
      context.strings.text_password_must_be_1_special;
  static String wrongTripcIdAndPasscode(BuildContext context) =>
      context.strings.text_wrong_tripcId_and_pass_code;
}
