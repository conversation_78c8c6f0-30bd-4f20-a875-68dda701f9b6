import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class AppEmpty extends StatelessWidget {
  const AppEmpty({
    super.key,
    this.message = '',
    this.onRefresh,
  });

  final String message;
  final VoidCallback? onRefresh;

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      child: Center(
        child: Column(
          children: [
            SizedBox(height: 90.H),
            AppAssets.init.imEmpty.widget(fit: BoxFit.cover),
            SizedBox(
              height: 8.H,
            ),
            TripcText(
              message,
              fontWeight: FontWeight.w400,
              fontSize: 16,
              textColor: AppAssets.origin().black,
              height: 1.2,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
      onRefresh: () async {
        onRefresh?.call();
      },
    );
  }
}
