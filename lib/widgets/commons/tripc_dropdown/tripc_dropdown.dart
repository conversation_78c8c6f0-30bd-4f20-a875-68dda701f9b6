import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcDropdownButton<T> extends StatefulWidget {
  const TripcDropdownButton(
      {super.key,
      this.hintText,
      this.onChanged,
      this.currentValue,
      required this.items,
      this.selectedItemBuilder,
      this.height = 35,
      this.searchHeight = 25,
      this.width = 104});
  final List<DropdownMenuItem<T>> items;
  final String? hintText;
  final Function(T?)? onChanged;
  final T? currentValue;
  final List<Widget> Function(BuildContext)? selectedItemBuilder;
  final double height;
  final double width;
  final double searchHeight;

  @override
  State<TripcDropdownButton<T>> createState() => _TripcDropdownButtonState<T>();
}

class _TripcDropdownButtonState<T> extends State<TripcDropdownButton<T>> {
  bool _isOpen = false;
  T? currentValue;
  final TextEditingController _searchController = TextEditingController();
  @override
  void initState() {
    currentValue = widget.currentValue;
    super.initState();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2<T>(
        value: currentValue,
        isExpanded: true,
        hint: TripcText(
          widget.hintText ?? 'Please select one',
          fontSize: 11,
          textColor: AppAssets.origin().darkGreyTextColor,
        ),
        onMenuStateChange: (isOpen) {
          setState(() {
            _isOpen = isOpen;
          });
          if (!isOpen) {
            _searchController.clear();
          }
        },
        items: widget.items,
        selectedItemBuilder: widget.selectedItemBuilder,
        onChanged: widget.onChanged ??
            (value) {
              setState(() {
                currentValue = value;
              });
            },
        buttonStyleData: ButtonStyleData(
            height: widget.height.H,
            width: widget.width.W,
            padding: EdgeInsets.symmetric(horizontal: 8.W),
            decoration: BoxDecoration(
                border: Border.all(
                  color: AppAssets.init.blackStroke,
                  width: 1.H,
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(6.SP),
                  topRight: Radius.circular(6.SP),
                  bottomLeft: Radius.circular(_isOpen ? 0 : 6.SP),
                  bottomRight: Radius.circular(_isOpen ? 0 : 6.SP),
                )),
            overlayColor: const WidgetStatePropertyAll(Colors.transparent)),
        iconStyleData: IconStyleData(
          icon: AppAssets.origin().icArrowDown.widget(width: 9.W),
          openMenuIcon: Transform.flip(
              flipY: true,
              child: AppAssets.origin().icArrowDown.widget(width: 9.W)),
        ),
        dropdownSearchData: DropdownSearchData(
          searchInnerWidgetHeight: widget.searchHeight.H,
          searchController: _searchController,
          searchInnerWidget: Container(
            height: widget.searchHeight.H,
            padding: EdgeInsets.symmetric(
              vertical: 2.H,
              horizontal: 4.W,
            ),
            child: TextFormField(
              controller: _searchController,
              expands: true,
              maxLines: null,
              cursorColor: AppAssets.init.secondaryColor,
              cursorWidth: 1.H,
              cursorHeight: 11.H,
              style: AppAssets.init.normalTextStyle.copyWith(
                  color: AppAssets.init.mainBlackColor,
                  fontSize: 8.SP,
                  height: 0),
              decoration: InputDecoration(
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 6.W,
                  vertical: 4.H,
                ),
                hintText: context.strings.text_search,
                hintStyle: AppAssets.init.mediumTextStyle.copyWith(
                  color: AppAssets.init.darkGreyTextColor,
                  fontSize: 8.SP,
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    width: 1.H,
                    color: AppAssets.init.secondaryColor,
                  ),
                  borderRadius: BorderRadius.circular(6.SP),
                ),
                border: OutlineInputBorder(
                  borderSide: BorderSide(
                    width: 0.5.H,
                    color: AppAssets.init.grayTextColor,
                  ),
                  borderRadius: BorderRadius.circular(6.SP),
                ),
              ),
            ),
          ),
          searchMatchFn: (item, searchValue) {
            if (item.value is String) {
              final data = item.value as String;
              return data.toLowerCase().contains(searchValue.toLowerCase());
            }
            return false;
          },
        ),
        dropdownStyleData: DropdownStyleData(
          maxHeight: 250.H,
          elevation: 2,
          padding: EdgeInsets.symmetric(horizontal: 10.W),
          decoration: BoxDecoration(
              border: Border.all(
                width: 1.H,
                color: AppAssets.init.blackStroke,
              ),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(6.SP),
                bottomRight: Radius.circular(6.SP),
              ),
              color: AppAssets.origin().whiteBackgroundColor,
              boxShadow: null),
        ),
        menuItemStyleData: MenuItemStyleData(
          height: widget.height.H,
          padding: EdgeInsets.zero,
        ),
      ),
    );
  }
}
