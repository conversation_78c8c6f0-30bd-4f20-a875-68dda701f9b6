import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/container_shimmer_loading.dart';

class TourShimmerLoading extends StatelessWidget {
  const TourShimmerLoading({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColor =
        AppAssets.origin().secondDarkGreyTextColor.withValues(alpha: 0.5);
    final color = AppAssets.origin().grayEFColor;
    return Stack(
      children: [
        Container(
            width: 184.W,
            decoration: BoxDecoration(
              color: AppAssets.origin().greyTextColorC8,
              borderRadius: BorderRadius.circular(12.SP),
              boxShadow: [
                AppAssets.origin()
                    .itemShadow
                    .copyWith(color: AppAssets.origin().black10)
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    ContainerLoading(
                        enableShimmer: false,
                        height: 117.H,
                        highlightColor: color,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12.SP),
                          topRight: Radius.circular(12.SP),
                        ),
                        color: AppAssets.origin()
                            .secondDarkGreyTextColor
                            .withValues(alpha: 0.3)),
                    Positioned(
                      bottom: 9.H,
                      left: 10.W,
                      child: Row(
                        children: [
                          ContainerLoading(
                              enableShimmer: true,
                              height: 16.H,
                              width: 16.H,
                              margin: EdgeInsets.only(right: 8.W),
                              borderRadius: BorderRadius.circular(16.SP),
                              highlightColor: color,
                              color: backgroundColor),
                          ContainerLoading(
                              enableShimmer: true,
                              height: 14.H,
                              width: 75.W,
                              highlightColor: color,
                              color: backgroundColor),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 8.H,
                      right: 8.W,
                      child: ContainerLoading(
                          enableShimmer: true,
                          height: 16.H,
                          width: 16.H,
                          borderRadius: BorderRadius.circular(16.SP),
                          highlightColor: color,
                          color: backgroundColor),
                    ),
                  ],
                ),
                Expanded(
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(8.H),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ContainerLoading(
                                  enableShimmer: true,
                                  height: 14.H,
                                  width: 130.W,
                                  margin:
                                      EdgeInsets.only(right: 8.W, bottom: 4.H),
                                  highlightColor: color,
                                  color: backgroundColor),
                              ContainerLoading(
                                  enableShimmer: true,
                                  height: 14.H,
                                  width: 45.W,
                                  highlightColor: color,
                                  color: backgroundColor),
                            ]),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                ContainerLoading(
                                    enableShimmer: true,
                                    height: 14.H,
                                    width: 45.W,
                                    highlightColor: color,
                                    color: backgroundColor),
                                ContainerLoading(
                                    enableShimmer: true,
                                    height: 14.H,
                                    width: 70.W,
                                    margin: EdgeInsets.symmetric(vertical: 4.H),
                                    highlightColor: color,
                                    color: backgroundColor),
                                ContainerLoading(
                                    enableShimmer: true,
                                    height: 14.H,
                                    width: 140.W,
                                    highlightColor: color,
                                    color: backgroundColor),
                              ]),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )),
        Positioned.fill(
          child: AppAssets.origin().lottieLoadingSweep.widget(
                repeat: true,
              ),
        )
      ],
    );
  }
}

class ListTourShimmerLoading extends StatelessWidget {
  const ListTourShimmerLoading({super.key, this.length = 4, this.padding});
  final int length;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        itemCount: length,
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        physics: const NeverScrollableScrollPhysics(),
        padding: padding ?? EdgeInsets.symmetric(horizontal: 16.W),
        separatorBuilder: (context, index) => SizedBox(width: 16.W),
        itemBuilder: (context, index) => const TourShimmerLoading());
  }
}

class ListTourGridShimmerLoading extends StatelessWidget {
  const ListTourGridShimmerLoading({super.key, this.length = 10});
  final int length;

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
        itemCount: length,
        padding: EdgeInsets.only(
            top: 28.H, bottom: context.spacingBottom, left: 16.W, right: 16.W),
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            childAspectRatio: 185.W / 263.H,
            crossAxisCount: 2,
            mainAxisSpacing: 16.H,
            crossAxisSpacing: 16.W),
        itemBuilder: (context, index) => const TourShimmerLoading());
  }
}

class CardShimmerLoading extends StatelessWidget {
  const CardShimmerLoading({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColor =
        AppAssets.origin().secondDarkGreyTextColor.withValues(alpha: 0.5);
    final color = AppAssets.origin().grayEFColor;
    return Stack(
      children: [
        Container(
            height: 120.H,
            decoration: BoxDecoration(
              color: AppAssets.origin().greyTextColorC8,
              borderRadius: BorderRadius.circular(12.SP),
              boxShadow: [
                AppAssets.origin()
                    .itemShadow
                    .copyWith(color: AppAssets.origin().black10)
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                ContainerLoading(
                    enableShimmer: true,
                    height: 120.H,
                    width: 130.W,
                    highlightColor: color,
                    borderRadius: BorderRadius.circular(8.SP),
                    color: backgroundColor),
                Expanded(
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(8.H),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ContainerLoading(
                                  enableShimmer: true,
                                  height: 20.H,
                                  width: 130.W,
                                  margin:
                                      EdgeInsets.only(right: 8.W, bottom: 4.H),
                                  highlightColor: color,
                                  color: backgroundColor),
                              ContainerLoading(
                                  enableShimmer: true,
                                  height: 14.H,
                                  width: 150.W,
                                  highlightColor: color,
                                  margin: EdgeInsets.symmetric(vertical: 5.H),
                                  color: backgroundColor),
                              ContainerLoading(
                                  enableShimmer: true,
                                  height: 14.H,
                                  width: 180.W,
                                  margin: EdgeInsets.symmetric(vertical: 4.H),
                                  highlightColor: color,
                                  color: backgroundColor),
                            ]),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                ContainerLoading(
                                    enableShimmer: true,
                                    height: 20.H,
                                    width: 140.W,
                                    highlightColor: color,
                                    color: backgroundColor),
                              ]),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )),
        Positioned.fill(
          child: AppAssets.origin().lottieLoadingSweep.widget(
                repeat: true,
              ),
        )
      ],
    );
  }
}

class ListCardTourShimmerLoading extends StatelessWidget {
  const ListCardTourShimmerLoading({super.key, this.length = 6, this.padding});
  final int length;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ContainerLoading(
            enableShimmer: true,
            margin: EdgeInsets.symmetric(vertical: 5.H),
            height: 20.H,
            width: 140.W,
            highlightColor: AppAssets.origin().grayEFColor,
            color: AppAssets.origin()
                .secondDarkGreyTextColor
                .withValues(alpha: 0.5)),
        ListView.separated(
            itemCount: length,
            shrinkWrap: true,
            scrollDirection: Axis.vertical,
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (context, index) => SizedBox(height: 10.H),
            itemBuilder: (context, index) => const CardShimmerLoading()),
      ],
    );
  }
}

class CardContactShimmerLoading extends StatelessWidget {
  const CardContactShimmerLoading({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColor =
        AppAssets.origin().secondDarkGreyTextColor.withValues(alpha: 0.5);
    final color = AppAssets.origin().grayEFColor;
    return Stack(
      children: [
        Container(
            height: 160.H,
            padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 16.W),
            decoration: BoxDecoration(
              color: AppAssets.origin().greyTextColorC8,
              borderRadius: BorderRadius.circular(12.SP),
              boxShadow: [
                AppAssets.origin()
                    .itemShadow
                    .copyWith(color: AppAssets.origin().black10)
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.bottomLeft,
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        ContainerLoading(
                            enableShimmer: true,
                            height: 30.H,
                            width: 170.W,
                            highlightColor: color,
                            color: backgroundColor),
                      ]),
                ),
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  ContainerLoading(
                      enableShimmer: true,
                      height: 20.H,
                      width: 250.W,
                      margin: EdgeInsets.symmetric(vertical: 8.H),
                      highlightColor: color,
                      color: backgroundColor),
                  ContainerLoading(
                      enableShimmer: true,
                      height: 20.H,
                      width: 200.W,
                      highlightColor: color,
                      margin: EdgeInsets.symmetric(vertical: 8.H),
                      color: backgroundColor),
                ]),
              ],
            )),
        Positioned.fill(
          child: AppAssets.origin().lottieLoadingSweep.widget(
                repeat: true,
              ),
        )
      ],
    );
  }
}

class ListCardContactShimmerLoading extends StatelessWidget {
  const ListCardContactShimmerLoading(
      {super.key, this.length = 4, this.padding});
  final int length;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        itemCount: length,
        shrinkWrap: true,
        scrollDirection: Axis.vertical,
        physics: const NeverScrollableScrollPhysics(),
        separatorBuilder: (context, index) => SizedBox(height: 8.H),
        itemBuilder: (context, index) => const CardContactShimmerLoading());
  }
}
