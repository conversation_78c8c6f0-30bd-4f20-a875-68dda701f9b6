import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/widgets/commons/base_shimmer_loading.dart';

class ContainerLoading extends StatelessWidget {
  const ContainerLoading(
      {super.key,
      this.width,
      this.height,
      this.child,
      this.margin,
      this.color,
      this.highlightColor,
      this.enableShimmer = false,
      this.borderRadius,
      this.decoration});

  final double? width;
  final double? height;
  final Widget? child;
  final EdgeInsets? margin;
  final Color? color;
  final Color? highlightColor;
  final bool enableShimmer;
  final BorderRadius? borderRadius;
  final BoxDecoration? decoration;

  @override
  Widget build(BuildContext context) {
    return enableShimmer
        ? BaseShimmerLoading(
            baseColor: color,
            highlightColor: highlightColor,
            child: BaseContainer(
              width: width,
              height: height,
              margin: margin,
              borderRadius: borderRadius,
              decoration: decoration,
              color: color,
              child: child,
            ),
          )
        : BaseContainer(
            width: width,
            height: height,
            margin: margin,
            borderRadius: borderRadius,
            decoration: decoration,
            color: color,
            child: child,
          );
  }
}

class BaseContainer extends StatelessWidget {
  const BaseContainer(
      {super.key,
      required this.width,
      required this.height,
      required this.margin,
      required this.child,
      this.borderRadius,
      this.decoration,
      this.color});

  final double? width;
  final double? height;
  final EdgeInsets? margin;
  final Widget? child;
  final BorderRadius? borderRadius;
  final BoxDecoration? decoration;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: decoration ??
          BoxDecoration(
            color: color ?? AppAssets.origin().whiteBackgroundColor,
            borderRadius: borderRadius ?? BorderRadius.circular(30),
          ),
      child: child,
    );
  }
}
