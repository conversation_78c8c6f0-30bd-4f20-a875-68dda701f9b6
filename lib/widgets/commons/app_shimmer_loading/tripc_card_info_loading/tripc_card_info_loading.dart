import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/container_shimmer_loading.dart';

class TripcCardInfoLoading extends StatelessWidget {
  const TripcCardInfoLoading({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundCard = AppAssets.origin().secondDarkGreyTextColor;
    final backgroundColor = AppAssets.origin().barrier80;
    final color = AppAssets.origin().grayEFColor;
    final radius = 12.SP;
    return Stack(
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 12.W)
              .copyWith(bottom: 20.H),
          decoration: BoxDecoration(
            color: backgroundCard,
            borderRadius: BorderRadius.circular(12.SP),
            boxShadow: [AppAssets.origin().itemShadow],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ContainerLoading(
                    enableShimmer: true,
                    width: 86.W,
                    height: 35.H,
                    highlightColor: color,
                    borderRadius: BorderRadius.circular(radius),
                    color: backgroundColor,
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 12.H),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        ContainerLoading(
                          enableShimmer: true,
                          width: 50.W,
                          height: 18.H,
                          margin: EdgeInsets.only(right: 16.W),
                          highlightColor: color,
                          borderRadius: BorderRadius.circular(radius),
                          color: backgroundColor,
                        ),
                        ContainerLoading(
                          enableShimmer: true,
                          width: 160.W,
                          height: 18.H,
                          highlightColor: color,
                          borderRadius: BorderRadius.circular(radius),
                          color: backgroundColor,
                        ),
                      ],
                    ),
                  ),
                  ContainerLoading(
                    enableShimmer: true,
                    width: 160.W,
                    height: 18.H,
                    highlightColor: color,
                    borderRadius: BorderRadius.circular(radius),
                    color: backgroundColor,
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 12.H, bottom: 24.H),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        ContainerLoading(
                          enableShimmer: true,
                          width: 50.W,
                          height: 18.H,
                          margin: EdgeInsets.only(right: 16.W),
                          highlightColor: color,
                          borderRadius: BorderRadius.circular(radius),
                          color: backgroundColor,
                        ),
                        ContainerLoading(
                          enableShimmer: true,
                          width: 160.W,
                          height: 18.H,
                          highlightColor: color,
                          borderRadius: BorderRadius.circular(radius),
                          color: backgroundColor,
                        ),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      ContainerLoading(
                        enableShimmer: true,
                        width: 75.W,
                        height: 18.H,
                        margin: EdgeInsets.only(right: 16.W),
                        highlightColor: color,
                        borderRadius: BorderRadius.circular(radius),
                        color: backgroundColor,
                      ),
                      ContainerLoading(
                        enableShimmer: true,
                        width: 50.W,
                        height: 18.H,
                        highlightColor: color,
                        borderRadius: BorderRadius.circular(radius),
                        color: backgroundColor,
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
        Positioned(
          right: 24.H,
          top: 8.H,
          child: ClipOval(
              child: ContainerLoading(
            enableShimmer: true,
            width: 60.W,
            height: 90.H,
            highlightColor: color,
            borderRadius: BorderRadius.circular(45.SP),
            color: backgroundColor,
          )),
        ),
        Positioned(
          right: 12.H,
          bottom: 12.H,
          child: ContainerLoading(
            enableShimmer: true,
            width: 45.H,
            height: 45.H,
            highlightColor: color,
            borderRadius: BorderRadius.circular(45.SP),
            color: backgroundColor,
          ),
        ),
        Positioned.fill(
          child: AppAssets.origin().lottieLoadingSweep.widget(
                repeat: true,
              ),
        )
      ],
    );
  }
}

class ListTripcCardInfoLoading extends StatelessWidget {
  const ListTripcCardInfoLoading({super.key, this.length = 6, this.padding});
  final int length;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        itemCount: length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: padding ?? EdgeInsets.zero,
        separatorBuilder: (context, index) => SizedBox(
              height: 8.H,
            ),
        itemBuilder: (context, index) => const TripcCardInfoLoading());
  }
}
