import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/app_shimmer_loading/container_shimmer_loading.dart';

class NumberOptionShimmerLoading extends StatelessWidget {
  const NumberOptionShimmerLoading({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColor = AppAssets.origin().barrier80;
    final color = AppAssets.origin().grayEFColor;
    final radius = 12.SP;
    return Stack(
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 8.H, horizontal: 12.W),
          decoration: BoxDecoration(
            color: AppAssets.origin().whiteBackgroundColor,
            borderRadius: BorderRadius.circular(12.SP),
            boxShadow: [AppAssets.origin().itemShadow],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ContainerLoading(
                enableShimmer: true,
                width: 100.W,
                height: 16.H,
                highlightColor: color,
                borderRadius: BorderRadius.circular(radius),
                color: backgroundColor,
              ),
              SizedBox(
                height: 8.H,
              ),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                ContainerLoading(
                  enableShimmer: true,
                  height: 16.H,
                  width: 85.W,
                  highlightColor: color,
                  borderRadius: BorderRadius.circular(radius),
                  color: backgroundColor,
                ),
                ContainerLoading(
                  enableShimmer: true,
                  margin: EdgeInsets.only(right: 8.W),
                  width: 100.W,
                  height: 17.H,
                  highlightColor: color,
                  borderRadius: BorderRadius.circular(4.SP),
                  color: backgroundColor,
                ),
              ]),
            ],
          ),
        ),
        Positioned.fill(
          child: AppAssets.origin().lottieLoadingSweep.widget(
                repeat: true,
              ),
        )
      ],
    );
  }
}

class ListNumberOptionShimmerLoading extends StatelessWidget {
  const ListNumberOptionShimmerLoading(
      {super.key, this.length = 4, this.padding});
  final int length;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        itemCount: length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: padding ?? EdgeInsets.zero,
        separatorBuilder: (context, index) => SizedBox(
              height: 8.H,
            ),
        itemBuilder: (context, index) => const NumberOptionShimmerLoading());
  }
}
