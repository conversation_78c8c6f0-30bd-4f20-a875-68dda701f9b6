import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class LastPageWarning extends StatelessWidget {
  const LastPageWarning({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppAssets.origin().icCube.widget(height: 20.H, width: 20.H),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 8.H,
        ),
        TripcText(
          context.strings.text_at_last_page,
          fontSize: 14,
          fontWeight: FontWeight.w300,
          textColor: AppAssets.origin().black,
        )
      ],
    );
  }
}
