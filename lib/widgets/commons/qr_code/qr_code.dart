import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:tripc_app/utils/app_extension.dart';

class TripcQrCode extends StatelessWidget {
  const TripcQrCode(
      {super.key,
      this.size = 250,
      required this.data,
      this.padding,
      this.margin});
  final double size;
  final String data;
  final EdgeInsets? padding;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: QrImageView(
        padding: EdgeInsets.zero,
        data: data,
        version: QrVersions.auto,
        size: size.H,
        gapless: true,
      ),
    );
  }
}
