import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class BaseCachedNetworkImage extends StatelessWidget {
  const BaseCachedNetworkImage({
    super.key,
    required this.imageUrl,
    this.placeholder,
    this.errorWidget,
    this.height,
    this.width,
    this.memCacheHeight,
    this.memCacheWidth,
    this.fit = BoxFit.cover,
    this.filterQuality = FilterQuality.low,
    this.useOldImageOnUrlChange = false,
    this.cacheKey,
  });

  final String imageUrl;
  final Widget Function(BuildContext, String, Object)? errorWidget;
  final Widget Function(BuildContext, String)? placeholder;
  final double? height;
  final double? width;
  final double? memCacheHeight;
  final double? memCacheWidth;
  final BoxFit fit;
  final FilterQuality filterQuality;
  final String? cacheKey;
  final bool useOldImageOnUrlChange;

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      placeholder: placeholder,
      errorWidget: errorWidget,
      cacheKey: cacheKey,
      useOldImageOnUrlChange: useOldImageOnUrlChange,
      fit: fit,
      height: height,
      width: width,
      filterQuality: filterQuality,
      fadeOutDuration: const Duration(),
      fadeInDuration: const Duration(),
      placeholderFadeInDuration: const Duration(),
      memCacheHeight: memCacheHeight?.toInt(),
      memCacheWidth: memCacheWidth?.toInt(),
    );
  }
}
