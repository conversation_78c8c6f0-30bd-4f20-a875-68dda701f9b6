import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcBoxInput extends StatelessWidget {
  const TripcBoxInput(
      {super.key,
      required this.headerTitle,
      required this.child,
      this.margin,
      this.gradient,
      this.titleBuilder});
  final String headerTitle;
  final Widget child;
  final EdgeInsetsGeometry? margin;
  final Widget Function(String headerTitle)? titleBuilder;
  final Gradient? gradient;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? EdgeInsets.zero,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.SP),
        color: AppAssets.origin().whiteBackgroundColor,
        boxShadow: [
          AppAssets.origin()
              .itemShadow
              .copyWith(color: AppAssets.origin().black10)
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [_buildHeader(), _buildContent()],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(8.SP),
            topLeft: Radius.circular(8.SP),
          ),
          gradient: gradient ?? AppAssets.origin().inputBoxHeaderGradient),
      height: 36.H,
      width: double.infinity,
      child: titleBuilder?.call(headerTitle) ??
          Align(
            alignment: Alignment.centerLeft,
            child: TripcText(
              headerTitle,
              fontSize: 16,
              textAlign: TextAlign.start,
              fontWeight: FontWeight.w500,
              padding: EdgeInsets.symmetric(horizontal: 8.W),
              maxLines: 1,
            ),
          ),
    );
  }

  Widget _buildContent() {
    return Container(
        padding: EdgeInsets.symmetric(vertical: 12.H, horizontal: 15.W),
        child: child);
  }
}
