import 'package:flutter/material.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

abstract interface class BottomSheetHelper {
  void show(
    BuildContext context, {
    required Widget child,
    double borderRadius,
  });
}

final class BottomSheetHelperImpl implements BottomSheetHelper {
  @override
  void show(
    BuildContext context, {
    required Widget child,
    ShapeBorder? shape,
    double borderRadius = 30,
    Color? backgroundColor,
  }) {
    showMaterialModalBottomSheet<void>(
      context: context,
      shape: shape ??
          RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(borderRadius.SP),
              topRight: Radius.circular(borderRadius.SP),
            ),
          ),
      elevation: 0,
      backgroundColor:
          backgroundColor ?? AppAssets.origin().black.withA<PERSON><PERSON>(70),
      expand: false,
      useRootNavigator: false,
      enableDrag: true,
      clipBehavior: Clip.hardEdge,
      builder: (BuildContext context) {
        return child;
      },
    ).whenComplete(() => unfocusKeyboard());
  }
}
