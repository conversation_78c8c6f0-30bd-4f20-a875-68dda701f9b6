import 'package:flutter/material.dart';
import 'package:flutter_debouncer/flutter_debouncer.dart';

abstract interface class DebounceHelper {
  void action({
    Duration? duration,
    VoidCallback? callBack,
  });
}

final class DebounceHelperImpl implements DebounceHelper {
  final Debouncer _debouncer = Debouncer();
  @override
  void action({
    Duration? duration,
    VoidCallback? callBack,
  }) {
    _debouncer.debounce(
        duration: duration ?? const Duration(milliseconds: 500),
        type: BehaviorType.trailingEdge,
        onDebounce: () async {
          callBack?.call();
        });
  }
}
