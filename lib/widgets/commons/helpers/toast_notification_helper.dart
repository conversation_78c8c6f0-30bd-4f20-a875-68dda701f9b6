import 'package:flutter/material.dart';
import 'package:toastification/toastification.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

final class ToastificationHelperImpl {
  void show(BuildContext? context,
      {required Widget child,
      Alignment? alignment,
      Widget? description,
      Widget? title,
      ToastificationType? toastificationType}) {
    toastification.dismissAll();
    toastification.show(
        context: context,
        style: ToastificationStyle.fillColored,
        primaryColor: AppAssets.origin().black70,
        padding: EdgeInsets.zero,
        margin: EdgeInsets.symmetric(vertical: 106.H, horizontal: 74.W),
        autoCloseDuration: const Duration(seconds: 3),
        title: title,
        description: description,
        alignment: alignment ?? Alignment.topCenter,
        direction: TextDirection.ltr,
        animationDuration: const Duration(milliseconds: 300),
        animationBuilder: (context, animation, alignment, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        showProgressBar: false,
        borderRadius: BorderRadius.circular(8.SP),
        closeOnClick: true,
        pauseOnHover: true,
        dragToClose: true,
        applyBlurEffect: true,
        closeButton: const ToastCloseButton(showType: CloseButtonShowType.none),
        showIcon: false);
  }

  void showCustom(BuildContext? context,
      {required Widget child,
      Alignment? alignment,
      Widget? description,
      Widget? title,
      ToastificationType? toastificationType}) {
    toastification.dismissAll();
    toastification.showCustom(
      context: context,
      autoCloseDuration: const Duration(seconds: 3),
      alignment: alignment ?? Alignment.topCenter,
      direction: TextDirection.ltr,
      animationDuration: const Duration(milliseconds: 300),
      animationBuilder: (context, animation, alignment, child) =>
          FadeTransition(
        opacity: animation,
        child: Align(
            alignment: Alignment.center,
            child: IntrinsicWidth(child: IntrinsicHeight(child: child))),
      ),
      builder: (context, holder) => Align(
          alignment: Alignment.center,
          child: IntrinsicWidth(child: IntrinsicHeight(child: child))),
    );
  }
}
