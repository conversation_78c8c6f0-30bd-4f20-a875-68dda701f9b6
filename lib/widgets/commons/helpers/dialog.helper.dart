import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

abstract interface class DialogHelper {
  void show(BuildContext context,
      {required Widget child, Function(dynamic value)? callback});
}

final class DialogHelperImpl implements <PERSON>alogHelper {
  @override
  void show(BuildContext context,
      {required Widget child,
      bool barrierDismissible = true,
      Function(dynamic value)? callback}) {
    showDialog(
            context: context,
            barrierDismissible: barrierDismissible,
            builder: (context) => child,
            barrierColor: AppAssets.origin().black70)
        .whenComplete(() => unfocusKeyboard())
        .then((value) => callback?.call(value));
  }
}
