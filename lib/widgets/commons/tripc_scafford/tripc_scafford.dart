import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

class TripcScaffold extends StatelessWidget {
  const TripcScaffold(
      {super.key,
      required this.body,
      this.appBar,
      this.bottomNavigationBar,
      this.backgroundColor,
      this.padding = EdgeInsets.zero,
      this.useBackgroundImage = false,
      this.leading,
      this.visibleAppBar = true,
      this.titleAppBar,
      this.needUnFocus = false,
      this.onLeadingPressed,
      this.actions,
      this.bottom,
      this.onPressed,
      this.resizeToAvoidBottomInset,
      this.extendBodyBehindAppBar = false,
      this.canPop = true,
      this.onPopScope,
      this.floatingActionButton,
      this.useBackgroundColor = false,
      this.hasBackButton = false,
      this.toolbarHeight = 24,
      this.leadingWidth = 48,
      this.titleSpacing = 0,
      this.backgroundImage,
      this.centerTitle = true,
      this.leadingPadding,
      this.appBarColor,
      this.leadingColor,
      this.hasAppbarBottomLine = true});

  final Widget body;
  final PreferredSizeWidget? appBar;
  final Widget? bottomNavigationBar;
  final Color? backgroundColor;
  final EdgeInsets padding;
  final bool useBackgroundImage;
  final Widget? leading;
  final bool visibleAppBar;
  final Widget? titleAppBar;
  final bool needUnFocus;
  final VoidCallback? onLeadingPressed;
  final List<Widget>? actions;
  final PreferredSizeWidget? bottom;
  final VoidCallback? onPressed;
  final bool? resizeToAvoidBottomInset;
  final bool extendBodyBehindAppBar;
  final bool canPop;
  final VoidCallback? onPopScope;
  final Widget? floatingActionButton;
  final DecorationImage? backgroundImage;
  final bool useBackgroundColor;
  final bool hasBackButton;
  final double toolbarHeight;
  final double leadingWidth;
  final Color? appBarColor;
  final double titleSpacing;
  final bool centerTitle;
  final EdgeInsetsGeometry? leadingPadding;
  final Color? leadingColor;
  final bool hasAppbarBottomLine;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        onPopScope?.call();
        unfocusKeyboard();
      },
      canPop: canPop,
      child: GestureDetector(
        onTap: () {
          onPressed?.call();
          if (!needUnFocus) {
            return;
          }
          unfocusKeyboard();
        },
        child: Container(
          decoration: BoxDecoration(
            image: backgroundImage,
            color: useBackgroundImage
                ? null
                : backgroundColor ?? AppAssets.origin().secondaryColor,
          ),
          child: Scaffold(
            resizeToAvoidBottomInset: resizeToAvoidBottomInset,
            backgroundColor: Colors.transparent,
            bottomNavigationBar: bottomNavigationBar,
            extendBodyBehindAppBar: extendBodyBehindAppBar,
            floatingActionButton: floatingActionButton,
            floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
            appBar: visibleAppBar
                ? appBar ??
                    AppBar(
                        centerTitle: centerTitle,
                        toolbarHeight: toolbarHeight,
                        leadingWidth: leadingWidth.H,
                        leading: Container(
                          margin: EdgeInsets.only(left: (leadingWidth / 2).H),
                          child: Center(
                            child: GestureDetector(
                                onTap: onLeadingPressed ??
                                    () {
                                      if (hasBackButton) {
                                        Navigator.pop(context);
                                      }
                                    },
                                child: leading ??
                                    (hasBackButton
                                        ? Padding(
                                            padding: leadingPadding ??
                                                EdgeInsets.zero,
                                            child: AppAssets.init.iconArrowleft
                                                .widget(
                                              color: leadingColor ??
                                                  AppAssets.origin().black,
                                            ),
                                          )
                                        : null)),
                          ),
                        ),
                        backgroundColor: appBarColor.isNotNull
                            ? appBarColor
                            : Colors.transparent,
                        scrolledUnderElevation: 0,
                        title: titleAppBar,
                        actions: actions,
                        bottom: titleAppBar.isNotNull
                            ? PreferredSize(
                                preferredSize: Size.fromHeight(14.H),
                                child: Divider(
                                  height: 1,
                                  color: hasAppbarBottomLine
                                      ? AppAssets.origin().lightGray
                                      : Colors.transparent,
                                ))
                            : bottom,
                        titleSpacing: titleSpacing)
                : null,
            body: body,
          ),
        ),
      ),
    );
  }
}
