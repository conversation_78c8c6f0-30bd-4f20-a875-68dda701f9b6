import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford_v2.dart';
import 'package:tripc_app/widgets/commons/tripc_search/tripc_search.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcSearchScaffoldV2 extends StatefulWidget {
  const TripcSearchScaffoldV2({
    super.key,
    required this.controller,
    required this.body,
    required this.keyword,
    required this.titleAppBar,
    this.onLeadingPressed,
    this.onPopScope,
    this.hintText,
    this.onTapSearch,
    this.onChanged,
    required this.backgroundColor,
    this.fillTfColor,
  });

  final TextEditingController controller;
  final Widget body;
  final String keyword;
  final Function(String)? onChanged;
  final Function()? onLeadingPressed;
  final Function()? onPopScope;
  final String? hintText;
  final Function()? onTapSearch;
  final String titleAppBar;
  final Color backgroundColor;
  final Color? fillTfColor;

  @override
  State<TripcSearchScaffoldV2> createState() => _TripcSearchScaffoldV2State();
}

class _TripcSearchScaffoldV2State extends State<TripcSearchScaffoldV2> {
  final GlobalKey _searchKey = GlobalKey();
  double _appBarHeight = kToolbarHeight;

  void _updateAppBarHeight() {
    final context = _searchKey.currentContext;
    final renderBox = context?.findRenderObject() as RenderBox?;
    setState(() {
      _appBarHeight = renderBox?.size.height ?? 0;
    });
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateAppBarHeight();
    });
  }

  @override
  Widget build(BuildContext context) {
    return TripcScaffoldV2(
      onPressed: () => unfocusKeyboard(),
      onLeadingPressed: widget.onLeadingPressed,
      onPopScope: widget.onPopScope,
      backgroundColor: widget.backgroundColor,
      toolbarHeight: _appBarHeight,

      titleSpacing: 0.W,
      titleAppBar: TripcText(
        widget.titleAppBar.toSentenceCase(),
        fontWeight: FontWeight.w600,
        fontSize: 16,
        textColor: AppAssets.origin().black,
      ),
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(40.H),
        child: Container(
          alignment: Alignment.topCenter,
          margin: EdgeInsets.only(bottom: 5.H),
          height: 40.H,
          child: Padding(
            key: _searchKey,
            padding: EdgeInsets.symmetric(horizontal: 15.W),
            child: TripcSearch(
              fillColor: widget.fillTfColor,
              fontWeight: FontWeight.w300,
              hintText: widget.hintText,
              controller: widget.controller,
              onChanged: widget.onChanged,
              keyword: widget.keyword,
              onSubmit: widget.onTapSearch,
              keyboardType: TextInputType.text,
              contentPadding: EdgeInsets.zero,
              iconSuffix: const Icon(Icons.close),
              radius: 12.SP,
              suffixIconColor: AppAssets.origin().secondDarkGreyTextColor,
            ),
          ),
        ),
      ),
      body: widget.body,
    );
  }
}
