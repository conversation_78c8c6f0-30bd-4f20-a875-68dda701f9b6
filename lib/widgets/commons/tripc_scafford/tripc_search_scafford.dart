import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_search/tripc_search.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcSearchScafford extends StatefulWidget {
  const TripcSearchScafford(
      {super.key,
      required this.controller,
      required this.body,
      required this.keyword,
      this.onLeadingPressed,
      this.onPopScope,
      this.hintText,
      this.onTapSearch,
      this.onChanged});
  final TextEditingController controller;
  final Widget body;
  final String keyword;
  final Function(String)? onChanged;
  final Function()? onLeadingPressed;
  final Function()? onPopScope;
  final String? hintText;
  final Function()? onTapSearch;

  @override
  State<TripcSearchScafford> createState() => _TripcSearchScaffordState();
}

class _TripcSearchScaffordState extends State<TripcSearchScafford> {
  final GlobalKey _searchKey = GlobalKey();
  double _appBarHeight = kToolbarHeight;
  void _updateAppBarHeight() {
    final context = _searchKey.currentContext;
    final renderBox = context?.findRenderObject() as RenderBox?;
    setState(() {
      _appBarHeight = renderBox?.size.height ?? 0;
    });
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateAppBarHeight();
    });
  }

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
        onPressed: () => unfocusKeyboard(),
        onLeadingPressed: widget.onLeadingPressed,
        onPopScope: widget.onPopScope,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        hasBackButton: true,
        toolbarHeight: _appBarHeight,
        titleSpacing: 0.W,
        titleAppBar: Padding(
          key: _searchKey,
          padding: EdgeInsets.symmetric(horizontal: 12.W),
          child: TripcSearch(
            fontWeight: FontWeight.w300,
            hintText: widget.hintText,
            controller: widget.controller,
            onChanged: widget.onChanged,
            keyword: widget.keyword,
            onSubmit: widget.onTapSearch,
            keyboardType: TextInputType.text,
            radius: 12,
            suffixIconColor: AppAssets.origin().secondDarkGreyTextColor,
          ),
        ),
        actions: [
          TripcText(
            onTap: widget.onTapSearch,
            context.strings.text_search,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            textCase: TextCaseType.none,
            textAlign: TextAlign.center,
            textColor: AppAssets.origin().darkBlueColor,
            padding: EdgeInsets.only(right: 23.W),
          )
        ],
        body: widget.body);
  }
}
