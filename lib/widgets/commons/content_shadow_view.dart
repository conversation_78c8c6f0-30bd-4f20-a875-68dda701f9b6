import 'package:flutter/material.dart';
import 'package:tripc_app/utils/app_extension.dart';

class ContentShadowView extends StatelessWidget {
  const ContentShadowView({super.key, this.child});
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.H),
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.black.withValues(alpha: 0.2),
        //     spreadRadius: 2,
        //     blurRadius: 10,
        //     offset: const Offset(0, 4),
        //   ),
        // ],
      ),
      child: child,
    );
  }
}
