import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';

class MoreInfoIcon extends StatelessWidget {
  const MoreInfoIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 12.W,
      height: 12.W,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppAssets.origin().grayTextColor,
      ),
      child: Center(
        child: Text(
          'i',
          style: AppAssets.init.normalTextStyle.copyWith(
            fontSize: 10.SP,
            height: 0,
            color: AppAssets.origin().whiteBackgroundColor,
          ),
        ),
      ),
    );
  }
}
