import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/status_display_notifier.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/note/tripc_tcent_receive_note.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_button.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TotalPriceArea extends StatelessWidget {
  const TotalPriceArea(
      {super.key,
      required this.total,
      this.titleButton,
      this.onTap,
      this.isDisable = false,
      this.isReceiveBottom = true});

  final String? titleButton;
  final bool isDisable;
  final int total;
  final VoidCallback? onTap;
  final bool isReceiveBottom;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TripcText(
              context.strings.text_total_price,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              textColor: AppAssets.origin().blackColor,
            ),
            TripcText(
              total.vnd,
              fontSize: 24,
              fontWeight: FontWeight.w700,
              textColor: AppAssets.origin().blackColor,
            ),
          ],
        ),
        TripcText(
          context.strings.text_surcharge_included,
          fontSize: 12,
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.italic,
          textColor: AppAssets.origin().secondDarkGreyTextColor,
          padding: EdgeInsets.only(top: 2.H),
        ),
        if (isReceiveBottom)
          SizedBox(
            height: 8.H,
          ),
        Visibility(
          visible: !isReceiveBottom,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TripcTcentReceiveNote(
                tcent: total.vndToTcent,
                mainAxisAlignment: MainAxisAlignment.start,
                padding: EdgeInsets.only(top: 9.H),
              ),
              SizedBox(
                height: 30.H,
              ),
            ],
          ),
        ),
        TripcButton(
          onPressed: onTap,
          isButtonDisabled: isDisable,
          title: titleButton ?? context.strings.text_book_now,
          height: 60,
        ),
        if (isReceiveBottom)
          if (globalReleaseStatusNotifier.isDisplayAll)
            TripcTcentReceiveNote(
              tcent: total.vndToTcent,
              mainAxisAlignment: MainAxisAlignment.center,
              padding: EdgeInsets.only(top: 9.H),
            ),
        SizedBox(
          height: 10.H,
        ),
      ],
    );
  }
}
