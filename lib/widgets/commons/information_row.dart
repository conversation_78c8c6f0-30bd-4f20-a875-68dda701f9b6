import 'package:flutter/material.dart';
import 'package:html2md/html2md.dart' as html2md;
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class InformationRow extends StatelessWidget {
  const InformationRow(
      {super.key,
      required this.title,
      required this.content,
      required this.titleWidth,
      this.contentFontSize = 14,
      this.contentFontWeight = FontWeight.w300,
      this.crossAxisAlignment = CrossAxisAlignment.start,
      this.isHtmlContent = false});
  final String title;
  final String content;
  final double titleWidth;
  final double contentFontSize;
  final FontWeight contentFontWeight;
  final CrossAxisAlignment crossAxisAlignment;
  final bool isHtmlContent;

  @override
  Widget build(BuildContext context) {
    final convertedContent = isHtmlContent ? html2md.convert(content) : content;

    return Row(
      crossAxisAlignment: crossAxisAlignment,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: TripcText(
            title,
            fontSize: 14,
            height: 2,
            fontWeight: FontWeight.w500,
            textAlign: TextAlign.start,
            textColor: AppAssets.origin().blackColor,
          ),
        ),
        Expanded(
          child: TripcText(
            convertedContent,
            fontSize: contentFontSize,
            height: 2,
            fontWeight: contentFontWeight,
            textAlign: TextAlign.start,
            maxLines: 2,
            textColor: AppAssets.origin().blackColor,
          ),
        ),
      ],
    );
  }
}
