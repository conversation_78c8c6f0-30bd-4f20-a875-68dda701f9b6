import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcBlackToast extends StatelessWidget {
  const TripcBlackToast({super.key, required this.message, this.margin});
  final String message;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: margin,
        padding: EdgeInsets.symmetric(horizontal: 18.W, vertical: 21.H),
        decoration: BoxDecoration(
          color: AppAssets.origin().black70,
          borderRadius: BorderRadius.circular(8.SP),
        ),
        child: TripcText(
          message,
          fontSize: 12,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          textColor: AppAssets.origin().whiteBackgroundColor,
        ));
  }
}
