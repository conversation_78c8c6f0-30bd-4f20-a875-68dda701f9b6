import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class ViewAllRow extends StatelessWidget {
  const ViewAllRow(
      {super.key,
      required this.title,
      required this.onTapViewAll,
      this.titleTextCase = TextCaseType.none,
      this.seeAllColor,
      this.buttonText,
      this.padding,
      this.fontStyle,
      this.fontWeight,
      this.titleColor,
      this.titleSize,
      this.titleFontWeight});
  final String title;
  final TextCaseType titleTextCase;
  final VoidCallback? onTapViewAll;
  final EdgeInsetsGeometry? padding;
  final Color? seeAllColor;
  final String? buttonText;
  final FontStyle? fontStyle;
  final FontWeight? fontWeight;
  final Color? titleColor;
  final double? titleSize;
  final FontWeight? titleFontWeight;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ??
          EdgeInsets.symmetric(horizontal: 24.W, vertical: 12.H)
              .copyWith(bottom: 6.H, right: 8.W),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TripcText(
            title,
            fontSize: titleSize ?? 16,
            fontWeight: titleFontWeight ?? FontWeight.w500,
            textCase: titleTextCase,
            textColor: titleColor ?? AppAssets.origin().black,
          ),
          TripcText(buttonText ?? context.strings.text_see_all,
              fontSize: 12,
              fontWeight: fontWeight ?? FontWeight.w300,
              fontStyle: fontStyle ?? FontStyle.italic,
              textColor: seeAllColor ?? AppAssets.origin().black,
              onTap: onTapViewAll),
        ],
      ),
    );
  }
}