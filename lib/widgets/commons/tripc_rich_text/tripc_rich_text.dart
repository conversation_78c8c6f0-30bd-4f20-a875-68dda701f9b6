import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/constants/widget_const.dart';

class TripcRichText extends StatelessWidget {
  const TripcRichText({
    super.key,
    required this.text,
    this.children = const [],
    this.fontWeight,
    this.fontSize,
    this.textColor,
    this.textAlign,
    this.padding = EdgeInsets.zero,
    this.maxLines,
    this.overflow,
    this.lineHeight,
  });

  final String text;
  final List<TextSpan> children;
  final Color? textColor;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final EdgeInsets padding;
  final int? maxLines;
  final TextOverflow? overflow;
  final double? lineHeight;
  static TextSpan buildTextSpan(
    BuildContext context, {
    required String text,
    Color? textColor,
    FontWeight? fontWeight,
    double? fontSize,
    VoidCallback? onTap,
    bool underline = false,
  }) {
    return TextSpan(
      text: text,
      recognizer: TapGestureRecognizer()..onTap = onTap,
      style: AppAssets.init.normalTextStyle.copyWith(
        color: textColor ?? AppAssets.origin().blackColor,
        fontWeight: fontWeight ?? FontWeight.w400,
        fontSize: fontSize?.H ?? WidgetConst.defaultFontSize.H,
        decoration: underline ? TextDecoration.underline : TextDecoration.none,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: RichText(
        maxLines: maxLines,
        overflow: overflow ?? TextOverflow.clip,
        textAlign: textAlign ?? TextAlign.center,
        text: TextSpan(
          text: text,
          style: AppAssets.init.normalTextStyle.copyWith(
              color: textColor ?? AppAssets.origin().blackColor,
              fontWeight: fontWeight ?? FontWeight.w400,
              fontSize: fontSize?.H ?? WidgetConst.defaultFontSize.H,
              height: lineHeight),
          children: children,
        ),
      ),
    );
  }
}
