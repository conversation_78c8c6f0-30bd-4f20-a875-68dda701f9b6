import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:tripc_app/pages/profile/components/tripc_not_yet_login_dialog.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/providers.dart';
import 'package:tripc_app/utils/app_extension.dart';
import '../../../models/app/text_case_type.dart';

class TripcText extends StatelessWidget {
  const TripcText(this.text,
      {super.key,
      this.textColor,
      this.fontSize,
      this.fontWeight,
      this.padding = EdgeInsets.zero,
      this.onTap,
      this.onFinished,
      this.height,
      this.maxLines,
      this.overflow,
      this.textCase = TextCaseType.none,
      this.fontStyle,
      this.decoration,
      this.underlineColor,
      this.textAlign,
      this.prefix,
      this.mainAxisAlignment,
      this.spacing = 4,
      this.minFontSize = 6,
      this.enableAutoResize = false,
      this.behavior = HitTestBehavior.opaque,
      this.decorationStyle,
      this.showSuggestLoginDialog = false,
      this.ignorePointer = false,
      this.isLogin,
      this.letterSpacing = 0});

  final String? text;
  final Color? textColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final EdgeInsets padding;
  final VoidCallback? onTap;
  final ValueChanged<bool>? onFinished;
  final double? height;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextCaseType textCase;
  final FontStyle? fontStyle;
  final TextDecoration? decoration;
  final Color? underlineColor;
  final TextAlign? textAlign;
  final Widget? prefix;
  final MainAxisAlignment? mainAxisAlignment;
  final double? spacing;
  final bool enableAutoResize;
  final HitTestBehavior behavior;
  final TextDecorationStyle? decorationStyle;
  final double minFontSize;
  final double? letterSpacing;
  final bool showSuggestLoginDialog;
  final bool? isLogin;
  final bool ignorePointer;

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      ignoring: ignorePointer,
      child: GestureDetector(
        behavior: behavior,
        onTap: () {
          if (showSuggestLoginDialog && isLogin != null && !isLogin!) {
            dialogHelpers.show(context, child: const NotYetLoginDialog());
            return;
          }
          onTap?.call();
        },
        child: Container(
          padding: padding,
          child: prefix == null
              ? enableAutoResize
                  ? AutoSizeText(text?.getTextByCasing(textCase) ?? '',
                      minFontSize: minFontSize,
                      overflow: overflow,
                      maxLines: maxLines,
                      textAlign: textAlign ?? TextAlign.center,
                      style: AppAssets.origin().normalTextStyle.copyWith(
                            color: textColor ?? AppAssets.origin().blackColor,
                            fontWeight: fontWeight ?? FontWeight.w500,
                            fontSize: fontSize?.SP ?? 16.SP,
                            height: height?.H ?? 0,
                            fontStyle: fontStyle,
                            decoration: decoration,
                            decorationColor: textColor,
                            letterSpacing: letterSpacing,
                          ))
                  : Text(text?.getTextByCasing(textCase) ?? '',
                      overflow: overflow,
                      maxLines: maxLines,
                      textAlign: textAlign ?? TextAlign.center,
                      style: AppAssets.origin().normalTextStyle.copyWith(
                            color: textColor ?? AppAssets.origin().blackColor,
                            fontWeight: fontWeight ?? FontWeight.w500,
                            fontSize: fontSize?.SP ?? 16.SP,
                            height: height?.H ?? 0,
                            fontStyle: fontStyle,
                            decoration: decoration,
                            decorationColor: textColor,
                            letterSpacing: letterSpacing,
                          ))
              : Row(
                  mainAxisAlignment:
                      mainAxisAlignment ?? MainAxisAlignment.center,
                  children: [
                    prefix!,
                    SizedBox(
                      width: spacing,
                    ),
                    if (enableAutoResize)
                      AutoSizeText(text?.getTextByCasing(textCase) ?? '',
                          overflow: overflow,
                          maxLines: maxLines,
                          minFontSize: minFontSize.SP,
                          textAlign: textAlign ?? TextAlign.center,
                          style: AppAssets.origin().normalTextStyle.copyWith(
                                color:
                                    textColor ?? AppAssets.origin().blackColor,
                                fontWeight: fontWeight ?? FontWeight.w500,
                                fontSize: fontSize?.SP ?? 16.SP,
                                height: height?.H ?? 0,
                                fontStyle: fontStyle,
                                decoration: decoration,
                                decorationStyle: decorationStyle,
                                decorationColor: textColor,
                                letterSpacing: letterSpacing,
                              ))
                    else
                      Flexible(
                        child: Text(text?.getTextByCasing(textCase) ?? '',
                            overflow: overflow,
                            maxLines: maxLines,
                            textAlign: textAlign ?? TextAlign.center,
                            style: AppAssets.origin().normalTextStyle.copyWith(
                                  color: textColor ??
                                      AppAssets.origin().blackColor,
                                  fontWeight: fontWeight ?? FontWeight.w500,
                                  fontSize: fontSize?.SP ?? 16.SP,
                                  height: height?.H,
                                  fontStyle: fontStyle,
                                  decoration: decoration,
                                  decorationColor: textColor,
                                  letterSpacing: letterSpacing,
                                )),
                      ),
                  ],
                ),
        ),
      ),
    );
  }
}
