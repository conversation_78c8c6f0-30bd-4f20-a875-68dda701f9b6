import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcTcentReceiveNote extends StatelessWidget {
  const TripcTcentReceiveNote(
      {super.key,
      required this.tcent,
      this.padding,
      this.hasExpand = false,
      this.mainAxisAlignment = MainAxisAlignment.center,
      this.fontWeight,
      this.title});
  final int tcent;
  final EdgeInsetsGeometry? padding;
  final bool hasExpand;
  final MainAxisAlignment mainAxisAlignment;
  final FontWeight? fontWeight;
  final String? title;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: mainAxisAlignment,
        children: [
          AppAssets.origin().icTcent.widget(height: 16.H, width: 16.H),
          Visibility(
              visible: hasExpand,
              replacement: _note(context),
              child: Expanded(child: _note(context)))
        ],
      ),
    );
  }

  Widget _note(BuildContext context) {
    return TripcText(
      // title ?? '${context.strings.text_receive_now} ${tcent.tcent}(${context.strings.text_respectively} ${tcent.tcentToVND.vnd})',
      title ?? '${context.strings.text_receive_now} ${tcent.tcent}',
      fontSize: 12,
      enableAutoResize: true,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      fontWeight: fontWeight ?? FontWeight.w500,
      textColor: AppAssets.origin().secondDarkYellow,
      textAlign: TextAlign.start,
      padding: EdgeInsets.only(left: 8.W),
    );
  }
}
