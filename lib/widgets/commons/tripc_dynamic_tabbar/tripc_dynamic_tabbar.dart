import 'package:dynamic_tabbar/dynamic_tabbar.dart';
import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/tripc_tab_data.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/custom_widget/app_dynamic_tabbar_custom.dart';

class TripcDynamicTabbar extends StatelessWidget {
  const TripcDynamicTabbar(
      {super.key,
      required this.tabs,
      this.onTabChanged,
      this.padding,
      this.initialIndex = 0});
  final List<TripcTabData> tabs;
  final Function(int? index)? onTabChanged;
  final EdgeInsetsGeometry? padding;
  final int initialIndex;

  @override
  Widget build(BuildContext context) {
    return DynamicTabBarWidgetCustom(
      initialIndex: initialIndex,
      overlayColor: WidgetStateProperty.all<Color>(Colors.transparent),
      dynamicTabs: _buildTabs(context),
      onTabControllerUpdated: (tabController) {},
      physicsTabBarView: const NeverScrollableScrollPhysics(),
      dividerColor: AppAssets.init.secondDarkGreyTextColor,
      dividerHeight: 1.H,
      indicatorWeight: 0,
      indicator: const UnderlineTabIndicator(
          borderSide: BorderSide(color: Colors.transparent)),
      indicatorSize: TabBarIndicatorSize.tab,
      labelStyle: AppAssets.init.superBoldTextStyle
          .copyWith(color: AppAssets.init.darkBlueColor, fontSize: 14.SP),
      unselectedLabelStyle: AppAssets.init.superBoldTextStyle
          .copyWith(color: AppAssets.init.blackColor, fontSize: 14.SP),
      showBackIcon: false,
      showNextIcon: false,
      labelPadding: EdgeInsets.zero,
      padding: padding ?? EdgeInsets.zero,
      tabAlignment: TabAlignment.fill,
      isScrollable: false,
      onTabChanged: onTabChanged ?? (index) {},
    );
  }

  List<TabData> _buildTabs(BuildContext context) {
    return tabs
        .map((tab) => TabData(
            index: tab.index,
            title: Tab(
                child: SizedBox(
              width: MediaQuery.of(context).size.width / tabs.length,
              child: Center(
                child: Text(
                  tab.title,
                ),
              ),
            )),
            content: tab.screen))
        .toList();
  }
}
