import 'package:flutter/material.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/tripc_text_filed/tripc_text_filed.dart';

class TripcSearch extends StatefulWidget {
  const TripcSearch(
      {super.key,
      required this.controller,
      this.onSubmit,
      this.onChanged,
      this.keyword = '',
      this.readOnly = false,
      this.margin,
      this.contentPadding,
      this.suffix,
      this.enabledBorder,
      this.focusedBorder,
      this.fillColor,
      this.hintText,
      this.keyboardType = TextInputType.number,
      this.fontSize = 14,
      this.width,
      this.height,
      this.borderColor,
      this.iconSuffix,
      this.iconSearchSize = 20,
      this.prefixPadding,
      this.fontWeight = FontWeight.w500,
      this.onTap,
      this.suffixIconColor,
      this.hintTextColor,
      this.prefixIconColor,
      this.prefixIcon,
      this.radius = 6});

  final TextEditingController controller;
  final Function? onSubmit;
  final Function(String)? onChanged;
  final String keyword;
  final bool readOnly;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? suffix;
  final Widget? iconSuffix;
  final InputBorder? enabledBorder;
  final InputBorder? focusedBorder;
  final Color? fillColor;
  final TextInputType keyboardType;
  final String? hintText;
  final double radius;
  final double fontSize;
  final double? width;
  final double? height;
  final Color? borderColor;
  final double iconSearchSize;
  final EdgeInsetsGeometry? prefixPadding;
  final FontWeight? fontWeight;
  final VoidCallback? onTap;
  final Color? suffixIconColor;
  final Color? hintTextColor;
  final Color? prefixIconColor;
  final Widget? prefixIcon;

  @override
  State<TripcSearch> createState() => _TripcSearchState();
}

class _TripcSearchState extends State<TripcSearch> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    widget.controller.value = widget.controller.value.copyWith(
      text: widget.keyword,
    );
    return Container(
      padding: widget.margin ?? EdgeInsets.zero,
      height: widget.height,
      width: widget.width ?? double.infinity,
      child: TripcTextField(
        onTap: widget.onTap,
        contentPadding:
            widget.contentPadding ?? EdgeInsets.symmetric(vertical: 12.H),
        textCase: TextCaseType.none,
        keyboardType: widget.keyboardType,
        fillColor: widget.fillColor,
        controller: widget.controller,
        readOnly: widget.readOnly,
        prefixIcon: Container(
          padding:
              widget.prefixPadding ?? EdgeInsets.only(left: 15.W, right: 12.W),
          child: widget.prefixIcon ?? AppAssets.init.iconSearch.widget(
            width: widget.iconSearchSize.H,
            height: widget.iconSearchSize.H,
            alignment: Alignment.center,
            color: widget.prefixIconColor,
          ),
        ),
        enabledBorder: widget.enabledBorder,
        focusedBorder: widget.focusedBorder,
        borderColor: widget.borderColor ?? AppAssets.origin().searchStrokeColor,
        prefixSpacing: 0,
        textColor: AppAssets.origin().blackTextColor,
        fontSize: widget.fontSize,
        fontWeight: widget.fontWeight,
        hintTextColor: widget.hintTextColor,
        hintText: widget.hintText ?? context.strings.text_search,
        borderRadius: BorderRadius.circular(widget.radius.SP),
        onSubmit: (value) {
          if (widget.onSubmit != null) {
            widget.onSubmit!();
          }
        },
        onChanged: widget.onChanged,
        suffix: widget.suffix ??
            (widget.controller.value.text.isNotEmpty
                ? Padding(
                    padding: EdgeInsets.only(right: 15.W),
                    child: GestureDetector(
                      onTap: () {
                        widget.controller.clear();
                        widget.onChanged?.call('');
                      },
                      child: widget.iconSuffix ??
                          AppAssets.init.iconCircleClose.widget(
                            width: 20.H,
                            height: 20.H,
                            color: widget.suffixIconColor,
                          ),
                    ),
                  )
                : null),
      ),
    );
  }
}
