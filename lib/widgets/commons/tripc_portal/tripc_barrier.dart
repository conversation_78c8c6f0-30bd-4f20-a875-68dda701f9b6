import 'package:flutter/material.dart';
import 'package:flutter_portal/flutter_portal.dart';

class TripCBarrier extends StatelessWidget {
  const TripCBarrier({
    super.key,
    required this.onClose,
    required this.visible,
    required this.child,
    this.isBlur = true,
    required this.aligned,
    this.follower,
  });

  final Widget child;
  final VoidCallback onClose;
  final bool visible;
  final bool isBlur;
  final Aligned aligned;
  final Widget? follower;

  @override
  Widget build(BuildContext context) {
    return PortalTarget(
      visible: visible,
      anchor: aligned,
      closeDuration: kThemeAnimationDuration,
      portalFollower: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onClose,
        child: TweenAnimationBuilder<Color>(
          duration: kThemeAnimationDuration,
          tween: _ColorTween(
            begin: Colors.transparent,
            end: isBlur && visible ? const Color(0xFF1F1F1F).withOpacity(0.8) : Colors.transparent,
          ),
          builder: (context, color, child) {
            return ColoredBox(color: color);
          },
        ),
      ),
      child: child,
    );
  }
}

class _ColorTween extends Tween<Color> {
  _ColorTween({required Color begin, required Color end})
      : super(begin: begin, end: end);

  @override
  Color lerp(double t) => Color.lerp(begin, end, t)!;
}
