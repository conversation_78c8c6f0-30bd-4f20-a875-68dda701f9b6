import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_portal/flutter_portal.dart';

class TripCPortal extends StatefulWidget {
  const TripCPortal({
    super.key,
    this.visible = false,
    required this.aligned,
    required this.onPressed,
    this.icon,
    this.follower,
    this.isBlur = true,
    this.onLongPressed,
    this.isCenter = true,
    required this.onClose,
  });

  final bool visible;
  final Aligned aligned;
  final VoidCallback onPressed;
  final Widget? icon;
  final Widget? follower;
  final bool isBlur;
  final VoidCallback? onLongPressed;
  final bool isCenter;
  final VoidCallback onClose;

  @override
  State<TripCPortal> createState() => _TripCPortalState();
}

class _TripCPortalState extends State<TripCPortal> {
  bool _showPortal = false;
  Timer? _autoCloseTimer;

  @override
  void dispose() {
    _autoCloseTimer?.cancel();
    super.dispose();
  }

  void _startAutoCloseTimer() {
    _autoCloseTimer?.cancel();
    _autoCloseTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        widget.onClose();
      }
    });
  }

  @override
  void didUpdateWidget(TripCPortal oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.visible != oldWidget.visible) {
      setState(() {
        _showPortal = widget.visible;
      });
      if (widget.visible) {
        _startAutoCloseTimer();
      } else {
        _autoCloseTimer?.cancel();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: widget.onLongPressed,
      onTap: widget.onPressed,
      child: PortalTarget(
        visible: widget.visible,
        anchor: widget.aligned,
        closeDuration: kThemeAnimationDuration,
        portalFollower: widget.follower != null
            ? GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: widget.onClose,
              child: TweenAnimationBuilder<double>(
                        tween: Tween(begin: 0.0, end: widget.visible ? 1.0 : 0.0),
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.decelerate,
                        builder: (context, value, child) {
              if (value == 0.0 && !widget.visible) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    setState(() {
                      _showPortal = false;
                    });
                  }
                });
              }
              return Opacity(
                opacity: value,
                child: Transform.translate(
                  offset: Offset(0, 20 * (1 - value)),
                  child: child,
                ),
              );
                        },
                        child: widget.follower,
                      ),
            )
            : null,
        child: widget.isCenter
            ? Center(child: widget.icon)
            : widget.icon ?? const SizedBox.shrink(),
      ),
    );
  }
}
