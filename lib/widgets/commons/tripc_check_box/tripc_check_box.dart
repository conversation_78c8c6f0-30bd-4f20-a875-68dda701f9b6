import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/constants/widget_const.dart';

enum CheckBoxType {
  checkmark,
  circular,
}

class TripcCheckbox extends StatefulWidget {
  const TripcCheckbox(
      {super.key,
      this.onChanged,
      this.size,
      this.fillColor,
      this.borderColor,
      this.fillColorChecked,
      this.borderColorChecked,
      this.value,
      this.borderRadius,
      this.shape,
      this.onTap,
      this.innerCircleSize = 10,
      this.type = CheckBoxType.circular,
      this.borderWidth = WidgetConst.defaultBorderWidth});

  final ValueChanged<bool>? onChanged;
  final VoidCallback? onTap;
  final double? size;
  final Color? fillColor;
  final Color? borderColor;
  final Color? fillColorChecked;
  final Color? borderColorChecked;
  final bool? value;
  final CheckBoxType type;
  final BorderRadiusGeometry? borderRadius;
  final BoxShape? shape;
  final double innerCircleSize;
  final double borderWidth;

  @override
  State<TripcCheckbox> createState() => _TripcCheckboxState();
}

class _TripcCheckboxState extends State<TripcCheckbox> {
  late bool _isChecked;

  @override
  void initState() {
    _isChecked = widget.value ?? false;
    super.initState();
  }

  Widget get childByType {
    return widget.type == CheckBoxType.checkmark
        ? Icon(Icons.check,
            size: widget.size, color: AppAssets.origin().darkBlueColor)
        : Container(
            width: widget.innerCircleSize.H,
            height: widget.innerCircleSize.H,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppAssets.origin().secondaryColor,
            ),
          );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        final value = widget.value;
        if (value == null) {
          setState(() {
            _isChecked = !_isChecked;
          });
          widget.onChanged?.call(_isChecked);
          return;
        }
        widget.onChanged?.call(!widget.value!);
        widget.onTap?.call();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: widget.size?.H,
        height: widget.size?.H,
        decoration: BoxDecoration(
          borderRadius: widget.borderRadius,
          color: widget.value ?? _isChecked
              ? widget.fillColorChecked ?? Colors.transparent
              : widget.fillColor ?? Colors.transparent,
          shape: widget.shape ?? BoxShape.circle,
          border: Border.all(
              width: widget.borderWidth.H,
              color: widget.value ?? _isChecked
                  ? widget.borderColorChecked ??
                      AppAssets.origin().secondaryColor
                  : widget.borderColor ?? AppAssets.origin().darkGreyTextColor),
        ),
        child: widget.value ?? _isChecked
            ? Center(
                child: childByType,
              )
            : null,
      ),
    );
  }
}
