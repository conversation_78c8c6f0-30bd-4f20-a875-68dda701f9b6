import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcCommingSoon extends StatelessWidget {
  const TripcCommingSoon({super.key});

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
        hasBackButton: false,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.W),
          child: <PERSON>um<PERSON>(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 132.H),
                child: AppAssets.origin()
                    .imCommingSoon
                    .widget(height: 179.H, fit: BoxFit.cover),
              ),
              TripcText(
                context.strings.text_comming_soon_text,
                fontSize: 16,
                height: 1.5,
                fontWeight: FontWeight.w400,
                padding: EdgeInsets.only(top: 40.H),
              )
            ],
          ),
        ));
  }
}
