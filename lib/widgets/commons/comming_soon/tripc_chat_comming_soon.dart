import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_button/tripc_icon_button.dart';
import 'package:tripc_app/widgets/commons/tripc_scafford/tripc_scafford.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

class TripcChatCommingSoon extends StatelessWidget {
  const TripcChatCommingSoon({super.key});

  @override
  Widget build(BuildContext context) {
    return TripcScaffold(
        hasBackButton: false,
        centerTitle: false,
        leadingWidth: 0,
        toolbarHeight: 31.H,
        backgroundColor: AppAssets.origin().whiteBackgroundColor,
        titleAppBar: TripcText(
          context.strings.text_messages,
          fontSize: 18,
          fontWeight: FontWeight.w700,
          textColor: AppAssets.origin().blackColor,
          padding: EdgeInsets.only(left: 24.W),
        ),
        actions: [
          TripcIconButton(
              onPressed: () => Navigator.pop(context),
              child: Padding(
                padding: EdgeInsets.only(right: 24.W),
                child: AppAssets.origin().icClose.widget(
                    height: 13.H,
                    width: 13.H,
                    color: AppAssets.origin().blackColor),
              ))
        ],
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.W),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 132.H),
                child: AppAssets.origin()
                    .imCommingSoon
                    .widget(height: 179.H, fit: BoxFit.cover),
              ),
              TripcText(
                context.strings.text_comming_soon_text,
                fontSize: 16,
                height: 1.5,
                fontWeight: FontWeight.w400,
                padding: EdgeInsets.only(top: 40.H),
              )
            ],
          ),
        ));
  }
}
