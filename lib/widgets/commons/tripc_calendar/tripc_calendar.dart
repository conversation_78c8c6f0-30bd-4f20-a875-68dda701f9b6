import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/constants/widget_const.dart';
import 'package:flutter_inner_shadow/flutter_inner_shadow.dart';
import 'package:intl/intl.dart';

typedef OnSelectRangeCallback = void Function(DateTime? start, DateTime? end);

final class TripcCalendar extends StatefulWidget {
  const TripcCalendar({
    super.key,
    required this.selectedDay,
    this.onSelectDate,
    this.onSelectRange,
    this.canSelectRange = false,
    this.startDate,
    this.endDate,
  });

  final DateTime selectedDay;
  final bool canSelectRange;
  final Function(DateTime)? onSelectDate;
  final OnSelectRangeCallback? onSelectRange;
  final DateTime? startDate;
  final DateTime? endDate;

  @override
  State<TripcCalendar> createState() => _TripcCalendarState();
}

class _TripcCalendarState extends State<TripcCalendar> {
  DateTime? _selectedDay;
  DateTime? _rangeStart;
  DateTime? _rangeEnd;
  late DateTime _focusedDay;

  @override
  void initState() {
    super.initState();
    _selectedDay = widget.selectedDay;
    _focusedDay = widget.selectedDay;
  }

  bool _isDateEnabled(DateTime day) {
    final nowDate = DateUtils.dateOnly(DateTime.now());
    final date = DateUtils.dateOnly(day);
    DateTime? startDateOnly;
    DateTime? endDateOnly;
    if (widget.startDate != null) {
      startDateOnly = DateUtils.dateOnly(widget.startDate!);
    }
    if (widget.endDate != null) {
      endDateOnly = DateUtils.dateOnly(widget.endDate!);
    }

    final effectiveMinDate =
        (startDateOnly != null && startDateOnly.isAfter(nowDate))
            ? startDateOnly
            : nowDate;

    if (date.isBefore(effectiveMinDate)) {
      return false;
    }
    if (endDateOnly != null && date.isAfter(endDateOnly)) {
      return false;
    }
    return true;
  }

  bool _isMonthBeforeMin(DateTime monthToCheck) {
    final nowDate = DateUtils.dateOnly(DateTime.now());
    DateTime? startDateOnly;
    if (widget.startDate != null) {
      startDateOnly = DateUtils.dateOnly(widget.startDate!);
    }
    final effectiveMinDate =
        (startDateOnly != null && startDateOnly.isAfter(nowDate))
            ? startDateOnly
            : nowDate;
    final firstOfMonth = DateTime(monthToCheck.year, monthToCheck.month, 1);
    final lastOfMonth = DateTime(monthToCheck.year, monthToCheck.month + 1, 1)
        .subtract(const Duration(days: 1));
    return DateUtils.dateOnly(lastOfMonth).isBefore(effectiveMinDate);
  }

  bool _isMonthAfterMax(DateTime monthToCheck) {
    if (widget.endDate == null) {
      return false;
    }
    final endDateOnly = DateUtils.dateOnly(widget.endDate!);
    final firstOfMonth = DateTime(monthToCheck.year, monthToCheck.month, 1);
    return DateUtils.dateOnly(firstOfMonth).isAfter(endDateOnly);
  }

  @override
  Widget build(BuildContext context) {
    final orangeBorder =
        Border.all(width: 2.H, color: AppAssets.origin().secondDarkOrange);
    final textStyle = TextStyle(
        fontSize: 15.SP,
        fontWeight: FontWeight.w500,
        color: AppAssets.origin().black81F);
    final todayDecoration = BoxDecoration(
        borderRadius: BorderRadius.circular(8.SP), border: orangeBorder);
    final selectedDecoration = BoxDecoration(
        borderRadius: BorderRadius.circular(8.SP),
        color: AppAssets.origin().darkBlue8FF);

    final canGoToPrevMonth = !_isMonthBeforeMin(_focusedDay);
    final canGoToNextMonth = !_isMonthAfterMax(_focusedDay);

    return TableCalendar(
      firstDay: DateTime.utc(1900, 1, 1),
      lastDay: DateTime.utc(2200, 12, 31),
      focusedDay: _focusedDay,
      availableGestures: AvailableGestures.none,
      calendarFormat: CalendarFormat.month,
      rowHeight: 46.H,
      daysOfWeekHeight: 36.H,
      enabledDayPredicate: _isDateEnabled,
      daysOfWeekStyle: DaysOfWeekStyle(
          dowTextFormatter: (date, locale) {
            return DateFormat.E(locale).format(date).substring(0, 2);
          },
          weekdayStyle: TextStyle(
            fontSize: 15.SP,
            fontWeight: FontWeight.w400,
            color: AppAssets.origin().dayOfWeekColor,
          )),
      headerStyle: HeaderStyle(
        formatButtonVisible: false,
        leftChevronVisible: canGoToPrevMonth,
        rightChevronVisible: canGoToNextMonth,
        leftChevronIcon: _buildCalendarArrow(isArrowRight: false),
        rightChevronIcon: _buildCalendarArrow(isArrowRight: true),
        leftChevronPadding: EdgeInsets.zero,
        leftChevronMargin: EdgeInsets.zero,
        rightChevronMargin: EdgeInsets.zero,
        formatButtonPadding: EdgeInsets.zero,
        titleCentered: true,
        titleTextStyle: textStyle,
        headerPadding: EdgeInsets.zero,
      ),
      selectedDayPredicate: (day) {
        if (!widget.canSelectRange) {
          return _selectedDay != null && isSameDay(_selectedDay, day);
        }
        return false;
      },
      rangeStartDay: widget.canSelectRange ? _rangeStart : null,
      rangeEndDay: widget.canSelectRange ? _rangeEnd : null,
      rangeSelectionMode: widget.canSelectRange
          ? RangeSelectionMode.enforced
          : RangeSelectionMode.disabled,
      onDaySelected: widget.canSelectRange
          ? null
          : (selectedDay, focusedDay) {
              if (!_isDateEnabled(selectedDay)) {
                return;
              }
              setState(() {
                _selectedDay = selectedDay;
                _focusedDay = focusedDay;
                _rangeStart = null;
                _rangeEnd = null;
              });
              widget.onSelectDate?.call(selectedDay);
            },
      onRangeSelected: widget.canSelectRange
          ? (start, end, focusedDay) {
              if (start == null) {
                setState(() {
                  _rangeStart = null;
                  _rangeEnd = null;
                  _selectedDay = null;
                  _focusedDay = focusedDay;
                });
                widget.onSelectRange?.call(null, null);
                return;
              }
              if (!_isDateEnabled(start)) {
                return;
              }
              if (end != null && !_isDateEnabled(end)) {
                return;
              }

              if (end != null &&
                  DateUtils.dateOnly(end).isBefore(DateUtils.dateOnly(start))) {
                return;
              }
              setState(() {
                _rangeStart = start;
                _rangeEnd = end;
                _selectedDay = null;
                _focusedDay = focusedDay;
              });
              widget.onSelectRange?.call(start, end);
            }
          : null,
      onPageChanged: (focusedDay) {
        setState(() {
          _focusedDay = focusedDay;
        });
      },
      calendarStyle: CalendarStyle(
        cellMargin: EdgeInsets.symmetric(vertical: 5.H),
        outsideTextStyle: textStyle.copyWith(
            color: AppAssets.origin().dayOfWeekColor,
            fontWeight: FontWeight.w400),
        defaultTextStyle: textStyle,
        weekendTextStyle: textStyle,
        selectedTextStyle:
            textStyle.copyWith(color: AppAssets.origin().whiteBackgroundColor),
        todayTextStyle: textStyle,
        defaultDecoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.SP),
        ),
        weekendDecoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.SP),
        ),
        outsideDecoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.SP),
        ),
        selectedDecoration: selectedDecoration,
        todayDecoration: todayDecoration,
        rangeStartTextStyle:
            textStyle.copyWith(color: AppAssets.origin().whiteBackgroundColor),
        rangeEndTextStyle:
            textStyle.copyWith(color: AppAssets.origin().whiteBackgroundColor),
        withinRangeTextStyle: textStyle,
        withinRangeDecoration: BoxDecoration(
          color: AppAssets.origin().lightBlueDFF,
          shape: BoxShape.rectangle,
        ),
        disabledTextStyle: textStyle.copyWith(
          color: AppAssets.origin().dayOfWeekColor.withOpacity(0.4),
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}

Widget _buildCalendarArrow({required bool isArrowRight}) {
  return InnerShadow(
    shadows: [
      AppAssets.origin().innnerShadowTop,
      AppAssets.origin().innnerShadowBottom,
    ],
    child: Container(
        height: 32.H,
        width: 32.H,
        padding: EdgeInsets.all(6.H),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.SP),
          color: AppAssets.origin().whiteBackgroundColor,
          border: Border.all(
              width: WidgetConst.defaultBorderWidth.SP,
              color: AppAssets.origin().stroke0E5Color),
        ),
        child: Transform.flip(
          flipX: isArrowRight,
          child: AppAssets.origin().icRoundNoseLeft.widget(),
        )),
  );
}
