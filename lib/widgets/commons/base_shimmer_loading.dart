import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:tripc_app/widgets/extensions/context_extension.dart';

class BaseShimmerLoading extends StatelessWidget {
  const BaseShimmerLoading({
    super.key,
    required this.child,
    this.baseColor,
    this.highlightColor,
  });

  final Widget child;
  final Color? baseColor;
  final Color? highlightColor;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: baseColor ?? context.appCustomPallet.secondaryColor!,
      highlightColor: highlightColor ?? context.appCustomPallet.bgForm!,
      child: child,
    );
  }
}
