import 'package:flutter/material.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/widgets/commons/tripc_rich_text/tripc_rich_text.dart';

class TripcHighlightKeyword extends StatelessWidget {
  const TripcHighlightKeyword({
    super.key,
    this.description = '',
    this.keyword = '',
    this.fontSize = 16,
    this.maxLines,
    this.overflow,
    this.textAlign,
  });

  final String description;
  final String keyword;
  final double fontSize;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextAlign? textAlign;

  @override
  Widget build(BuildContext context) {
    return TripcRichText(
      text: '',
      fontSize: fontSize,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      children: [
        TextSpan(
          children: highlightOccurrences(context, description, keyword),
          style: AppAssets.origin()
              .normalTextStyle
              .copyWith(fontSize: 16, color: AppAssets.origin().black),
        ),
      ],
    );
  }

  List<TextSpan> highlightOccurrences(
      BuildContext context, String source, String query) {
    if (query.isEmpty) return [TextSpan(text: source)];

    final lowerSource = source.toLowerCase();
    final lowerQuery = query.toLowerCase();
    final matches = lowerQuery.allMatches(lowerSource);

    if (matches.isEmpty) return [TextSpan(text: source)];

    int lastMatchEnd = 0;
    final List<TextSpan> spans = [];
    for (final match in matches) {
      if (match.start > lastMatchEnd) {
        spans.add(TextSpan(text: source.substring(lastMatchEnd, match.start)));
      }

      spans.add(TextSpan(
        text: source.substring(match.start, match.end),
        style: AppAssets.origin()
            .boldTextStyle
            .copyWith(fontSize: 16, color: AppAssets.origin().black),
      ));

      lastMatchEnd = match.end;
    }
    // Add remaining text after last match
    if (lastMatchEnd < source.length) {
      spans.add(TextSpan(text: source.substring(lastMatchEnd)));
    }

    return spans;
  }
}
