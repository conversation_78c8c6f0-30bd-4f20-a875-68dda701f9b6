import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tripc_app/models/app/text_case_type.dart';
import 'package:tripc_app/models/remote/membership_repsonse/api_membership_model.dart';
import 'package:tripc_app/pages/profile/components/tripc_icon_more.dart';
import 'package:tripc_app/services/app/app_assets.dart';
import 'package:tripc_app/services/providers/tripc_account_provider.dart';
import 'package:tripc_app/utils/app_extension.dart';
import 'package:tripc_app/widgets/commons/tripc_text/tripc_text.dart';

import '../../status_display_notifier.dart';

class TripcCardInfo extends ConsumerWidget {
  const TripcCardInfo(
      {super.key,
      required this.width,
      required this.tripcIDMembership,
      this.onPressed,
      this.hasIconMore = false,
      this.isCardSelected = false});
  final double width;
  final VoidCallback? onPressed;
  final bool hasIconMore;
  final MembershipModel? tripcIDMembership;
  final bool isCardSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(pAccountProvider.select((value) => value.user));
    return Stack(children: [
      ClipRRect(
        borderRadius: BorderRadius.circular(12), // Ensure rounded corners
        child: tripcIDMembership?.rankType.getBackGround.widget(
          width: width,
          height: width * 198 / 345,
        ),
      ),
      SizedBox(
        width: width,
        height: width * 198 / 345,
        child: Row(
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(left: 12.W, top: 12.H, bottom: 18.H),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        AppAssets.origin()
                            .logo
                            .widget(width: 86.W, height: 35.H),
                        const SizedBox(
                          width: 8,
                        ),
                        if (isCardSelected)
                          AppAssets.origin().icCardStar.widget(
                                width: 24.W,
                                height: 24.H,
                              ),
                      ],
                    ),
                    Row(
                      children: [
                        TripcText(
                          context.strings.text_card_name_label,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          textColor: Colors.white,
                          textAlign: TextAlign.start,
                        ),
                        const TripcText('  '),
                        Expanded(
                          child: TripcText(
                            user?.fullname,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            textColor: Colors.white,
                            letterSpacing: 1.5.W,
                            maxLines: 1,
                            textAlign: TextAlign.start,
                            overflow: TextOverflow.ellipsis,
                            textCase: TextCaseType.upper,
                          ),
                        ),
                      ],
                    ),
                    TripcText(
                      tripcIDMembership?.number?.formatTripcId,
                      fontSize: 16,
                      textColor: Colors.white,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 2.W,
                    ),
                    Row(
                      children: [
                        TripcText(
                          context.strings.text_card_exp_label,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          textColor: Colors.white,
                          textAlign: TextAlign.start,
                        ),
                        const TripcText('  '),
                        TripcText(
                          tripcIDMembership?.expiredDate?.parseDateTime().ddYY,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          textColor: Colors.white,
                          letterSpacing: 1.5.W,
                        ),
                      ],
                    ),
                    Visibility(
                      visible: globalReleaseStatusNotifier.isDisplayAll,
                      child: Row(
                        children: [
                          TripcText(
                            context.strings.text_card_balance_account,
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            textColor: Colors.white,
                            textAlign: TextAlign.start,
                          ),
                          const TripcText('  '),
                          TripcText(
                            tripcIDMembership?.tcent?.tcent,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            textColor: Colors.white,
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      Positioned(
        bottom: 12.H,
        right: 0.W,
        top: 0,
        child: Padding(
          padding: EdgeInsets.only(bottom: 0.H),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              AppAssets.init.imgCardAvatar.widget(
                width: 100.W,
                height: 104.H,
              ),
              Row(
                children: [
                  AppAssets.init.icSpinningGradient.widget(
                    width: 43.W,
                    height: 46.H,
                  ),
                  SizedBox(
                    width: 12.W,
                  )
                ],
              )
            ],
          ),
        ),
      ),
      Visibility(
        visible: hasIconMore,
        child: Positioned(
            top: 20.H,
            right: 20.H,
            child: IconMoreGradientButton(
              onTap: onPressed,
            )),
      )
    ]);
  }
}
