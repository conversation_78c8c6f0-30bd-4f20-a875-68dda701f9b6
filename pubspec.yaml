name: tripc_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.

# dev version:
#version: 1.0.2+25
# prod version:
version: 1.0.2+7

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  meta: ^1.9.1
  flutter_screenutil: ^5.9.0
  flutter_svg: ^2.0.7
  flutter_spinkit: ^5.2.0
  fluttertoast: ^8.2.12
  logger: ^1.1.0
  tuple: ^2.0.2
  dio: ^5.3.3
  email_validator: ^2.1.17
  npreferences: ^5.0.0
  simple_animations: ^5.0.2
  flutter_riverpod: ^2.4.1
  riverpod_annotation: ^2.1.6
  auto_size_text: ^3.0.0
  http_parser: ^4.0.2
  loading_animation_widget: ^1.2.1
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^3.3.1
  flutter_native_splash: ^2.2.16
  intl_utils: ^2.8.5
  path_provider: ^2.1.3
  curl_logger_dio_interceptor: ^1.0.0
  shared_preferences: ^2.2.3
  device_info_plus: ^9.1.1
  persistent_bottom_nav_bar_v2: ^5.2.3
  flutter_portal: ^1.1.4
  gradient_circular_progress_indicator: ^0.0.4
  smooth_page_indicator: ^1.2.0+3
  slider_button: ^2.1.0
  blur: ^4.0.0
  pin_input_text_field: ^4.5.2
  qr_flutter: ^4.1.0
  flutter_staggered_grid_view: ^0.7.0
  dropdown_button2: ^2.3.9
  table_calendar: ^3.2.0
  modal_bottom_sheet: ^3.0.0
  flutter_inner_shadow: 0.0.1
  dotted_line: ^3.2.3
  dynamic_tabbar: ^1.0.9
  google_sign_in: ^6.2.1
  firebase_auth: ^5.1.4
  firebase_core: ^3.3.0
  flutter_facebook_auth: ^7.1.1
  crypto: ^3.0.6
  timezone: ^0.9.4
  toastification: ^3.0.1
  flutter_debouncer: ^2.0.0
  timelines_plus: ^1.0.6
  collection: ^1.18.0
  flutter_markdown: ^0.7.6+2
  vnpay_flutter: ^1.0.3
  readmore: ^3.0.0
  sign_in_with_apple: ^6.1.4
  webview_flutter: ^4.10.0
  image_picker: ^1.1.2
  permission_handler: ^11.4.0
  connectivity_plus: ^6.1.3
  dart_jsonwebtoken: ^2.17.0
  html2md: ^1.3.2
  flutter_inappwebview: ^6.1.5
  image_gallery_saver: ^2.0.3
  url_launcher: ^6.3.1
  flutter_html: ^3.0.0

  intl: any
  markdown: any
flutter_intl:
  enabled: true

dependency_overrides:
  intl: ^0.18.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/app/fonts/
    - assets/app/svg/
    - assets/app/png/
    - assets/app/lottie/

  fonts:
    - family: Pretendard
      fonts:
        - asset: assets/app/fonts/Pretendard/Pretendard-Black.otf
        - asset: assets/app/fonts/Pretendard/Pretendard-Bold.otf
        - asset: assets/app/fonts/Pretendard/Pretendard-ExtraBold.otf
        - asset: assets/app/fonts/Pretendard/Pretendard-ExtraLight.otf
        - asset: assets/app/fonts/Pretendard/Pretendard-Light.otf
          weight: 100
        - asset: assets/app/fonts/Pretendard/Pretendard-Medium.otf
        - asset: assets/app/fonts/Pretendard/Pretendard-SemiBold.otf
        - asset: assets/app/fonts/Pretendard/Pretendard-Thin.otf
        - asset: assets/app/fonts/Pretendard/Pretendard-Regular.otf

    - family: Roboto
      fonts:
        - asset: assets/base/fonts/Roboto/Roboto-Thin.ttf
          weight: 100
        - asset: assets/base/fonts/Roboto/Roboto-Light.ttf
          weight: 300
        - asset: assets/base/fonts/Roboto/Roboto-Regular.ttf
          weight: 400
        - asset: assets/base/fonts/Roboto/Roboto-Medium.ttf
          weight: 500
        - asset: assets/base/fonts/Roboto/Roboto-Bold.ttf
          weight: 700
        - asset: assets/base/fonts/Roboto/Roboto-Black.ttf
          weight: 900
